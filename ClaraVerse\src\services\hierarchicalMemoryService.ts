/**
 * SERVICE MÉMOIRE HIÉRARCHIQUE
 * Gestion intelligente de la mémoire conversation avec niveaux hiérarchiques
 */

import { ClaraMessage } from '../types/clara-types';

// Types pour la mémoire hiérarchique
export interface MessageSummary {
  id: string;
  timestamp: number;
  userQuery: string;
  assistantResponse: string;
  topics: string[];
  entities: string[];
  decisions: string[];
  documentsUsed: string[];
  importance: number; // 1-10
}

export interface TopicSummary {
  topic: string;
  firstMention: number;
  lastMention: number;
  frequency: number;
  keyPoints: string[];
  relatedDocuments: string[];
  userInterest: number; // 1-10
}

export interface DocumentContext {
  filename: string;
  lastUsed: number;
  usageCount: number;
  topics: string[];
  userQueries: string[];
  effectiveness: number; // 1-10 basé sur feedback
}

export interface ConversationMemory {
  immediate: ClaraMessage[];      // 10 derniers messages (actuel)
  recent: MessageSummary[];       // 50 derniers résumés
  longTerm: TopicSummary[];       // Sujets principaux
  documents: DocumentContext[];    // Documents utilisés
  userPreferences: Record<string, any>; // Préférences apprises
}

export class HierarchicalMemoryService {
  private memory: ConversationMemory;
  private readonly IMMEDIATE_LIMIT = 10;
  private readonly RECENT_LIMIT = 50;
  private readonly LONG_TERM_LIMIT = 20;

  constructor() {
    this.memory = {
      immediate: [],
      recent: [],
      longTerm: [],
      documents: [],
      userPreferences: {}
    };
  }

  /**
   * Ajouter un nouveau message à la mémoire hiérarchique
   */
  public addMessage(message: ClaraMessage, documentsUsed: string[] = []): void {
    // 1. Ajouter à la mémoire immédiate
    this.memory.immediate.push(message);
    
    // 2. Maintenir la limite de mémoire immédiate
    if (this.memory.immediate.length > this.IMMEDIATE_LIMIT) {
      const oldMessage = this.memory.immediate.shift();
      if (oldMessage) {
        this.archiveToRecent(oldMessage, documentsUsed);
      }
    }

    // 3. Mettre à jour le contexte des documents
    this.updateDocumentContext(documentsUsed, message);

    // 4. Extraire et stocker les entités/sujets
    this.extractAndStoreTopics(message);
  }

  /**
   * Archiver un message vers la mémoire récente
   */
  private archiveToRecent(message: ClaraMessage, documentsUsed: string[]): void {
    // Trouver le message utilisateur correspondant pour créer un résumé
    const userMessage = this.findCorrespondingUserMessage(message);
    
    if (userMessage && message.role === 'assistant') {
      const summary: MessageSummary = {
        id: `${userMessage.id}_${message.id}`,
        timestamp: message.timestamp || Date.now(),
        userQuery: userMessage.content,
        assistantResponse: this.summarizeResponse(message.content),
        topics: this.extractTopics(userMessage.content + ' ' + message.content),
        entities: this.extractEntities(userMessage.content + ' ' + message.content),
        decisions: this.extractDecisions(message.content),
        documentsUsed: documentsUsed,
        importance: this.calculateImportance(userMessage, message, documentsUsed)
      };

      this.memory.recent.push(summary);

      // Maintenir la limite de mémoire récente
      if (this.memory.recent.length > this.RECENT_LIMIT) {
        const oldSummary = this.memory.recent.shift();
        if (oldSummary) {
          this.archiveToLongTerm(oldSummary);
        }
      }
    }
  }

  /**
   * Archiver vers la mémoire long terme
   */
  private archiveToLongTerm(summary: MessageSummary): void {
    // Mettre à jour ou créer des résumés de sujets
    for (const topic of summary.topics) {
      let topicSummary = this.memory.longTerm.find(t => t.topic === topic);
      
      if (!topicSummary) {
        topicSummary = {
          topic,
          firstMention: summary.timestamp,
          lastMention: summary.timestamp,
          frequency: 1,
          keyPoints: [],
          relatedDocuments: [...summary.documentsUsed],
          userInterest: summary.importance
        };
        this.memory.longTerm.push(topicSummary);
      } else {
        topicSummary.lastMention = summary.timestamp;
        topicSummary.frequency++;
        topicSummary.keyPoints.push(...summary.decisions);
        topicSummary.relatedDocuments.push(...summary.documentsUsed);
        topicSummary.userInterest = Math.max(topicSummary.userInterest, summary.importance);
      }
    }

    // Maintenir la limite long terme
    if (this.memory.longTerm.length > this.LONG_TERM_LIMIT) {
      this.memory.longTerm.sort((a, b) => b.userInterest - a.userInterest);
      this.memory.longTerm = this.memory.longTerm.slice(0, this.LONG_TERM_LIMIT);
    }
  }

  /**
   * Obtenir le contexte de mémoire pour une nouvelle question
   */
  public getMemoryContext(query: string): {
    immediate: ClaraMessage[];
    relevantRecent: MessageSummary[];
    relevantTopics: TopicSummary[];
    suggestedDocuments: string[];
  } {
    const queryTopics = this.extractTopics(query);
    const queryEntities = this.extractEntities(query);

    // Mémoire immédiate (toujours incluse)
    const immediate = [...this.memory.immediate];

    // Mémoire récente pertinente
    const relevantRecent = this.memory.recent
      .filter(summary => 
        this.hasTopicOverlap(summary.topics, queryTopics) ||
        this.hasEntityOverlap(summary.entities, queryEntities)
      )
      .sort((a, b) => b.importance - a.importance)
      .slice(0, 5);

    // Sujets long terme pertinents
    const relevantTopics = this.memory.longTerm
      .filter(topic => queryTopics.includes(topic.topic))
      .sort((a, b) => b.userInterest - a.userInterest)
      .slice(0, 3);

    // Documents suggérés basés sur l'historique
    const suggestedDocuments = this.getSuggestedDocuments(queryTopics, queryEntities);

    return {
      immediate,
      relevantRecent,
      relevantTopics,
      suggestedDocuments
    };
  }

  /**
   * Mettre à jour le contexte des documents
   */
  private updateDocumentContext(documentsUsed: string[], message: ClaraMessage): void {
    for (const filename of documentsUsed) {
      let docContext = this.memory.documents.find(d => d.filename === filename);
      
      if (!docContext) {
        docContext = {
          filename,
          lastUsed: Date.now(),
          usageCount: 1,
          topics: this.extractTopics(message.content),
          userQueries: message.role === 'user' ? [message.content] : [],
          effectiveness: 5 // Score initial neutre
        };
        this.memory.documents.push(docContext);
      } else {
        docContext.lastUsed = Date.now();
        docContext.usageCount++;
        docContext.topics.push(...this.extractTopics(message.content));
        if (message.role === 'user') {
          docContext.userQueries.push(message.content);
        }
      }
    }
  }

  /**
   * Extraire les sujets d'un texte
   */
  private extractTopics(text: string): string[] {
    // Implémentation simple - peut être améliorée avec NLP
    const topics: string[] = [];
    const words = text.toLowerCase().split(/\s+/);
    
    // Mots-clés techniques
    const techKeywords = ['api', 'database', 'sql', 'python', 'javascript', 'react', 'node', 'server'];
    const businessKeywords = ['projet', 'client', 'budget', 'deadline', 'meeting', 'presentation'];
    const documentKeywords = ['pdf', 'document', 'fichier', 'rapport', 'analyse'];

    for (const word of words) {
      if (techKeywords.includes(word)) topics.push('technique');
      if (businessKeywords.includes(word)) topics.push('business');
      if (documentKeywords.includes(word)) topics.push('documents');
    }

    return [...new Set(topics)];
  }

  /**
   * Extraire les entités d'un texte
   */
  private extractEntities(text: string): string[] {
    // Implémentation simple - détection de noms, dates, etc.
    const entities: string[] = [];
    
    // Détection de noms propres (mots commençant par une majuscule)
    const namePattern = /\b[A-Z][a-z]+\b/g;
    const names = text.match(namePattern) || [];
    entities.push(...names);

    // Détection de dates
    const datePattern = /\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2}/g;
    const dates = text.match(datePattern) || [];
    entities.push(...dates);

    return [...new Set(entities)];
  }

  /**
   * Extraire les décisions d'un texte
   */
  private extractDecisions(text: string): string[] {
    const decisions: string[] = [];
    const decisionPatterns = [
      /nous avons décidé de (.+?)[\.\n]/gi,
      /la décision est de (.+?)[\.\n]/gi,
      /il faut (.+?)[\.\n]/gi,
      /nous devons (.+?)[\.\n]/gi
    ];

    for (const pattern of decisionPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        decisions.push(...matches);
      }
    }

    return decisions;
  }

  /**
   * Calculer l'importance d'un échange
   */
  private calculateImportance(userMessage: ClaraMessage, assistantMessage: ClaraMessage, documentsUsed: string[]): number {
    let importance = 5; // Base

    // Plus de documents utilisés = plus important
    importance += Math.min(documentsUsed.length * 2, 3);

    // Messages longs = plus important
    if (userMessage.content.length > 200) importance += 1;
    if (assistantMessage.content.length > 500) importance += 1;

    // Mots-clés importants
    const importantKeywords = ['important', 'urgent', 'décision', 'problème', 'solution'];
    const combinedText = userMessage.content + ' ' + assistantMessage.content;
    for (const keyword of importantKeywords) {
      if (combinedText.toLowerCase().includes(keyword)) {
        importance += 1;
      }
    }

    return Math.min(importance, 10);
  }

  /**
   * Résumer une réponse longue
   */
  private summarizeResponse(content: string): string {
    if (content.length <= 200) return content;
    
    // Prendre les 150 premiers caractères + "..."
    return content.substring(0, 150) + '...';
  }

  /**
   * Trouver le message utilisateur correspondant
   */
  private findCorrespondingUserMessage(assistantMessage: ClaraMessage): ClaraMessage | null {
    // Chercher le dernier message utilisateur dans la mémoire immédiate
    for (let i = this.memory.immediate.length - 1; i >= 0; i--) {
      const msg = this.memory.immediate[i];
      if (msg.role === 'user' && msg.timestamp && assistantMessage.timestamp) {
        if (assistantMessage.timestamp - msg.timestamp < 60000) { // Dans la minute
          return msg;
        }
      }
    }
    return null;
  }

  /**
   * Vérifier le chevauchement de sujets
   */
  private hasTopicOverlap(topics1: string[], topics2: string[]): boolean {
    return topics1.some(topic => topics2.includes(topic));
  }

  /**
   * Vérifier le chevauchement d'entités
   */
  private hasEntityOverlap(entities1: string[], entities2: string[]): boolean {
    return entities1.some(entity => entities2.includes(entity));
  }

  /**
   * Obtenir des suggestions de documents
   */
  private getSuggestedDocuments(queryTopics: string[], queryEntities: string[]): string[] {
    return this.memory.documents
      .filter(doc => 
        this.hasTopicOverlap(doc.topics, queryTopics) ||
        doc.userQueries.some(query => 
          queryEntities.some(entity => query.includes(entity))
        )
      )
      .sort((a, b) => {
        // Trier par efficacité et usage récent
        const scoreA = a.effectiveness + (Date.now() - a.lastUsed) / (1000 * 60 * 60 * 24); // Pénalité pour ancienneté
        const scoreB = b.effectiveness + (Date.now() - b.lastUsed) / (1000 * 60 * 60 * 24);
        return scoreB - scoreA;
      })
      .slice(0, 5)
      .map(doc => doc.filename);
  }

  /**
   * Extraire et stocker les sujets d'un message
   */
  private extractAndStoreTopics(message: ClaraMessage): void {
    const topics = this.extractTopics(message.content);
    
    for (const topic of topics) {
      let topicSummary = this.memory.longTerm.find(t => t.topic === topic);
      
      if (!topicSummary) {
        topicSummary = {
          topic,
          firstMention: message.timestamp || Date.now(),
          lastMention: message.timestamp || Date.now(),
          frequency: 1,
          keyPoints: [],
          relatedDocuments: [],
          userInterest: 5
        };
        this.memory.longTerm.push(topicSummary);
      } else {
        topicSummary.lastMention = message.timestamp || Date.now();
        topicSummary.frequency++;
      }
    }
  }

  /**
   * Obtenir les statistiques de mémoire
   */
  public getMemoryStats(): {
    immediate: number;
    recent: number;
    longTerm: number;
    documents: number;
    totalTopics: number;
  } {
    return {
      immediate: this.memory.immediate.length,
      recent: this.memory.recent.length,
      longTerm: this.memory.longTerm.length,
      documents: this.memory.documents.length,
      totalTopics: [...new Set(this.memory.longTerm.map(t => t.topic))].length
    };
  }

  /**
   * Exporter la mémoire pour sauvegarde
   */
  public exportMemory(): ConversationMemory {
    return JSON.parse(JSON.stringify(this.memory));
  }

  /**
   * Importer la mémoire depuis une sauvegarde
   */
  public importMemory(memory: ConversationMemory): void {
    this.memory = memory;
  }
}

// Instance globale
export const hierarchicalMemory = new HierarchicalMemoryService();
