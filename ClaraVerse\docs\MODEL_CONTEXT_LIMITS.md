# 🧠 **LIMITES DE CONTEXTE DES MODÈLES - CONFIGURATION OPTIMALE**

## 📋 **MODÈLES PRINCIPAUX UTILISÉS**

### **🎯 QWEN 3 SERIES - Mod<PERSON><PERSON>rincipaux**
| Modèle | Limite <PERSON> | Limite Chars | Stratégie |
|--------|---------------|--------------|-----------|
| **Qwen 3 4B** | 35,000 | ~140K | Peut aller un peu au-delà de 30K |
| **Qwen 3 7B** | 35,000 | ~140K | Peut aller un peu au-delà de 30K |
| **Qwen 3 8B** | 35,000 | ~140K | Peut aller un peu au-delà de 30K |
| **Qwen 3 14B** | 35,000 | ~140K | Peut aller un peu au-delà de 30K |
| **<PERSON>wen 3 32B** | 35,000 | ~140K | 🆕 À ajouter plus tard |

### **🎯 GEMMA SERIES - Mod<PERSON>les U<PERSON>**
| Mod<PERSON><PERSON> | Limite Tokens | Limite Chars | Stratégie |
|--------|---------------|--------------|-----------|
| **Gemma 27B** | 32,000 | ~128K | 🆕 À ajouter plus tard |
| **Gemma 2 27B** | 32,000 | ~128K | Variante possible |

### **🎯 AUTRES MODÈLES SUPPORTÉS**
| Modèle | Limite Tokens | Limite Chars | Usage |
|--------|---------------|--------------|-------|
| **CodeQwen** | 35,000 | ~140K | Code - même limite que Qwen 3 |
| **Qwen 2.5** | 32,000 | ~128K | Version précédente |
| **Qwen 2** | 32,000 | ~128K | Version précédente |

---

## ⚙️ **CONFIGURATION TECHNIQUE**

### **🎯 STRATÉGIE QWEN 3**
```typescript
// Version étendue jusqu'à 110K+ mais limité à 80K pour LM Studio
// Recommandation : ne pas dépasser 30K pour sécurité, mais peut aller un peu au-delà
'qwen3-4b': 35000,    // Peut aller un peu au-delà de 30K
'qwen3-7b': 35000,    // Peut aller un peu au-delà de 30K  
'qwen3-8b': 35000,    // Peut aller un peu au-delà de 30K
'qwen3-14b': 35000,   // Peut aller un peu au-delà de 30K
'qwen3-32b': 35000,   // 🆕 NOUVEAU - À ajouter plus tard
```

### **🧠 LOGIQUE DE DÉTECTION**
1. **Recherche exacte** : Par nom de modèle complet
2. **Recherche partielle** : Par fragments de nom (insensible à la casse)
3. **Heuristiques** : Basées sur la taille (32B, 27B, etc.)
4. **Fallback** : 8000 tokens (conservative)

### **📊 SEUILS DE COMPRESSION**
| Contexte Modèle | Seuil Déclenchement | Urgence Immédiate | Arrière-plan |
|------------------|---------------------|-------------------|--------------|
| **35K tokens** | 30K chars | >126K chars (90%) | >98K chars (70%) |
| **32K tokens** | 30K chars | >115K chars (90%) | >90K chars (70%) |
| **8K tokens** | 30K chars | >29K chars (90%) | >22K chars (70%) |

---

## 🔧 **IMPLÉMENTATION**

### **📍 Fichiers Concernés**
- `IntelligentCompressor.ts` : Détection et logique de compression
- `ClaraAssistant.tsx` : Fonction utilitaire partagée
- `constants.ts` : Configuration globale

### **🎯 Points Clés**
1. **Qwen 3** : Limite à 35K tokens (peut dépasser 30K)
2. **Gemma 27B** : Limite à 32K tokens
3. **Seuil global** : 30K caractères pour déclenchement
4. **Stratégie** : Conservative mais optimisée pour les modèles utilisés

### **🚀 Modèles à Ajouter**
- [ ] **Qwen 3 32B** : Quand disponible
- [ ] **Gemma 27B** : Quand ajouté
- [ ] **Autres variantes** : Selon besoins

---

## 📈 **PERFORMANCE ATTENDUE**

### **🎯 Qwen 3 (35K tokens)**
- **Compression à 70%** : ~98K caractères
- **Compression à 50%** : ~70K caractères  
- **Compression à 60%** : ~84K caractères

### **🎯 Gemma 27B (32K tokens)**
- **Compression à 70%** : ~90K caractères
- **Compression à 50%** : ~64K caractères
- **Compression à 60%** : ~77K caractères

---

## ✅ **VALIDATION**

### **🧪 Tests de Détection**
```javascript
// Dans la console du navigateur :
compressor.updateModelContext('qwen3-8b');  // → 35000 tokens
compressor.updateModelContext('gemma-27b'); // → 32000 tokens
compressor.updateModelContext('unknown');   // → 8000 tokens (fallback)
```

### **📊 Logs de Vérification**
```
🎯 Model detected: qwen3-8b → 35000 tokens (matched: qwen3-8b)
🧠 Context Manager updated for model: qwen3-8b (35000 tokens)
```

---

**🎊 Configuration optimale pour les modèles réellement utilisés avec stratégie intelligente !**
