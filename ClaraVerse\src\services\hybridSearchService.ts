/**
 * SERVICE RECHERCHE HYBRIDE
 * Combine recherche vectorielle, textuelle et sémantique pour des résultats optimaux
 */

import { DocumentInfo } from '../types/gdpr-types';

export interface SearchResult {
  document: DocumentInfo;
  content: string;
  score: number;
  searchType: 'vector' | 'text' | 'semantic' | 'direct';
  relevanceFactors: string[];
  confidence: number;
}

export interface HybridSearchOptions {
  useVectorSearch: boolean;
  useTextSearch: boolean;
  useSemanticSearch: boolean;
  useDirectContent: boolean;
  maxResults: number;
  minScore: number;
  boostFactors: {
    vector: number;
    text: number;
    semantic: number;
    direct: number;
  };
}

export class HybridSearchService {
  private readonly defaultOptions: HybridSearchOptions = {
    useVectorSearch: true,
    useTextSearch: true,
    useSemanticSearch: true,
    useDirectContent: true,
    maxResults: 10,
    minScore: 0.3,
    boostFactors: {
      vector: 1.0,
      text: 0.8,
      semantic: 1.2,
      direct: 1.5
    }
  };

  /**
   * Effectuer une recherche hybride
   */
  public async search(
    query: string,
    documents: DocumentInfo[],
    options: Partial<HybridSearchOptions> = {}
  ): Promise<SearchResult[]> {
    const searchOptions = { ...this.defaultOptions, ...options };
    const allResults: SearchResult[] = [];

    // 1. Recherche directe dans le contenu (priorité maximale)
    if (searchOptions.useDirectContent) {
      const directResults = await this.directContentSearch(query, documents);
      allResults.push(...directResults.map(result => ({
        ...result,
        score: result.score * searchOptions.boostFactors.direct,
        searchType: 'direct' as const
      })));
    }

    // 2. Recherche textuelle (BM25-like)
    if (searchOptions.useTextSearch) {
      const textResults = await this.textSearch(query, documents);
      allResults.push(...textResults.map(result => ({
        ...result,
        score: result.score * searchOptions.boostFactors.text,
        searchType: 'text' as const
      })));
    }

    // 3. Recherche vectorielle (si RAG Premium disponible)
    if (searchOptions.useVectorSearch) {
      const vectorResults = await this.vectorSearch(query, documents);
      allResults.push(...vectorResults.map(result => ({
        ...result,
        score: result.score * searchOptions.boostFactors.vector,
        searchType: 'vector' as const
      })));
    }

    // 4. Recherche sémantique (LightRAG si disponible)
    if (searchOptions.useSemanticSearch) {
      const semanticResults = await this.semanticSearch(query, documents);
      allResults.push(...semanticResults.map(result => ({
        ...result,
        score: result.score * searchOptions.boostFactors.semantic,
        searchType: 'semantic' as const
      })));
    }

    // 5. Fusionner et classer les résultats
    const mergedResults = this.mergeAndRankResults(allResults, searchOptions);

    return mergedResults;
  }

  /**
   * Recherche directe dans le contenu des documents
   */
  private async directContentSearch(query: string, documents: DocumentInfo[]): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const queryTerms = this.extractSearchTerms(query);

    for (const document of documents) {
      if (!document.content) continue;

      const content = document.content.toLowerCase();
      const queryLower = query.toLowerCase();

      let score = 0;
      const relevanceFactors: string[] = [];

      // Recherche exacte de la phrase
      if (content.includes(queryLower)) {
        score += 1.0;
        relevanceFactors.push('Phrase exacte trouvée');
      }

      // Recherche des termes individuels
      let termMatches = 0;
      for (const term of queryTerms) {
        if (content.includes(term.toLowerCase())) {
          termMatches++;
        }
      }

      if (termMatches > 0) {
        const termScore = termMatches / queryTerms.length;
        score += termScore * 0.8;
        relevanceFactors.push(`${termMatches}/${queryTerms.length} termes trouvés`);
      }

      // Bonus pour les correspondances dans le nom de fichier
      if (document.filename.toLowerCase().includes(queryLower)) {
        score += 0.5;
        relevanceFactors.push('Correspondance nom de fichier');
      }

      if (score > 0) {
        // Extraire le contexte pertinent
        const relevantContent = this.extractRelevantContext(document.content, query);

        results.push({
          document,
          content: relevantContent,
          score,
          searchType: 'direct',
          relevanceFactors,
          confidence: Math.min(score, 1.0)
        });
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * Recherche textuelle avec scoring BM25-like
   */
  private async textSearch(query: string, documents: DocumentInfo[]): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const queryTerms = this.extractSearchTerms(query);

    // Calculer IDF pour chaque terme
    const termIDF = this.calculateIDF(queryTerms, documents);

    for (const document of documents) {
      if (!document.content) continue;

      const docTerms = this.extractSearchTerms(document.content);
      const docLength = docTerms.length;
      
      let score = 0;
      const relevanceFactors: string[] = [];

      for (const term of queryTerms) {
        const termFreq = docTerms.filter(t => t === term).length;
        
        if (termFreq > 0) {
          // BM25-like scoring
          const tf = termFreq / docLength;
          const idf = termIDF.get(term) || 0;
          const termScore = tf * idf;
          
          score += termScore;
          relevanceFactors.push(`Terme "${term}": ${termFreq} occurrences`);
        }
      }

      if (score > 0) {
        const relevantContent = this.extractRelevantContext(document.content, query);

        results.push({
          document,
          content: relevantContent,
          score: Math.min(score, 1.0),
          searchType: 'text',
          relevanceFactors,
          confidence: Math.min(score * 0.8, 1.0)
        });
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * Recherche vectorielle (placeholder pour RAG Premium)
   */
  private async vectorSearch(query: string, documents: DocumentInfo[]): Promise<SearchResult[]> {
    // TODO: Intégrer avec RAG Premium Service
    // Pour l'instant, retourner un résultat vide
    console.log('🔍 Vector search not yet implemented');
    return [];
  }

  /**
   * Recherche sémantique (placeholder pour LightRAG)
   */
  private async semanticSearch(query: string, documents: DocumentInfo[]): Promise<SearchResult[]> {
    // TODO: Intégrer avec LightRAG
    // Pour l'instant, retourner un résultat vide
    console.log('🔍 Semantic search not yet implemented');
    return [];
  }

  /**
   * Fusionner et classer les résultats de différentes sources
   */
  private mergeAndRankResults(
    allResults: SearchResult[],
    options: HybridSearchOptions
  ): SearchResult[] {
    // Grouper par document
    const documentResults = new Map<string, SearchResult[]>();

    for (const result of allResults) {
      const key = `${result.document.id}_${result.document.filename}`;
      if (!documentResults.has(key)) {
        documentResults.set(key, []);
      }
      documentResults.get(key)!.push(result);
    }

    // Fusionner les scores pour chaque document
    const mergedResults: SearchResult[] = [];

    for (const [, results] of documentResults) {
      if (results.length === 1) {
        // Un seul résultat pour ce document
        mergedResults.push(results[0]);
      } else {
        // Fusionner plusieurs résultats pour le même document
        const merged = this.mergeDocumentResults(results);
        mergedResults.push(merged);
      }
    }

    // Filtrer par score minimum et limiter les résultats
    return mergedResults
      .filter(result => result.score >= options.minScore)
      .sort((a, b) => b.score - a.score)
      .slice(0, options.maxResults);
  }

  /**
   * Fusionner plusieurs résultats pour le même document
   */
  private mergeDocumentResults(results: SearchResult[]): SearchResult {
    const baseResult = results[0];
    
    // Calculer le score combiné (moyenne pondérée)
    let totalScore = 0;
    let totalWeight = 0;

    for (const result of results) {
      const weight = this.getSearchTypeWeight(result.searchType);
      totalScore += result.score * weight;
      totalWeight += weight;
    }

    const combinedScore = totalWeight > 0 ? totalScore / totalWeight : 0;

    // Combiner les facteurs de pertinence
    const allFactors = results.flatMap(r => r.relevanceFactors);
    const uniqueFactors = [...new Set(allFactors)];

    // Prendre le contenu du résultat avec le meilleur score
    const bestResult = results.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    // Calculer la confiance combinée
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;

    return {
      document: baseResult.document,
      content: bestResult.content,
      score: combinedScore,
      searchType: 'direct', // Type principal
      relevanceFactors: uniqueFactors,
      confidence: Math.min(avgConfidence * 1.2, 1.0) // Bonus pour résultats multiples
    };
  }

  /**
   * Obtenir le poids d'un type de recherche
   */
  private getSearchTypeWeight(searchType: SearchResult['searchType']): number {
    const weights = {
      direct: 1.5,
      semantic: 1.2,
      vector: 1.0,
      text: 0.8
    };
    return weights[searchType] || 1.0;
  }

  /**
   * Extraire les termes de recherche d'une requête
   */
  private extractSearchTerms(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2)
      .filter(term => !this.isStopWord(term));
  }

  /**
   * Vérifier si un mot est un mot vide
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais',
      'pour', 'avec', 'dans', 'sur', 'par', 'ce', 'cette', 'ces', 'que', 'qui',
      'the', 'a', 'an', 'and', 'or', 'but', 'for', 'with', 'in', 'on', 'by'
    ]);
    return stopWords.has(word.toLowerCase());
  }

  /**
   * Calculer l'IDF (Inverse Document Frequency) pour les termes
   */
  private calculateIDF(terms: string[], documents: DocumentInfo[]): Map<string, number> {
    const termIDF = new Map<string, number>();
    const totalDocs = documents.length;

    for (const term of terms) {
      let docsWithTerm = 0;
      
      for (const doc of documents) {
        if (doc.content && doc.content.toLowerCase().includes(term)) {
          docsWithTerm++;
        }
      }

      const idf = docsWithTerm > 0 ? Math.log(totalDocs / docsWithTerm) : 0;
      termIDF.set(term, idf);
    }

    return termIDF;
  }

  /**
   * Extraire le contexte pertinent autour des termes de recherche
   */
  private extractRelevantContext(content: string, query: string, contextLength: number = 300): string {
    const queryTerms = this.extractSearchTerms(query);
    const contentLower = content.toLowerCase();
    
    // Trouver la première occurrence d'un terme de recherche
    let bestPosition = -1;
    let bestTerm = '';

    for (const term of queryTerms) {
      const position = contentLower.indexOf(term.toLowerCase());
      if (position !== -1 && (bestPosition === -1 || position < bestPosition)) {
        bestPosition = position;
        bestTerm = term;
      }
    }

    if (bestPosition === -1) {
      // Aucun terme trouvé, retourner le début du contenu
      return content.substring(0, contextLength) + (content.length > contextLength ? '...' : '');
    }

    // Extraire le contexte autour du terme trouvé
    const start = Math.max(0, bestPosition - contextLength / 2);
    const end = Math.min(content.length, start + contextLength);
    
    let context = content.substring(start, end);
    
    // Ajouter des ellipses si nécessaire
    if (start > 0) context = '...' + context;
    if (end < content.length) context = context + '...';

    return context;
  }

  /**
   * Obtenir des statistiques de recherche
   */
  public getSearchStats(results: SearchResult[]): {
    totalResults: number;
    bySearchType: Record<string, number>;
    averageScore: number;
    averageConfidence: number;
  } {
    const stats = {
      totalResults: results.length,
      bySearchType: {} as Record<string, number>,
      averageScore: 0,
      averageConfidence: 0
    };

    if (results.length === 0) return stats;

    // Compter par type de recherche
    for (const result of results) {
      stats.bySearchType[result.searchType] = (stats.bySearchType[result.searchType] || 0) + 1;
    }

    // Calculer les moyennes
    stats.averageScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
    stats.averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;

    return stats;
  }
}

// Instance globale
export const hybridSearch = new HybridSearchService();
