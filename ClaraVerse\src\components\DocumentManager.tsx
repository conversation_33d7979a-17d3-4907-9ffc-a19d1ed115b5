/**
 * Document Manager - Version complète avec OCR intégré et visualisation
 * Système unifié de gestion des documents avec traitement OCR premium
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  FileText,
  Upload,
  Search,
  Filter,
  Edit3,
  Trash2,
  Eye,
  Download,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Settings,
  Database,
  FileImage,
  Languages,
  Layout,
  Maximize2,
  Minimize2,
  X
} from 'lucide-react';

// Types
interface DocumentInfo {
  id: number;
  filename: string;
  fileType: string;
  collection_name: string;
  metadata: {
    processed_with_ocr?: boolean;
    ocr_language?: string;
    total_pages?: number;
    ocr_confidence?: number;
    text_blocks_count?: number;
    processing_time?: number;
    source_file?: string;
    file_type?: string;
  };
  created_at: string;
  chunk_count: number;
  content?: string;
}

interface Collection {
  name: string;
  documentCount: number;
  createdAt: string;
  description: string;
}

interface DocumentManagerProps {
  onPageChange: (page: string) => void;
}

interface UploadSettings {
  selectedCollection: string;
}

interface ProcessingStatus {
  isProcessing: boolean;
  currentFile: string;
  stage: 'upload' | 'ocr' | 'indexing' | 'complete';
  progress: number;
  message: string;
}

const DocumentManager: React.FC<DocumentManagerProps> = ({ onPageChange }) => {
  // États principaux
  const [documents, setDocuments] = useState<DocumentInfo[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // États de filtrage et recherche
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  
  // États d'upload et traitement
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadSettings, setUploadSettings] = useState<UploadSettings>({
    selectedCollection: 'default_collection'
  });
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    isProcessing: false,
    currentFile: '',
    stage: 'upload',
    progress: 0,
    message: ''
  });
  
  // États de visualisation
  const [viewMode, setViewMode] = useState<'list' | 'preview' | 'edit'>('list');
  const [editContent, setEditContent] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Références
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ============================================================================
  // Chargement des données
  // ============================================================================

  const loadCollections = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:5001/collections');
      if (response.ok) {
        const data = await response.json();
        setCollections(data.collections || []);
      }
    } catch (error) {
      console.error('Erreur chargement collections:', error);
    }
  }, []);

  const loadDocuments = useCallback(async (collectionName?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const url = collectionName
        ? `http://localhost:5001/documents?collection_name=${encodeURIComponent(collectionName)}`
        : 'http://localhost:5001/documents';
        
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents || []);
      } else {
        setError('Erreur lors du chargement des documents');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Chargement initial
  useEffect(() => {
    loadCollections();
    loadDocuments();
  }, [loadCollections, loadDocuments]);

  // ============================================================================
  // Gestion de l'upload et traitement
  // ============================================================================

  const updateProcessingStatus = (stage: ProcessingStatus['stage'], progress: number, message: string, filename?: string) => {
    setProcessingStatus(prev => ({
      ...prev,
      isProcessing: true,
      stage,
      progress,
      message,
      currentFile: filename || prev.currentFile
    }));
  };

  const handleFileUpload = useCallback(async (files: FileList) => {
    if (!files.length) return;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const baseProgress = (i / files.length) * 100;

        // Étape 1: Préparation
        updateProcessingStatus('upload', baseProgress, `Préparation de ${file.name}...`, file.name);
        await new Promise(resolve => setTimeout(resolve, 200));

        const formData = new FormData();
        formData.append('file', file);
        formData.append('collection_name', uploadSettings.selectedCollection);
        formData.append('use_ocr', 'true');
        formData.append('ocr_language', 'fra+eng');
        formData.append('preserve_layout', 'true');
        formData.append('metadata', JSON.stringify({
          uploaded_via: 'document_manager',
          upload_timestamp: new Date().toISOString()
        }));

        // Étape 2: Analyse du contenu
        updateProcessingStatus('ocr', baseProgress + 20, `Analyse de ${file.name}...`, file.name);

        const response = await fetch('http://localhost:5001/documents/upload', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Erreur upload ${file.name}: ${response.statusText}`);
        }

        // Étape 3: Organisation
        updateProcessingStatus('indexing', baseProgress + 70, `Organisation de ${file.name}...`, file.name);

        const result = await response.json();
        console.log(`✅ ${file.name} traité:`, {
          processed_with_ocr: result.processed_with_ocr,
          text_length: result.text_length,
          collection: uploadSettings.selectedCollection
        });

        // Simulation du temps d'indexation pour UX
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Étape 4: Finalisation
      updateProcessingStatus('complete', 100, 'Finalisation...', '');
      await new Promise(resolve => setTimeout(resolve, 500));

      // 🔄 RECHARGEMENT UNIFIÉ
      await loadDocuments(uploadSettings.selectedCollection);
      await loadCollections();

      // 🔄 SYNCHRONISATION STORE
      try {
        const { useDocumentStore } = await import('../stores/documentStore');
        const reloadDocuments = useDocumentStore.getState().reloadDocuments;
        await reloadDocuments();
        console.log('🔄 Store synchronisé après upload batch');
      } catch (storeError) {
        console.warn('⚠️ Échec synchronisation store:', storeError);
      }

      setShowUploadModal(false);

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erreur traitement');
      updateProcessingStatus('upload', 0, 'Erreur lors du traitement', '');
    } finally {
      // Nettoyage du statut après un délai
      setTimeout(() => {
        setProcessingStatus({
          isProcessing: false,
          currentFile: '',
          stage: 'upload',
          progress: 0,
          message: ''
        });
      }, 2000);
    }
  }, [uploadSettings, loadDocuments, loadCollections]);

  const triggerFileUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // ============================================================================
  // Gestion des documents
  // ============================================================================

  const handleDocumentSelect = useCallback(async (document: DocumentInfo) => {
    setSelectedDocument(document);
    setIsLoading(true);
    
    try {
      const response = await fetch(`http://localhost:5001/documents/${document.id}`);
      if (response.ok) {
        const fullDocument = await response.json();
        setSelectedDocument(fullDocument);
        setEditContent(fullDocument.content || '');
        setViewMode('preview');
      }
    } catch (error) {
      console.error('Erreur chargement document:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleDocumentDelete = useCallback(async (document: DocumentInfo) => {
    console.log('🗑️ Tentative de suppression du document:', document);
    console.log('🔍 Document ID:', document.id, 'Type:', typeof document.id);

    if (!document.id) {
      setError('Erreur: ID du document manquant');
      return;
    }

    if (!confirm(`Supprimer le document "${document.filename}" ?`)) return;

    try {
      const deleteUrl = `http://localhost:5001/documents/${document.id}`;
      console.log('🌐 URL de suppression:', deleteUrl);

      const response = await fetch(deleteUrl, {
        method: 'DELETE'
      });

      console.log('📡 Réponse suppression:', response.status, response.statusText);

      if (response.ok) {
        console.log('✅ Document supprimé avec succès');

        // 🔄 RECHARGEMENT UNIFIÉ : Local puis store
        await loadDocuments(selectedCollection || undefined);
        await loadCollections();

        try {
          const { useDocumentStore } = await import('../stores/documentStore');
          const reloadDocuments = useDocumentStore.getState().reloadDocuments;
          await reloadDocuments();
          console.log('🔄 Store synchronisé après suppression');
        } catch (storeError) {
          console.warn('⚠️ Échec synchronisation store:', storeError);
        }

        if (selectedDocument?.id === document.id) {
          setSelectedDocument(null);
          setViewMode('list');
        }
      } else {
        const errorText = await response.text();
        console.error('❌ Erreur suppression:', response.status, errorText);
        setError(`Erreur lors de la suppression: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('❌ Exception suppression:', error);
      setError(error instanceof Error ? error.message : 'Erreur suppression');
    }
  }, [selectedCollection, loadDocuments, loadCollections, selectedDocument]);

  // ============================================================================
  // Filtrage et recherche
  // ============================================================================

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = !searchQuery || 
      doc.filename.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.fileType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCollection = !selectedCollection || 
      doc.collection_name === selectedCollection;
    
    return matchesSearch && matchesCollection;
  });

  // ============================================================================
  // Rendu des composants
  // ============================================================================

  const renderHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <Database className="w-8 h-8 text-wema-500" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Document Manager
          </h1>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Ajouter Document
        </button>
        
        <button
          onClick={() => loadDocuments(selectedCollection || undefined)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          disabled={isLoading}
        >
          <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>
    </div>
  );

  const renderUploadModal = () => {
    if (!showUploadModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-wema-100 dark:bg-wema-900/30 rounded-lg">
              <Upload className="w-5 h-5 text-wema-600 dark:text-wema-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Ajouter des Documents</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Reconnaissance automatique du texte</p>
            </div>
          </div>
          
          {/* Paramètres d'upload */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Collection</label>
              <select
                value={uploadSettings.selectedCollection}
                onChange={(e) => setUploadSettings(prev => ({ ...prev, selectedCollection: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-wema-500 focus:border-transparent"
              >
                <option value="default_collection">Collection par défaut</option>
                {collections.map(col => (
                  <option key={col.name} value={col.name}>{col.name}</option>
                ))}
              </select>
            </div>

            {/* Informations traitement automatique */}
            <div className="bg-wema-50 dark:bg-wema-900/20 border border-wema-200 dark:border-wema-800 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium text-wema-700 dark:text-wema-300">Traitement Intelligent</span>
              </div>
              <p className="text-xs text-wema-600 dark:text-wema-400">
                Extraction automatique du texte et organisation pour une recherche facile
              </p>
            </div>
          </div>
          
          {/* Zone d'upload */}
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center mb-4">
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-400 mb-2">Glissez vos fichiers ici ou</p>
            <button
              onClick={triggerFileUpload}
              className="px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors disabled:opacity-50"
              disabled={processingStatus.isProcessing}
            >
              {processingStatus.isProcessing ? 'Traitement...' : 'Parcourir'}
            </button>
          </div>

          {/* Statut de traitement */}
          {processingStatus.isProcessing && (
            <div className="mb-4 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin text-wema-500" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {processingStatus.currentFile}
                  </span>
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {Math.round(processingStatus.progress)}%
                </span>
              </div>

              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                <div
                  className="bg-wema-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${processingStatus.progress}%` }}
                />
              </div>

              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                <div className={`w-2 h-2 rounded-full ${
                  processingStatus.stage === 'upload' ? 'bg-wema-500' :
                  processingStatus.progress > 20 ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                <span>Upload</span>

                <div className={`w-2 h-2 rounded-full ${
                  processingStatus.stage === 'ocr' ? 'bg-wema-500' :
                  processingStatus.progress > 60 ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                <span>Analyse</span>

                <div className={`w-2 h-2 rounded-full ${
                  processingStatus.stage === 'indexing' ? 'bg-wema-500' :
                  processingStatus.progress > 90 ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                <span>Organisation</span>

                <div className={`w-2 h-2 rounded-full ${
                  processingStatus.stage === 'complete' ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                <span>Terminé</span>
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {processingStatus.message}
              </p>
            </div>
          )}
          
          {/* Boutons */}
          <div className="flex justify-end gap-2">
            <button
              onClick={() => setShowUploadModal(false)}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={processingStatus.isProcessing}
            >
              {processingStatus.isProcessing ? 'Traitement en cours...' : 'Fermer'}
            </button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.md,.csv,.png,.jpg,.jpeg,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            className="hidden"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {renderHeader()}
        {renderUploadModal()}
        
        {/* Filtres et recherche */}
        <div className="mb-6 flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Rechercher des documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-wema-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCollection}
            onChange={(e) => setSelectedCollection(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-wema-500"
          >
            <option value="">Toutes les collections</option>
            {collections.map((collection) => (
              <option key={collection.name} value={collection.name}>
                {collection.name} ({collection.documentCount} docs)
              </option>
            ))}
          </select>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters
                ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <Filter className="w-5 h-5" />
          </button>
        </div>

        {/* État de chargement */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-wema-500" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Chargement...</span>
          </div>
        )}

        {/* État d'erreur */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-700 dark:text-red-400">{error}</span>
            </div>
          </div>
        )}

        {/* Layout principal */}
        {!isLoading && !error && (
          <div className={`grid grid-cols-1 gap-6 ${isExpanded ? 'lg:grid-cols-1' : 'lg:grid-cols-3'}`}>
            {/* Liste des documents */}
            <div className={`${isExpanded ? 'hidden' : 'lg:col-span-2'}`}>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Documents ({filteredDocuments.length})
                  </h2>
                </div>

                <div className="p-4 flex-1 overflow-y-auto">
                  {filteredDocuments.length === 0 ? (
                    <div className="text-center py-16">
                      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FileText className="w-8 h-8 text-gray-400" />
                      </div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Aucun document trouvé
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        {searchQuery || selectedCollection ?
                          'Essayez de modifier vos filtres de recherche' :
                          'Commencez par ajouter vos premiers documents'
                        }
                      </p>
                      {!searchQuery && !selectedCollection && (
                        <button
                          onClick={() => setShowUploadModal(true)}
                          className="flex items-center gap-2 px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors mx-auto"
                        >
                          <Plus className="w-4 h-4" />
                          Ajouter des documents
                        </button>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredDocuments.map((document) => (
                        <div
                          key={document.id}
                          className={`flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer ${
                            selectedDocument?.id === document.id
                              ? 'bg-wema-50 dark:bg-wema-900/20 border border-wema-200 dark:border-wema-800'
                              : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                          onClick={() => handleDocumentSelect(document)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="relative">
                              <FileText className="w-5 h-5 text-gray-400" />
                              {document.metadata.processed_with_ocr && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full" title="Document analysé" />
                              )}
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900 dark:text-white">
                                {document.filename}
                              </h3>
                              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                <span>{document.fileType?.toUpperCase() || 'UNKNOWN'}</span>
                                <span>{document.chunk_count} chunks</span>
                                {document.metadata.processed_with_ocr && (
                                  <span className="flex items-center gap-1 text-green-600 dark:text-green-400">
                                    <FileImage className="w-3 h-3" />
                                    Analysé
                                  </span>
                                )}
                                {document.metadata.total_pages && (
                                  <span>{document.metadata.total_pages} pages</span>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDocumentSelect(document);
                              }}
                              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
                              title="Voir le document"
                            >
                              <Eye className="w-4 h-4" />
                            </button>

                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedDocument(document);
                                setViewMode('edit');
                              }}
                              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
                              title="Éditer le document"
                            >
                              <Edit3 className="w-4 h-4" />
                            </button>

                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDocumentDelete(document);
                              }}
                              className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg transition-colors"
                              title="Supprimer le document"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Panneau de visualisation */}
            <div className={`${isExpanded ? 'col-span-1' : 'lg:col-span-1'}`}>
              {selectedDocument ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {isExpanded && (
                          <button
                            onClick={() => setIsExpanded(false)}
                            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                            title="Retour à la liste"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {selectedDocument.filename}
                        </h3>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setViewMode('preview')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'preview'
                              ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                          }`}
                          title="Aperçu"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setViewMode('edit')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'edit'
                              ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                          }`}
                          title="Édition"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setIsExpanded(!isExpanded)}
                          className="p-2 rounded-lg transition-colors hover:bg-gray-100 dark:hover:bg-gray-800"
                          title={isExpanded ? "Réduire" : "Agrandir"}
                        >
                          {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>

                    {/* Métadonnées du document */}
                    <div className="mt-3 space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex justify-between">
                        <span>Type:</span>
                        <span>{selectedDocument.fileType?.toUpperCase() || 'UNKNOWN'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Collection:</span>
                        <span>{selectedDocument.collection_name}</span>
                      </div>
                      {selectedDocument.metadata.processed_with_ocr && (
                        <>
                          <div className="flex justify-between">
                            <span>Statut:</span>
                            <span className="text-green-600 dark:text-green-400">✓ Analysé</span>
                          </div>
                          {selectedDocument.metadata.total_pages && (
                            <div className="flex justify-between">
                              <span>Pages:</span>
                              <span>{selectedDocument.metadata.total_pages}</span>
                            </div>
                          )}
                          {selectedDocument.metadata.ocr_confidence && (
                            <div className="flex justify-between">
                              <span>Qualité:</span>
                              <span>{Math.round(selectedDocument.metadata.ocr_confidence)}%</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {/* Contenu du document */}
                  <div className={`p-4 overflow-y-auto ${isExpanded ? 'h-[70vh]' : 'h-96'}`}>
                    {viewMode === 'preview' ? (
                      <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-white font-mono">
                        {selectedDocument.content || 'Chargement du contenu...'}
                      </pre>
                    ) : (
                      <textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="w-full h-full resize-none border-0 focus:ring-0 bg-transparent text-gray-900 dark:text-white font-mono text-sm"
                        placeholder="Contenu du document..."
                      />
                    )}
                  </div>

                  {viewMode === 'edit' && (
                    <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => {
                          // TODO: Implémenter la sauvegarde
                          console.log('Sauvegarde:', editContent);
                        }}
                        className="w-full px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
                      >
                        Sauvegarder
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col">
                  {/* En-tête du panneau vide */}
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      Aperçu du Document
                    </h3>
                  </div>

                  {/* Contenu centré */}
                  <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
                    <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-6">
                      <FileText className="w-10 h-10 text-gray-400" />
                    </div>

                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Aucun document sélectionné
                    </h4>

                    <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-sm">
                      Cliquez sur un document dans la liste pour voir son contenu et ses détails
                    </p>

                    {filteredDocuments.length === 0 && (
                      <button
                        onClick={() => setShowUploadModal(true)}
                        className="flex items-center gap-2 px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                        Ajouter votre premier document
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentManager;
