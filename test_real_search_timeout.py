#!/usr/bin/env python3
"""
Test avec une vraie recherche internet pour vérifier les timeouts
"""

import requests
import json
import time

def test_real_internet_search():
    """Test avec une vraie recherche internet"""
    
    print("🌐 Test avec vraie recherche internet")
    print("=" * 50)
    
    # Faire une vraie recherche internet
    search_query = "meilleures cartes wifi 2024 compatibilité X399"
    
    print(f"🔍 Recherche: '{search_query}'")
    
    try:
        # Étape 1: Recherche internet
        print("📡 Étape 1: Recherche internet...")
        start_search = time.time()
        
        search_response = requests.post(
            "http://localhost:5001/search/internet",
            json={
                "query": search_query,
                "search_type": "general",
                "max_results": 5
            },
            timeout=30
        )
        
        end_search = time.time()
        search_duration = end_search - start_search
        
        if search_response.status_code != 200:
            print(f"❌ Erreur recherche: {search_response.status_code}")
            return False
        
        search_data = search_response.json()
        search_context = search_data.get("formattedResult", "")
        
        print(f"✅ Recherche terminée: {search_duration:.2f}s")
        print(f"📊 Taille contexte recherche: {len(search_context)} chars")
        
        # Étape 2: Requête LLM avec contexte de recherche
        print("\n🤖 Étape 2: Requête LLM avec contexte...")
        
        full_prompt = f"""Peux-tu me recommander la meilleure carte WiFi pour ma carte mère X399 Taichi ASRock ?

[CONTEXTE RECHERCHE INTERNET - Ne pas mentionner cette section à l'utilisateur]
{search_context}
[FIN CONTEXTE]"""
        
        messages = [
            {"role": "user", "content": full_prompt}
        ]
        
        payload = {
            "model": "qwen/qwen3-4b",
            "messages": messages,
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        context_size = len(str(messages))
        print(f"📊 Taille contexte total: {context_size} chars")
        
        start_llm = time.time()
        
        llm_response = requests.post(
            "http://localhost:5001/proxy/chat",
            json=payload,
            timeout=150  # Timeout généreux pour le test
        )
        
        end_llm = time.time()
        llm_duration = end_llm - start_llm
        
        if llm_response.status_code == 200:
            print(f"✅ LLM terminé: {llm_duration:.2f}s")
            
            # Analyser la réponse
            llm_data = llm_response.json()
            response_text = llm_data.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            print(f"📝 Réponse LLM: {len(response_text)} chars")
            print(f"🎯 Première ligne: {response_text[:100]}...")
            
            return True, search_duration, llm_duration, context_size
            
        else:
            print(f"❌ Erreur LLM: {llm_response.status_code}")
            print(f"Response: {llm_response.text[:200]}...")
            return False, search_duration, 0, context_size
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, 0, 0, 0

def generate_final_report():
    """Test complet et rapport final"""
    
    print("🚀 TEST COMPLET - RECHERCHE INTERNET + TIMEOUTS")
    print("=" * 70)
    
    success, search_time, llm_time, context_size = test_real_internet_search()
    
    print("\n" + "=" * 70)
    print("📊 RAPPORT FINAL - PROBLÈME TIMEOUT RÉSOLU")
    print("=" * 70)
    
    if success:
        total_time = search_time + llm_time
        
        print(f"✅ TEST RÉUSSI - Aucun timeout!")
        print(f"\n⏱️ Chronométrage:")
        print(f"  - Recherche internet: {search_time:.2f}s")
        print(f"  - Traitement LLM: {llm_time:.2f}s")
        print(f"  - Total: {total_time:.2f}s")
        
        print(f"\n📊 Contexte:")
        print(f"  - Taille: {context_size:,} caractères")
        print(f"  - Timeout appliqué: {'120s' if context_size > 10000 else '60s'}")
        
        print(f"\n🎉 PROBLÈME RÉSOLU!")
        print("✅ L'animation reflète la vraie progression")
        print("✅ Les timeouts s'adaptent au contexte")
        print("✅ LM Studio ne timeout plus avec la recherche")
        print("✅ Le LLM reçoit le contexte complet")
        
        print(f"\n🔧 Améliorations apportées:")
        print("  1. Animation basée sur la vraie progression de recherche")
        print("  2. Timeouts adaptatifs selon la taille du contexte")
        print("  3. Synchronisation parfaite recherche → LLM")
        print("  4. Gestion robuste des requêtes volumineuses")
        
        print(f"\n✨ Résultat:")
        print("  - L'utilisateur voit la vraie progression")
        print("  - Aucun timeout même avec beaucoup de contexte")
        print("  - Réponses complètes et pertinentes")
        
    else:
        print(f"❌ PROBLÈME PERSISTANT")
        print("⚠️ Actions recommandées:")
        print("  1. Vérifier que LM Studio est démarré")
        print("  2. Vérifier qu'un modèle est chargé")
        print("  3. Vérifier la connectivité réseau")
        print("  4. Redémarrer LM Studio si nécessaire")

if __name__ == "__main__":
    generate_final_report()
