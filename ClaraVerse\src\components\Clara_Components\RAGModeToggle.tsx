import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface RAGModeToggleProps {
  fastMode: boolean;
  onToggle: (fastMode: boolean) => void;
  className?: string;
}

const RAGModeToggle: React.FC<RAGModeToggleProps> = ({ 
  fastMode, 
  onToggle, 
  className = '' 
}) => {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Label Mode Performant */}
      <div className="flex items-center space-x-2">
        <Brain className={`w-4 h-4 transition-colors duration-200 ${
          !fastMode ? 'text-blue-500' : 'text-gray-400'
        }`} />
        <span className={`text-sm font-medium transition-colors duration-200 ${
          !fastMode ? 'text-blue-600' : 'text-gray-500'
        }`}>
          Performant
        </span>
      </div>

      {/* Toggle Switch Rond */}
      <div 
        className={`relative inline-flex h-6 w-11 items-center rounded-full cursor-pointer transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
          fastMode 
            ? 'bg-gradient-to-r from-orange-400 to-red-500 shadow-lg' 
            : 'bg-gradient-to-r from-blue-400 to-blue-600 shadow-lg'
        }`}
        onClick={() => onToggle(!fastMode)}
        role="switch"
        aria-checked={fastMode}
        aria-label={fastMode ? "Mode Rapide activé" : "Mode Performant activé"}
      >
        {/* Cercle mobile */}
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform duration-200 ease-in-out ${
            fastMode ? 'translate-x-6' : 'translate-x-1'
          }`}
        >
          {/* Icône dans le cercle */}
          <div className="flex items-center justify-center h-full w-full">
            {fastMode ? (
              <Zap className="w-2.5 h-2.5 text-orange-500" />
            ) : (
              <Brain className="w-2.5 h-2.5 text-blue-500" />
            )}
          </div>
        </span>
      </div>

      {/* Label Mode Rapide */}
      <div className="flex items-center space-x-2">
        <Zap className={`w-4 h-4 transition-colors duration-200 ${
          fastMode ? 'text-orange-500' : 'text-gray-400'
        }`} />
        <span className={`text-sm font-medium transition-colors duration-200 ${
          fastMode ? 'text-orange-600' : 'text-gray-500'
        }`}>
          Rapide
        </span>
      </div>

      {/* Tooltip informatif */}
      <div className="relative group">
        <div className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center cursor-help">
          <span className="text-xs text-gray-600">?</span>
        </div>
        
        {/* Tooltip content */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          <div className="text-center">
            <div className="font-semibold mb-1">
              {fastMode ? '⚡ Mode Rapide' : '🧠 Mode Performant'}
            </div>
            <div className="text-gray-300">
              {fastMode 
                ? 'Réponses ultra-rapides (~1s)\nMoins de contexte RAG'
                : 'Analyse complète (~3-5s)\nContexte RAG maximum'
              }
            </div>
          </div>
          {/* Flèche du tooltip */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>
    </div>
  );
};

export default RAGModeToggle;
