#!/usr/bin/env python3
"""
Analyse la taille des dossiers pour identifier les gros consommateurs d'espace
"""

import os
import sys
from pathlib import Path

def get_size(path):
    """Calcule la taille d'un dossier ou fichier"""
    if os.path.isfile(path):
        return os.path.getsize(path)
    
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except (OSError, PermissionError):
        pass
    return total

def format_size(size_bytes):
    """Formate la taille en unités lisibles"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.1f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes} B"

def analyze_directory(directory="."):
    """Analyse tous les dossiers dans le répertoire donné"""
    print(f"🔍 Analyse de la taille des dossiers dans: {os.path.abspath(directory)}")
    print("=" * 60)
    
    items = []
    
    # Analyser tous les éléments du dossier
    try:
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isdir(item_path):
                size = get_size(item_path)
                items.append((item, size, "DIR"))
            elif os.path.isfile(item_path):
                size = get_size(item_path)
                if size > 10 * 1024 * 1024:  # Fichiers > 10MB
                    items.append((item, size, "FILE"))
    except PermissionError:
        print("❌ Permission refusée pour lire le dossier")
        return
    
    # Trier par taille décroissante
    items.sort(key=lambda x: x[1], reverse=True)
    
    # Afficher les résultats
    total_size = 0
    print(f"{'Nom':<30} {'Taille':<15} {'Type':<6}")
    print("-" * 60)
    
    for name, size, item_type in items:
        if size > 1024 * 1024:  # Afficher seulement > 1MB
            print(f"{name:<30} {format_size(size):<15} {item_type:<6}")
            total_size += size
    
    print("-" * 60)
    print(f"{'TOTAL ANALYSÉ':<30} {format_size(total_size):<15}")
    
    # Identifier les candidats à la suppression
    print("\n🗑️ CANDIDATS À LA SUPPRESSION:")
    print("-" * 40)
    
    candidates = []
    for name, size, item_type in items:
        if item_type == "DIR":
            if name in ["node_modules", "__pycache__", ".git", "dist", "build"]:
                if name == "node_modules":
                    print(f"⚠️  {name:<20} {format_size(size):<15} (Développement - peut être régénéré)")
                elif name == "__pycache__":
                    print(f"✅ {name:<20} {format_size(size):<15} (Cache Python - peut être supprimé)")
                    candidates.append(name)
                elif name == ".git":
                    print(f"⚠️  {name:<20} {format_size(size):<15} (Historique Git - garder)")
                elif name in ["dist", "build"]:
                    print(f"⚠️  {name:<20} {format_size(size):<15} (Build - peut être régénéré)")
            elif "test" in name.lower() and size > 100 * 1024 * 1024:  # Tests > 100MB
                print(f"❓ {name:<20} {format_size(size):<15} (Tests volumineux - vérifier)")
            elif name.endswith(("_temp", "_tmp", "temp", "tmp")):
                print(f"✅ {name:<20} {format_size(size):<15} (Temporaire - peut être supprimé)")
                candidates.append(name)
            elif "cache" in name.lower():
                print(f"✅ {name:<20} {format_size(size):<15} (Cache - peut être supprimé)")
                candidates.append(name)
    
    if candidates:
        print(f"\n💡 Vous pourriez libérer environ {format_size(sum(get_size(c) for c in candidates if os.path.exists(c)))} en supprimant les caches/temporaires")
    
    return items

if __name__ == "__main__":
    analyze_directory()
