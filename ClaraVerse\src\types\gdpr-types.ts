/**
 * Types for GDPR Document Processing System
 * Comprehensive type definitions for OCR, anonymization, and document processing
 */

// ============================================================================
// Core Document Types
// ============================================================================

export interface ProcessedDocument {
  id: string;
  filename: string;
  fileType: string;
  size: number;
  uploadedAt: Date;
  status: DocumentStatus;
  collectionName: string;
}

export type DocumentStatus = 
  | 'uploading'
  | 'processing'
  | 'ocr_complete'
  | 'anonymization_preview'
  | 'anonymization_complete'
  | 'ready_for_rag'
  | 'completed'
  | 'error';

// ============================================================================
// OCR Types
// ============================================================================

export interface TextBlock {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  confidence: number;
  pageNumber: number;
}

export interface TableInfo {
  table_id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  rows: number;
  cols: number;
  cells: string[][];
  confidence: number;
  page_number: number;
}

export interface OCRResult {
  success: boolean;
  text: string;
  formatted_text: string;
  text_blocks: TextBlock[];
  tables?: TableInfo[];
  metadata: {
    imageSize?: [number, number];
    totalPages?: number;
    language: string;
    preserveLayout: boolean;
    dpi?: number;
    totalBlocks: number;
    totalTables?: number;
    tableDetectionEnabled?: boolean;
    ocrCapabilities?: any;
  };
  error_message?: string;
}

export interface OCRRequest {
  file_base64: string;
  file_type: string;
  preserve_layout?: boolean;
  language?: string;
  dpi?: number;
  detect_tables?: boolean;
  table_mode?: boolean;
}

// ============================================================================
// GDPR Anonymization Types
// ============================================================================

export interface AnonymizationMapping {
  originalText: string;
  anonymizedText: string;
  entityType: string;
  startPos: number;
  endPos: number;
  replacementId: string;
}

export interface HighlightedRange {
  start: number;
  end: number;
  entityType: string;
  confidence: number;
  originalText: string;
  replacement: string;
  isAnonymized?: boolean; // Track if this range should be anonymized
}

export interface AnonymizationResult {
  success: boolean;
  anonymized_text: string;
  mappings: string; // JSON string of AnonymizationMapping[]
  highlighted_ranges: HighlightedRange[];
  metadata: {
    entitiesFound: number;
    language: string;
    entityTypes: string[];
  };
}

export interface AnonymizationPreview {
  totalEntities: number;
  entityTypes: Record<string, number>;
  entities: Array<{
    text: string;
    type: string;
    start: number;
    end: number;
    confidence: number;
  }>;
}

export interface AnonymizationRequest {
  text: string;
  language?: string;
  preserve_layout?: boolean;
  custom_ranges?: HighlightedRange[];
}

export interface DeanonymizationRequest {
  anonymized_text: string;
  mappings_json: string;
  restore_entities?: string[];
}

// ============================================================================
// Document Processing Workflow Types
// ============================================================================

export interface DocumentProcessingStep {
  id: string;
  name: string;
  status: StepStatus;
  progress: number;
  message?: string;
  error?: string;
  startTime?: Date;
  endTime?: Date;
}

export type StepStatus = 'pending' | 'running' | 'completed' | 'error' | 'skipped';

export interface DocumentProcessingWorkflow {
  documentId: string;
  steps: DocumentProcessingStep[];
  currentStepIndex: number;
  overallProgress: number;
  status: DocumentStatus;
}

export interface DocumentProcessRequest {
  fileBase64: string;
  fileType: string;
  filename: string;
  collectionName?: string;
  anonymize?: boolean;
  preserveLayout?: boolean;
  ocrLanguage?: string;
  anonymizationLanguage?: string;
}

export interface DocumentProcessResponse {
  success: boolean;
  documentId: number;
  collectionName: string;
  ocrResult: {
    text: string;
    formattedText: string;
    metadata: any;
  };
  anonymizationResult?: {
    anonymizedText: string;
    mappings: string;
    highlightedRanges: HighlightedRange[];
    metadata: any;
  };
  ragChunksCreated: number;
}

// ============================================================================
// UI Component Types
// ============================================================================

export interface DocumentUploadConfig {
  acceptedTypes: string[];
  maxFileSize: number;
  maxFiles: number;
  allowMultiple: boolean;
}

export interface DocumentPreviewConfig {
  showLineNumbers: boolean;
  highlightAnonymizations: boolean;
  allowEditing: boolean;
  showConfidenceScores: boolean;
}

export interface AnonymizationControlsConfig {
  enabledEntityTypes: string[];
  defaultLanguage: string;
  showPreview: boolean;
  allowPartialRestore: boolean;
}

// ============================================================================
// Entity Types for GDPR
// ============================================================================

export const GDPR_ENTITY_TYPES = {
  // Standard entities
  PERSON: 'PERSON',
  EMAIL_ADDRESS: 'EMAIL_ADDRESS',
  PHONE_NUMBER: 'PHONE_NUMBER',
  CREDIT_CARD: 'CREDIT_CARD',
  IBAN_CODE: 'IBAN_CODE',
  IP_ADDRESS: 'IP_ADDRESS',
  LOCATION: 'LOCATION',
  ORGANIZATION: 'ORGANIZATION',
  DATE_TIME: 'DATE_TIME',
  NRP: 'NRP', // National Registration Number
  MEDICAL_LICENSE: 'MEDICAL_LICENSE',
  URL: 'URL',

  // French-specific entities for legal documents
  FRENCH_SSN: 'FRENCH_SSN',
  FRENCH_RIB: 'FRENCH_RIB',
  FRENCH_ADDRESS: 'FRENCH_ADDRESS',
  FRENCH_PERSON: 'FRENCH_PERSON',
  FRENCH_PHONE: 'FRENCH_PHONE',
  FRENCH_LEGAL_ID: 'FRENCH_LEGAL_ID'
} as const;

export type GDPREntityType = keyof typeof GDPR_ENTITY_TYPES;

export interface EntityTypeConfig {
  type: GDPREntityType;
  label: string;
  description: string;
  color: string;
  enabled: boolean;
  priority: number;
}

// ============================================================================
// Error Types
// ============================================================================

export interface ProcessingError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  step?: string;
}

// ============================================================================
// Settings and Configuration
// ============================================================================

export interface GDPRProcessingSettings {
  defaultOcrLanguage: string;
  defaultAnonymizationLanguage: string;
  preserveLayoutByDefault: boolean;
  autoAnonymizeByDefault: boolean;
  defaultCollection: string;
  enabledEntityTypes: GDPREntityType[];
  ocrDpi: number;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ============================================================================
// Collection Types (extending existing)
// ============================================================================

export interface Collection {
  id: number;
  name: string;
  description: string;
  documentCount: number;
  createdAt: string;
}

// ============================================================================
// Document Management Types
// ============================================================================

export interface DocumentInfo {
  id: number;
  filename: string;
  fileType: string;
  collectionName: string;
  metadata: Record<string, any>;
  createdAt: string;
  chunkCount: number;
  isAnonymized?: boolean;
  originalContent?: string;
  anonymizedContent?: string;
  content?: string; // 🔧 CONTENU OCR POUR RAG
  anonymizationMappings?: Record<string, string>;
}

export interface DocumentEditRequest {
  documentId: number;
  newContent: string;
  preserveAnonymization?: boolean;
}

export interface DocumentSearchResult {
  document: DocumentInfo;
  relevantChunks: Array<{
    content: string;
    metadata: Record<string, any>;
    score?: number;
  }>;
}

export interface DocumentManagerState {
  documents: DocumentInfo[];
  collections: Collection[];
  selectedDocument: DocumentInfo | null;
  selectedCollection: string | null;
  searchQuery: string;
  searchResults: DocumentSearchResult[];
  isLoading: boolean;
  error: string | null;
}

// ============================================================================
// File Upload Types
// ============================================================================

export interface FileUploadProgress {
  fileId: string;
  filename: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// ============================================================================
// Utility Types
// ============================================================================

export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;

// Helper type for partial updates
export type PartialUpdate<T> = Partial<T> & { id: string };

// Event types for component communication
export interface DocumentProcessingEvent {
  type: 'step_started' | 'step_completed' | 'step_error' | 'workflow_completed';
  documentId: string;
  stepId?: string;
  data?: any;
  error?: ProcessingError;
}
