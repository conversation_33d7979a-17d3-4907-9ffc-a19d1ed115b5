/**
 * Classe de base pour tous les modes RAG
 */

import { 
  RagDocument, 
  Rag<PERSON><PERSON>ult, 
  RagProcessingResult, 
  RagModeInterface, 
  OnContentChunk,
  DocumentAnalysis 
} from '../types/RagTypes';

export abstract class BaseMode implements RagModeInterface {
  protected readonly threshold: number;
  protected readonly maxContextLength: number;

  constructor(threshold: number = 20000, maxContextLength: number = 100000) {
    this.threshold = threshold;
    this.maxContextLength = maxContextLength;
  }

  abstract processDocuments(
    documents: RagDocument[],
    userQuery: string,
    onContentChunk?: OnContentChunk
  ): Promise<RagProcessingResult>;

  /**
   * Analyse les documents pour déterminer la méthode de traitement
   */
  protected analyzeDocuments(documents: RagDocument[]): DocumentAnalysis[] {
    return documents.map(doc => {
      const contentLength = doc.content?.length || 0;
      
      return {
        doc,
        contentLength,
        useRAG: contentLength > this.threshold,
        processingMethod: contentLength > this.threshold ? 'rag' : 'direct'
      };
    });
  }

  /**
   * Construit le contexte final à partir des résultats RAG
   */
  protected buildRagContext(ragResults: RagResult[]): { content: string; totalLength: number } {
    let ragContext = '\n\n📚 **Relevant Documents:**\n\n';
    let totalContextLength = ragContext.length;

    ragResults.forEach((result, index) => {
      const content = result.content || '';
      const docSection = `**Document ${index + 1}** (Relevance: ${(result.score || 0).toFixed(2)}):\n${content}\n\n`;

      ragContext += docSection;
      totalContextLength += docSection.length;
    });

    return {
      content: ragContext,
      totalLength: totalContextLength
    };
  }

  /**
   * Crée un résultat RAG à partir d'un document
   */
  protected createRagResult(doc: RagDocument, score: number): RagResult {
    return {
      content: doc.content || `Document: ${doc.filename}\n[Contenu non disponible]`,
      score,
      metadata: {
        source_file: doc.filename,
        document_id: doc.id,
        type: doc.fileType || 'document'
      }
    };
  }

  /**
   * Log les statistiques de traitement
   */
  protected logProcessingStats(
    mode: string,
    documents: RagDocument[],
    ragResults: RagResult[],
    processingTime: number,
    analysis?: DocumentAnalysis[]
  ): void {
    console.log(`🔍 ${mode} Processing Stats:`, {
      documentsProcessed: documents.length,
      resultsGenerated: ragResults.length,
      processingTime: `${processingTime}ms`,
      directDocuments: analysis?.filter(a => !a.useRAG).length || 0,
      ragProcessedDocuments: analysis?.filter(a => a.useRAG).length || 0,
      averageScore: ragResults.length > 0 
        ? (ragResults.reduce((sum, r) => sum + r.score, 0) / ragResults.length).toFixed(2)
        : 0
    });
  }
}
