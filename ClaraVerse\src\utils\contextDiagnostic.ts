/**
 * 🔍 DIAGNOSTIC DU CONTEXTE - WeMa IA
 * 
 * Outil pour analyser et diagnostiquer les problèmes de contexte
 * qui causent les hallucinations du LLM
 */

export interface ContextDiagnostic {
  totalMessages: number;
  systemPromptLength: number;
  ragMessages: {
    count: number;
    totalLength: number;
    duplicates: number;
  };
  userMessages: number;
  assistantMessages: number;
  duplicatedContent: string[];
  estimatedTokens: number;
  issues: string[];
  recommendations: string[];
}

export interface ChatMessage {
  role: string;
  content: string;
  metadata?: any;
}

/**
 * Analyser le contexte envoyé au LLM
 */
export function analyzeContext(
  systemPrompt: string,
  messages: ChatMessage[]
): ContextDiagnostic {
  const diagnostic: ContextDiagnostic = {
    totalMessages: messages.length,
    systemPromptLength: systemPrompt.length,
    ragMessages: { count: 0, totalLength: 0, duplicates: 0 },
    userMessages: 0,
    assistantMessages: 0,
    duplicatedContent: [],
    estimatedTokens: 0,
    issues: [],
    recommendations: []
  };

  // Analyser les messages
  const ragContents = new Set<string>();
  const allContents = new Map<string, number>();

  for (const message of messages) {
    // Compter par rôle
    if (message.role === 'user') diagnostic.userMessages++;
    if (message.role === 'assistant') diagnostic.assistantMessages++;

    // Détecter les messages RAG
    if (message.role === 'system' && message.metadata?.type === 'rag') {
      diagnostic.ragMessages.count++;
      diagnostic.ragMessages.totalLength += message.content.length;

      // Détecter les doublons RAG
      const contentHash = message.content.substring(0, 200);
      if (ragContents.has(contentHash)) {
        diagnostic.ragMessages.duplicates++;
      } else {
        ragContents.add(contentHash);
      }
    }

    // Détecter tous les doublons
    const contentKey = `${message.role}:${message.content.substring(0, 100)}`;
    const count = allContents.get(contentKey) || 0;
    allContents.set(contentKey, count + 1);
    
    if (count > 0) {
      diagnostic.duplicatedContent.push(contentKey);
    }
  }

  // Estimer les tokens
  const totalChars = systemPrompt.length + messages.reduce((sum, msg) => sum + msg.content.length, 0);
  diagnostic.estimatedTokens = Math.ceil(totalChars / 4); // Estimation approximative

  // Identifier les problèmes
  if (diagnostic.ragMessages.duplicates > 0) {
    diagnostic.issues.push(`🚨 ${diagnostic.ragMessages.duplicates} documents RAG dupliqués détectés`);
  }

  if (diagnostic.duplicatedContent.length > 0) {
    diagnostic.issues.push(`🚨 ${diagnostic.duplicatedContent.length} messages dupliqués détectés`);
  }

  if (diagnostic.ragMessages.count > 0 && systemPrompt.includes('document')) {
    diagnostic.issues.push(`🚨 Possible double injection RAG (system prompt + historique)`);
  }

  if (diagnostic.estimatedTokens > 30000) {
    diagnostic.issues.push(`⚠️ Contexte très long: ${diagnostic.estimatedTokens} tokens estimés`);
  }

  if (diagnostic.ragMessages.totalLength > diagnostic.totalMessages * 1000) {
    diagnostic.issues.push(`⚠️ Documents RAG disproportionnés par rapport à la conversation`);
  }

  // Générer des recommandations
  if (diagnostic.ragMessages.duplicates > 0) {
    diagnostic.recommendations.push('Nettoyer les documents RAG dupliqués');
  }

  if (diagnostic.duplicatedContent.length > 0) {
    diagnostic.recommendations.push('Implémenter une déduplication plus efficace');
  }

  if (diagnostic.estimatedTokens > 25000) {
    diagnostic.recommendations.push('Activer la compression de contexte');
  }

  if (diagnostic.ragMessages.count === 0 && diagnostic.userMessages > 0) {
    diagnostic.recommendations.push('Vérifier si des documents RAG devraient être disponibles');
  }

  return diagnostic;
}

/**
 * Afficher le diagnostic dans la console
 */
export function logContextDiagnostic(diagnostic: ContextDiagnostic): void {
  console.group('🔍 DIAGNOSTIC DU CONTEXTE');
  
  console.log('📊 Statistiques générales:');
  console.log(`  - Messages total: ${diagnostic.totalMessages}`);
  console.log(`  - System prompt: ${diagnostic.systemPromptLength} chars`);
  console.log(`  - Tokens estimés: ${diagnostic.estimatedTokens}`);
  
  console.log('👥 Répartition des messages:');
  console.log(`  - Utilisateur: ${diagnostic.userMessages}`);
  console.log(`  - Assistant: ${diagnostic.assistantMessages}`);
  console.log(`  - RAG: ${diagnostic.ragMessages.count} (${diagnostic.ragMessages.totalLength} chars)`);
  
  if (diagnostic.issues.length > 0) {
    console.warn('🚨 Problèmes détectés:');
    diagnostic.issues.forEach(issue => console.warn(`  ${issue}`));
  }
  
  if (diagnostic.recommendations.length > 0) {
    console.info('💡 Recommandations:');
    diagnostic.recommendations.forEach(rec => console.info(`  ${rec}`));
  }
  
  if (diagnostic.duplicatedContent.length > 0) {
    console.warn('🔄 Contenu dupliqué:');
    diagnostic.duplicatedContent.slice(0, 5).forEach(dup => 
      console.warn(`  ${dup.substring(0, 80)}...`)
    );
    if (diagnostic.duplicatedContent.length > 5) {
      console.warn(`  ... et ${diagnostic.duplicatedContent.length - 5} autres`);
    }
  }
  
  console.groupEnd();
}

/**
 * Analyser et logger le contexte en une seule fonction
 */
export function diagnoseContext(systemPrompt: string, messages: ChatMessage[]): ContextDiagnostic {
  const diagnostic = analyzeContext(systemPrompt, messages);
  logContextDiagnostic(diagnostic);
  return diagnostic;
}

/**
 * Vérifier si le contexte est sain
 */
export function isContextHealthy(diagnostic: ContextDiagnostic): boolean {
  return diagnostic.issues.length === 0 && 
         diagnostic.duplicatedContent.length === 0 &&
         diagnostic.ragMessages.duplicates === 0;
}

/**
 * Obtenir un score de qualité du contexte (0-100)
 */
export function getContextQualityScore(diagnostic: ContextDiagnostic): number {
  let score = 100;
  
  // Pénalités
  score -= diagnostic.issues.length * 20;
  score -= diagnostic.duplicatedContent.length * 5;
  score -= diagnostic.ragMessages.duplicates * 15;
  
  // Bonus pour un contexte bien structuré
  if (diagnostic.userMessages > 0 && diagnostic.assistantMessages > 0) {
    score += 10;
  }
  
  if (diagnostic.estimatedTokens < 20000) {
    score += 5;
  }
  
  return Math.max(0, Math.min(100, score));
}
