# 🧪 Script de Test de Connectivité WeMa IA (PowerShell)
# 
# Teste la connectivité vers tous les services :
# - Backend local (localhost:5001)
# - Serveur Ollama central (*********:11434)
# - LM Studio local (localhost:1234)

Write-Host "🚀 WeMa IA - Test de Connectivité" -ForegroundColor Cyan
Write-Host "Architecture: Backend local + Inférence déléguée" -ForegroundColor Gray
Write-Host ("=" * 60) -ForegroundColor Gray

# Configuration des services
$services = @{
    "backend" = @{
        name = "Backend Local"
        url = "http://localhost:5001/health"
        timeout = 5
        required = $true
    }
    "ollama" = @{
        name = "Ollama Central (Inférence IA)"
        url = "http://***********:11434/api/tags"
        timeout = 8
        required = $false
    }
    "lmstudio" = @{
        name = "LM Studio Local (Fallback)"
        url = "http://localhost:1234/v1/models"
        timeout = 3
        required = $false
    }
}

$results = @{}

# Fonction de test pour un service
function Test-Service {
    param(
        [string]$ServiceKey,
        [hashtable]$Config
    )
    
    Write-Host "🔍 Testing $($Config.name)..." -ForegroundColor Yellow
    Write-Host "   URL: $($Config.url)" -ForegroundColor Gray
    
    $startTime = Get-Date
    
    try {
        $response = Invoke-WebRequest -Uri $Config.url -Method GET -TimeoutSec $Config.timeout -ErrorAction Stop
        $responseTime = [math]::Round(((Get-Date) - $startTime).TotalMilliseconds)
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($Config.name) - OK" -ForegroundColor Green
            Write-Host "   Response time: ${responseTime}ms" -ForegroundColor Gray
            Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Gray
            
            # Analyser le contenu JSON si possible
            try {
                $data = $response.Content | ConvertFrom-Json
                
                if ($ServiceKey -eq "ollama" -and $data.models) {
                    Write-Host "   Models available: $($data.models.Count)" -ForegroundColor Gray
                    if ($data.models.Count -gt 0) {
                        $sampleModels = $data.models[0..2] | ForEach-Object { $_.name }
                        Write-Host "   Sample models: $($sampleModels -join ', ')" -ForegroundColor Gray
                    }
                } elseif ($ServiceKey -eq "lmstudio" -and $data.data) {
                    Write-Host "   Models available: $($data.data.Count)" -ForegroundColor Gray
                    if ($data.data.Count -gt 0) {
                        $sampleModels = $data.data[0..2] | ForEach-Object { $_.id }
                        Write-Host "   Sample models: $($sampleModels -join ', ')" -ForegroundColor Gray
                    }
                } elseif ($ServiceKey -eq "backend") {
                    Write-Host "   Service: $($data.service)" -ForegroundColor Gray
                    Write-Host "   Version: $($data.version)" -ForegroundColor Gray
                    Write-Host "   RAG Available: $($data.rag_available)" -ForegroundColor Gray
                }
            } catch {
                # Ignore JSON parsing errors
            }
            
            return @{
                success = $true
                responseTime = $responseTime
                statusCode = $response.StatusCode
            }
        } else {
            Write-Host "❌ $($Config.name) - HTTP Error" -ForegroundColor Red
            Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Red
            Write-Host "   Response time: ${responseTime}ms" -ForegroundColor Gray
            
            return @{
                success = $false
                error = "HTTP $($response.StatusCode)"
                responseTime = $responseTime
            }
        }
    } catch {
        $responseTime = [math]::Round(((Get-Date) - $startTime).TotalMilliseconds)
        
        if ($_.Exception.Message -like "*timeout*" -or $_.Exception.Message -like "*timed out*") {
            Write-Host "⏰ $($Config.name) - Timeout" -ForegroundColor Yellow
            Write-Host "   Timeout after: $($Config.timeout)s" -ForegroundColor Gray
        } else {
            Write-Host "❌ $($Config.name) - Connection Error" -ForegroundColor Red
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "   Response time: ${responseTime}ms" -ForegroundColor Gray
        }
        
        return @{
            success = $false
            error = $_.Exception.Message
            responseTime = $responseTime
        }
    }
}

# Tester tous les services
foreach ($serviceKey in $services.Keys) {
    $config = $services[$serviceKey]
    $results[$serviceKey] = Test-Service -ServiceKey $serviceKey -Config $config
    Write-Host ""
}

# Analyser le statut global
Write-Host ("=" * 60) -ForegroundColor Gray
Write-Host "📊 ANALYSE DU STATUT GLOBAL" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Gray

$backend = $results["backend"]
$ollama = $results["ollama"]
$lmstudio = $results["lmstudio"]

if (-not $backend.success) {
    Write-Host "🚨 STATUT: CRITIQUE" -ForegroundColor Red
    Write-Host "❌ Backend local indisponible - Application non fonctionnelle" -ForegroundColor Red
    Write-Host "💡 Action requise: Démarrer le backend Python (py_backend/main.py)" -ForegroundColor Yellow
    $overallStatus = "critical"
} elseif ($ollama.success) {
    Write-Host "✅ STATUT: OPTIMAL" -ForegroundColor Green
    Write-Host "🌐 Serveur central Ollama accessible - Inférence IA optimale" -ForegroundColor Green
    Write-Host "🏗️ Architecture: Backend local + Inférence déléguée" -ForegroundColor Gray
    $overallStatus = "optimal"
} elseif ($lmstudio.success) {
    Write-Host "⚠️ STATUT: DÉGRADÉ" -ForegroundColor Yellow
    Write-Host "🏠 Serveur central indisponible - Fallback LM Studio actif" -ForegroundColor Yellow
    Write-Host "💡 Recommandation: Vérifier la connectivité vers *********:11434" -ForegroundColor Yellow
    $overallStatus = "degraded"
} else {
    Write-Host "🚨 STATUT: CRITIQUE" -ForegroundColor Red
    Write-Host "❌ Aucun service d'inférence IA disponible" -ForegroundColor Red
    Write-Host "💡 Actions requises:" -ForegroundColor Yellow
    Write-Host "   1. Vérifier la connectivité réseau vers *********:11434" -ForegroundColor Yellow
    Write-Host "   2. Ou démarrer LM Studio local sur localhost:1234" -ForegroundColor Yellow
    $overallStatus = "critical"
}

# Résumé final
Write-Host ""
Write-Host ("=" * 60) -ForegroundColor Gray
Write-Host "📋 RÉSUMÉ DES TESTS" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Gray

foreach ($serviceKey in $services.Keys) {
    $config = $services[$serviceKey]
    $result = $results[$serviceKey]
    
    $status = if ($result.success) { "✅ OK" } else { "❌ FAIL" }
    $time = if ($result.responseTime) { "$($result.responseTime)ms" } else { "N/A" }
    
    $serviceName = $config.name.PadRight(30)
    $timeFormatted = $time.PadLeft(8)
    
    if ($result.success) {
        Write-Host "$status $serviceName $timeFormatted" -ForegroundColor Green
    } else {
        Write-Host "$status $serviceName $timeFormatted" -ForegroundColor Red
        if ($config.required) {
            Write-Host "     ⚠️ Service critique indisponible" -ForegroundColor Yellow
        }
    }
}

Write-Host ("=" * 60) -ForegroundColor Gray

# Code de sortie
if ($overallStatus -eq "critical") {
    exit 1
} else {
    exit 0
}
