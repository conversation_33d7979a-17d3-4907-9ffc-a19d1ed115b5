/**
 * 🧪 TEST SCRIPT - LM Studio Preloading
 * 
 * Ce script teste le nouveau système de préchargement optimisé
 * pour s'assurer que les modèles se chargent correctement quand
 * on clique sur le chat.
 */

console.log('🧪 Testing LM Studio Preloading System...');

// Test 1: Vérifier que LM Studio est disponible
async function testLMStudioAvailability() {
  console.log('\n📡 Test 1: LM Studio Availability');
  
  try {
    const response = await fetch('http://localhost:1234/v1/models', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ LM Studio is available');
      console.log(`📊 Found ${data.data?.length || 0} models`);
      return true;
    } else {
      console.log('❌ LM Studio responded with error:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ LM Studio is not available:', error.message);
    return false;
  }
}

// Test 2: Tester le préchargement via backend
async function testBackendPreload() {
  console.log('\n🔄 Test 2: Backend Preload Endpoint');
  
  try {
    const response = await fetch('http://localhost:8000/proxy/lmstudio/preload', {
      method: 'POST',
      signal: AbortSignal.timeout(15000),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend preload successful:', data);
      return true;
    } else {
      console.log('❌ Backend preload failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Backend preload error:', error.message);
    return false;
  }
}

// Test 3: Tester le préchargement direct
async function testDirectPreload() {
  console.log('\n🎯 Test 3: Direct Preload');
  
  try {
    const payload = {
      model: 'auto',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 1,
      temperature: 0
    };
    
    const response = await fetch('http://localhost:1234/v1/chat/completions', {
      method: 'POST',
      signal: AbortSignal.timeout(15000),
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Direct preload successful');
      console.log('📝 Response preview:', data.choices?.[0]?.message?.content?.substring(0, 50) + '...');
      return true;
    } else {
      console.log('❌ Direct preload failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Direct preload error:', error.message);
    return false;
  }
}

// Test 4: Simuler la navigation vers Clara
function testNavigationEvent() {
  console.log('\n🎯 Test 4: Navigation Event Simulation');
  
  try {
    // Simuler l'événement de navigation
    const preloadEvent = new CustomEvent('clara-navigation-preload', {
      detail: { timestamp: Date.now() }
    });
    
    window.dispatchEvent(preloadEvent);
    console.log('✅ Navigation event dispatched successfully');
    
    // Vérifier que l'événement peut être écouté
    let eventReceived = false;
    const testListener = () => {
      eventReceived = true;
      console.log('✅ Navigation event received by listener');
    };
    
    window.addEventListener('clara-navigation-preload', testListener);
    window.dispatchEvent(preloadEvent);
    window.removeEventListener('clara-navigation-preload', testListener);
    
    return eventReceived;
  } catch (error) {
    console.log('❌ Navigation event test failed:', error.message);
    return false;
  }
}

// Test 5: Mesurer la performance du préchargement
async function testPreloadPerformance() {
  console.log('\n⚡ Test 5: Preload Performance');
  
  const startTime = performance.now();
  
  try {
    const success = await testDirectPreload();
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    
    console.log(`📊 Preload duration: ${duration}ms`);
    
    if (duration < 5000) {
      console.log('✅ Performance is good (< 5s)');
    } else if (duration < 10000) {
      console.log('⚠️ Performance is acceptable (< 10s)');
    } else {
      console.log('❌ Performance is poor (> 10s)');
    }
    
    return { success, duration };
  } catch (error) {
    console.log('❌ Performance test failed:', error.message);
    return { success: false, duration: -1 };
  }
}

// Exécuter tous les tests
async function runAllTests() {
  console.log('🚀 Starting LM Studio Preload Tests...\n');
  
  const results = {
    availability: await testLMStudioAvailability(),
    backendPreload: await testBackendPreload(),
    directPreload: await testDirectPreload(),
    navigationEvent: testNavigationEvent(),
    performance: await testPreloadPerformance()
  };
  
  console.log('\n📊 TEST RESULTS SUMMARY:');
  console.log('========================');
  Object.entries(results).forEach(([test, result]) => {
    const status = result === true || (result?.success === true) ? '✅' : '❌';
    const extra = result?.duration ? ` (${result.duration}ms)` : '';
    console.log(`${status} ${test}${extra}`);
  });
  
  const passedTests = Object.values(results).filter(r => 
    r === true || r?.success === true
  ).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${Object.keys(results).length} tests passed`);
  
  if (passedTests === Object.keys(results).length) {
    console.log('🎉 All tests passed! LM Studio preloading is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check LM Studio setup and backend connectivity.');
  }
  
  return results;
}

// Exporter pour utilisation dans la console
if (typeof window !== 'undefined') {
  window.testLMStudioPreload = runAllTests;
  console.log('💡 Run window.testLMStudioPreload() to test the preloading system');
}

// Auto-run si appelé directement
if (typeof module === 'undefined') {
  runAllTests();
}
