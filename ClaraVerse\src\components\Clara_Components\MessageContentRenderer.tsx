/**
 * Message Content Renderer Component
 * 
 * Enhanced content renderer for Clara messages that supports:
 * - Markdown rendering with syntax highlighting
 * - HTML code detection and preview
 * - Code block syntax highlighting
 * - Interactive elements
 */

import React, { useState, useMemo, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Eye, EyeOff, ExternalLink, Code2 } from 'lucide-react';
import { copyToClipboard } from '../../utils/clipboard';
import mermaid from 'mermaid';
import { logger, LogCategory } from '../../utils/logger';

interface MessageContentRendererProps {
  content: string;
  className?: string;
  isDark?: boolean;
  isStreaming?: boolean;
}

/**
 * 🧹 Nettoyer et restructurer le code Mermaid pour une syntaxe parfaite
 */
const cleanMermaidCode = (code: string): string => {
  // 1. Nettoyer les caractères spéciaux
  let cleaned = code
    .replace(/é/g, 'e').replace(/è/g, 'e').replace(/ê/g, 'e')
    .replace(/à/g, 'a').replace(/ù/g, 'u').replace(/ç/g, 'c')
    .replace(/ô/g, 'o').replace(/î/g, 'i').replace(/É/g, 'E').replace(/À/g, 'A')
    .replace(/'/g, ' ').replace(/'/g, ' ').replace(/`/g, ' ')
    .replace(/\(/g, ' ').replace(/\)/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // 2. Extraire les connexions avec regex
  const graphMatch = cleaned.match(/graph\s+(TD|LR|TB|RL)/i);
  const graphType = graphMatch ? graphMatch[0] : 'graph TD';

  // 3. Extraire toutes les connexions A --> B[Label]
  const connections: string[] = [];
  const connectionRegex = /([A-Z])\s*(?:\[([^\]]+)\])?\s*-->\s*([A-Z])\s*(?:\[([^\]]+)\])?/g;

  let match;
  while ((match = connectionRegex.exec(cleaned)) !== null) {
    const [, fromNode, fromLabel, toNode, toLabel] = match;

    // 📏 Nettoyer et optimiser les labels pour l'affichage
    const cleanFromLabel = fromLabel ?
      fromLabel.replace(/[^\w\s-]/g, '').trim().substring(0, 40) : '';
    const cleanToLabel = toLabel ?
      toLabel.replace(/[^\w\s-]/g, '').trim().substring(0, 40) : '';

    // 🔧 Ajouter des retours à la ligne pour les textes longs
    const formatLabel = (label: string): string => {
      if (label.length > 20) {
        const words = label.split(' ');
        const lines = [];
        let currentLine = '';

        for (const word of words) {
          if ((currentLine + ' ' + word).length > 20) {
            if (currentLine) lines.push(currentLine);
            currentLine = word;
          } else {
            currentLine = currentLine ? currentLine + ' ' + word : word;
          }
        }
        if (currentLine) lines.push(currentLine);

        return lines.join('<br/>');
      }
      return label;
    };

    const formattedFromLabel = formatLabel(cleanFromLabel);
    const formattedToLabel = formatLabel(cleanToLabel);

    // 🎨 Construire la connexion avec labels formatés
    let connection = '';
    if (formattedFromLabel) {
      connection += `    ${fromNode}["${formattedFromLabel}"]`;
    }
    if (formattedToLabel) {
      connection += `\n    ${toNode}["${formattedToLabel}"]`;
    }
    connection += `\n    ${fromNode} --> ${toNode}`;

    connections.push(connection);
  }

  // 4. Si pas de connexions trouvées, essayer une approche plus simple
  if (connections.length === 0) {
    // Fallback : créer un diagramme simple et beau
    return `graph TD
    A["🚀 Début"]
    B["⚙️ Traitement"]
    C["✅ Fin"]
    A --> B
    B --> C

    classDef default fill:#DBEAFE,stroke:#2563EB,stroke-width:2px,color:#1E293B
    classDef startNode fill:#10B981,stroke:#059669,stroke-width:2px,color:#FFFFFF
    classDef endNode fill:#EF4444,stroke:#DC2626,stroke-width:2px,color:#FFFFFF

    class A startNode
    class C endNode`;
  }

  // 5. Assembler le diagramme final avec style
  let result = `${graphType}\n${connections.join('\n')}`;

  // 🎨 Ajouter des styles CSS pour un rendu plus beau et des boîtes adaptatives
  result += `\n
    classDef default fill:#DBEAFE,stroke:#2563EB,stroke-width:2px,color:#1E293B,font-weight:500,font-size:12px
    classDef highlight fill:#10B981,stroke:#059669,stroke-width:2px,color:#FFFFFF,font-weight:600,font-size:12px
    classDef important fill:#F59E0B,stroke:#D97706,stroke-width:2px,color:#FFFFFF,font-weight:500,font-size:12px

    %% Configuration pour des boîtes compactes
    %%{init: {"flowchart": {"htmlLabels": true, "curve": "basis", "padding": 15}}}%%`;

  // Code Mermaid nettoyé et optimisé
  return result;
};

/**
 * 📊 Composant Mermaid pour le rendu des diagrammes dans Markdown
 */
const MermaidBlock: React.FC<{
  children: string;
  isDark: boolean;
}> = ({ children, isDark }) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [originalCode] = useState(children); // Garder le code original pour l'affichage
  const [diagramHeight, setDiagramHeight] = useState<number | null>(null);

  useEffect(() => {
    const renderMermaid = async () => {
      if (!mermaidRef.current || !children.trim()) return;

      try {
        setIsLoading(true);
        setError(null);

        // 🧹 NETTOYER le code Mermaid des caractères problématiques
        const cleanedCode = cleanMermaidCode(children);

        // 🎨 Initialiser Mermaid avec un style moderne et compact
        mermaid.initialize({
          startOnLoad: false,
          theme: isDark ? 'dark' : 'base',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, -apple-system, sans-serif',
          // 🎨 CSS personnalisé pour des diagrammes compacts
          themeCSS: `
            .node rect, .node circle, .node ellipse, .node polygon {
              stroke-width: 2px !important;
            }
            .node .label {
              font-size: 12px !important;
              font-weight: 500 !important;
              line-height: 1.2 !important;
            }
            .edgeLabel {
              font-size: 11px !important;
              background-color: rgba(255, 255, 255, 0.8) !important;
              padding: 2px 4px !important;
              border-radius: 3px !important;
            }
            .cluster rect {
              stroke-width: 1px !important;
            }
          `,
          // 🎨 CONFIGURATION STYLE MODERNE
          themeVariables: {
            // Couleurs principales
            primaryColor: isDark ? '#3B82F6' : '#60A5FA',
            primaryTextColor: isDark ? '#F8FAFC' : '#1E293B',
            primaryBorderColor: isDark ? '#1E40AF' : '#3B82F6',

            // Couleurs de fond
            background: isDark ? '#0F172A' : '#FFFFFF',
            secondaryColor: isDark ? '#1E293B' : '#F1F5F9',
            tertiaryColor: isDark ? '#334155' : '#E2E8F0',

            // Couleurs des lignes
            lineColor: isDark ? '#64748B' : '#475569',
            edgeLabelBackground: isDark ? '#1E293B' : '#FFFFFF',

            // Couleurs des nœuds
            mainBkg: isDark ? '#1E40AF' : '#DBEAFE',
            nodeBorder: isDark ? '#3B82F6' : '#2563EB',
            clusterBkg: isDark ? '#0F172A' : '#F8FAFC',

            // Typographie compacte
            fontFamily: 'ui-sans-serif, system-ui, sans-serif',
            fontSize: '12px',
            fontWeight: '500'
          },
          // 🎨 CONFIGURATION FLOWCHART OPTIMISÉE POUR COMPACITÉ
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
            padding: 15,          // Padding réduit pour plus de compacité
            nodeSpacing: 50,      // Espacement réduit entre nœuds
            rankSpacing: 60,      // Espacement réduit entre niveaux
            diagramPadding: 20,   // Padding du diagramme réduit
            wrappingWidth: 150,   // Largeur réduite pour retour à la ligne
            titleTopMargin: 10,   // Marge du titre réduite
            subGraphTitleMargin: {
              top: 5,
              bottom: 5
            }
          },
          // 🎨 CONFIGURATION GÉNÉRALE COMPACTE
          maxTextSize: 90000,
          maxEdges: 999,
          wrap: true,
          fontSize: 12,           // Taille de police réduite
          deterministicIds: true, // IDs déterministes pour la cohérence
          suppressErrorRendering: false
        });

        // Générer un ID unique
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Valider et rendre avec le code nettoyé
        const isValid = await mermaid.parse(cleanedCode);
        if (isValid) {
          const { svg } = await mermaid.render(id, cleanedCode);
          mermaidRef.current.innerHTML = svg;

          // 🚀 STABILITÉ: Capturer la hauteur réelle du diagramme
          setTimeout(() => {
            if (mermaidRef.current) {
              const svgElement = mermaidRef.current.querySelector('svg');
              if (svgElement) {
                const height = svgElement.getBoundingClientRect().height;
                setDiagramHeight(height);
              }
            }
          }, 100);
        } else {
          throw new Error('Invalid Mermaid syntax');
        }
      } catch (err) {
        logger.error(LogCategory.UI, 'Mermaid rendering error', err);
        setError(err instanceof Error ? err.message : 'Failed to render diagram');
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = '';
        }
      } finally {
        setIsLoading(false);
      }
    };

    renderMermaid();
  }, [children, isDark]);

  if (error) {
    return (
      <div className="my-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <p className="text-red-600 dark:text-red-400 text-sm mb-2">❌ Erreur de rendu du diagramme</p>
        <p className="text-xs text-red-500 mb-2">Les caractères spéciaux français peuvent causer des problèmes.</p>
        <details>
          <summary className="text-xs text-red-500 cursor-pointer">Voir le code source original</summary>
          <pre className="mt-2 text-xs text-red-500 bg-white dark:bg-gray-900 p-2 rounded border font-mono overflow-x-auto">
            {originalCode}
          </pre>
        </details>
        <details className="mt-2">
          <summary className="text-xs text-red-500 cursor-pointer">Voir le code nettoyé</summary>
          <pre className="mt-2 text-xs text-red-500 bg-white dark:bg-gray-900 p-2 rounded border font-mono overflow-x-auto">
            {cleanMermaidCode(originalCode)}
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className="my-4 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 border border-blue-200 dark:border-gray-600 rounded-lg shadow-sm">
      {/* 🚀 STABLE: Container avec hauteur prédictive pour éviter les sauts */}
      <div
        className="relative w-full overflow-x-auto transition-all duration-300 ease-out"
        style={{
          minHeight: isLoading ? '120px' : diagramHeight ? `${diagramHeight + 20}px` : 'auto'
        }}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-wema-500"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">Génération du diagramme...</span>
            </div>
          </div>
        )}
        <div
          ref={mermaidRef}
          className={`mermaid-diagram ${isLoading ? 'opacity-0' : 'opacity-100'} transition-all duration-500 ease-out w-full`}
          style={{
            textAlign: 'center',
            padding: '10px',
            transform: isLoading ? 'translateY(10px)' : 'translateY(0)'
          }}
        />
      </div>
    </div>
  );
};

interface CodeBlockProps {
  children: string;
  className?: string;
  language?: string;
  isInline?: boolean;
  isDark?: boolean;
  isStreaming?: boolean;
}

const CodeBlock: React.FC<CodeBlockProps> = React.memo(({ children, className, language, isInline = false, isDark = false, isStreaming = false }) => {
  const [copied, setCopied] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);
  
  // Extract language from className (format: "language-javascript")
  const lang = useMemo(() => language || className?.replace('language-', '') || 'text', [language, className]);
  
  // Check if this is HTML content that could be previewed (memoized to prevent re-renders)
  const isHtmlContent = useMemo(() => {
    return lang === 'html' || lang === 'xml' || 
      (children.trim().startsWith('<') && children.trim().endsWith('>'));
  }, [lang, children]);
  
  // Initialize preview state only once when component mounts
  useEffect(() => {
    setShowPreview(isHtmlContent && !isStreaming);
  }, [isHtmlContent]); // Only depend on isHtmlContent, not isStreaming
  
  // Force code view during streaming to prevent iframe reloading
  const shouldShowPreview = showPreview && !isStreaming && isHtmlContent;
  
  // Create a stable key for the iframe that only changes when content actually changes
  const iframeKey = useMemo(() => {
    // 🔧 FIX: Utiliser une fonction UTF-8 safe au lieu de btoa
    const safeEncode = (str: string) => {
      try {
        // Encoder en UTF-8 puis en base64
        return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) =>
          String.fromCharCode(parseInt(p1, 16))
        ));
      } catch (error) {
        // Fallback: utiliser un hash simple
        return str.split('').reduce((hash, char) => {
          const charCode = char.charCodeAt(0);
          return ((hash << 5) - hash) + charCode;
        }, 0).toString(36);
      }
    };

    return `iframe-${lang}-${safeEncode(children.substring(0, 100))}`;
  }, [lang, children]);
  
  const handleCopy = async () => {
    const success = await copyToClipboard(children);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };
  
  const handleTogglePreview = () => {
    setShowPreview(!showPreview);
  };
  
  const previewHtml = useMemo(() => {
    if (!isHtmlContent) return '';
    
    // Basic sanitization and enhancement for preview
    let html = children.trim();
    
    // Add basic styling if not present
    if (!html.includes('<style') && !html.includes('style=')) {
      html = `
        <style>
          body { 
            font-family: system-ui, -apple-system, sans-serif; 
            margin: 20px; 
            line-height: 1.6; 
          }
          * { box-sizing: border-box; }
        </style>
        ${html}
      `;
    }
    
    return html;
  }, [isHtmlContent, children]);

  // For inline code, render simply
  if (isInline) {
    return (
      <code className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded text-sm font-mono">
        {children}
      </code>
    );
  }

  return (
    <div className="relative group my-4" ref={previewRef}>
      {/* Code block header */}
      <div className="flex items-center justify-between bg-gray-50/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-t-lg text-sm backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <Code2 className="w-4 h-4" />
          <span className="font-medium capitalize">{lang}</span>
          {isHtmlContent && (
            <span className={`text-xs px-2 py-1 rounded border ${
              isStreaming 
                ? 'bg-yellow-500/20 dark:bg-yellow-400/20 text-yellow-700 dark:text-yellow-300 border-yellow-300/30 dark:border-yellow-500/30'
                : showPreview 
                  ? 'bg-green-500/20 dark:bg-green-400/20 text-green-700 dark:text-green-300 border-green-300/30 dark:border-green-500/30'
                  : 'bg-blue-500/20 dark:bg-blue-400/20 text-blue-700 dark:text-blue-300 border-blue-300/30 dark:border-blue-500/30'
            }`}>
              {isStreaming ? 'Streaming...' : shouldShowPreview ? 'Live Preview' : 'Source Code'}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {isHtmlContent && (
            <button
              onClick={handleTogglePreview}
              disabled={isStreaming}
              className={`flex items-center gap-1 px-2 py-1 rounded transition-colors ${
                isStreaming 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'hover:bg-gray-200/70 dark:hover:bg-gray-600/50'
              }`}
              title={
                isStreaming 
                  ? 'Preview disabled during streaming'
                  : shouldShowPreview 
                    ? 'Show source code' 
                    : 'Show live preview'
              }
            >
              {shouldShowPreview ? <Code2 className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span className="text-xs">{shouldShowPreview ? 'Source' : 'Preview'}</span>
            </button>
          )}
          
          <button
            onClick={handleCopy}
            className="flex items-center gap-1 px-2 py-1 hover:bg-gray-200/70 dark:hover:bg-gray-600/50 rounded transition-colors"
            title="Copy code"
          >
            <Copy className="w-4 h-4" />
            <span className="text-xs">{copied ? 'Copied!' : 'Copy'}</span>
          </button>
        </div>
      </div>

      {/* Content area - either code or preview */}
      <div className="relative">
        {shouldShowPreview ? (
          /* HTML Preview */
          <div className="bg-white dark:bg-gray-900 rounded-b-lg overflow-hidden border border-gray-300 dark:border-gray-600 border-t-0">
            <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 flex items-center gap-2 border-b border-gray-200 dark:border-gray-700">
              <ExternalLink className="w-4 h-4" />
              Live Preview
            </div>
            <iframe
              key={iframeKey}
              srcDoc={previewHtml}
              className="w-full h-96 border-0"
              title="HTML Preview"
              sandbox="allow-scripts"
              style={{ minHeight: '300px' }}
            />
          </div>
        ) : (
          /* Code block */
          <SyntaxHighlighter
            style={isDark ? oneDark : oneLight}
            language={lang}
            customStyle={{
              margin: 0,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
              borderBottomLeftRadius: '0.5rem',
              borderBottomRightRadius: '0.5rem',
              backgroundColor: isDark ? 'rgba(31, 41, 55, 0.9)' : 'rgba(249, 250, 251, 0.9)',
            }}
            codeTagProps={{
              style: {
                fontSize: '14px',
                fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
              }
            }}
          >
            {children}
          </SyntaxHighlighter>
        )}
      </div>
    </div>
  );
});

const MessageContentRenderer: React.FC<MessageContentRendererProps> = React.memo(({ 
  content, 
  className = '',
  isDark = false,
  isStreaming = false
}) => {
  const [darkMode, setDarkMode] = useState(isDark);

  // Update dark mode detection
  useEffect(() => {
    const updateDarkMode = () => {
      setDarkMode(document.documentElement.classList.contains('dark'));
    };
    
    updateDarkMode();
    
    // Listen for theme changes
    const observer = new MutationObserver(updateDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  // Check if content looks like it might be Markdown
  const hasMarkdownFeatures = useMemo(() => {
    const markdownPatterns = [
      /^#{1,6}\s/m,     // Headers
      /\*\*.*?\*\*/,    // Bold
      /\*.*?\*/,        // Italic
      /`.*?`/,          // Inline code
      /```[\s\S]*?```/, // Code blocks
      /^\s*[-*+]\s/m,   // Lists
      /^\s*\d+\.\s/m,   // Numbered lists
      /\[.*?\]\(.*?\)/, // Links
      /!\[.*?\]\(.*?\)/ // Images
    ];
    
    return markdownPatterns.some(pattern => pattern.test(content));
  }, [content]);

  // If content doesn't appear to be Markdown, render as plain text
  if (!hasMarkdownFeatures) {
    return (
      <div className={`prose prose-base dark:prose-invert max-w-none ${className}`}>
        <div className="whitespace-pre-wrap text-gray-800 dark:text-gray-200 leading-relaxed text-[15px]">
          {content}
        </div>
      </div>
    );
  }

  // Render as Markdown
  return (
    <div className={`prose prose-base dark:prose-invert max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          // Custom code block renderer avec support Mermaid
          code: ({ node, inline, className, children, ...props }: any) => {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';

            // 📊 DÉTECTION MERMAID : Rendre les diagrammes visuellement
            if (language === 'mermaid' && !inline) {
              return (
                <MermaidBlock isDark={darkMode}>
                  {String(children).replace(/\n$/, '')}
                </MermaidBlock>
              );
            }

            return (
              <CodeBlock
                language={language}
                className={className}
                isInline={inline}
                isDark={darkMode}
                isStreaming={isStreaming}
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </CodeBlock>
            );
          },

          // Fix DOM nesting: prevent div/pre inside p tags
          p: ({ children, ...props }) => {
            // Check if children contain block elements that shouldn't be in p tags
            const hasBlockElements = React.Children.toArray(children).some((child: any) => {
              return child?.type === 'div' || child?.props?.className?.includes('language-');
            });

            // If block elements are present, use div instead of p
            if (hasBlockElements) {
              return <div className="mb-4" {...props}>{children}</div>;
            }

            return <p className="mb-4" {...props}>{children}</p>;
          },
          
          // Enhanced link renderer
          a: ({ href, children, ...props }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline decoration-blue-400 underline-offset-2"
              {...props}
            >
              {children}
            </a>
          ),
          
          // Enhanced table renderer
          table: ({ children, ...props }) => (
            <div className="overflow-x-auto my-4">
              <table className="min-w-full border border-gray-200 dark:border-gray-700 rounded-lg" {...props}>
                {children}
              </table>
            </div>
          ),
          
          // Enhanced blockquote renderer
          blockquote: ({ children, ...props }) => (
            <blockquote 
              className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 dark:bg-blue-900/20 rounded-r-lg italic text-gray-700 dark:text-gray-300"
              {...props}
            >
              {children}
            </blockquote>
          ),

          // Enhanced ordered list renderer
          ol: ({ children, ...props }) => (
            <ol 
              className="list-decimal list-outside ml-6 mb-4 space-y-1 text-gray-800 dark:text-gray-200"
              {...props}
            >
              {children}
            </ol>
          ),

          // Enhanced unordered list renderer
          ul: ({ children, ...props }) => (
            <ul 
              className="list-disc list-outside ml-6 mb-4 space-y-1 text-gray-800 dark:text-gray-200"
              {...props}
            >
              {children}
            </ul>
          ),

          // Enhanced list item renderer
          li: ({ children, ...props }) => (
            <li 
              className="pl-2 leading-relaxed text-gray-800 dark:text-gray-200"
              {...props}
            >
              {children}
            </li>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
});

// Set display names for React DevTools
CodeBlock.displayName = 'CodeBlock';
MessageContentRenderer.displayName = 'MessageContentRenderer';

export default MessageContentRenderer; 