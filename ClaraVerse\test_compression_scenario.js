/**
 * 🧪 Test de compression IA - Scénario réaliste
 * 
 * Simule une conversation longue avec documents pour tester
 * le système de compression intelligente
 */

// Simulation d'une conversation réaliste avec documents
const testConversation = [
  {
    id: "msg-001",
    role: "user",
    content: "Salut ! J'ai besoin d'aide pour analyser mon CV et préparer un entretien d'embauche.",
    timestamp: new Date("2024-06-16T08:00:00Z"),
    metadata: {}
  },
  {
    id: "msg-002", 
    role: "assistant",
    content: "Bonjour ! Je serais ravi de vous aider avec votre CV et la préparation d'entretien. Pouvez-vous partager votre CV pour que je puisse l'analyser ?",
    timestamp: new Date("2024-06-16T08:00:30Z"),
    metadata: {}
  },
  {
    id: "msg-003",
    role: "user", 
    content: "Voici mon CV, peux-tu l'analyser stp et me donner des conseils ?",
    timestamp: new Date("2024-06-16T08:01:00Z"),
    metadata: {
      attachments: [{
        id: "cv-001",
        filename: "CV_<PERSON>_Dupont.pdf",
        content: `JEAN DUPONT
Développeur Full-Stack Senior
📧 <EMAIL> | 📱 +33 6 12 34 56 78 | 🌐 LinkedIn: /in/jeandupont

PROFIL PROFESSIONNEL
Développeur Full-Stack passionné avec 8 ans d'expérience dans le développement d'applications web modernes. Expert en JavaScript, React, Node.js et architectures cloud. Reconnu pour ma capacité à livrer des solutions techniques innovantes et à diriger des équipes de développement.

EXPÉRIENCE PROFESSIONNELLE

Senior Full-Stack Developer | TechCorp Solutions | 2020 - Présent
• Développement et maintenance d'applications React/Node.js pour 500K+ utilisateurs
• Architecture et implémentation de microservices sur AWS (Lambda, ECS, RDS)
• Encadrement d'une équipe de 5 développeurs juniors
• Amélioration des performances applicatives de 40% via optimisation du code
• Mise en place de pipelines CI/CD avec Jenkins et Docker
• Technologies: React, TypeScript, Node.js, PostgreSQL, Redis, AWS

Lead Developer | StartupTech | 2018 - 2020
• Création from scratch d'une plateforme SaaS B2B (0 à 10K utilisateurs)
• Développement d'APIs REST et GraphQL haute performance
• Implémentation d'architecture event-driven avec RabbitMQ
• Gestion de base de données MongoDB et optimisation des requêtes
• Technologies: Vue.js, Express.js, MongoDB, Docker, Kubernetes

Full-Stack Developer | WebAgency Pro | 2016 - 2018
• Développement de sites e-commerce sur Shopify et WooCommerce
• Intégration de systèmes de paiement (Stripe, PayPal, Adyen)
• Optimisation SEO et performance web (Core Web Vitals)
• Collaboration avec équipes design et marketing
• Technologies: PHP, Laravel, MySQL, jQuery, SASS

FORMATION
Master en Informatique | Université Paris-Saclay | 2014 - 2016
Licence en Informatique | Université Paris-Sud | 2011 - 2014

COMPÉTENCES TECHNIQUES
Frontend: React, Vue.js, Angular, TypeScript, HTML5, CSS3, SASS
Backend: Node.js, Express, PHP, Laravel, Python, Django
Bases de données: PostgreSQL, MongoDB, MySQL, Redis
Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, GitLab CI
Outils: Git, Jira, Figma, Postman, VS Code

PROJETS PERSONNELS
• EcoTracker - App mobile React Native pour suivi empreinte carbone (5K téléchargements)
• DevTools Extension - Extension Chrome pour développeurs (2K utilisateurs actifs)
• Open Source - Contributeur actif sur plusieurs projets GitHub (500+ stars)

LANGUES
Français: Natif | Anglais: Courant (TOEIC 950) | Espagnol: Intermédiaire

CENTRES D'INTÉRÊT
Veille technologique, Hackathons (3 victoires), Escalade, Photographie`
      }]
    }
  },
  {
    id: "msg-004",
    role: "assistant",
    content: `Excellent CV ! Voici mon analyse détaillée :

## 🎯 **POINTS FORTS**

### **Expérience solide**
- 8 ans d'expérience progressive avec montée en responsabilités
- Évolution claire : Developer → Lead → Senior
- Expérience variée : startup, agence, grande entreprise

### **Compétences techniques actuelles**
- Stack moderne : React, Node.js, TypeScript
- Cloud & DevOps : AWS, Docker, Kubernetes
- Architecture : microservices, event-driven

### **Impact quantifié**
- 500K+ utilisateurs, amélioration 40% performances
- Croissance 0 à 10K utilisateurs
- Encadrement équipe 5 développeurs

## 🔧 **SUGGESTIONS D'AMÉLIORATION**

### **1. Ajouter section certifications**
- AWS Solutions Architect
- Certifications React/Node.js

### **2. Détailler les réalisations**
- Métriques business (chiffre d'affaires, conversion)
- Technologies spécifiques utilisées par projet

### **3. Soft skills**
- Leadership, communication, résolution de problèmes
- Méthodologies agiles (Scrum, Kanban)

## 🎯 **RECOMMANDATIONS POUR L'ENTRETIEN**

Préparez-vous à détailler :
- Architecture des projets complexes
- Défis techniques surmontés
- Gestion d'équipe et mentoring
- Veille technologique et apprentissage continu`,
    timestamp: new Date("2024-06-16T08:02:00Z"),
    metadata: {}
  },
  {
    id: "msg-005",
    role: "user",
    content: "Merci pour cette analyse ! J'ai un entretien chez une fintech demain. Ils recherchent un Lead Developer pour une équipe de 8 personnes. Quelles questions techniques puis-je attendre ?",
    timestamp: new Date("2024-06-16T08:05:00Z"),
    metadata: {}
  },
  {
    id: "msg-006",
    role: "assistant", 
    content: `Parfait ! Pour une fintech, voici les questions techniques probables :

## 🏦 **QUESTIONS SPÉCIFIQUES FINTECH**

### **Sécurité & Conformité**
- "Comment gérez-vous la sécurité des données financières ?"
- "Expérience avec PCI DSS, GDPR, KYC/AML ?"
- "Chiffrement des données sensibles ?"

### **Performance & Scalabilité**
- "Comment optimiser une API avec 10M+ transactions/jour ?"
- "Gestion de la charge en temps réel ?"
- "Stratégies de cache pour données financières ?"

### **Architecture**
- "Conception d'un système de paiement distribué ?"
- "Event sourcing pour audit trail ?"
- "Microservices vs monolithe pour fintech ?"

## 👥 **QUESTIONS LEADERSHIP**

### **Gestion d'équipe**
- "Comment structureriez-vous une équipe de 8 développeurs ?"
- "Processus de code review et qualité ?"
- "Gestion des deadlines et priorités ?"

### **Technique + Management**
- "Comment former l'équipe aux nouvelles technologies ?"
- "Résolution de conflits techniques ?"
- "Métriques de performance équipe ?"

## 🎯 **PRÉPAREZ CES EXEMPLES**

1. **Projet complexe** avec votre équipe de 5 chez TechCorp
2. **Optimisation performance** (40% d'amélioration)
3. **Architecture microservices** chez StartupTech
4. **Gestion de crise** technique

Voulez-vous qu'on simule un entretien technique ?`,
    timestamp: new Date("2024-06-16T08:07:00Z"),
    metadata: {}
  },
  {
    id: "msg-007",
    role: "user",
    content: "Oui excellente idée ! Simule un entretien technique. Tu peux jouer le rôle du CTO de la fintech.",
    timestamp: new Date("2024-06-16T08:08:00Z"),
    metadata: {}
  },
  {
    id: "msg-008",
    role: "assistant",
    content: `🎭 **SIMULATION D'ENTRETIEN - CTO FinTech**

---

**CTO:** Bonjour Jean ! Je suis Marc, CTO de PayFlow. Merci d'être venu. J'ai lu votre CV, très impressionnant ! 

Commençons par une question technique : 

**"Nous traitons 5 millions de transactions par jour. Notre API commence à montrer des signes de latence. Comment diagnostiqueriez-vous et résoudriez-vous ce problème ?"**

*[Prenez votre temps pour répondre comme si c'était un vrai entretien. Je vous donnerai des follow-up questions selon votre réponse]*

---

💡 **Conseils pour votre réponse :**
- Approche méthodique (diagnostic → analyse → solution)
- Mentionnez des outils concrets
- Pensez monitoring, profiling, optimisation DB
- N'hésitez pas à poser des questions de clarification`,
    timestamp: new Date("2024-06-16T08:09:00Z"),
    metadata: {}
  },
  {
    id: "msg-009",
    role: "user",
    content: `Bonjour Marc ! Merci de me recevoir.

Pour diagnostiquer ce problème de latence, j'adopterais une approche systématique :

**1. Diagnostic initial**
- Analyser les métriques APM (New Relic, DataDog) pour identifier les endpoints lents
- Examiner les logs d'erreurs et timeouts
- Vérifier les métriques infrastructure (CPU, RAM, I/O)

**2. Analyse approfondie**
- Profiling de l'application (Node.js profiler, flame graphs)
- Analyse des requêtes DB les plus coûteuses
- Vérification des connexions réseau et latence externe

**3. Solutions potentielles**
- Optimisation des requêtes SQL (index, requêtes N+1)
- Mise en place de cache Redis pour données fréquentes
- Load balancing et scaling horizontal
- Optimisation du code (async/await, memory leaks)

**Questions de clarification :**
- Quel est le SLA actuel vs attendu ?
- Y a-t-il des pics de charge spécifiques ?
- Architecture actuelle (monolithe/microservices) ?`,
    timestamp: new Date("2024-06-16T08:12:00Z"),
    metadata: {}
  },
  {
    id: "msg-010",
    role: "assistant",
    content: `**CTO:** Excellente approche méthodique ! J'aime votre réflexe de poser des questions de clarification.

Pour répondre : SLA actuel 200ms, on vise 100ms. Pics entre 9h-11h et 14h-16h. Architecture microservices avec 12 services.

**Follow-up technique :**

**"Parfait. Maintenant, supposons que vous identifiez que 60% de la latence vient des requêtes à notre service de vérification KYC qui appelle des APIs externes. Comment optimiseriez-vous cela tout en maintenant la conformité réglementaire ?"**

---

**Question leadership :**

**"En parallèle, votre équipe de 8 développeurs commence à être stressée par ces problèmes de performance. Deux développeurs juniors ont fait des erreurs récemment. Comment gérez-vous cette situation ?"**

---

💡 **Attendu :**
- Solutions techniques créatives pour KYC
- Gestion humaine et processus qualité
- Équilibre performance/conformité`,
    timestamp: new Date("2024-06-16T08:15:00Z"),
    metadata: {}
  }
];

// Continuer la conversation pour atteindre 20K+ caractères
const additionalMessages = [
  {
    id: "msg-011",
    role: "user",
    content: `**Optimisation KYC :**

Pour le service KYC, plusieurs stratégies :

**1. Cache intelligent**
- Cache Redis avec TTL adapté aux exigences réglementaires
- Cache des résultats positifs (24h) vs négatifs (1h)
- Invalidation proactive sur mise à jour réglementaire

**2. Optimisation des appels externes**
- Parallélisation des vérifications non-dépendantes
- Circuit breaker pour éviter les cascading failures
- Retry avec backoff exponentiel

**3. Pré-vérification**
- Vérification asynchrone lors de l'onboarding
- Queue de traitement avec priorités
- Mise à jour périodique des statuts

**Gestion d'équipe :**

**1. Réduction du stress**
- Réunion d'équipe transparente sur la situation
- Priorisation claire des tâches critiques
- Pair programming pour les juniors

**2. Processus qualité**
- Code review obligatoire pour tous
- Tests automatisés renforcés
- Checklist de déploiement

**3. Support**
- Sessions de formation sur les bonnes pratiques
- Mentoring individuel pour les juniors
- Rotation des tâches critiques`,
    timestamp: new Date("2024-06-16T08:18:00Z"),
    metadata: {}
  },
  {
    id: "msg-012",
    role: "assistant",
    content: `**CTO:** Impressionnant ! Vous pensez à la fois technique et humain. J'apprécie particulièrement l'idée de cache avec TTL différencié selon le résultat.

**Question architecture :**

**"Nous envisageons de migrer vers une architecture event-driven pour améliorer la résilience. Vous avez mentionné RabbitMQ dans votre expérience. Comment concevriez-vous l'architecture événementielle pour notre système de paiement ?"**

**Contraintes :**
- Garantie de livraison des événements
- Ordre des transactions critique
- Audit trail complet
- Rollback en cas d'échec

---

**Question stratégique :**

**"En tant que Lead, comment voyez-vous l'évolution de notre stack technique dans les 2 prochaines années ? Quelles technologies émergentes pourraient nous donner un avantage concurrentiel ?"**

---

💡 **Points à couvrir :**
- Event sourcing, CQRS
- Message ordering, idempotence
- Vision technologique, innovation`,
    timestamp: new Date("2024-06-16T08:22:00Z"),
    metadata: {}
  },
  {
    id: "msg-013",
    role: "user",
    content: `**Architecture Event-Driven :**

**1. Design des événements**
- Event sourcing avec store immutable des événements
- CQRS pour séparer lecture/écriture
- Événements métier : PaymentInitiated, PaymentProcessed, PaymentFailed

**2. Garanties de livraison**
- Message queues persistantes (RabbitMQ avec durability)
- Acknowledgment explicite après traitement complet
- Dead letter queues pour gestion des échecs

**3. Ordre et cohérence**
- Partitioning par user_id ou account_id
- Saga pattern pour transactions distribuées
- Idempotence avec clés de déduplication

**4. Audit trail**
- Event store comme source de vérité
- Snapshots périodiques pour performance
- Versioning des événements pour évolution

**Vision technologique 2 ans :**

**1. IA/ML intégrée**
- Détection fraude en temps réel
- Scoring de risque dynamique
- Chatbots pour support client

**2. Blockchain sélective**
- Smart contracts pour certains produits
- Stablecoins pour transferts internationaux
- DeFi protocols pour yield farming

**3. Edge computing**
- Traitement local pour latence ultra-faible
- Compliance multi-juridictionnelle
- Résilience géographique`,
    timestamp: new Date("2024-06-16T08:26:00Z"),
    metadata: {}
  },
  {
    id: "msg-014",
    role: "assistant",
    content: `**CTO:** Fantastique ! Votre vision est très alignée avec notre roadmap. L'approche event sourcing avec CQRS est exactement ce qu'on envisage.

**Question finale technique :**

**"Dernière question technique : vous devez implémenter un système de rate limiting pour nos APIs publiques. Nous avons différents tiers clients (gratuit: 100 req/min, premium: 1000 req/min, enterprise: illimité). Comment l'architectureriez-vous ?"**

**Contraintes :**
- Latence < 5ms pour la vérification
- Résistant aux pics de trafic
- Monitoring en temps réel
- Possibilité de burst temporaire

---

**Questions RH :**

**"Parfait ! Maintenant quelques questions sur votre motivation :**

1. **Pourquoi quitter TechCorp après 4 ans ?**
2. **Qu'est-ce qui vous attire dans la fintech ?**
3. **Comment voyez-vous votre évolution dans 3 ans ?**
4. **Avez-vous des questions sur PayFlow ou le poste ?"**

---

💡 **Préparez-vous à :**
- Solution technique rate limiting
- Motivation authentique
- Questions pertinentes sur l'entreprise`,
    timestamp: new Date("2024-06-16T08:30:00Z"),
    metadata: {}
  },
  {
    id: "msg-015",
    role: "user",
    content: `**Rate Limiting Architecture :**

**1. Design distribué**
- Redis Cluster pour compteurs partagés
- Sliding window algorithm pour précision
- Clés structurées : "rate_limit:{client_id}:{window}"

**2. Implementation**
- Middleware Express.js avec lua scripts Redis
- Atomic increment + TTL pour performance
- Fallback local en cas de Redis down

**3. Tiers et burst**
- Configuration dynamique par client
- Burst allowance : 20% du quota/minute
- Reset quotas à intervalles fixes

**4. Monitoring**
- Métriques Prometheus : hits, rejets, latence
- Alertes sur seuils dépassés
- Dashboard Grafana temps réel

**Questions RH :**

**1. Pourquoi quitter TechCorp ?**
Après 4 ans, j'ai envie de nouveaux défis. TechCorp m'a permis de grandir techniquement et managérialement, mais je souhaite rejoindre un secteur plus impactant. La fintech combine innovation technique et utilité sociale.

**2. Attrait fintech ?**
- Impact direct sur la vie des gens
- Défis techniques complexes (sécurité, performance, conformité)
- Secteur en forte croissance avec innovation constante
- Opportunité de démocratiser l'accès aux services financiers

**3. Évolution 3 ans ?**
- Consolider mon expertise en architecture distribuée
- Développer mes compétences en IA/ML appliquée à la finance
- Évoluer vers un rôle d'Engineering Manager ou Principal Engineer
- Contribuer à la stratégie produit et technique

**Mes questions :**

1. **Quelle est la stack technique actuelle de PayFlow ?**
2. **Comment l'équipe gère-t-elle la veille technologique ?**
3. **Quels sont les principaux défis techniques à venir ?**
4. **Culture d'entreprise et processus de décision ?**
5. **Opportunités de formation et conférences ?**`,
    timestamp: new Date("2024-06-16T08:35:00Z"),
    metadata: {}
  },
  {
    id: "msg-016",
    role: "assistant",
    content: `**CTO:** Excellent ! Votre solution rate limiting est très solide, j'aime l'approche sliding window avec Redis.

**Réponses à vos questions :**

**1. Stack technique PayFlow :**
- Backend: Node.js, TypeScript, Express, Fastify
- Frontend: React, Next.js, TypeScript
- Databases: PostgreSQL (principal), Redis (cache), MongoDB (analytics)
- Infrastructure: AWS (EKS, RDS, ElastiCache), Terraform
- Monitoring: DataDog, Sentry, Grafana
- CI/CD: GitLab CI, ArgoCD

**2. Veille technologique :**
- Tech talks internes hebdomadaires
- Budget formation 3000€/an/développeur
- Participation à 2 conférences/an
- Slack channels dédiés par technologie
- POCs trimestriels sur nouvelles techs

**3. Défis techniques 2024-2025 :**
- Migration vers architecture event-driven
- Implémentation ML pour détection fraude
- Expansion européenne (multi-compliance)
- Open Banking APIs (PSD2)
- Performance : 1M transactions/jour → 10M

**4. Culture & décision :**
- Équipes autonomes avec ownership produit
- RFC process pour décisions techniques majeures
- Retrospectives et amélioration continue
- Remote-first avec bureaux Paris/Londres

**5. Formation :**
- Conférences : Devoxx, KubeCon, FinTech conferences
- Certifications AWS/GCP prises en charge
- Temps dédié R&D : 20% du temps
- Mentoring externe avec experts industrie

**Conclusion :**

Jean, je suis très impressionné par votre profil ! Votre expérience technique, votre vision architecturale et votre approche du leadership correspondent parfaitement à ce qu'on recherche.

**Prochaines étapes :**
1. Entretien avec notre Head of Product (30min)
2. Test technique à la maison (architecture d'un système de paiement)
3. Rencontre avec l'équipe (1h)
4. Décision finale sous 48h

**Questions de ma part :**
- Disponibilité pour commencer ?
- Prétentions salariales ?
- Préférence télétravail/bureau ?

Merci pour cet excellent échange !`,
    timestamp: new Date("2024-06-16T08:40:00Z"),
    metadata: {}
  },
  {
    id: "msg-017",
    role: "user",
    content: `Merci Marc ! Cet échange était passionnant et confirme mon intérêt pour PayFlow.

**Mes réponses :**

**Disponibilité :**
Préavis de 2 mois chez TechCorp, donc disponible début septembre. Possibilité de négocier pour mi-août si besoin urgent.

**Prétentions salariales :**
Fourchette 75-85K€ selon package global (variable, actions, avantages). Actuellement à 70K€ + 10% variable chez TechCorp.

**Télétravail/Bureau :**
Hybride idéal : 2-3 jours bureau pour collaboration équipe, 2-3 jours remote pour focus technique. Flexible selon besoins projets.

**Questions complémentaires :**

1. **Équipe actuelle :** Profils et séniorité des 8 développeurs ?
2. **Roadmap produit :** Nouvelles fonctionnalités prioritaires 2024 ?
3. **Partenariats :** Intégrations bancaires et fintechs prévues ?
4. **Croissance :** Objectifs utilisateurs et revenus 2024-2025 ?
5. **Concurrence :** Positionnement vs Stripe, Adyen, autres ?

**Test technique :**
J'ai hâte de travailler sur l'architecture système de paiement ! Délai souhaité pour le rendu ?

**Feedback entretien :**
Très professionnel, questions pertinentes couvrant technique et leadership. L'approche collaborative de PayFlow transparaît dans cet échange.

Merci encore et j'attends vos retours avec impatience !`,
    timestamp: new Date("2024-06-16T08:43:00Z"),
    metadata: {}
  },
  {
    id: "msg-018",
    role: "assistant",
    content: `**CTO:** Parfait Jean ! Vos réponses sont très satisfaisantes.

**Réponses rapides :**

**Équipe actuelle :**
- 2 Senior (5+ ans exp)
- 3 Mid-level (2-4 ans)
- 3 Junior (0-2 ans)
- Stack : 60% backend, 40% frontend
- Très bonne cohésion, culture bienveillante

**Test technique :**
Délai 5 jours, rendu attendu :
- Architecture système distribué
- Choix technologiques justifiés
- Gestion des cas d'erreur
- Scalabilité et sécurité
- Documentation claire

**Prochains steps :**
1. Je vous envoie le test technique aujourd'hui
2. Entretien Head of Product programmé vendredi
3. Rencontre équipe la semaine suivante

Votre profil nous intéresse beaucoup ! À très bientôt.

---

🎭 **FIN DE SIMULATION**

---

Alors, comment vous êtes-vous senti pendant cette simulation ?

**Points à retenir :**
✅ Approche méthodique des problèmes techniques
✅ Équilibre technique/management
✅ Questions pertinentes sur l'entreprise
✅ Motivation claire et authentique

**Axes d'amélioration :**
- Préparer plus d'exemples concrets de votre expérience
- Anticiper les questions sur vos échecs/difficultés
- Rechercher davantage l'entreprise avant l'entretien

Voulez-vous qu'on travaille sur d'autres aspects de votre préparation ?`,
    timestamp: new Date("2024-06-16T08:45:00Z"),
    metadata: {}
  },
  {
    id: "msg-019",
    role: "user",
    content: `Excellente simulation ! Très réaliste et bien structurée.

**Mon ressenti :**
- Questions techniques pertinentes et progressives
- Bon équilibre entre technique pur et leadership
- Ambiance bienveillante mais exigeante
- Durée réaliste (45min simulées)

**Ce qui m'a aidé :**
- Approche méthodique pour diagnostiquer les problèmes
- Exemples concrets de mon CV
- Questions de clarification avant de répondre
- Préparation des questions sur l'entreprise

**Points à améliorer :**
- Préparer plus d'anecdotes d'échecs et apprentissages
- Quantifier davantage mes réalisations
- Anticiper questions sur gestion de conflits
- Rechercher plus en détail l'entreprise cible

**Questions complémentaires :**

1. **Comment gérer le stress** pendant l'entretien technique ?
2. **Négociation salariale :** timing et arguments ?
3. **Questions pièges** courantes en entretien tech ?
4. **Follow-up** après entretien : bonnes pratiques ?

**Autres préparations souhaitées :**
- Simulation entretien avec questions comportementales
- Préparation du test technique architecture
- Stratégie de négociation d'offre

Cette simulation m'a donné confiance ! Merci pour ce travail de qualité.`,
    timestamp: new Date("2024-06-16T08:48:00Z"),
    metadata: {}
  },
  {
    id: "msg-020",
    role: "assistant",
    content: `Ravi que la simulation vous ait plu ! Vos retours sont très pertinents.

## 🧠 **RÉPONSES À VOS QUESTIONS**

### **1. Gestion du stress technique**
- **Respiration :** Prenez 2-3 secondes avant de répondre
- **Clarification :** "Puis-je reformuler pour m'assurer de bien comprendre ?"
- **Pensée à voix haute :** Verbalisez votre raisonnement
- **Honnêteté :** "Je ne connais pas cette techno, mais voici comment j'approcherais..."

### **2. Négociation salariale**
**Timing :** Après qu'ils montrent un intérêt fort, jamais en premier
**Arguments :**
- Valeur apportée (équipe, projets, résultats)
- Benchmark marché (Glassdoor, enquêtes salaires)
- Package global (formation, télétravail, évolution)
- Négociation win-win, pas confrontation

### **3. Questions pièges courantes**
- "Votre plus gros défaut ?" → Défaut réel + plan d'amélioration
- "Pourquoi cette reconversion ?" → Évolution naturelle, pas fuite
- "Où vous voyez-vous dans 5 ans ?" → Ambition alignée avec l'entreprise
- "Salaire actuel ?" → "Je préfère discuter de la valeur que j'apporte"

### **4. Follow-up post-entretien**
- Email de remerciement sous 24h
- Rappel des points clés discutés
- Réponse à une question restée en suspens
- Réaffirmation de votre intérêt

## 🎯 **PROCHAINES SIMULATIONS PROPOSÉES**

### **A. Entretien comportemental**
- Questions STAR (Situation, Task, Action, Result)
- Gestion de conflits, échecs, leadership
- Valeurs et culture d'entreprise

### **B. Test technique architecture**
- Conception système de paiement distribué
- Choix technologiques et trade-offs
- Documentation et présentation

### **C. Négociation d'offre**
- Réception d'une proposition
- Contre-proposition argumentée
- Finalisation des conditions

**Laquelle vous intéresse le plus ?**

Votre préparation est déjà excellente ! 🚀`,
    timestamp: new Date("2024-06-16T08:52:00Z"),
    metadata: {}
  }
];

// Ajouter encore plus de messages pour dépasser 20K
const moreMessages = [
  {
    id: "msg-021",
    role: "user",
    content: `Parfait ! Je voudrais commencer par l'entretien comportemental avec les questions STAR.

**Mais avant, j'ai une question sur mon CV :**

J'ai un trou de 6 mois en 2019 entre StartupTech et TechCorp. En réalité, j'ai pris un congé sabbatique pour voyager en Asie et apprendre le développement mobile. Comment présenter ça positivement ?

**Aussi, quelques situations à préparer en STAR :**

1. **Conflit d'équipe :** Désaccord technique majeur avec un collègue senior
2. **Échec projet :** Migration technique qui a mal tourné
3. **Leadership :** Motivation d'un développeur junior démotivé
4. **Innovation :** Proposition d'une nouvelle technologie adoptée
5. **Gestion de crise :** Bug critique en production un vendredi soir

**Questions spécifiques :**
- Comment structurer une réponse STAR efficace ?
- Durée idéale par réponse ?
- Comment éviter de critiquer d'anciens collègues/employeurs ?
- Faut-il préparer des versions courtes et longues ?

Prêt pour la simulation comportementale !`,
    timestamp: new Date("2024-06-16T08:55:00Z"),
    metadata: {}
  },
  {
    id: "msg-022",
    role: "assistant",
    content: `Excellentes questions ! Commençons par le trou de CV puis la méthode STAR.

## 🎯 **TROU DE CV - STRATÉGIE POSITIVE**

**❌ À éviter :** "J'ai pris une pause, j'étais fatigué..."
**✅ À dire :** "J'ai pris 6 mois pour me former au développement mobile et découvrir les écosystèmes tech asiatiques. Cette expérience m'a apporté une perspective internationale et de nouvelles compétences React Native que j'ai ensuite appliquées chez TechCorp."

**Points clés :**
- Apprentissage et développement personnel
- Vision internationale (atout pour fintech)
- Compétences concrètes acquises
- Application directe dans le poste suivant

## 📋 **MÉTHODE STAR OPTIMISÉE**

### **Structure :**
- **S**ituation (20%) : Contexte bref et clair
- **T**ask (20%) : Votre rôle et objectif
- **A**ction (50%) : Ce que VOUS avez fait concrètement
- **R**esult (10%) : Résultats mesurables + apprentissage

### **Timing :** 90 secondes à 2 minutes max
### **Focus :** 70% sur vos actions, pas le contexte

## 🎭 **SIMULATION ENTRETIEN COMPORTEMENTAL**

---

**RH:** Bonjour Jean ! Je suis Sarah, DRH de PayFlow. Après votre excellent entretien technique avec Marc, j'aimerais mieux vous connaître.

**"Commençons par une situation où vous avez dû gérer un conflit au sein de votre équipe. Pouvez-vous me raconter une situation concrète ?"**

---

💡 **Conseils pour votre réponse :**
- Choisissez un conflit technique, pas personnel
- Montrez votre rôle de médiateur/leader
- Insistez sur la résolution et l'apprentissage
- Évitez de blâmer qui que ce soit

**À vous !**`,
    timestamp: new Date("2024-06-16T08:58:00Z"),
    metadata: {}
  },
  {
    id: "msg-023",
    role: "user",
    content: `**Situation :** Chez TechCorp, lors du développement d'une nouvelle API, deux développeurs seniors de mon équipe étaient en désaccord sur l'architecture : l'un voulait une approche REST classique, l'autre poussait pour GraphQL. Le débat devenait tendu et bloquait l'avancement du projet.

**Task :** En tant que Lead Developer, je devais résoudre ce conflit rapidement pour respecter notre deadline de 3 semaines et maintenir la cohésion d'équipe.

**Action :**
1. **Écoute individuelle :** J'ai organisé des one-on-one avec chacun pour comprendre leurs arguments techniques et leurs préoccupations
2. **Analyse objective :** J'ai créé un tableau comparatif REST vs GraphQL avec critères : performance, complexité, maintenance, learning curve équipe
3. **POC parallèles :** J'ai proposé que chacun développe un prototype sur 2 jours pour comparer concrètement
4. **Décision collective :** Réunion d'équipe pour présenter les POCs et décider ensemble selon nos contraintes projet

**Result :** Nous avons choisi REST pour ce projet (simplicité et expertise équipe), mais planifié une formation GraphQL pour le projet suivant. Les deux développeurs ont apprécié l'approche collaborative. Projet livré dans les temps et relation d'équipe renforcée.

**Apprentissage :** L'importance de transformer les conflits techniques en opportunités d'apprentissage collectif.`,
    timestamp: new Date("2024-06-16T09:02:00Z"),
    metadata: {}
  }
];

// Calculer la taille totale
const allMessages = [...testConversation, ...additionalMessages, ...moreMessages];
const totalChars = allMessages.reduce((sum, msg) => sum + msg.content.length, 0);

console.log(`📊 Conversation de test générée :`);
console.log(`- Messages: ${allMessages.length}`);
console.log(`- Caractères: ${totalChars.toLocaleString()}`);
console.log(`- Seuil compression: 20,000 caractères`);
console.log(`- Déclenchement: ${totalChars > 20000 ? '✅ OUI' : '❌ NON'}`);

// Export pour utilisation
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testConversation: allMessages, totalChars };
}
