[{"category": "data-integration", "name": "Synchronize Your Google Sheets With Postgres", "description": "This workflow automatically synchronizes data between a Google Sheet and a Postgres database, ensuring that the data is up-to-date and consistent.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2081-synchronize-your-google-sheets-with-postgres/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2081-synchronize-your-google-sheets-with-postgres/workflow.json", "nodeCount": 10, "nodeNames": ["Schedule Trigger", "Compare Datasets", "Split Out Relevant Fields", "Retrieve Sheets Data", "Select Rows in Postgres", "Insert Rows", "Update Rows", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2"], "tags": ["data-integration", "google", "postgres", "sheets", "synchronize"]}, {"category": "data-integration", "name": "Import Data From Mysql Into Google Sheets", "description": "This workflow automatically retrieves data from a MySQL database, and then appends that data to a Google Sheets spreadsheet on a weekly schedule.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1753-import-data-from-mysql-into-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1753-import-data-from-mysql-into-google-sheets/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "MySQL - select", "Google Sheets - write"], "tags": ["data", "data-integration", "google", "import", "mysql", "sheets"]}, {"category": "data-integration", "name": "Replace Images In Google Docs Documents And Download As Pdf Docx", "description": "This workflow automates the process of finding and replacing images in Google Docs documents, allowing users to dynamically update document images from a database of image URLs and optionally generate shareable PDF and DOCX versions of the updated documents.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2501-replace-images-in-google-docs-documents-and-download-as-pdf-docx/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2501-replace-images-in-google-docs-documents-and-download-as-pdf-docx/workflow.json", "nodeCount": 18, "nodeNames": ["When clicking ‘Test workflow’", "Sticky Note9", "Find Image ID in Docx", "Make file shareable publically (optional)", "Image URL", "Find & Copy Docx Template", "<PERSON><PERSON>", "Own datasource", "Sticky Note43", "<PERSON><PERSON> Note44", "Sticky Note45", "Replace Image in Docx", "Sticky Note46", "Sticky Note47", "Download File - Docx", "Download File - PDF", "Sticky Note48", "Sticky Note49"], "tags": ["as", "data-integration", "docs", "documents", "docx", "download", "google", "images", "pdf", "replace"]}, {"category": "data-integration", "name": "Import Data From Google Sheets Into Mysql", "description": "This workflow automatically retrieves data from a Google Sheet, and then inserts the data into a MySQL database on a weekly schedule.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1752-import-data-from-google-sheets-into-mysql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1752-import-data-from-google-sheets-into-mysql/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "MySQL - insert", "Google Sheets - read"], "tags": ["data", "data-integration", "google", "import", "mysql", "sheets"]}, {"category": "data-integration", "name": "Sync Data Between Google Drive And Aws S3", "description": "This workflow automatically uploads files updated in a specific Google Drive folder to an AWS S3 bucket, with additional metadata tags.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1396-sync-data-between-google-drive-and-aws-s3/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1396-sync-data-between-google-drive-and-aws-s3/workflow.json", "nodeCount": 4, "nodeNames": ["Google Drive Trigger", "<PERSON><PERSON>", "AWS S3  - get", "AWS S3 - upload"], "tags": ["aws", "between", "data", "data-integration", "drive", "google", "s3", "sync"]}, {"category": "data-integration", "name": "Streamline Data From An N8N Form Into Google Sheet Airtable And Email Sending", "description": "This workflow collects data from a form, processes it, and saves the formatted data to Google Sheets and Airtable while also sending customized emails to the submitter.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2087-streamline-data-from-an-n8n-form-into-google-sheet-airtable-and-email-sending/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2087-streamline-data-from-an-n8n-form-into-google-sheet-airtable-and-email-sending/workflow.json", "nodeCount": 10, "nodeNames": ["n8n Form Trigger", "Extracting Date and Time Fields from 'submittedAt' Field", "Format the Fields", "Airtable", "<PERSON><PERSON>", "Google Sheets", "Gmail", "Gmail1", "Sticky Note1", "Sticky Note2"], "tags": ["airtable", "an", "data", "data-integration", "email", "form", "google", "n8n", "sending", "sheet", "streamline"]}, {"category": "data-integration", "name": "Import Json Data Into Google Sheets And Csv File", "description": "This workflow fetches random user data from an API, extracts relevant information, and appends it to a Google Sheet while also generating a CSV file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1737-import-json-data-into-google-sheets-and-csv-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1737-import-json-data-into-google-sheets-and-csv-file/workflow.json", "nodeCount": 6, "nodeNames": ["HTTP Request", "Google Sheets", "Set", "Spreadsheet File", "Note", "Note1"], "tags": ["csv", "data", "data-integration", "file", "google", "import", "json", "sheets"]}, {"category": "data-integration", "name": "Add Product Ideas To Google Sheets Via A Slack", "description": "This workflow allows Slack users to submit new ideas, which are then automatically added to a Google Sheets document, and a follow-up message is sent to the user requesting more details.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2141-add-product-ideas-to-google-sheets-via-a-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2141-add-product-ideas-to-google-sheets-via-a-slack/workflow.json", "nodeCount": 8, "nodeNames": ["Webhook", "<PERSON><PERSON>", "Sticky Note1", "Set me up", "Sticky Note2", "Switch", "Add to Google Sheets", "Hidden message to <PERSON><PERSON><PERSON> to add feature details"], "tags": ["add", "data-integration", "google", "ideas", "product", "sheets", "slack", "via"]}, {"category": "data-integration", "name": "Collect And Label Images And Send To Google Sheets", "description": "This workflow automates the process of searching for images, analyzing them using AWS Rekognition, and then appending the image details and labels to a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1401-collect-and-label-images-and-send-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1401-collect-and-label-images-and-send-to-google-sheets/workflow.json", "nodeCount": 4, "nodeNames": ["HTTP Request1", "AWS Rekognition1", "Google Sheets2", "Set3"], "tags": ["collect", "data-integration", "google", "images", "label", "send", "sheets"]}, {"category": "data-integration", "name": "Google Calendar To Slack Status And <PERSON>", "description": "This workflow automatically updates your Slack status based on your Google Calendar events, allowing you to easily communicate your availability and activity to your team.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1420-google-calendar-to-slack-status-and-philips-hue/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1420-google-calendar-to-slack-status-and-philips-hue/workflow.json", "nodeCount": 9, "nodeNames": ["On clicking 'execute'", "Google Calendar", "Light - Busy", "Light - Available", "Switch", "Light - Personal", "Event Started", "Slack - Status", "Set CalColor"], "tags": ["calendar", "data-integration", "google", "hue", "philips", "slack", "status"]}, {"category": "data-integration", "name": "Get Csv From Url And Convert To Excel", "description": "This workflow automates the process of converting a CSV file from a public data source into an Excel (.xlsx) file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2041-get-csv-from-url-and-convert-to-excel/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2041-get-csv-from-url-and-convert-to-excel/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Execute Workflow\"", "Import CSV", "Download CSV", "Convert to Excel", "<PERSON><PERSON>"], "tags": ["convert", "csv", "data-integration", "excel", "get", "url"]}, {"category": "data-integration", "name": "Archive Empty Pages In Notion Database", "description": "This workflow automatically archives Notion database pages with empty properties or content on a daily schedule.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1478-archive-empty-pages-in-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1478-archive-empty-pages-in-notion-database/workflow.json", "nodeCount": 10, "nodeNames": ["Get All Databases", "Get All Database Pages", "Get Page Blocks", "Process Blocks", "SplitInBatches", "Check for empty properties", "Archive Page", "If toDelete", "If Empty Properties", "Every day @ 2am"], "tags": ["archive", "data-integration", "database", "empty", "notion", "pages"]}, {"category": "data-integration", "name": "Monitor Changes In Google Sheets Every 45 Mins", "description": "This workflow automatically checks a Google Sheet for new data, and sends a notification to a Mattermost channel whenever new information is added.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/864-monitor-changes-in-google-sheets-every-45-mins/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/864-monitor-changes-in-google-sheets-every-45-mins/workflow.json", "nodeCount": 4, "nodeNames": ["Send message", "Check if new data", "Read data", "Execute every 45 mins"], "tags": ["45", "changes", "data-integration", "every", "google", "mins", "monitor", "sheets"]}, {"category": "data-integration", "name": "Convert <PERSON><PERSON> To An Excel File", "description": "This workflow accepts data via a webhook, converts it into a spreadsheet file, and responds to the webhook with the generated file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1435-convert-json-to-an-excel-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1435-convert-json-to-an-excel-file/workflow.json", "nodeCount": 4, "nodeNames": ["Webhook", "Item Lists", "Spreadsheet File", "Respond to Webhook"], "tags": ["an", "convert", "data-integration", "excel", "file", "json"]}, {"category": "data-integration", "name": "Sync Zendesk Tickets With Subsequent Comments To Github Issues", "description": "This workflow automatically creates or updates a GitHub issue when a new Zendesk ticket is created, providing a seamless integration between customer support and development.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1832-sync-zendesk-tickets-with-subsequent-comments-to-github-issues/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1832-sync-zendesk-tickets-with-subsequent-comments-to-github-issues/workflow.json", "nodeCount": 7, "nodeNames": ["Get ticket", "IF", "Update ticket", "Determine", "Create issue", "Create comment on existing issue", "On new Zendesk ticket"], "tags": ["comments", "data-integration", "github", "issues", "subsequent", "sync", "tickets", "zendesk"]}, {"category": "data-integration", "name": "Read Xml File And Store Content In Google Sheets", "description": "This workflow downloads an XML file, parses its contents, and writes the data to a new Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1810-read-xml-file-and-store-content-in-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1810-read-xml-file-and-store-content-in-google-sheets/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "Note", "Download XML File", "Parse XML content", "Create new spreadsheet file", "Define header row", "Split out food items", "Write header row", "Wait for spreadsheet creation", "Write data to sheet"], "tags": ["content", "data-integration", "file", "google", "read", "sheets", "store", "xml"]}, {"category": "data-integration", "name": "Send A Daily Summary Of Your Google Calendar Events To Slack", "description": "This workflow automatically retrieves upcoming events from a Google Calendar, compares the event dates with the current date, and sends a daily summary of the day's meetings to a Slack channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1277-send-a-daily-summary-of-your-google-calendar-events-to-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1277-send-a-daily-summary-of-your-google-calendar-events-to-slack/workflow.json", "nodeCount": 12, "nodeNames": ["Google Calendar", "Function", "Date & Time", "IF", "Date & Time1", "Set", "<PERSON><PERSON>", "Set1", "Date & Time2", "Function1", "<PERSON><PERSON>ck", "<PERSON><PERSON>"], "tags": ["calendar", "daily", "data-integration", "events", "google", "send", "slack", "summary"]}, {"category": "data-integration", "name": "Download A File From Google Drive", "description": "This workflow allows users to download a file from Google Drive and save it to a local file system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/515-download-a-file-from-google-drive/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/515-download-a-file-from-google-drive/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Google Drive", "Write Binary File"], "tags": ["data-integration", "download", "drive", "file", "google"]}, {"category": "data-integration", "name": "Sync Notion Database Pages As Clickup Tasks", "description": "This workflow automatically updates a ClickUp task and the corresponding Notion database page when changes are made to the Notion database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1835-sync-notion-database-pages-as-clickup-tasks/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1835-sync-notion-database-pages-as-clickup-tasks/workflow.json", "nodeCount": 5, "nodeNames": ["On updated database page", "Update an existing task", "On task status updated", "Get database page by ClickUp ID", "Update the status of found database page"], "tags": ["as", "clickup", "data-integration", "database", "notion", "pages", "sync", "tasks"]}, {"category": "data-integration", "name": "Sync Tasks Automatically From Todoist To Notion", "description": "This workflow automatically syncs Todoist tasks with a specific label to a Notion database, and then updates the Todoist task to indicate it has been sent to Notion.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1778-sync-tasks-automatically-from-todoist-to-notion/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1778-sync-tasks-automatically-from-todoist-to-notion/workflow.json", "nodeCount": 4, "nodeNames": ["On schedule", "Get all tasks with specific label", "Add to Notion database", "Replace label on task"], "tags": ["automatically", "data-integration", "notion", "sync", "tasks", "todoist"]}, {"category": "data-integration", "name": "Import Multiple Csv To Google Sheets", "description": "This workflow automates the process of importing and organizing subscriber data from multiple CSV files into a Google Sheets spreadsheet, ensuring data integrity and providing a centralized view of the subscriber base.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1968-import-multiple-csv-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1968-import-multiple-csv-to-google-sheets/workflow.json", "nodeCount": 9, "nodeNames": ["When clicking \"Execute Workflow\"", "Read Binary Files", "Split In Batches", "Read CSV", "Remove duplicates", "Keep only subscribers", "Sort by date", "Upload to spreadsheet", "Assign source file name"], "tags": ["csv", "data-integration", "google", "import", "multiple", "sheets"]}, {"category": "data-integration", "name": "Sync Data Between Multiple Google Spreadsheets", "description": "This workflow automatically reads data from a Google Sheet, and then updates two other Google Sheets with the same data on a scheduled interval.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/6-sync-data-between-multiple-google-spreadsheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/6-sync-data-between-multiple-google-spreadsheets/workflow.json", "nodeCount": 4, "nodeNames": ["Read Sheet", "<PERSON><PERSON>", "Write Sheet 2", "Write Sheet 1"], "tags": ["between", "data", "data-integration", "google", "multiple", "spreadsheets", "sync"]}, {"category": "data-integration", "name": "Insert And Read Data From Google Sheets", "description": "This workflow allows users to easily append data to a Google Sheet by manually triggering the workflow and setting the data to be added.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/600-insert-and-read-data-from-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/600-insert-and-read-data-from-google-sheets/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Google Sheets", "Set", "Google Sheets1"], "tags": ["data", "data-integration", "google", "insert", "read", "sheets"]}, {"category": "data-integration", "name": "Excel To Postgres", "description": "This workflow extracts data from a binary spreadsheet file, processes it, and inserts the data into a PostgreSQL database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/excel-to-postgres/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/excel-to-postgres/workflow.json", "nodeCount": 3, "nodeNames": ["Read Binary File", "Spreadsheet File1", "Insert Rows1"], "tags": ["data-integration", "excel", "postgres"]}, {"category": "data-integration", "name": "Convert Sql Table Into Excel Spreadsheet", "description": "This workflow exports a MySQL table to an XLSX spreadsheet file, providing a convenient way to access and share data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1872-convert-sql-table-into-excel-spreadsheet/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1872-convert-sql-table-into-excel-spreadsheet/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Execute Workflow\"", "TableName", "LoadMySQLData", "SaveSpreadsheet", "<PERSON><PERSON>"], "tags": ["convert", "data-integration", "excel", "spreadsheet", "sql", "table"]}, {"category": "data-integration", "name": "Read In An Excel Spreadsheet File", "description": "This workflow allows users to manually trigger the extraction and processing of data from a spreadsheet file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/890-read-in-an-excel-spreadsheet-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/890-read-in-an-excel-spreadsheet-file/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Spreadsheet File", "Read Binary File"], "tags": ["an", "data-integration", "excel", "file", "read", "spreadsheet"]}, {"category": "data-integration", "name": "Import Csv From Url To Google Sheets", "description": "This workflow automates the process of downloading a COVID-19 data CSV file, filtering the data to include only DACH (Germany, Austria, Switzerland) countries in 2023, and then uploading the filtered data to a Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1969-import-csv-from-url-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1969-import-csv-from-url-to-google-sheets/workflow.json", "nodeCount": 7, "nodeNames": ["When clicking \"Execute Workflow\"", "Upload to spreadsheet", "Add unique field", "Import CSV", "Download CSV", "Keep only DACH in 2023", "<PERSON><PERSON>"], "tags": ["csv", "data-integration", "google", "import", "sheets", "url"]}, {"category": "data-integration", "name": "Google Maps Scraper", "description": "This workflow scrapes Google Maps data efficiently using the SerpAPI service and saves the results to a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2063-google-maps-scraper/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2063-google-maps-scraper/workflow.json", "nodeCount": 20, "nodeNames": ["When clicking \"Execute Workflow\"", "Extract next start value", "Merge all values from SERPAPI", "Transform data in the right format", "Add rows in Google Sheets", "SERPAPI - Scrape Google Maps URL", "Remove duplicate items", "Split out items", "Remove empty values", "Google Sheets - Get searches  to scrap", "Extract keyword and location from URL", "Sticky Note1", "Run workflow every hours", "Sticky Note2", "Sticky Note3", "Update Status to Success", "Update Status to Error", "<PERSON><PERSON>", "Continue IF Loop is complete", "Sticky Note4"], "tags": ["data-integration", "google", "maps", "scraper"]}, {"category": "data-integration", "name": "Automated Congratulations With Google Sheets Twilio And N8N", "description": "This workflow automatically sends personalized congratulatory messages to employees on their special occasions, enhancing employee engagement and morale.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/770-automated-congratulations-with-google-sheets-twilio-and-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/770-automated-congratulations-with-google-sheets-twilio-and-n8n/workflow.json", "nodeCount": 8, "nodeNames": ["Daily Trigger", "Congratulations Calendar", "Any Event Today?", "Do Nothing", "Congratulations Messages", "Merge Data", "Personalize Message", "Send SMS"], "tags": ["automated", "congratulations", "data-integration", "google", "n8n", "sheets", "twi<PERSON>"]}, {"category": "data-integration", "name": "Google Spreadsheet To Html Variant With Spreadsheet File", "description": "This workflow allows users to automatically generate an HTML file from data stored in a Google Sheet, triggered by a webhook.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1756-google-spreadsheet-to-html-variant-with-spreadsheet-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1756-google-spreadsheet-to-html-variant-with-spreadsheet-file/workflow.json", "nodeCount": 3, "nodeNames": ["Read from Google Sheets", "Create HTML file", "Webhook"], "tags": ["data-integration", "file", "google", "html", "spreadsheet", "variant"]}, {"category": "data-integration", "name": "Send A Chatgpt Email Reply And Save Responses To Google Sheets", "description": "This workflow sends a ChatGPT email reply when an email is received from specific email recipients, and saves the initial email and the GPT response to a Google spreadsheet. It also records feedback on the GPT responses, which can be used to fine-tune the model.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1898-send-a-chatgpt-email-reply-and-save-responses-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1898-send-a-chatgpt-email-reply-and-save-responses-to-google-sheets/workflow.json", "nodeCount": 49, "nodeNames": ["Generate reply", "On email received", "Only continue for specific emails", "Configure", "Note5", "Send reply to recipient", "Generate UUID", "Thanks for your response!", "Extract message content (advanced)", "If spreadsheet doesn't exist", "Successfully created or updated row", "Note1", "Note2", "Note3", "Create spreadsheet", "Store spreadsheet ID", "Paste data", "If no sheet IDs", "Create or update rows", "Get data from `Format data`", "Get data from `Format data` node", "Format data", "Send email reply", "On feedback given", "Send feedback for fine-tuned data", "Show HTML page", "Get sheet IDs #1", "Note", "If no spreadsheet in configuration #2", "Store specific sheet IDs #2", "Get sheet IDs #2", "If no spreadsheet in configuration #1", "Store specific sheet IDs #1", "Email template", "Record feedback", "Fallback route", "Identify trigger #2", "Identify trigger #1", "Do not send unfinished email reply", "If reply is complete", "<PERSON><PERSON>", "Do not send email to this recipient", "Send reply to database", "Sticky Note1", "Determine which trigger ran", "Sticky Note2", "Is text within token limit?", "Do nothing", "Sticky Note3"], "tags": ["chatgpt", "data-integration", "email", "google", "reply", "responses", "save", "send", "sheets"]}, {"category": "data-integration", "name": "Sync Discord Scheduled Events To Google Calendar", "description": "This workflow automatically syncs scheduled events from a Discord server to a Google Calendar, ensuring that the calendar is up-to-date with the latest event details.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1940-sync-discord-scheduled-events-to-google-calendar/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1940-sync-discord-scheduled-events-to-google-calendar/workflow.json", "nodeCount": 9, "nodeNames": ["List scheduled events from Discord", "On schedule", "Update event details", "Create event", "Get events", "Create or update?", "Configure", "Sticky Note1", "<PERSON><PERSON>"], "tags": ["calendar", "data-integration", "discord", "events", "google", "scheduled", "sync"]}, {"category": "data-integration", "name": "Xml To Sql Database Import", "description": "This workflow extracts product data from an XML file, transforms it into a JSON format, and then inserts the data into a MySQL database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1948-xml-to-sql-database-import/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1948-xml-to-sql-database-import/workflow.json", "nodeCount": 9, "nodeNames": ["When clicking \"Execute Workflow\"", "Read Binary Files", "Item Lists", "Extract binary data", "XML to JSON", "Add new records", "Create new table", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["data-integration", "database", "import", "sql", "xml"]}, {"category": "data-integration", "name": "Kv Cloudflare Key Value Database Full Api Integration Workflow", "description": "This workflow provides a comprehensive solution for managing Key-Value (KV) pairs in Cloudflare's KV storage, allowing users to perform various actions such as creating, deleting, renaming, and managing KV namespaces and their contents.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2046-kv-cloudflare-key-value-database-full-api-integration-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2046-kv-cloudflare-key-value-database-full-api-integration-workflow/workflow.json", "nodeCount": 47, "nodeNames": ["<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Delete KV", "Sticky Note3", "Delete KV1", "KV to Rename", "Account Path", "Sticky Note4", "Sticky Note5", "Delete KVs inside NM", "Sticky Note6", "Create KV-NM", "Write KVs inside NM", "Sticky Note7", "-Get <PERSON> inside NM", "Sticky Note8", "Sticky Note9", "Sticky Note10", "Delete KV inside NM", "Sticky Note11", "Read Value Of KV In NM", "Sticky Note12", "Sticky Note13", "Sticky Note14", "Sticky Note15", "Manual Trigger", "List KV-NMs (1)", "Set KV-NM Name (2)", "Set KV-NM Name (1)", "Set KV-NM Name (3)", "List KV-NMs (2)", "List KV-NMs (3)", "Set KV-NM Name (4)", "Write V & MD of KV In NM", "Set KV-NM Name (5)", "Set KV-NM Name (6)", "List KV-NMs (4)", "List KV-NMs (5)", "List KV-NMs (6)", "List KV-NMs (7)", "List KV-NMs (8)", "List KV-NMs (9)", "List KV-NMs (10)", "Set KV-NM Name (7)", "Set KV-NM Name (8)", "<PERSON> from Key"], "tags": ["api", "cloudflare", "data-integration", "database", "full", "integration", "key", "kv", "value"]}, {"category": "data-integration", "name": "Waitlist Form Stored In Googlesheet With Email Verification Step", "description": "This workflow automates the process of collecting user information for a waitlist, generating a verification code, and storing the data in a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2550-waitlist-form-stored-in-googlesheet-with-email-verification-step/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2550-waitlist-form-stored-in-googlesheet-with-email-verification-step/workflow.json", "nodeCount": 19, "nodeNames": ["Add  to Waitlist Sheet", "Clean and Standardize", "Send Verification Email", "Validate with Verification Code", "<PERSON><PERSON>", "Sticky Note1", "Get all Data from the Prev Form + Current", "Additional Data for the Sheet", "Every Step Data", "is the Code correct?", "Let the User Reenter Code", "<PERSON><PERSON>", "Sticky Note2", "Save Intend to List", "Save as Verified", "Sticky Note3", "Generate Random Verification Code", "Waitlist Form", "Note3"], "tags": ["data-integration", "email", "form", "googlesheet", "step", "stored", "verification", "waitlist"]}, {"category": "data-integration", "name": "Generate And Insert Data Into A Postgres Database", "description": "This workflow automatically generates and stores sensor data (humidity readings) in a PostgreSQL database on a regular schedule.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/356-generate-and-insert-data-into-a-postgres-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/356-generate-and-insert-data-into-a-postgres-database/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "Function", "Postgres"], "tags": ["data", "data-integration", "database", "generate", "insert", "postgres"]}, {"category": "data-integration", "name": "Send Labeled Email To A Notion Database", "description": "This workflow automatically creates Notion database pages for emails with a specific label, and removes the label when the task is completed in Notion.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1939-send-labeled-email-to-a-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1939-send-labeled-email-to-a-notion-database/workflow.json", "nodeCount": 14, "nodeNames": ["On schedule", "Derive last request time", "Get emails from label and last request time", "Create database page", "Try get database page", "If checked off", "On updated database page", "Remove label from target email", "Not yet checked off", "do nothing", "<PERSON><PERSON>", "If found", "do nothing", "If database page not found", "Find my email address", "<PERSON><PERSON>"], "tags": ["data-integration", "database", "email", "labeled", "notion", "send"]}, {"category": "data-integration", "name": "Transfer Google Analytics Data To Airtable Database", "description": "This workflow retrieves Google Analytics data, processes it, and appends the results to an Airtable database, providing valuable insights for business decision-making.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/892-transfer-google-analytics-data-to-airtable-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/892-transfer-google-analytics-data-to-airtable-database/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Google Analytics", "Set", "Airtable"], "tags": ["airtable", "analytics", "data", "data-integration", "database", "google", "transfer"]}, {"category": "data-integration", "name": "Get Email Notifications For Newly Uploaded Google Drive Files", "description": "This workflow automatically sends an email notification whenever a new file is created in a specific Google Drive folder.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1283-get-email-notifications-for-newly-uploaded-google-drive-files/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1283-get-email-notifications-for-newly-uploaded-google-drive-files/workflow.json", "nodeCount": 2, "nodeNames": ["Google Drive Trigger", "Send Email"], "tags": ["data-integration", "drive", "email", "files", "get", "google", "newly", "notifications", "uploaded"]}, {"category": "data-integration", "name": "Working With Excel Spreadsheet Files Xls Xlsx", "description": "This workflow allows users to load spreadsheet files from various sources, manipulate the data, and optionally save or upload the modified file to local or cloud storage platforms.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1826-working-with-excel-spreadsheet-files-xls-xlsx/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1826-working-with-excel-spreadsheet-files-xls-xlsx/workflow.json", "nodeCount": 24, "nodeNames": ["Note", "Note1", "Read Binary File", "Note7", "Note8", "On clicking 'execute'", "Note2", "Note4", "Note5", "Write Binary File", "Note6", "Note9", "Note10", "Note11", "Note3", "Download from Google Drive", "Download from Microsoft OneDrive", "Download Excel File", "Work out Age", "Upload to SFTP", "Upload to Google Drive", "Upload to Microsoft OneDrive", "Read Spreadsheet File", "Write Spreadsheet File"], "tags": ["data-integration", "excel", "files", "spreadsheet", "working", "xls", "xlsx"]}, {"category": "data-integration", "name": "Sync Jira Issues With Subsequent Comments To Notion Database", "description": "This workflow automatically creates, updates, or deletes Notion database pages based on Jira issue events, providing a seamless integration between the two platforms.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1831-sync-jira-issues-with-subsequent-comments-to-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1831-sync-jira-issues-with-subsequent-comments-to-notion-database/workflow.json", "nodeCount": 10, "nodeNames": ["Create database page", "Note", "Find database page", "Switch", "IF", "Delete issue", "On issues created/updated/deleted", "Lookup table", "Create custom Notion filters", "Update issue"], "tags": ["comments", "data-integration", "database", "issues", "jira", "notion", "subsequent", "sync"]}, {"category": "data-integration", "name": "Send Google Drive Files To Notion Database", "description": "This workflow automatically creates a new Notion database page whenever a file is uploaded to a specific Google Drive folder, providing a seamless way to organize and track file uploads.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1819-send-google-drive-files-to-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1819-send-google-drive-files-to-notion-database/workflow.json", "nodeCount": 2, "nodeNames": ["On file upload", "Create database page"], "tags": ["data-integration", "database", "drive", "files", "google", "notion", "send"]}, {"category": "data-integration", "name": "Get All Excel Workbooks", "description": "This workflow allows users to retrieve data from a Microsoft Excel spreadsheet by manually triggering the workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/566-get-all-excel-workbooks/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/566-get-all-excel-workbooks/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Microsoft Excel"], "tags": ["all", "data-integration", "excel", "get", "workbooks"]}, {"category": "data-integration", "name": "Chat With A Database Using Ai", "description": "This workflow enables users to interact with a conversational AI agent that can query a Postgres database and provide natural language responses, powered by the OpenAI language model.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2090-chat-with-a-database-using-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2090-chat-with-a-database-using-ai/workflow.json", "nodeCount": 5, "nodeNames": ["OpenAI Chat Model", "AI Agent", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["ai", "chat", "data-integration", "database", "using"]}, {"category": "data-integration", "name": "Backup N8N Workflows To Google Drive", "description": "This workflow automatically retrieves a list of workflows from an n8n instance, downloads the details of each workflow, and uploads the workflow data as JSON files to a specified Google Drive folder.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1150-backup-n8n-workflows-to-google-drive/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1150-backup-n8n-workflows-to-google-drive/workflow.json", "nodeCount": 9, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Move Binary Data", "Map", "Get Workflow", "Get Workflow List", "FunctionItem", "Google Drive", "Run Daily at 2:30am"], "tags": ["backup", "data-integration", "drive", "google", "n8n", "workflows"]}, {"category": "data-integration", "name": "Transform Data In Google Sheets", "description": "This workflow automates the process of managing rental property data in a Google Sheet, including adding new properties, updating existing ones, and generating reports.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/694-transform-data-in-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/694-transform-data-in-google-sheets/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Google Sheets2", "Set1", "Google Sheets1", "Google Sheets", "Google Sheets3", "Set"], "tags": ["data", "data-integration", "google", "sheets", "transform"]}, {"category": "data-integration", "name": "Insert Excel Data To Postgres", "description": "This workflow extracts data from a binary spreadsheet file, processes it, and then inserts the data into a PostgreSQL database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1-insert-excel-data-to-postgres/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1-insert-excel-data-to-postgres/workflow.json", "nodeCount": 3, "nodeNames": ["Read Binary File", "Spreadsheet File1", "Insert Rows1"], "tags": ["data", "data-integration", "excel", "insert", "postgres"]}, {"category": "data-integration", "name": "Add A Task To Google Tasks", "description": "This workflow allows users to create a new task in Google Tasks with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/428-add-a-task-to-google-tasks/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/428-add-a-task-to-google-tasks/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Google Tasks"], "tags": ["add", "data-integration", "google", "task", "tasks"]}, {"category": "data-integration", "name": "Add Data From Google Sheet To Dropbox", "description": "This workflow automatically reads data from a Google Sheet, converts it to an Excel file, and uploads the file to Dropbox on a 15-minute interval.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/11-add-data-from-google-sheet-to-dropbox/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/11-add-data-from-google-sheet-to-dropbox/workflow.json", "nodeCount": 4, "nodeNames": ["Read Sheet", "Convert to XLS", "Upload Dropbox", "Trigger all 15 min"], "tags": ["add", "data", "data-integration", "dropbox", "google", "sheet"]}, {"category": "data-integration", "name": "Transfer Data From Postgres To Excel", "description": "This workflow extracts product data from a PostgreSQL database, converts it into a spreadsheet file, and saves the file to the local file system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2-transfer-data-from-postgres-to-excel/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2-transfer-data-from-postgres-to-excel/workflow.json", "nodeCount": 3, "nodeNames": ["Run Query", "Spreadsheet File", "Write Binary File"], "tags": ["data", "data-integration", "excel", "postgres", "transfer"]}, {"category": "data-integration", "name": "Generate Seo Keyword Search Volume Data Using Google Api", "description": "This workflow automates the process of retrieving keyword search volume data from the Google Ads API, enabling users to conduct data-driven SEO research and planning.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2494-generate-seo-keyword-search-volume-data-using-google-api/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2494-generate-seo-keyword-search-volume-data-using-google-api/workflow.json", "nodeCount": 12, "nodeNames": ["When clicking ‘Test workflow’", "Sticky Note1", "Split Out by KW", "Sticky Note2", "Sticky Note25", "<PERSON><PERSON> Note26", "Sticky Note19", "Sticky Note20", "Sticky Note21", "Set >=20 Keywords", "Connect to your own database.", "Get Search Data"], "tags": ["api", "data", "data-integration", "generate", "google", "keyword", "search", "seo", "using", "volume"]}, {"category": "data-integration", "name": "Sync Tasks Data Between Notion And Asana", "description": "This workflow synchronizes tasks between Asana and Notion, automatically creating or updating tasks based on changes in Asana.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1769-sync-tasks-data-between-notion-and-asana/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1769-sync-tasks-data-between-notion-and-asana/workflow.json", "nodeCount": 10, "nodeNames": ["Determine create/update", "Update task", "Create task", "Get tasks", "Find tasks", "Get unique tasks", "Determine", "Check required fields exist", "Update deadline", "On update"], "tags": ["asana", "between", "data", "data-integration", "notion", "sync", "tasks"]}, {"category": "data-integration", "name": "Send New Clockify Invoice To Notion Database", "description": "This workflow automatically creates a new Notion database page whenever a new invoice is generated in Clockify, allowing for seamless tracking and organization of invoices.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1834-send-new-clockify-invoice-to-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1834-send-new-clockify-invoice-to-notion-database/workflow.json", "nodeCount": 3, "nodeNames": ["On new invoice in Clockify", "Create database page", "Note"], "tags": ["clockify", "data-integration", "database", "invoice", "new", "notion", "send"]}, {"category": "data-integration", "name": "Create Zoom Meeting Link From Google Calendar Invite", "description": "This workflow automatically schedules Zoom meetings based on upcoming calendar events, ensuring efficient coordination and productivity.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1340-create-zoom-meeting-link-from-google-calendar-invite/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1340-create-zoom-meeting-link-from-google-calendar-invite/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Zoom", "Date & Time", "Google Calendar", "IF Zoom meeting", "Cron Once a Day"], "tags": ["calendar", "create", "data-integration", "google", "invite", "link", "meeting", "zoom"]}, {"category": "data-integration", "name": "Save N8N Cloud Invoices Received In Gmail In Google Drive", "description": "This workflow automatically downloads and saves Paddle invoice PDFs to a specified Google Drive folder, helping users efficiently manage their invoices.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2393-save-n8n-cloud-invoices-received-in-gmail-in-google-drive/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2393-save-n8n-cloud-invoices-received-in-gmail-in-google-drive/workflow.json", "nodeCount": 13, "nodeNames": ["<PERSON><PERSON>", "Only n8n Paddle invoice mails", "Split Out", "Only keep invoice link", "Do nothing on other emails", "<PERSON><PERSON>", "Setup", "Download Invoice PDF from URL", "Rename file", "Move to the correct folder", "Upload PDF to Drive", "Sticky Note1", "Extract \"a-tags\" from email"], "tags": ["cloud", "data-integration", "drive", "gmail", "google", "invoices", "n8n", "received", "save"]}, {"category": "data-integration", "name": "Database Alerts With Notion And Signl4", "description": "This workflow automatically monitors and updates the status of alerts in a Notion database, sending notifications to SIGNL4 for new alerts and resolving closed alerts.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1122-database-alerts-with-notion-and-signl4/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1122-database-alerts-with-notion-and-signl4/workflow.json", "nodeCount": 13, "nodeNames": ["Function", "<PERSON><PERSON> Trigger", "Webhook", "Function", "Notion Update", "Interval", "SIGNL4 Resolve", "SIGNL4 Alert", "Notion Update Read", "Notion Read Open", "Notion Read New", "Notion Update Final", "SIGNL4 Alert 2"], "tags": ["alerts", "data-integration", "database", "notion", "signl4"]}, {"category": "data-integration", "name": "Export Json File To Google Sheets", "description": "This workflow reads a JSON file, moves the binary data, and appends the data to a Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1736-export-json-file-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1736-export-json-file-to-google-sheets/workflow.json", "nodeCount": 3, "nodeNames": ["Google Sheets1", "read json file", "move binary data 2"], "tags": ["data-integration", "export", "file", "google", "json", "sheets"]}, {"category": "data-integration", "name": "Write All Linear Tickets To Google Sheets", "description": "This workflow automatically retrieves all tickets from a specific team in a project management tool (Linear), applies custom fields, and writes the tickets to a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2148-write-all-linear-tickets-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2148-write-all-linear-tickets-to-google-sheets/workflow.json", "nodeCount": 14, "nodeNames": ["Every day at 06:00", "Get all your team's tickets", "Sticky Note1", "if has next page", "Get end cursor", "Get next page", "<PERSON><PERSON>", "Split out the tickets", "Sticky Note2", "Set custom fields", "Sticky Note3", "Sticky Note4", "Write tickets to Sheets", "Flatten object to have simple fields to filter by"], "tags": ["all", "data-integration", "google", "linear", "sheets", "tickets", "write"]}, {"category": "data-integration", "name": "Send Google Analytics Data To A I To Analyze Then Save Results In Baserow", "description": "This workflow analyzes Google Analytics data, compares it to the previous week, and sends the insights to an AI model to generate SEO recommendations, which are then saved to a Baserow database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2517-send-google-analytics-data-to-a-i-to-analyze-then-save-results-in-baserow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2517-send-google-analytics-data-to-a-i-to-analyze-then-save-results-in-baserow/workflow.json", "nodeCount": 22, "nodeNames": ["Schedule Trigger", "When clicking ‘Test workflow’", "<PERSON><PERSON>", "Sticky Note1", "Get Page Engagement Stats for this week", "Get Page Engagement Stats for prior week", "Parse data from Google Analytics", "Parse GA data", "Get Google Search Results for this week", "Get Google Search Results for last week", "Parse Google Analytics Data", "Parse Google Analytics Data1", "Get Country views data for this week", "Get Country views data for last week", "Parse Google analytics data", "Parse Google analytics data1", "Send page data to A.I.", "Send page Search data to A.I.", "Send country view data to A.I.", "Save A.I. output to Baserow", "Sticky Note2", "Sticky Note3"], "tags": ["analytics", "analyze", "baserow", "data", "data-integration", "google", "i", "results", "save", "send", "then"]}, {"category": "data-integration", "name": "Send Specific Pdf Attachments From Gmail To Google Drive Using Openai", "description": "This workflow automatically processes email attachments, analyzes their content using OpenAI, and uploads relevant PDFs to a specified Google Drive folder.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1897-send-specific-pdf-attachments-from-gmail-to-google-drive-using-openai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1897-send-specific-pdf-attachments-from-gmail-to-google-drive-using-openai/workflow.json", "nodeCount": 18, "nodeNames": ["Read PDF", "<PERSON><PERSON>", "Configure", "Is PDF", "Not a PDF", "Is matched", "This is a matched PDF", "This is not a matched PDF", "Iterate over email attachments", "OpenAI matches PDF textual content", "<PERSON><PERSON>", "Upload file to folder", "On email received", "Note5", "Ignore large PDFs", "Is text within token limit?", "Has attachments?", "There are no attachments"], "tags": ["attachments", "data-integration", "drive", "gmail", "google", "openai", "pdf", "send", "specific", "using"]}, {"category": "data-integration", "name": "Load Data Into Spreadsheet Or Database", "description": "This workflow allows users to create a new record in a destination system (e.g., Google Sheet, Airtable, or database) by manually triggering the workflow and using mock data from a CRM system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/980-load-data-into-spreadsheet-or-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/980-load-data-into-spreadsheet-or-database/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Set", "Mock data (CRM Contacts)", "Replace me"], "tags": ["data", "data-integration", "database", "load", "or", "spreadsheet"]}, {"category": "data-integration", "name": "Update Time Tracking Projects Based On Syncro Status Changes", "description": "This workflow automatically manages Clockify projects based on incoming webhook data, archiving resolved tickets and updating unresolved tickets.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1492-update-time-tracking-projects-based-on-syncro-status-changes/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1492-update-time-tracking-projects-based-on-syncro-status-changes/workflow.json", "nodeCount": 6, "nodeNames": ["Webhook", "Clockify", "HTTP Request", "IF1", "Clockify1", "HTTP Request1"], "tags": ["based", "changes", "data-integration", "projects", "status", "syncro", "time", "tracking", "update"]}, {"category": "data-integration", "name": "Sync Your Github Issues To Your Notion Database", "description": "This workflow automatically manages GitHub issues by creating, updating, deleting, closing, and reopening Notion database pages based on the actions taken on the issues.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1804-sync-your-github-issues-to-your-notion-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1804-sync-your-github-issues-to-your-notion-database/workflow.json", "nodeCount": 11, "nodeNames": ["Create database page", "Note", "Find database page", "Switch", "IF", "Edit issue", "Delete issue", "Create custom Notion filters", "Close issue", "Reopen issue", "Trigger on issues"], "tags": ["data-integration", "database", "github", "issues", "notion", "sync"]}, {"category": "data-integration", "name": "Google Sheets To Dropbox", "description": "This workflow automatically reads data from a Google Sheet, converts it to an Excel file, and uploads the file to Dropbox on a 15-minute interval, providing a convenient way to regularly backup and share important spreadsheet data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/google-sheets-to-dropbox/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/google-sheets-to-dropbox/workflow.json", "nodeCount": 4, "nodeNames": ["Read Sheet", "Convert to XLS", "Upload Dropbox", "Trigger all 15 min"], "tags": ["data-integration", "dropbox", "google", "sheets"]}, {"category": "data-integration", "name": "Identify New Google Sheets Rows", "description": "This workflow automatically processes new rows in a Google Sheet, marking them as processed and performing additional actions as needed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1754-identify-new-google-sheets-rows/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1754-identify-new-google-sheets-rows/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Is new?", "Do something here", "<PERSON> as processed", "Read sheet", "Set processed value", "Run every 5 minutes"], "tags": ["data-integration", "google", "identify", "new", "rows", "sheets"]}, {"category": "data-integration", "name": "Receive Google Sheet Data Via Rest Api", "description": "This workflow allows users to capture and store data from a webhook in a Google Sheets spreadsheet, providing a simple way to collect and organize information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/226-receive-google-sheet-data-via-rest-api/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/226-receive-google-sheet-data-via-rest-api/workflow.json", "nodeCount": 2, "nodeNames": ["Google Sheets", "Webhook"], "tags": ["api", "data", "data-integration", "google", "receive", "rest", "sheet", "via"]}, {"category": "data-integration", "name": "Exponential Backoff For Google Apis", "description": "This workflow implements an exponential backoff mechanism to handle rate limiting issues when interacting with the Google Sheets API, ensuring reliable and resilient data processing.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2556-exponential-backoff-for-google-apis/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2556-exponential-backoff-for-google-apis/workflow.json", "nodeCount": 8, "nodeNames": ["When clicking ‘Test workflow’", "Exponential Backoff", "Stop and Error", "Loop Over Items", "Google Sheets", "Wait", "Check Max Retries", "<PERSON><PERSON>"], "tags": ["apis", "backoff", "data-integration", "exponential", "google"]}, {"category": "data-integration", "name": "Save Telegram Daily Messages To Google Sheets", "description": "This workflow allows users to submit journal entries via Telegram, which are then automatically added to a Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1388-save-telegram-daily-messages-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1388-save-telegram-daily-messages-to-google-sheets/workflow.json", "nodeCount": 3, "nodeNames": ["Add entry to sheet", "Get journal reply", "Parse message"], "tags": ["daily", "data-integration", "google", "messages", "save", "sheets", "telegram"]}, {"category": "data-integration", "name": "Collects Images From Web Search Results And Send To Google Sheets", "description": "This workflow automates the process of extracting text from images, storing the extracted text and image metadata in a Google Sheet, providing a convenient way to organize and analyze visual data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1395-collects-images-from-web-search-results-and-send-to-google-sheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1395-collects-images-from-web-search-results-and-send-to-google-sheets/workflow.json", "nodeCount": 6, "nodeNames": ["AWS Rekognition", "HTTP Request", "HTTP Request1", "Set1", "Function1", "Google Sheets1"], "tags": ["collects", "data-integration", "google", "images", "results", "search", "send", "sheets", "web"]}, {"category": "data-integration", "name": "Google Spreadsheet To Html Variant With Js Function", "description": "This workflow retrieves data from a Google Sheet, generates an HTML table with the data, and responds to a webhook with the HTML content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1757-google-spreadsheet-to-html-variant-with-js-function/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1757-google-spreadsheet-to-html-variant-with-js-function/workflow.json", "nodeCount": 4, "nodeNames": ["Read from Google Sheets", "Respond to Webhook", "Build HTML", "Webhook"], "tags": ["data-integration", "function", "google", "html", "js", "spreadsheet", "variant"]}, {"category": "data-integration", "name": "Sync Google Sheets Data With Mysql", "description": "This workflow automatically synchronizes data between a Google Sheet and a MySQL database, updating the Google Sheet's \"DB Status\" column and triggering notifications for outdated records.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1964-sync-google-sheets-data-with-mysql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1964-sync-google-sheets-data-with-mysql/workflow.json", "nodeCount": 15, "nodeNames": ["When clicking \"Execute Workflow\"", "Compare Datasets", "Send Notifications", "Schedule Trigger", "Google Sheet Data", "SQL Get inquiries from Google", "Add MySQL records", "Rename GSheet variables", "No reply too long?", "DB Status assigned?", "Update GSheet status", "DB Status in sync?", "Sync MySQL data", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["data", "data-integration", "google", "mysql", "sheets", "sync"]}, {"category": "data-integration", "name": "Send Sms Alerts Based On Database Queries Twilio And Postgres", "description": "This workflow periodically checks a Postgres database for sensor readings above a certain threshold, sends a notification via Twilio, and updates the database to mark the notification as sent.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/357-send-sms-alerts-based-on-database-queries-twilio-and-postgres/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/357-send-sms-alerts-based-on-database-queries-twilio-and-postgres/workflow.json", "nodeCount": 5, "nodeNames": ["<PERSON><PERSON>", "Postgres", "<PERSON><PERSON><PERSON>", "Set", "Postgres1"], "tags": ["alerts", "based", "data-integration", "database", "postgres", "queries", "send", "sms", "twi<PERSON>"]}, {"category": "data-integration", "name": "Create Update And Get A Document In Google Cloud Firestore", "description": "This workflow creates a new document in a Google Cloud Firestore database, updates the document with additional data, and then retrieves the updated document.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/839-create-update-and-get-a-document-in-google-cloud-firestore/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/839-create-update-and-get-a-document-in-google-cloud-firestore/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Google Cloud Firestore", "Set", "Set1", "Google Cloud Firestore1", "Google Cloud Firestore2"], "tags": ["cloud", "create", "data-integration", "document", "firestore", "get", "google", "update"]}, {"category": "data-integration", "name": "Automate Google Analytics Reporting", "description": "This workflow aggregates Google Analytics data, compares this week's performance to the prior week, and emails a detailed report.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2549-automate-google-analytics-reporting/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/2549-automate-google-analytics-reporting/workflow.json", "nodeCount": 23, "nodeNames": ["When clicking ‘Test workflow’", "<PERSON><PERSON>", "Sticky Note1", "Get Page Engagement Stats for this week", "Get Page Engagement Stats for prior week", "Get Google Search Results for this week", "Get Country views data for this week", "Sticky Note4", "Aggregate Data", "Get Google Search Results for prior week", "Get Country views data for prior week", "Parse - Get Page Engagement This Week", "Parse - Get Page Engagement Prior Week", "Parse - Get Google Search This Week", "Parse - Get Google Search Prior Week", "Parse - Country Views This Week", "Parse - Country Views Prior Week", "Set urlStrings", "Format Data", "Input All", "Sticky Note5", "Email the Report", "Schedule Trigger"], "tags": ["analytics", "automate", "data-integration", "google", "reporting"]}, {"category": "data-integration", "name": "Send Alert When Data Is Created In App Database", "description": "This workflow automatically triggers a Slack notification whenever a new urgent bug is reported in the \"Product & Design\" team, allowing for quick response and resolution.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1932-send-alert-when-data-is-created-in-app-database/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-integration/1932-send-alert-when-data-is-created-in-app-database/workflow.json", "nodeCount": 10, "nodeNames": ["<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note5", "<PERSON><PERSON>", "When clicking \"Execute Workflow\"", "Code", "Filter", "Set", "<PERSON><PERSON>ck"], "tags": ["alert", "app", "created", "data", "data-integration", "database", "send", "when"]}, {"category": "api-webhooks", "name": "Visual Regression Testing With Apify And Ai Vision Model", "description": "This workflow automates a visual regression testing process for websites, using AI-powered image comparison to detect and report changes.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2419-visual-regression-testing-with-apify-and-ai-vision-model/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2419-visual-regression-testing-with-apify-and-ai-vision-model/workflow.json", "nodeCount": 34, "nodeNames": ["Base Image", "Google Gemini Chat Model", "Structured Output Parser", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Loop Over Items", "Wait", "Download Screenshot", "Upload to Drive", "Update Base Image", "<PERSON><PERSON>", "Schedule Trigger", "Get URLs with Missing Base Images", "Run Webpage Screenshot", "Run Webpage Screenshot1", "Has Changes", "Combine Row and Result", "Wait1", "Aggregate", "Create Report", "When clicking ‘Test workflow’", "Get Webpages List", "For Each Webpage...", "Sticky Note4", "Sticky Note5", "Sticky Note6", "Sticky Note7", "Download New Screenshot", "Combine Screenshots", "Sticky Note8", "Sticky Note9", "Visual Regression Agent"], "tags": ["ai", "api-webhooks", "apify", "model", "regression", "testing", "vision", "visual"]}, {"category": "api-webhooks", "name": "Monitor Multiple Github Repos Via Webhook", "description": "This workflow allows you to monitor multiple GitHub repositories for changes and receive notifications when new commits are pushed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2435-monitor-multiple-github-repos-via-webhook/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2435-monitor-multiple-github-repos-via-webhook/workflow.json", "nodeCount": 19, "nodeNames": ["When clicking ‘Test workflow’", "<PERSON><PERSON>", "Split Out", "Register Github Webhook", "Split Out1", "Delete Github Webhook", "Sticky Note4", "Sticky Note5", "Sticky Note6", "Fields", "Notify Slack", "Telegram", "Repos to Monitor", "Sticky Note7", "Sticky Note8", "Webhook Trigger", "Repos to Monitor1", "Get Existing Hook", "Hook URL"], "tags": ["api-webhooks", "github", "monitor", "multiple", "repos", "via", "webhook"]}, {"category": "api-webhooks", "name": "Pulling Data From Services That N8N Doesnt Have A Pre Built Integration For", "description": "This workflow demonstrates various use cases of the HTTP Request node, including data scraping, splitting response data into items, and handling pagination.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1748-pulling-data-from-services-that-n8n-doesnt-have-a-pre-built-integration-for/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1748-pulling-data-from-services-that-n8n-doesnt-have-a-pre-built-integration-for/workflow.json", "nodeCount": 14, "nodeNames": ["On clicking 'execute'", "Note", "Note2", "Set", "Note6", "Note1", "Item Lists - Create Items from Body", "HTML Extract - Extract Article Title", "Item Lists - Fetch Body", "If - Are we finished?", "Set - Increment Page", "HTTP Request - Get Mock Albums", "HTTP Request - Get Wikipedia Page", "HTTP Request - Get my Stars"], "tags": ["api-webhooks", "built", "data", "doesnt", "have", "integration", "n8n", "pre", "pulling", "services", "that"]}, {"category": "api-webhooks", "name": "Creating An Api Endpoint", "description": "This workflow creates a simple API endpoint that generates a Google search URL based on the first and last name provided in the webhook request.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1750-creating-an-api-endpoint/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1750-creating-an-api-endpoint/workflow.json", "nodeCount": 5, "nodeNames": ["Webhook", "Note1", "Respond to Webhook", "Create URL string", "Note3"], "tags": ["an", "api", "api-webhooks", "creating", "endpoint"]}, {"category": "api-webhooks", "name": "Manage Adobe Acrobat E Signatures With Webhooks", "description": "This workflow processes incoming webhook requests, enriches the data, and responds with a custom header and additional data stored in the workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1588-manage-adobe-acrobat-e-signatures-with-webhooks/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1588-manage-adobe-acrobat-e-signatures-with-webhooks/workflow.json", "nodeCount": 5, "nodeNames": ["Function", "POST", "reg-GET", "webhook-response", "SetWebhookData"], "tags": ["acrobat", "adobe", "api-webhooks", "e", "manage", "signatures", "webhooks"]}, {"category": "api-webhooks", "name": "Send Github Notifications To Discord Webhook", "description": "This workflow monitors GitHub notifications and sends a summary to a Discord channel when new notifications are available.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/471-send-github-notifications-to-discord-webhook/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/471-send-github-notifications-to-discord-webhook/workflow.json", "nodeCount": 6, "nodeNames": ["@Get Issue", "<PERSON><PERSON>", "Discord", "Function", "IF", "@Get Date 1 min ago"], "tags": ["api-webhooks", "discord", "github", "notifications", "send", "webhook"]}, {"category": "api-webhooks", "name": "Low Code Api For Flutterflow Apps", "description": "This workflow sets up a low-code API endpoint that can be used to fetch data from a customer datastore and return it to a Flutterflow app.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2274-low-code-api-for-flutterflow-apps/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2274-low-code-api-for-flutterflow-apps/workflow.json", "nodeCount": 9, "nodeNames": ["Customer Datastore (n8n training)", "insert into variable", "Aggregate variable", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "On new flutterflow call", "Respond to flutterflow", "Sticky Note3"], "tags": ["api", "api-webhooks", "apps", "code", "flutterflow", "low"]}, {"category": "api-webhooks", "name": "Create Update And Get An Entry In Strapi", "description": "This workflow automates the process of creating and updating content in a Strapi headless CMS using the n8n workflow automation tool.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/779-create-update-and-get-an-entry-in-strapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/779-create-update-and-get-an-entry-in-strapi/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON><PERSON>", "Set", "Strapi1", "Set1", "Strapi2"], "tags": ["an", "api-webhooks", "create", "entry", "get", "strapi", "update"]}, {"category": "api-webhooks", "name": "Webhook Returning Xml", "description": "This workflow creates a webhook that accepts JSON data, converts it to XML, and responds with the XML data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/119-webhook-returning-xml/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/119-webhook-returning-xml/workflow.json", "nodeCount": 4, "nodeNames": ["XML", "Set", "Respond to Webhook", "Webhook"], "tags": ["api-webhooks", "returning", "webhook", "xml"]}, {"category": "api-webhooks", "name": "Api Queries Data From Graphql", "description": "This workflow retrieves country information based on a country code provided through a webhook, and then formats the response into a user-friendly message.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/216-api-queries-data-from-graphql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/216-api-queries-data-from-graphql/workflow.json", "nodeCount": 4, "nodeNames": ["GraphQL", "Function", "Set", "Webhook"], "tags": ["api", "api-webhooks", "data", "graphql", "queries"]}, {"category": "api-webhooks", "name": "Manipulate Pdf With Adobe Developer Api", "description": "This workflow is an Adobe API wrapper that allows users to upload a PDF file, process it using various Adobe PDF services, and download the resulting file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2424-manipulate-pdf-with-adobe-developer-api/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2424-manipulate-pdf-with-adobe-developer-api/workflow.json", "nodeCount": 20, "nodeNames": ["When clicking ‘Test workflow’", "Create Asset", "Execute Workflow Trigger", "<PERSON><PERSON>", "Sticky Note1", "Adobe API Query", "Load a test pdf file", "Query + File", "Query + File + Asset information", "Process Query", "Wait 5 second", "Try to download the result", "Switch", "Forward response to origin workflow", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Authenticartion (get token)", "Upload PDF File (asset)"], "tags": ["adobe", "api", "api-webhooks", "developer", "manipulate", "pdf"]}, {"category": "api-webhooks", "name": "Send Rss Feed Data To Webhook", "description": "This workflow periodically checks an RSS feed, filters the articles based on specific keywords, and stores the unique articles in a MongoDB database, while also sending notifications about the new articles.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/159-send-rss-feed-data-to-webhook/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/159-send-rss-feed-data-to-webhook/workflow.json", "nodeCount": 18, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "RSS Feed Read", "SplitInBatches1", "IF", "End1", "IF1", "<PERSON><PERSON>", "Merge1", "IF realtors or real estate", "IF restaurant(s)", "SplitInBatches", "MongoDB: Find Article", "MongoDB: Find Article1", "MongoDB: Insert", "MongoDB: Insert1", "Webhook", "Webhook1"], "tags": ["api-webhooks", "data", "feed", "rss", "send", "webhook"]}, {"category": "api-webhooks", "name": "<PERSON><PERSON> Extractor", "description": "This workflow automates the process of crawling and extracting API documentation from the web, analyzing the content to identify API operations, and generating a custom JSON schema for each service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2658-api-schema-extractor/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/2658-api-schema-extractor/workflow.json", "nodeCount": 88, "nodeNames": ["When clicking ‘Test workflow’", "Web Search For API Schema", "Scrape Webpage Contents", "Results to List", "Recursive Character Text Splitter1", "Content Chunking @ 50k Chars", "Split Out Chunks", "Default Data Loader", "Set Embedding Variables", "Execute Workflow Trigger", "Execution Data", "EventRouter", "Google Gemini Chat Model", "Successful Runs", "For Each Document...", "Embeddings Google Gemini", "Has API Documentation?", "Store Document Embeddings", "Embeddings Google Gemini1", "Google Gemini Chat Model1", "Extract API Operations", "Search in Relevant Docs", "Wait", "Re<PERSON><PERSON>", "Filter Results", "Research", "Has Results?", "Response Empty", "Response OK", "Combine Docs", "Template to List", "Query Templates", "Google Gemini Chat Model2", "For Each Template...", "Query & Docs", "Identify Service Products", "Extract API Templates", "Embeddings Google Gemini2", "Search in Relevant Docs1", "Combine Docs1", "Query & Docs1", "For Each Template...1", "Merge Lists", "Remove Duplicates", "Append Row", "Response OK1", "Has Operations?", "Response Empty1", "Research Pending", "Research Result", "Research Error", "Extract Pending", "Research Event", "Extract Event", "Extract", "Extract Result", "Extract Error", "Get API Operations", "Contruct JSON Sc<PERSON>a", "Upload to Drive", "Set Upload Fields", "Response OK2", "Generate Event", "Generate Pending", "Generate", "Generate Error", "Generate Result", "Get All Extract", "Get All Research", "For Each Research...", "For Each Extract...", "Wait1", "All Research Done?", "All Extract Done?", "Get All Generate", "All Generate Done?", "For Each Generate...", "Wait2", "Has Results?1", "Response Scrape Error", "Has Results?3", "Response No API Docs", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5"], "tags": ["api", "api-webhooks", "extractor", "schema"]}, {"category": "api-webhooks", "name": "Webhooks With Mattermost", "description": "This workflow generates a random cocktail recipe and posts it to a Mattermost channel when a webhook is triggered.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/351-webhooks-with-mattermost/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/351-webhooks-with-mattermost/workflow.json", "nodeCount": 3, "nodeNames": ["Webhook", "HTTP Request", "Mattermost"], "tags": ["api-webhooks", "mattermost", "webhooks"]}, {"category": "api-webhooks", "name": "Get The Last Five Spacex Launches From The Spacex Land Api Using Graphql", "description": "This workflow retrieves the details of the 5 most recent SpaceX launches, providing valuable information about the missions, launch sites, rockets, and associated ships.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/558-get-the-last-five-spacex-launches-from-the-spacex-land-api-using-graphql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/558-get-the-last-five-spacex-launches-from-the-spacex-land-api-using-graphql/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "GraphQL"], "tags": ["api", "api-webhooks", "five", "get", "graphql", "land", "last", "launches", "spacex", "using"]}, {"category": "api-webhooks", "name": "Automate Testimonials In Strapi With N8N", "description": "This workflow automatically collects and analyzes tweets related to Strapi and n8n.io, filters out retweets and old tweets, and stores the relevant tweets along with their sentiment analysis in a Strapi content management system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1535-automate-testimonials-in-strapi-with-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1535-automate-testimonials-in-strapi-with-n8n/workflow.json", "nodeCount": 14, "nodeNames": ["Simplify Result", "Store in Strapi", "Every 30 Minutes", "Is Retweet or Old?", "Search Tweets", "Webhook", "Simplify Webhook Result", "Analyze Form Submission", "Analyze Tweet", "Merge Form Sentiment with Source", "<PERSON><PERSON> Tweet Sentiment with Source", "Positive Form Sentiment?", "Store Form Submission in Strapi", "Positive Tweet Sentiment?"], "tags": ["api-webhooks", "automate", "n8n", "strapi", "testimonials"]}, {"category": "api-webhooks", "name": "Handle Verification For Twitter Webhook", "description": "This workflow generates a secure response token by applying a SHA256 HMAC encryption to a webhook input, providing a secure way to authenticate and validate incoming requests.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1440-handle-verification-for-twitter-webhook/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1440-handle-verification-for-twitter-webhook/workflow.json", "nodeCount": 3, "nodeNames": ["Webhook", "Crypto", "Set"], "tags": ["api-webhooks", "handle", "twitter", "verification", "webhook"]}, {"category": "api-webhooks", "name": "Store Data Received From Webhook In Json", "description": "This workflow retrieves a random cocktail recipe from an API, converts the response to binary data, and saves it as a JSON file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/652-store-data-received-from-webhook-in-json/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/652-store-data-received-from-webhook-in-json/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "HTTP Request", "Move Binary Data", "Write Binary File"], "tags": ["api-webhooks", "data", "json", "received", "store", "webhook"]}, {"category": "api-webhooks", "name": "Use Redis To Rate Limit Your Low Code Api", "description": "This workflow manages API usage limits by tracking API key usage in Redis and sending a message when limits are exceeded.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1236-use-redis-to-rate-limit-your-low-code-api/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/1236-use-redis-to-rate-limit-your-low-code-api/workflow.json", "nodeCount": 11, "nodeNames": ["Airtable", "Redis", "Redis1", "Set1", "Webhook1", "Function", "Set", "Set2", "Set3", "Per hour", "Per minute"], "tags": ["api", "api-webhooks", "code", "limit", "low", "rate", "redis", "use"]}, {"category": "api-webhooks", "name": "Create An Event In Posthog When A Request Is Made To A Webhook Url", "description": "This workflow captures and tracks user events from a web application using PostHog, a product analytics platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/968-create-an-event-in-posthog-when-a-request-is-made-to-a-webhook-url/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/api-webhooks/968-create-an-event-in-posthog-when-a-request-is-made-to-a-webhook-url/workflow.json", "nodeCount": 2, "nodeNames": ["PostHog", "Webhook"], "tags": ["an", "api-webhooks", "create", "event", "made", "posthog", "request", "url", "webhook", "when"]}, {"category": "document-processing", "name": "Compress Binary Files To Zip Format", "description": "This workflow automates the process of downloading images, compressing them into a ZIP file, and uploading the compressed file to Dropbox, providing a convenient way to backup and share visual assets.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/908-compress-binary-files-to-zip-format/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/908-compress-binary-files-to-zip-format/workflow.json", "nodeCount": 5, "nodeNames": ["Dropbox", "Compression", "HTTP Request1", "HTTP Request", "On clicking 'execute'"], "tags": ["binary", "compress", "document-processing", "files", "format", "zip"]}, {"category": "document-processing", "name": "Create A Document In Outline For Each New Gitlab Release", "description": "This workflow automatically creates a new document in Outline (a knowledge base tool) whenever a new release is pushed to a GitLab repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1375-create-a-document-in-outline-for-each-new-gitlab-release/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1375-create-a-document-in-outline-for-each-new-gitlab-release/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON><PERSON><PERSON>", "HTTP Request", "IF"], "tags": ["create", "document", "document-processing", "each", "gitlab", "new", "outline", "release"]}, {"category": "document-processing", "name": "Download And Compress Folder From S3 To Zip File", "description": "This workflow downloads all files from a specific folder in an AWS S3 bucket, compresses them into a ZIP file, and makes the compressed file available for download.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2451-download-and-compress-folder-from-s3-to-zip-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2451-download-and-compress-folder-from-s3-to-zip-file/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking ‘Test workflow’", "List ALL Files*", "Download ALL Files from Folder*", "All into one Item (include Binary)", "Note3", "Compress all of them to a ZIP"], "tags": ["compress", "document-processing", "download", "file", "folder", "s3", "zip"]}, {"category": "document-processing", "name": "Push And Update Files In Github", "description": "This workflow automates the process of updating a file in a GitHub repository and adding a new file, then pushing the changes back to the remote repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1942-push-and-update-files-in-github/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1942-push-and-update-files-in-github/workflow.json", "nodeCount": 13, "nodeNames": ["When clicking \"Execute Workflow\"", "<PERSON><PERSON>", "GitHub push edited file", "Update README and add new file", "Add files", "Commit", "<PERSON><PERSON>", "config", "<PERSON><PERSON>", "Sticky Note1", "GitHub get file", "Decode file", "Sticky Note2"], "tags": ["document-processing", "files", "github", "push", "update"]}, {"category": "document-processing", "name": "Create 2 Xml Files With And Without Xml Attributes", "description": "This workflow retrieves a set of random products from a MySQL database, formats the product data into XML files, and saves them to the local file system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1949-create-2-xml-files-with-and-without-xml-attributes/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1949-create-2-xml-files-with-and-without-xml-attributes/workflow.json", "nodeCount": 13, "nodeNames": ["When clicking \"Execute Workflow\"", "Show 16 random products", "Define file structure", "Concatenate Items", "Convert to XML", "Move Binary Data", "Define file structure1", "Concatenate Items1", "Convert to XML1", "Move Binary Data1", "<PERSON><PERSON>", "Sticky Note1", "Write Binary File"], "tags": ["2", "attributes", "create", "document-processing", "files", "without", "xml"]}, {"category": "document-processing", "name": "Upsert Huge Documents In A Vector Store With Supabase And Notion", "description": "This workflow automatically ingests and embeds content from a Notion knowledge base, stores the embeddings in a Supabase vector store, and provides a conversational interface to query the knowledge base using natural language.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2568-upsert-huge-documents-in-a-vector-store-with-supabase-and-notion/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2568-upsert-huge-documents-in-a-vector-store-with-supabase-and-notion/workflow.json", "nodeCount": 34, "nodeNames": ["Embeddings OpenAI", "Token Splitter", "Loop Over Items", "Question and Answer Chain", "Vector Store Retriever", "OpenAI Chat Model", "When chat message received", "Schedule Trigger", "<PERSON><PERSON>", "Limit", "Limit1", "Delete old embeddings if exist", "Get page blocks", "Default Data Loader", "Sticky Note1", "Input Reference", "<PERSON><PERSON> Trigger", "Get updated pages", "Sticky Note23", "Sticky Note24", "Sticky Note25", "<PERSON><PERSON> Note26", "Sticky Note27", "Sticky Note28", "Supabase Vector Store1", "Sticky Note30", "Sticky Note31", "Supabase Vector Store", "Sticky Note32", "Sticky Note29", "Sticky Note33", "Sticky Note34", "Sticky Note35", "Concatenate to single string"], "tags": ["document-processing", "documents", "huge", "notion", "store", "supabase", "upsert", "vector"]}, {"category": "document-processing", "name": "Send A File From S3 To Aws Textract", "description": "This workflow extracts text and data from an image file stored in an AWS S3 bucket using the AWS Textract service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1282-send-a-file-from-s3-to-aws-textract/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1282-send-a-file-from-s3-to-aws-textract/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "AWS Textract", "AWS S3"], "tags": ["aws", "document-processing", "file", "s3", "send", "textract"]}, {"category": "document-processing", "name": "Read Multiple Files From Disk", "description": "This workflow allows users to manually trigger the reading and processing of binary files (e.g., images) located in a specific directory.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/578-read-multiple-files-from-disk/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/578-read-multiple-files-from-disk/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Read Binary Files"], "tags": ["disk", "document-processing", "files", "multiple", "read"]}, {"category": "document-processing", "name": "Export Sql Table Into Csv File", "description": "This workflow extracts data from a Microsoft SQL Server table and saves it as a CSV file, enabling users to easily share or work with the data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1914-export-sql-table-into-csv-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1914-export-sql-table-into-csv-file/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Execute Workflow\"", "Sticky Note1", "TableName", "LoadMSSQLData", "SaveCSV"], "tags": ["csv", "document-processing", "export", "file", "sql", "table"]}, {"category": "document-processing", "name": "Simple File Based Key Value Store Writekey", "description": "This workflow reads binary files, modifies their content, and writes the modified files to a specified location.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1407-simple-file-based-key-value-store-writekey/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1407-simple-file-based-key-value-store-writekey/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "Write Binary File", "Config", "Read Binary Files", "SetKeyValue", "BinaryToJSON", "JSONToBinary", "SplitInBatches", "Repeat", "Done"], "tags": ["based", "document-processing", "file", "key", "simple", "store", "value", "writekey"]}, {"category": "document-processing", "name": "Ai Agent To Chat With Files In Supabase Storage", "description": "This workflow automates the process of fetching, processing, and storing text and PDF files from a Supabase storage, enabling an AI-powered chatbot to query and retrieve context-based information from the uploaded files.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2621-ai-agent-to-chat-with-files-in-supabase-storage/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/2621-ai-agent-to-chat-with-files-in-supabase-storage/workflow.json", "nodeCount": 33, "nodeNames": ["Get All files", "Default Data Loader", "Recursive Character Text Splitter", "Extract Document PDF", "Embeddings OpenAI", "Create File record2", "If", "Get All Files", "Download", "Loop Over Items", "When clicking ‘Test workflow’", "Aggregate", "When chat message received", "OpenAI Chat Model1", "Embeddings OpenAI2", "OpenAI Chat Model2", "Vector Store Tool1", "Switch", "Insert into Supabase Vectorstore", "<PERSON><PERSON>", "AI Agent", "Supabase Vector Store", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Sticky Note9", "Sticky Note7", "Sticky Note6", "Sticky Note8", "Sticky Note10"], "tags": ["agent", "ai", "chat", "document-processing", "files", "storage", "supabase"]}, {"category": "document-processing", "name": "Download A File And Upload It To An Ftp Server", "description": "This workflow automates the process of downloading an image from a URL, uploading it to an FTP server, and then listing the contents of the upload directory on the FTP server.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/663-download-a-file-and-upload-it-to-an-ftp-server/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/663-download-a-file-and-upload-it-to-an-ftp-server/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "FTP", "FTP1", "HTTP Request"], "tags": ["an", "document-processing", "download", "file", "ftp", "it", "server", "upload"]}, {"category": "document-processing", "name": "Simple File Based Key Value Store Getkey", "description": "This workflow allows users to read a binary file, convert it to JSON, and return a specific value from the JSON data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1408-simple-file-based-key-value-store-getkey/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1408-simple-file-based-key-value-store-getkey/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Config", "Read Binary File", "BinaryToJSON", "ReturnValue"], "tags": ["based", "document-processing", "file", "getkey", "key", "simple", "store", "value"]}, {"category": "document-processing", "name": "Respond With File Download To Incoming Http Request", "description": "This workflow allows users to download a PDF file by sending a GET request to a specific endpoint.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1920-respond-with-file-download-to-incoming-http-request/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1920-respond-with-file-download-to-incoming-http-request/workflow.json", "nodeCount": 3, "nodeNames": ["On GET request", "Fetch binary file", "Respond with attachment"], "tags": ["document-processing", "download", "file", "http", "incoming", "request", "respond"]}, {"category": "document-processing", "name": "Monitor A File For Changes And Send An Alert", "description": "This workflow automatically monitors and resolves alerts by reading and writing binary data, integrating with the SIGNL4 incident management platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/967-monitor-a-file-for-changes-and-send-an-alert/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/967-monitor-a-file-for-changes-and-send-an-alert/workflow.json", "nodeCount": 9, "nodeNames": ["<PERSON><PERSON>", "Write Binary File", "Read Binary File", "Binary to JSON", "JSON to Binary", "<PERSON> as <PERSON>", "IF", "SIGNL4 Resolve", "SIGNL4 Alert"], "tags": ["alert", "an", "changes", "document-processing", "file", "monitor", "send"]}, {"category": "document-processing", "name": "Transfer Json Data To Csv File", "description": "This workflow reads a JSON file, moves the binary data, and appends the data to a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1791-transfer-json-data-to-csv-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1791-transfer-json-data-to-csv-file/workflow.json", "nodeCount": 3, "nodeNames": ["Google Sheets1", "read json file", "move binary data 2"], "tags": ["csv", "data", "document-processing", "file", "json", "transfer"]}, {"category": "document-processing", "name": "Write A File To The Host Machine", "description": "This workflow allows users to download an image file from a specified URL and save it to a local file on their desktop.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/590-write-a-file-to-the-host-machine/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/590-write-a-file-to-the-host-machine/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "HTTP Request", "Write Binary File"], "tags": ["document-processing", "file", "host", "machine", "write"]}, {"category": "document-processing", "name": "Export Csv File To Json", "description": "This workflow converts a CSV file into a JSON file, enabling easy data transformation and storage.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1731-export-csv-file-to-json/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1731-export-csv-file-to-json/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Read Binary File", "Spreadsheet File1", "Move Binary Data", "Write Binary File"], "tags": ["csv", "document-processing", "export", "file", "json"]}, {"category": "document-processing", "name": "Execute Multiple Command Lines Based On Text File Inputs", "description": "This workflow reads a binary file, processes its contents, and writes the file names to a log file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/913-execute-multiple-command-lines-based-on-text-file-inputs/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/913-execute-multiple-command-lines-based-on-text-file-inputs/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Read Binary File", "Move Binary Data", "Function", "Execute Command", "IF", "NoOp"], "tags": ["based", "command", "document-processing", "execute", "file", "inputs", "lines", "multiple", "text"]}, {"category": "document-processing", "name": "Read A File From Disk", "description": "This workflow allows users to read and process binary files, such as images, on demand.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/577-read-a-file-from-disk/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/577-read-a-file-from-disk/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Read Binary File"], "tags": ["disk", "document-processing", "file", "read"]}, {"category": "document-processing", "name": "Transcribe Audio Files From Cloud Storage", "description": "This workflow automatically transcribes audio files uploaded to a Google Drive folder, stores the transcripts in an AWS S3 bucket, and appends the transcription details to a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1394-transcribe-audio-files-from-cloud-storage/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1394-transcribe-audio-files-from-cloud-storage/workflow.json", "nodeCount": 8, "nodeNames": ["Google Sheets", "AWS Transcribe 2", "AWS Transcribe 1", "AWS S3 1", "AWS S3 2", "Set", "Google Drive Trigger1", "Wait"], "tags": ["audio", "cloud", "document-processing", "files", "storage", "transcribe"]}, {"category": "document-processing", "name": "Read A Spreadsheet File", "description": "This workflow allows users to manually trigger the extraction and processing of data from a spreadsheet file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/586-read-a-spreadsheet-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/586-read-a-spreadsheet-file/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Spreadsheet File", "Read Binary File"], "tags": ["document-processing", "file", "read", "spreadsheet"]}, {"category": "document-processing", "name": "Extract Text From A Pdf File", "description": "This workflow allows users to extract text and metadata from PDF files by first reading the binary file and then processing it through a PDF reader.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/585-extract-text-from-a-pdf-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/585-extract-text-from-a-pdf-file/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Read Binary File", "Read PDF"], "tags": ["document-processing", "extract", "file", "pdf", "text"]}, {"category": "document-processing", "name": "Create An Event File And Send It As An Email Attachment", "description": "This workflow allows users to create and send an iCalendar event invitation for an n8n community meetup via email.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1083-create-an-event-file-and-send-it-as-an-email-attachment/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1083-create-an-event-file-and-send-it-as-an-email-attachment/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "iCalendar", "Send Email"], "tags": ["an", "as", "attachment", "create", "document-processing", "email", "event", "file", "it", "send"]}, {"category": "document-processing", "name": "Prepare Csv Files With Gpt 4", "description": "This workflow generates a list of 10 random user profiles with fictional character names, saves them as CSV files, and stores them on the local disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1967-prepare-csv-files-with-gpt-4/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1967-prepare-csv-files-with-gpt-4/workflow.json", "nodeCount": 11, "nodeNames": ["When clicking \"Execute Workflow\"", "OpenAI", "Split In Batches", "<PERSON><PERSON>", "Parse JSON", "Make JSON Table", "Convert to CSV", "Save to Disk", "Strip UTF BOM bytes", "Create valid binary", "Sticky Note1"], "tags": ["4", "csv", "document-processing", "files", "gpt", "prepare"]}, {"category": "document-processing", "name": "Push Json Data Into An App Or To Spreadsheet File", "description": "This workflow fetches data from an API, maps it to a Google Sheets spreadsheet, and also exports it as a CSV file, providing a versatile way to integrate data from various sources.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1933-push-json-data-into-an-app-or-to-spreadsheet-file/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1933-push-json-data-into-an-app-or-to-spreadsheet-file/workflow.json", "nodeCount": 8, "nodeNames": ["HTTP Request", "Spreadsheet File", "Note", "Note1", "When clicking \"Execute Workflow\"", "<PERSON><PERSON>", "Google Sheets", "Set"], "tags": ["an", "app", "data", "document-processing", "file", "json", "or", "push", "spreadsheet"]}, {"category": "document-processing", "name": "Manage Files In S3", "description": "This workflow downloads an image from a URL, uploads it to an S3 bucket, and then retrieves a list of all files in the same S3 bucket.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/674-manage-files-in-s3/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/674-manage-files-in-s3/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "HTTP Request", "S3", "S"], "tags": ["document-processing", "files", "manage", "s3"]}, {"category": "document-processing", "name": "Import A Json File From Gmail Into A Spreadsheet", "description": "This workflow automatically retrieves a JSON file from Gmail, converts it to a CSV spreadsheet, and saves the file to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1734-import-a-json-file-from-gmail-into-a-spreadsheet/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/1734-import-a-json-file-from-gmail-into-a-spreadsheet/workflow.json", "nodeCount": 4, "nodeNames": ["Gmail", "write spreadsheet file", "move binary data", "Note6"], "tags": ["document-processing", "file", "gmail", "import", "json", "spreadsheet"]}, {"category": "document-processing", "name": "Get The Community Profile Of A Repository", "description": "This workflow allows users to retrieve information about the n8n GitHub repository with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/450-get-the-community-profile-of-a-repository/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/450-get-the-community-profile-of-a-repository/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON><PERSON>"], "tags": ["community", "document-processing", "get", "profile", "repository"]}, {"category": "document-processing", "name": "Insert A Document In Mongodb", "description": "This workflow allows users to manually trigger the insertion of a key-value pair into a MongoDB database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/503-insert-a-document-in-mongodb/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/document-processing/503-insert-a-document-in-mongodb/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Set", "MongoDB"], "tags": ["document", "document-processing", "insert", "mongodb"]}, {"category": "automation", "name": "Trigger A Build In Travis Ci When Code Changes Are Push To A Github Repo", "description": "This workflow automatically triggers a Travis CI build when a new pull request is opened or a push event occurs in the n8n repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1132-trigger-a-build-in-travis-ci-when-code-changes-are-push-to-a-github-repo/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1132-trigger-a-build-in-travis-ci-when-code-changes-are-push-to-a-github-repo/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON><PERSON>", "IF", "TravisCI", "NoOp"], "tags": ["are", "automation", "build", "changes", "ci", "code", "github", "push", "repo", "travis", "trigger", "when"]}, {"category": "automation", "name": "Create Update And Get Records In Quick Base", "description": "This workflow allows users to create, update, and retrieve records in a Quick Base application.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/805-create-update-and-get-records-in-quick-base/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/805-create-update-and-get-records-in-quick-base/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Quick Base", "Set", "Set1", "Quick Base1", "Quick Base2"], "tags": ["automation", "base", "create", "get", "quick", "records", "update"]}, {"category": "automation", "name": "Encrypt Some Data Using The Crypto Node", "description": "This workflow allows users to encrypt a simple text message using a built-in cryptography tool.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/574-encrypt-some-data-using-the-crypto-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/574-encrypt-some-data-using-the-crypto-node/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Crypto"], "tags": ["automation", "crypto", "data", "encrypt", "node", "some", "using"]}, {"category": "automation", "name": "Advanced Slackbot With N8N", "description": "This workflow handles Slack commands, validates the request, executes the appropriate workflow, and provides debugging information to the user.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2157-advanced-slackbot-with-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2157-advanced-slackbot-with-n8n/workflow.json", "nodeCount": 34, "nodeNames": ["Start thread", "send help", "Validate Slack token", "Sticky Note3", "Reply to user that command was received", "if has workflow", "Set config", "Send debug url", "if create thread", "Alert user that thread was created", "Add debug info", "Execute target workflow", "Add thread info", "Handle other commands", "Set thread info", "Unknown command", "Set vars", "Webhook to call for Slack command", "Reply to user directly", "<PERSON><PERSON>", "Command workflow trigger", "if has flag", "If matches env variable", "Found user", "Format data into nice structure", "REPLACE ME WITH TRIGGER", "Delete user here for example", "Get user here for example", "Confirm user was deleted", "Replying to thread", "Sticky Note2", "Sticky Note1", "parse command", "Validate webhook signature"], "tags": ["advanced", "automation", "n8n", "slackbot"]}, {"category": "automation", "name": "Create Update And Get Activity In Strava", "description": "This workflow allows users to create, update, and retrieve Strava activities, providing a seamless way to manage their fitness data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/744-create-update-and-get-activity-in-strava/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/744-create-update-and-get-activity-in-strava/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Strava", "Strava1", "Strava2"], "tags": ["activity", "automation", "create", "get", "strava", "update"]}, {"category": "automation", "name": "Manage Users Automatically In Reqres In", "description": "This workflow automates a series of HTTP requests to the Reqres.in API, allowing users to retrieve, create, and update user data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/602-manage-users-automatically-in-reqres-in/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/602-manage-users-automatically-in-reqres-in/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "HTTP Request", "HTTP Request1", "HTTP Request2"], "tags": ["automatically", "automation", "manage", "reqres", "users"]}, {"category": "automation", "name": "Whatsapp Starter Workflow", "description": "This workflow sets up a WhatsApp chatbot that responds to incoming messages by echoing the message back to the user.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2162-whatsapp-starter-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2162-whatsapp-starter-workflow/workflow.json", "nodeCount": 8, "nodeNames": ["Verify", "Respond to Webhook", "Echo the message back", "Is message?", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Respond"], "tags": ["automation", "starter", "whatsapp"]}, {"category": "automation", "name": "Send Tweets Every Minute To Mattermost", "description": "This workflow automatically monitors Twitter for mentions of \"n8n_io\", filters out duplicate tweets, and posts the latest unique tweets to a Mattermost channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/875-send-tweets-every-minute-to-mattermost/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/875-send-tweets-every-minute-to-mattermost/workflow.json", "nodeCount": 5, "nodeNames": ["Twitter", "<PERSON><PERSON>", "Function", "Set", "Mattermost"], "tags": ["automation", "every", "mattermost", "minute", "send", "tweets"]}, {"category": "automation", "name": "Create An Array Of Objects", "description": "This workflow generates a set of mock data objects and then transforms them into a single array of objects, providing a simple way to create sample data for testing or demonstration purposes.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/767-create-an-array-of-objects/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/767-create-an-array-of-objects/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON>", "Create an array of objects"], "tags": ["an", "array", "automation", "create", "objects"]}, {"category": "automation", "name": "Conversational Interviews With Ai Agents And N8N Forms", "description": "This workflow automates an AI-powered user interview process, recording the questions and answers in a Google Sheet for later analysis.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2566-conversational-interviews-with-ai-agents-and-n8n-forms/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2566-conversational-interviews-with-ai-agents-and-n8n-forms/workflow.json", "nodeCount": 40, "nodeNames": ["Stop Interview?", "Generate Row", "Generate Row1", "Clear For Next Interview", "<PERSON><PERSON>", "Send Reply To Agent", "Sticky Note1", "Start Interview", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Get Answer", "Sticky Note5", "Set Interview Topic", "UUID", "Generate Row2", "Create Session", "Update Session", "Update Session1", "Update Session2", "Valid Session?", "Respond to Webhook", "Window Buffer Memory2", "Window Buffer Memory", "Sticky Note6", "Redirect to Completion Screen", "Sticky Note7", "Sticky Note8", "Webhook", "404 Not Found", "AI Researcher", "Parse Response", "<PERSON><PERSON><PERSON>", "Show Transcript", "Save to Google Sheet", "Session to List", "Messages To JSON", "Sticky Note9", "Query By Session", "Get Session"], "tags": ["agents", "ai", "automation", "conversational", "forms", "interviews", "n8n"]}, {"category": "automation", "name": "Filtering And Branching Data", "description": "This workflow filters and routes customer data based on various conditions, providing a flexible way to manage customer information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1746-filtering-and-branching-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1746-filtering-and-branching-data/workflow.json", "nodeCount": 9, "nodeNames": ["On clicking 'execute'", "Customer Datastore", "Note", "Note1", "Note2", "Note3", "Country equals US", "Country is empty or Name contains 'Max'", "Country based branching"], "tags": ["automation", "branching", "data", "filtering"]}, {"category": "automation", "name": "Insert And Update Data In Airtable", "description": "This workflow allows users to create, retrieve, and update records in an Airtable database with a simple manual trigger.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/818-insert-and-update-data-in-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/818-insert-and-update-data-in-airtable/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Airtable", "Airtable1", "Set", "Set1", "Airtable2"], "tags": ["airtable", "automation", "data", "insert", "update"]}, {"category": "automation", "name": "Wordpress Ai Chatbot To Enhance User Experience With Supabase And Openai", "description": "This workflow is designed to create and manage embeddings for a WordPress website's content, enabling a conversational AI agent to provide relevant information to website visitors based on their questions.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2504-wordpress-ai-chatbot-to-enhance-user-experience-with-supabase-and-openai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2504-wordpress-ai-chatbot-to-enhance-user-experience-with-supabase-and-openai/workflow.json", "nodeCount": 53, "nodeNames": ["When clicking ‘Test workflow’", "Embeddings OpenAI", "Default Data Loader", "Token Splitter", "Embeddings OpenAI1", "OpenAI Chat Model", "Postgres Chat Memory", "Respond to Webhook", "Set fields", "Embeddings OpenAI2", "Default Data Loader1", "Token Splitter1", "Markdown1", "Postgres", "Aggregate", "Aggregate1", "Aggregate2", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Wordpress - Get all posts", "Wordpress - Get all pages", "Sticky Note3", "Set fields1", "Filter - Only published &  unprotected content", "HTML To Markdown", "Supabase - Store workflow execution", "Sticky Note4", "Every 30 seconds", "Sticky Note5", "Wordpress - Get posts modified after last workflow execution", "Wordpress - Get posts modified after last workflow execution1", "Set fields2", "Filter - Only published and unprotected content", "Loop Over Items", "Set fields3", "Set fields4", "Store documents on Supabase", "Store workflow execution id and timestamptz", "Aggregate documents", "Sticky Note6", "Sticky Note7", "Postgres - Create documents table", "Postgres - Create workflow execution history table", "Merge Wordpress Posts and Pages", "Merge retrieved WordPress posts and pages", "Postgres - Filter on existing documents", "Supabase - Delete row if documents exists", "Switch", "When chat message received", "Supabase - Retrieve documents from chatinput", "AI Agent", "Supabase Vector Store"], "tags": ["ai", "automation", "chatbot", "enhance", "experience", "openai", "supabase", "user", "wordpress"]}, {"category": "automation", "name": "Create <PERSON>ana Ticket From Terminal Bash Dash", "description": "This workflow automates the creation of Asana tasks based on incoming webhook requests, providing a simple way to integrate external systems with Asana.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/987-create-asana-ticket-from-terminal-bash-dash/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/987-create-asana-ticket-from-terminal-bash-dash/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "Webhook", "Set"], "tags": ["asana", "automation", "bash", "create", "dash", "terminal", "ticket"]}, {"category": "automation", "name": "Text Automations Using Apple Shortcuts", "description": "This workflow enables users to automate common text editing tasks, such as translation, grammar correction, and content length adjustment, using Apple Shortcuts and OpenAI's language models.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2456-text-automations-using-apple-shortcuts/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2456-text-automations-using-apple-shortcuts/workflow.json", "nodeCount": 10, "nodeNames": ["Sticky Note1", "Switch", "Respond to Shortcut", "<PERSON>hook from Shortcut", "OpenAI - <PERSON>", "OpenAI - <PERSON>", "OpenAI - Correct Grammar", "OpenAI - To Spanish", "OpenAI - To English", "<PERSON><PERSON>"], "tags": ["apple", "automation", "automations", "shortcuts", "text", "using"]}, {"category": "automation", "name": "Sum Or Aggregate A Column Of Spreadsheet Or Table Data", "description": "This workflow generates mock data, sums the values, and returns the total, providing a simple way to perform basic data processing tasks.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1497-sum-or-aggregate-a-column-of-spreadsheet-or-table-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1497-sum-or-aggregate-a-column-of-spreadsheet-or-table-data/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Mock data", "Summing function"], "tags": ["aggregate", "automation", "column", "data", "or", "spreadsheet", "sum", "table"]}, {"category": "automation", "name": "Receive Updates For Github Events", "description": "This workflow automatically triggers an action whenever an event occurs in a specified GitHub repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/527-receive-updates-for-github-events/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/527-receive-updates-for-github-events/workflow.json", "nodeCount": 1, "nodeNames": ["<PERSON><PERSON><PERSON>"], "tags": ["automation", "events", "github", "receive", "updates"]}, {"category": "automation", "name": "Send Automated Daily Reminders On Telegram", "description": "This workflow sends a daily reminder to a Telegram chat, prompting the user to record their activities from the previous day.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1387-send-automated-daily-reminders-on-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1387-send-automated-daily-reminders-on-telegram/workflow.json", "nodeCount": 3, "nodeNames": ["Morning reminder", "format reminder", "Send journal reminder"], "tags": ["automated", "automation", "daily", "reminders", "send", "telegram"]}, {"category": "automation", "name": "Get Articles From Hacker News", "description": "This workflow retrieves the latest news from Hacker News, a popular technology news aggregator, upon manual execution.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/525-get-articles-from-hacker-news/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/525-get-articles-from-hacker-news/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Hacker News"], "tags": ["articles", "automation", "get", "hacker", "news"]}, {"category": "automation", "name": "Read An Rss Feed", "description": "This workflow allows users to manually trigger the retrieval and display of the latest articles from a specified RSS feed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/583-read-an-rss-feed/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/583-read-an-rss-feed/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "RSS Feed Read"], "tags": ["an", "automation", "feed", "read", "rss"]}, {"category": "automation", "name": "Suggest Meeting Slots Using Ai", "description": "This workflow automatically processes incoming emails, identifies appointment requests, checks the user's calendar availability, and composes a personalized response to the sender with proposed meeting times.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1953-suggest-meeting-slots-using-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1953-suggest-meeting-slots-using-ai/workflow.json", "nodeCount": 21, "nodeNames": ["<PERSON><PERSON>", "Chat OpenAI", "Workflow Tool", "Chat OpenAI1", "<PERSON><PERSON>", "Sticky Note1", "Google Calendar", "Execute Workflow Trigger", "Format response", "Stringify Response", "Extract start", "end and name", "<PERSON><PERSON> only confirmed and with set time", "Is appointment request", "Classify appointment", "Structured Output Parser", "Sticky Note2", "Sticky Note3", "Sort", "<PERSON> as read", "Send Reply", "Agent"], "tags": ["ai", "automation", "meeting", "slots", "suggest", "using"]}, {"category": "automation", "name": "Assign Issues To Interested Contributors", "description": "This workflow automatically assigns GitHub issues to the issue creator or the commenter who requests to work on the issue, helping to streamline issue management and collaboration.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1274-assign-issues-to-interested-contributors/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1274-assign-issues-to-interested-contributors/workflow.json", "nodeCount": 11, "nodeNames": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Switch", "IF no assignee?", "NoOp", "IF wants to work?", "IF not assigned?", "Assign Issue Creator", "Add Comment", "NoOp1", "Assign <PERSON>"], "tags": ["assign", "automation", "contributors", "interested", "issues"]}, {"category": "automation", "name": "Create An Rss Feed Based On A Website S Content", "description": "This workflow generates an RSS feed of the latest blog posts from the \"Release\" category on the Baserow website, providing a convenient way for users to stay up-to-date with the latest product updates and changes.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1418-create-an-rss-feed-based-on-a-website-s-content/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1418-create-an-rss-feed-based-on-a-website-s-content/workflow.json", "nodeCount": 12, "nodeNames": ["On clicking 'execute'", "Item Lists", "Extract Posts", "Fetch Website", "Set URL", "Complete Link", "Format Date", "Create RSS Items", "Webhook", "Respond to Webhook", "Prepare Response", "Extract Fields"], "tags": ["an", "automation", "based", "content", "create", "feed", "rss", "s", "website"]}, {"category": "automation", "name": "Archive Spotify S Discover Weekly Playlist", "description": "This workflow automatically saves the user's Spotify Discover Weekly playlist tracks to an archive playlist, ensuring a backup of the user's weekly music discoveries.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/697-archive-spotify-s-discover-weekly-playlist/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/697-archive-spotify-s-discover-weekly-playlist/workflow.json", "nodeCount": 7, "nodeNames": ["Get Playlists", "Get Tracks", "Save to Archive", "Find Archive Playlist", "Find Weekly Playlist", "<PERSON><PERSON>", "Schedule Trigger"], "tags": ["archive", "automation", "discover", "playlist", "s", "spotify", "weekly"]}, {"category": "automation", "name": "Merge Binary Objects On Multiple Items Into A Single Item", "description": "This workflow downloads multiple image files from a predefined set of URLs and merges them into a single item with multiple binary objects.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1916-merge-binary-objects-on-multiple-items-into-a-single-item/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1916-merge-binary-objects-on-multiple-items-into-a-single-item/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking \"Execute Workflow\"", "Set URLs", "HTTP Request", "Merge items", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["automation", "binary", "item", "items", "merge", "multiple", "objects", "single"]}, {"category": "automation", "name": "Write Http Query String On Image", "description": "This workflow allows users to receive a webhook, fetch an image, and then add text to the image based on the webhook data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/3-write-http-query-string-on-image/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/3-write-http-query-string-on-image/workflow.json", "nodeCount": 3, "nodeNames": ["Webhook", "Edit Image", "Read File URL"], "tags": ["automation", "http", "image", "query", "string", "write"]}, {"category": "automation", "name": "Force Ai To Use A Specific Output Format", "description": "This workflow uses natural language processing and language models to generate a response that provides the 5 largest states in the USA by area along with their 3 largest cities and their populations.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1957-force-ai-to-use-a-specific-output-format/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1957-force-ai-to-use-a-specific-output-format/workflow.json", "nodeCount": 11, "nodeNames": ["When clicking \"Execute Workflow\"", "Prompt", "LLM Chain", "Structured Output Parser", "Auto-fixing Output Parser", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "OpenAI Chat Model", "OpenAI Chat Model1"], "tags": ["ai", "automation", "force", "format", "output", "specific", "use"]}, {"category": "automation", "name": "Send Updates About The Position Of The Iss Every Minute To A Topic In Kafka", "description": "This workflow periodically retrieves the current position of the International Space Station (ISS) and publishes the data to a Kafka topic.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/750-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-kafka/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/750-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-kafka/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON>", "HTTP Request", "Set", "Kafka"], "tags": ["about", "automation", "every", "iss", "kafka", "minute", "position", "send", "topic", "updates"]}, {"category": "automation", "name": "Send Http Requests To A List Of Urls", "description": "This workflow periodically fetches data from a list of URLs and processes the responses.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2276-send-http-requests-to-a-list-of-urls/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2276-send-http-requests-to-a-list-of-urls/workflow.json", "nodeCount": 4, "nodeNames": ["Split Out", "Schedule Trigger", "HTTP Request", "URLs List"], "tags": ["automation", "http", "list", "requests", "send", "urls"]}, {"category": "automation", "name": "Validate Totp Token Without Creating A Credential", "description": "This workflow allows you to verify the validity of a 6-digit Time-based One-Time Password (TOTP) code using a provided TOTP secret.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2379-validate-totp-token-without-creating-a-credential/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2379-validate-totp-token-without-creating-a-credential/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "TOTP VALIDATION", "IF CODE IS VALID", "<PERSON><PERSON>", "EXAMPLE FIELDS"], "tags": ["automation", "creating", "credential", "token", "totp", "validate", "without"]}, {"category": "automation", "name": "Display Project Data On A Smashing Dashboard", "description": "This workflow retrieves and displays various metrics for an open-source project, including Docker, npm, GitHub, and Product Hunt data, to provide a comprehensive overview of the project's performance and popularity.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/693-display-project-data-on-a-smashing-dashboard/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/693-display-project-data-on-a-smashing-dashboard/workflow.json", "nodeCount": 24, "nodeNames": ["<PERSON><PERSON>", "Dashboard Configuration", "Retrieve Docker Data", "<PERSON><PERSON>", "Docker Stars", "Retrieve npm Data", "GitHub Watchers", "GitHub Forks", "GitHub Open Issues", "GitHub Stars", "npm Maintenance", "npm Popularity", "npm Quality", "npm Final", "Product Hunt Rating", "Product Hunt Reviews", "Product Hunt Votes", "Product Hunt Comments", "GitHub", "Retrieve Product Hunt Data", "Massage npm Data", "Massage Product Hunt Data", "Massage Docker Data", "Massage GitHub Data"], "tags": ["automation", "dashboard", "data", "display", "project", "smashing"]}, {"category": "automation", "name": "Create A Table And Insert And Update Data In The Table In Snowflake", "description": "This workflow creates a Snowflake table, inserts data into it, and then updates the data in the table.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/824-create-a-table-and-insert-and-update-data-in-the-table-in-snowflake/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/824-create-a-table-and-insert-and-update-data-in-the-table-in-snowflake/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Set", "Snowflake", "Snowflake1", "Set1", "Snowflake2"], "tags": ["automation", "create", "data", "insert", "snowflake", "table", "update"]}, {"category": "automation", "name": "Insert Data Into A New Row For A Table In Coda", "description": "This workflow allows users to easily add data to a Coda document by clicking a manual trigger.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/482-insert-data-into-a-new-row-for-a-table-in-coda/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/482-insert-data-into-a-new-row-for-a-table-in-coda/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Coda", "Set"], "tags": ["automation", "coda", "data", "insert", "new", "row", "table"]}, {"category": "automation", "name": "N8N Workflow Backup Management With Dropbox And Airtable", "description": "This workflow automatically retrieves and saves n8n workflow details to Airtable and Dropbox, with support for CRON-based execution.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/823-n8n-workflow-backup-management-with-dropbox-and-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/823-n8n-workflow-backup-management-with-dropbox-and-airtable/workflow.json", "nodeCount": 19, "nodeNames": ["On clicking 'execute'", "Function", "SplitInBatches", "IF", "NoOp", "Airtable", "Airtable1", "Airtable2", "Set", "Set1", "Get All Workflows", "Prepare data", "Prepare data1", "<PERSON><PERSON>", "Move Binary Data", "Dropbox", "Get Workflow Details", "Get file link", "IF Airtable record exists?"], "tags": ["airtable", "automation", "backup", "dropbox", "management", "n8n"]}, {"category": "automation", "name": "Report N8N Workflow Errors Directly To Your Email", "description": "This workflow sends an email notification when an error occurs in another workflow, providing details about the error and a link to the execution log.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2160-report-n8n-workflow-errors-directly-to-your-email/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2160-report-n8n-workflow-errors-directly-to-your-email/workflow.json", "nodeCount": 4, "nodeNames": ["On Error", "Sticky Note3", "<PERSON><PERSON>", "Gmail"], "tags": ["automation", "directly", "email", "errors", "n8n", "report"]}, {"category": "automation", "name": "Get Workflows Affected By 0 214 3 Migration", "description": "This workflow helps identify potentially affected workflows and nodes in an n8n instance after upgrading to version 0.214.3, where some workflows might have been re-wired incorrectly due to changes in nodes with more than one output.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1892-get-workflows-affected-by-0-214-3-migration/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1892-get-workflows-affected-by-0-214-3-migration/workflow.json", "nodeCount": 9, "nodeNames": ["<PERSON><PERSON>", "Get all workflows", "Webhook", "Parse potentially affected workflows", "Sticky Note1", "Sticky Note2", "Generate Report", "Serve HTML Report", "Sticky Note3"], "tags": ["0", "214", "3", "affected", "automation", "get", "migration", "workflows"]}, {"category": "automation", "name": "Create <PERSON> Json Items From An Array", "description": "This workflow generates a list of mock data objects and transforms them into individual JSON items, providing a simple way to work with structured data in n8n.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/766-create-multiple-json-items-from-an-array/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/766-create-multiple-json-items-from-an-array/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON>", "Create JSON-items"], "tags": ["an", "array", "automation", "create", "items", "json", "multiple"]}, {"category": "automation", "name": "Add A Datapoint To Beeminder On Strava Activity Update", "description": "This workflow automatically logs Strava activities to a Beeminder goal, helping users stay accountable and motivated to achieve their fitness goals.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/900-add-a-datapoint-to-beeminder-on-strava-activity-update/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/900-add-a-datapoint-to-beeminder-on-strava-activity-update/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "tags": ["activity", "add", "automation", "beeminder", "datapoint", "strava", "update"]}, {"category": "automation", "name": "Report N8N Workflow Errors To Slack", "description": "This workflow is designed to automatically send a Slack notification when an error occurs in a production workflow, providing valuable information to the team for troubleshooting and monitoring purposes.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2150-report-n8n-workflow-errors-to-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2150-report-n8n-workflow-errors-to-slack/workflow.json", "nodeCount": 5, "nodeNames": ["<PERSON><PERSON>ck", "On Error", "Set message", "Sticky Note3", "<PERSON><PERSON>"], "tags": ["automation", "errors", "n8n", "report", "slack"]}, {"category": "automation", "name": "Preparing Data To Be Sent To A Service", "description": "This workflow generates customer data, transforms it into the format required by Google Sheets, and then upserts (creates or updates) the records in a Google Sheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1751-preparing-data-to-be-sent-to-a-service/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1751-preparing-data-to-be-sent-to-a-service/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Note", "Create or Update record in Google Sheet", "Note1", "Set - Prepare fields", "Customer Datastore - Generate some data"], "tags": ["automation", "be", "data", "preparing", "sent", "service"]}, {"category": "automation", "name": "Create A Task In Clickup", "description": "This workflow allows users to manually trigger the creation of a new task in ClickUp, a popular project management tool.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/485-create-a-task-in-clickup/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/485-create-a-task-in-clickup/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "ClickUp"], "tags": ["automation", "clickup", "create", "task"]}, {"category": "automation", "name": "Get Details Of A Gitlab Repository", "description": "This workflow allows users to retrieve information about a specific GitLab repository with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/465-get-details-of-a-gitlab-repository/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/465-get-details-of-a-gitlab-repository/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Gitlab"], "tags": ["automation", "details", "get", "gitlab", "repository"]}, {"category": "automation", "name": "Automate Assigning Github Issues", "description": "This workflow automatically assigns GitHub issues to the issue creator or the commenter who requests to work on the issue, helping to streamline the issue management process.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1442-automate-assigning-github-issues/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1442-automate-assigning-github-issues/workflow.json", "nodeCount": 10, "nodeNames": ["Switch", "IF no assignee?", "NoOp", "IF wants to work?", "IF not assigned?", "Assign Issue Creator", "Add Comment", "NoOp1", "Assign <PERSON>", "Github Trigger1"], "tags": ["assigning", "automate", "automation", "github", "issues"]}, {"category": "automation", "name": "Get All Orders In Shopify", "description": "This workflow allows users to retrieve a list of all products from their Shopify store with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/548-get-all-orders-in-shopify/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/548-get-all-orders-in-shopify/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Shopify"], "tags": ["all", "automation", "get", "orders", "shopify"]}, {"category": "automation", "name": "Sharepoint List Fetch With Oauth <PERSON>ken", "description": "This workflow automates the process of fetching data from a SharePoint list by generating an OAuth token and using it to make authenticated requests to the SharePoint API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2527-sharepoint-list-fetch-with-oauth-token/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2527-sharepoint-list-fetch-with-oauth-token/workflow.json", "nodeCount": 5, "nodeNames": ["Generate OAuth Token", "Fetch SharePoint List", "Schedule Trigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "tags": ["automation", "fetch", "list", "o<PERSON>h", "sharepoint", "token"]}, {"category": "automation", "name": "Get Data From Hacker News And Send To Airtable Or Via Sms", "description": "This workflow automatically retrieves the top 3 articles from Hacker News, extracts unique words from the article titles, translates them to German, and sends the translated words as a daily SMS message.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/888-get-data-from-hacker-news-and-send-to-airtable-or-via-sms/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/888-get-data-from-hacker-news-and-send-to-airtable-or-via-sms/workflow.json", "nodeCount": 8, "nodeNames": ["Daily trigger", "Get top 3 articles", "Extract words", "Translate", "Filter data", "Save today's words", "Craft message", "Send SMS"], "tags": ["airtable", "automation", "data", "get", "hacker", "news", "or", "send", "sms", "via"]}, {"category": "automation", "name": "Import Csv Into Mysql", "description": "This workflow automates the process of importing a CSV file containing concert data into a MySQL database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1839-import-csv-into-mysql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1839-import-csv-into-mysql/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Read From File", "Convert To Spreadsheet", "Insert into MySQL"], "tags": ["automation", "csv", "import", "mysql"]}, {"category": "automation", "name": "Create Update And Get An Object From Bubble", "description": "This workflow creates a new Bubble object, updates its properties, and then retrieves the updated object.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1041-create-update-and-get-an-object-from-bubble/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1041-create-update-and-get-an-object-from-bubble/workflow.json", "nodeCount": 3, "nodeNames": ["Bubble", "Bubble1", "Bubble2"], "tags": ["an", "automation", "bubble", "create", "get", "object", "update"]}, {"category": "automation", "name": "Create Update And Get An Item From Webflow", "description": "This workflow creates a new Webflow item, updates its details, and then retrieves the updated item.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1048-create-update-and-get-an-item-from-webflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1048-create-update-and-get-an-item-from-webflow/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Webflow", "Webflow2", "Webflow1"], "tags": ["an", "automation", "create", "get", "item", "update", "webflow"]}, {"category": "automation", "name": "Track An Event In Segment", "description": "This workflow allows users to track custom events in Segment, a popular customer data platform, with a simple manual trigger.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/495-track-an-event-in-segment/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/495-track-an-event-in-segment/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Segment"], "tags": ["an", "automation", "event", "segment", "track"]}, {"category": "automation", "name": "Create A New Member Update The Infromation Create A Note And Post In Orbit", "description": "This workflow automates the process of creating and updating a member's profile in an Orbit workspace, adding tags, notes, and posts to the member's profile.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/765-create-a-new-member-update-the-infromation-create-a-note-and-post-in-orbit/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/765-create-a-new-member-update-the-infromation-create-a-note-and-post-in-orbit/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Orbit", "Orbit1", "Orbit2", "Orbit3"], "tags": ["automation", "create", "infromation", "member", "new", "note", "orbit", "post", "update"]}, {"category": "automation", "name": "Airtable Automate Recurring Tasks", "description": "This workflow automates the creation of recurring tasks in Airtable, ensuring timely task assignments and updates for improved team productivity.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2070-airtable-automate-recurring-tasks/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2070-airtable-automate-recurring-tasks/workflow.json", "nodeCount": 14, "nodeNames": ["Get Automated Task", "Create Task", "Get Task Template", "<PERSON>signee", "Get Client", "Calculate Dates", "Update Automated Record", "Notify Assignee", "<PERSON><PERSON>", "Sticky Note1", "Entered View  \"First Task - Create Task\"", "Sticky Note2", "Sticky Note3", "Airtable Base ID's"], "tags": ["airtable", "automate", "automation", "recurring", "tasks"]}, {"category": "automation", "name": "Generate Sql Queries From Schema Only Ai Powered", "description": "This workflow enables users to interact with a MySQL database using natural language conversations powered by an AI agent, providing dynamic SQL queries and formatted results.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2508-generate-sql-queries-from-schema-only-ai-powered/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2508-generate-sql-queries-from-schema-only-ai-powered/workflow.json", "nodeCount": 29, "nodeNames": ["OpenAI Chat Model", "Window Buffer Memory", "No Operation", "do nothing", "List all tables in a database", "Extract database schema", "Add table name to output", "Convert data to binary", "Save file locally", "Extract data from file", "<PERSON><PERSON>", "AI Agent", "<PERSON><PERSON>", "Sticky Note1", "When clicking \"Test workflow\"", "Sticky Note2", "Sticky Note3", "Combine schema data and chat input", "Load the schema from the local file", "Extract SQL query", "Check if query exists", "Sticky Note4", "Sticky Note5", "Sticky Note7", "Sticky Note6", "Sticky Note8", "Format query results", "Run SQL query", "Prepare final output", "Combine query result and chat answer"], "tags": ["ai", "automation", "generate", "only", "powered", "queries", "schema", "sql"]}, {"category": "automation", "name": "Get Company Data And Store It In Airtable", "description": "This workflow automatically fetches brand information from Brandfetch, sets relevant data fields, and appends the data to an Airtable table, providing a streamlined way to manage and store brand information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/835-get-company-data-and-store-it-in-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/835-get-company-data-and-store-it-in-airtable/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Brandfetch", "Brandfetch1", "Set", "Airtable"], "tags": ["airtable", "automation", "company", "data", "get", "it", "store"]}, {"category": "automation", "name": "Get Long Lived Facebook User Or Page Access Token", "description": "This workflow generates long-lived Facebook user and page access tokens, enabling seamless integration with Facebook's API for various business applications.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2535-get-long-lived-facebook-user-or-page-access-token/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2535-get-long-lived-facebook-user-or-page-access-token/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Get Long Lived FB User Token", "Get Long Lived FB Page Token", "<PERSON><PERSON>", "Set Parameter"], "tags": ["access", "automation", "facebook", "get", "lived", "long", "or", "page", "token", "user"]}, {"category": "automation", "name": "Cron Routines With Telegram", "description": "This workflow automatically cleans up old package tracking records in a MySQL database and sends notifications via Telegram.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1364-cron-routines-with-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1364-cron-routines-with-telegram/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Telegram", "Telegram1", "Webhook", "limparPacoteCliente1", "limpaPacoteCliente0"], "tags": ["automation", "cron", "routines", "telegram"]}, {"category": "automation", "name": "Domain Extractor", "description": "This workflow extracts the domain name from a given URL or email address and checks if it is a free mail provider.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2036-domain-extractor/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2036-domain-extractor/workflow.json", "nodeCount": 4, "nodeNames": ["Extract domain", "Execute Workflow Trigger", "Prepare data before function", "<PERSON><PERSON>"], "tags": ["automation", "domain", "extractor"]}, {"category": "automation", "name": "Git Backup Of Workflows And Credentials", "description": "This workflow automatically backs up your n8n workflows and credentials to a Git repository on a scheduled basis.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1053-git-backup-of-workflows-and-credentials/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1053-git-backup-of-workflows-and-credentials/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Export Workflows", "Export Credentials", "git add", "git commit", "git push", "<PERSON><PERSON>"], "tags": ["automation", "backup", "credentials", "git", "workflows"]}, {"category": "automation", "name": "Extract Information From An Image Of A Receipt", "description": "This workflow allows users to extract information from an image using the Mindee API, triggered by a manual action.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/702-extract-information-from-an-image-of-a-receipt/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/702-extract-information-from-an-image-of-a-receipt/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "HTTP Request"], "tags": ["an", "automation", "extract", "image", "information", "receipt"]}, {"category": "automation", "name": "Get Contributors Information From Github In Slack", "description": "This workflow retrieves GitHub user details, including their name, email, company, and location, and sends a Slack message with the information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/563-get-contributors-information-from-github-in-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/563-get-contributors-information-from-github-in-slack/workflow.json", "nodeCount": 4, "nodeNames": ["Webhook", "GraphQL", "Function", "<PERSON><PERSON>ck"], "tags": ["automation", "contributors", "get", "github", "information", "slack"]}, {"category": "automation", "name": "Weekly Coffee Chat Mattermost Version", "description": "This workflow automates the process of organizing virtual coffee breaks for a team, creating random groups, sending announcements, and scheduling calendar invites.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/853-weekly-coffee-chat-mattermost-version/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/853-weekly-coffee-chat-mattermost-version/workflow.json", "nodeCount": 6, "nodeNames": ["Divide into groups", "Greetings", "Weekly trigger on monday", "Announce groups", "Employees in coffee chat channel", "Send calendar invites"], "tags": ["automation", "chat", "coffee", "mattermost", "version", "weekly"]}, {"category": "automation", "name": "Demonstrates The Use Of The Item Index Method", "description": "This workflow retrieves customer data from a custom datastore, sets an API key, and sends the customer names to a webhook.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1330-demonstrates-the-use-of-the-item-index-method/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1330-demonstrates-the-use-of-the-item-index-method/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Set", "Customer Datastore", "HTTP Request"], "tags": ["automation", "demonstrates", "index", "item", "method", "use"]}, {"category": "automation", "name": "Create Linear Tickets From Notion Content", "description": "This workflow automatically creates Linear issues from Notion blocks, with the ability to customize the issue title, assignee, and description.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2138-create-linear-tickets-from-notion-content/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2138-create-linear-tickets-from-notion-content/workflow.json", "nodeCount": 24, "nodeNames": ["Loop Over Items", "Fetch Linear team details", "Get issue contents", "Aggregate", "Prepare issue data", "Create linear issue", "Set assignee and title", "Team missing?", "Set page URL", "Set team ID", "Add link to Notion block", "Get issue URL", "Shorten title", "Sticky Note1", "Sticky Note2", "Sticky Note4", "Sticky Note5", "Sticky Note3", "Unimported", "unchecked to_do blocks only", "n8n Form Trigger", "Get issues", "Convert contents to Markdown", "Respond with error", "Respond with error1"], "tags": ["automation", "content", "create", "linear", "notion", "tickets"]}, {"category": "automation", "name": "Extract Infromation From A Receipt And Store It In Airtable", "description": "This workflow automates the process of capturing and storing expense information from receipts, providing a streamlined way to manage business finances.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/741-extract-infromation-from-a-receipt-and-store-it-in-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/741-extract-infromation-from-a-receipt-and-store-it-in-airtable/workflow.json", "nodeCount": 5, "nodeNames": ["Get Receipt", "Get Image", "Extract Information", "Set Information", "Store Information"], "tags": ["airtable", "automation", "extract", "infromation", "it", "receipt", "store"]}, {"category": "automation", "name": "Run A Sql Query On Postgres", "description": "This workflow allows users to execute a SQL query on a Postgres database and retrieve the results with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/458-run-a-sql-query-on-postgres/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/458-run-a-sql-query-on-postgres/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Postgres"], "tags": ["automation", "postgres", "query", "run", "sql"]}, {"category": "automation", "name": "Send Selected G<PERSON>ub Events To Slack", "description": "This workflow automatically sends a Slack notification whenever a new star is added or removed from an n8n repository on GitHub.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/4-send-selected-github-events-to-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/4-send-selected-github-events-to-slack/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON><PERSON>", "IF", "Slack - Add", "Slack - Remove"], "tags": ["automation", "events", "github", "selected", "send", "slack"]}, {"category": "automation", "name": "Avoid Rate Limiting By Batching Http Requests", "description": "This workflow retrieves customer data from a datastore, splits it into batches, and sends each batch to a third-party API, with a delay between each batch to avoid rate limiting.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1243-avoid-rate-limiting-by-batching-http-requests/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1243-avoid-rate-limiting-by-batching-http-requests/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Customer Datastore", "SplitInBatches", "HTTP Request", "Wait", "Replace Me"], "tags": ["automation", "avoid", "batching", "http", "limiting", "rate", "requests"]}, {"category": "automation", "name": "Upload Bulk Records From Csv Airtable Interfaces", "description": "This workflow automates the process of uploading CSV files to Airtable, creating new lead records, and updating the status of the uploaded files.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2071-upload-bulk-records-from-csv-airtable-interfaces/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2071-upload-bulk-records-from-csv-airtable-interfaces/workflow.json", "nodeCount": 17, "nodeNames": ["Get File ID", "Status Failed", "Status Uploaded", "<PERSON><PERSON>", "Sticky Note1", "Campaign is Not Empty", "Campaign Not Empty", "Campaign Not Empty1", "Read File", "Airtable Base IDs", "Status Processing", "Download File", "Create Records", "Sticky Note2", "New Upload", "Sticky Note3", "Sticky Note4"], "tags": ["airtable", "automation", "bulk", "csv", "interfaces", "records", "upload"]}, {"category": "automation", "name": "Restore Backed Up Workflows From Github To N8N", "description": "This workflow restores backed-up workflows from a GitHub repository to an n8n instance, ensuring that only new workflows are added and avoiding duplicates.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2289-restore-backed-up-workflows-from-github-to-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2289-restore-backed-up-workflows-from-github-to-n8n/workflow.json", "nodeCount": 17, "nodeNames": ["On clicking 'execute'", "Globals", "Note", "Workflow name already exists", "If workflow already exists", "Set n8n existing workflows names", "GitHub - get all files", "n8n - get all workflows", "GitHub - get each file", "Set name and content", "n8n - create workflow", "Note1", "Merge globals and files", "Merge <PERSON> and n8n workflows - Keep only non existing workflows based on the name", "Note2", "Note3", "Note4"], "tags": ["automation", "backed", "github", "n8n", "restore", "up", "workflows"]}, {"category": "automation", "name": "Create A Table In Quest Db And Insert Data", "description": "This workflow creates a table in a QuestDB database, sets values for the table, and then retrieves the data from the table.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/592-create-a-table-in-quest-db-and-insert-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/592-create-a-table-in-quest-db-and-insert-data/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Set", "QuestDB", "QuestDB1"], "tags": ["automation", "create", "data", "db", "insert", "quest", "table"]}, {"category": "automation", "name": "Get A Board From Monday Com", "description": "This workflow allows users to manually trigger a connection to their Monday.com board, enabling them to retrieve data from their project management platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/556-get-a-board-from-monday-com/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/556-get-a-board-from-monday-com/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Monday.com"], "tags": ["automation", "board", "com", "get", "monday"]}, {"category": "automation", "name": "Assign Values To Variables Using The Set Node", "description": "This simple workflow allows users to manually trigger the execution of a set of predefined data values.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/526-assign-values-to-variables-using-the-set-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/526-assign-values-to-variables-using-the-set-node/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Set"], "tags": ["assign", "automation", "node", "set", "using", "values", "variables"]}, {"category": "automation", "name": "Merge Data For Multiple Executions", "description": "This workflow aggregates content from multiple RSS feeds, splitting the data into batches and merging the results to provide a comprehensive view of the latest content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1160-merge-data-for-multiple-executions/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1160-merge-data-for-multiple-executions/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Merge Data", "Function", "RSS Feed Read", "SplitInBatches", "IF"], "tags": ["automation", "data", "executions", "merge", "multiple"]}, {"category": "automation", "name": "Read Rss Feed From Two Different Sources", "description": "This workflow fetches RSS feed data from multiple sources and processes them in batches.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/687-read-rss-feed-from-two-different-sources/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/687-read-rss-feed-from-two-different-sources/workflow.json", "nodeCount": 4, "nodeNames": ["RSS Feed Read", "When clicking \"Execute Workflow\"", "Code", "Loop Over Items"], "tags": ["automation", "different", "feed", "read", "rss", "sources", "two"]}, {"category": "automation", "name": "Send A Tweet To Twitter", "description": "This workflow allows users to easily post a tweet with a custom message by triggering the workflow manually.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/445-send-a-tweet-to-twitter/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/445-send-a-tweet-to-twitter/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Twitter"], "tags": ["automation", "send", "tweet", "twitter"]}, {"category": "automation", "name": "Export Wordpress Posts To Spreadsheet", "description": "This workflow retrieves data from a WordPress site, converts it to a CSV file, and saves the file to the local system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/635-export-wordpress-posts-to-spreadsheet/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/635-export-wordpress-posts-to-spreadsheet/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Wordpress", "Spreadsheet File", "Write Binary File"], "tags": ["automation", "export", "posts", "spreadsheet", "wordpress"]}, {"category": "automation", "name": "Standup Bot 1 4 Initialize", "description": "This workflow allows users to easily configure a Mattermost bot by writing a JSON configuration file to a specified location.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1472-standup-bot-1-4-initialize/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1472-standup-bot-1-4-initialize/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Write Binary File", "Move Binary Data", "Use Default Config"], "tags": ["1", "4", "automation", "bot", "initialize", "standup"]}, {"category": "automation", "name": "Standup Bot 2 4 Read Config", "description": "This workflow allows users to read and convert a binary configuration file into a JSON format, enabling easy access and manipulation of the configuration data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1473-standup-bot-2-4-read-config/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1473-standup-bot-2-4-read-config/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Read Config File", "Convert to JSON"], "tags": ["2", "4", "automation", "bot", "config", "read", "standup"]}, {"category": "automation", "name": "Prompt Based Object Detection With Gemini 2 0", "description": "This workflow uses Gemini 2.0's prompt-based object detection capabilities to identify and draw bounding boxes around rabbits in an image.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2649-prompt-based-object-detection-with-gemini-2-0/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2649-prompt-based-object-detection-with-gemini-2-0/workflow.json", "nodeCount": 14, "nodeNames": ["When clicking ‘Test workflow’", "Get Variables", "Get Test Image", "Gemini 2.0 Object Detection", "Scale Normalised Coords", "Draw Bounding Boxes", "Get Image Info", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Sticky Note6"], "tags": ["0", "2", "automation", "based", "detection", "gemini", "object", "prompt"]}, {"category": "automation", "name": "Find A New Book Recommendations", "description": "This workflow automatically retrieves a random book recommendation from the Open Library based on a specified subject, and sends the recommendation via email on a weekly basis.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/869-find-a-new-book-recommendations/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/869-find-a-new-book-recommendations/workflow.json", "nodeCount": 13, "nodeNames": ["On clicking 'execute'", "Every Friday at 11:00 AM", "Set Subject", "Retrieve Book Count", "Check Book Count", "Select Random Book", "Retrieve Detailed Book Info", "Retrieve Basic Book Info", "Book Recommendation", "Filtered Book Info", "Create Author String", "Send No Book Email", "Send Book Email"], "tags": ["automation", "book", "find", "new", "recommendations"]}, {"category": "automation", "name": "Add A Check Condition For A Loop In N8N", "description": "This workflow allows users to manually trigger a Twitter post, with the ability to conditionally skip the post after a certain number of executions.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1130-add-a-check-condition-for-a-loop-in-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1130-add-a-check-condition-for-a-loop-in-n8n/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "IF", "NoOp", "Twitter"], "tags": ["add", "automation", "check", "condition", "loop", "n8n"]}, {"category": "automation", "name": "Push Your Public Ip To Namecheaps Dynamic Dns", "description": "This workflow automatically updates the IP address for a set of subdomains on Namecheap's Dynamic DNS service, ensuring that your website or application remains accessible even with a changing IP address.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1576-push-your-public-ip-to-namecheaps-dynamic-dns/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1576-push-your-public-ip-to-namecheaps-dynamic-dns/workflow.json", "nodeCount": 7, "nodeNames": ["<PERSON><PERSON>", "Checks IP if new", "subdomains", "Loops trough Subdomain list", "Send data to Namecheap", "Get Public IP address", "yourdomain.com"], "tags": ["automation", "dns", "dynamic", "ip", "namecheaps", "public", "push"]}, {"category": "automation", "name": "Classify New Bugs In Linear With Openai S Gpt 4 And Move Them To The Right Team", "description": "This workflow automatically classifies new bug tickets in a Linear project and assigns them to the appropriate team for resolution.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2154-classify-new-bugs-in-linear-with-openai-s-gpt-4-and-move-them-to-the-right-team/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2154-classify-new-bugs-in-linear-with-openai-s-gpt-4-and-move-them-to-the-right-team/workflow.json", "nodeCount": 12, "nodeNames": ["<PERSON><PERSON>", "Only tickets that need to be classified", "<PERSON><PERSON>", "Update team", "Get all linear teams", "Set team ID", "Set me up", "Sticky Note1", "Check if AI was able to find a team", "Notify in Slack", "Merge data", "OpenAI"], "tags": ["4", "automation", "bugs", "classify", "gpt", "linear", "move", "new", "openai", "right", "s", "team", "them"]}, {"category": "automation", "name": "Using The Merge Node Merge By Key", "description": "This workflow consolidates and merges data from two different sources, enabling users to efficiently combine and analyze employee information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/100-using-the-merge-node-merge-by-key/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/100-using-the-merge-node-merge-by-key/workflow.json", "nodeCount": 5, "nodeNames": ["Data 1", "Data 2", "Convert Data 1", "Convert Data 2", "<PERSON><PERSON>"], "tags": ["automation", "key", "merge", "node", "using"]}, {"category": "automation", "name": "Create A New Folder In Box", "description": "This workflow allows users to create a new folder in their Box account with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/559-create-a-new-folder-in-box/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/559-create-a-new-folder-in-box/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Box"], "tags": ["automation", "box", "create", "folder", "new"]}, {"category": "automation", "name": "Send Updates About The Position Of The Iss Every Minute To A Topic In Rabbitmq", "description": "This workflow retrieves the current position of the International Space Station (ISS) from an API, processes the data, and publishes it to a RabbitMQ queue for further use.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/844-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-rabbitmq/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/844-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-rabbitmq/workflow.json", "nodeCount": 4, "nodeNames": ["RabbitMQ", "Set", "HTTP Request", "<PERSON><PERSON>"], "tags": ["about", "automation", "every", "iss", "minute", "position", "rabbitmq", "send", "topic", "updates"]}, {"category": "automation", "name": "Introduction To The Http Tool", "description": "This workflow allows users to interact with an AI agent that can scrape web pages and suggest activities based on user input.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2343-introduction-to-the-http-tool/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2343-introduction-to-the-http-tool/workflow.json", "nodeCount": 12, "nodeNames": ["<PERSON><PERSON>", "OpenAI Chat Model", "Sticky Note1", "OpenAI Chat Model1", "Activity Tool", "Set ChatInput1", "AI Agent1", "Set ChatInput", "When clicking ‘Test workflow’", "AI Agent", "Webscraper Tool", "Sticky Note2"], "tags": ["automation", "http", "introduction", "tool"]}, {"category": "automation", "name": "Receive Updates Of The Position Of The Iss Every Minute", "description": "This workflow retrieves and stores the current location data of the International Space Station (ISS) on a regular schedule, filtering out any duplicate data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/880-receive-updates-of-the-position-of-the-iss-every-minute/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/880-receive-updates-of-the-position-of-the-iss-every-minute/workflow.json", "nodeCount": 4, "nodeNames": ["Function", "Set", "HTTP Request", "<PERSON><PERSON>"], "tags": ["automation", "every", "iss", "minute", "position", "receive", "updates"]}, {"category": "automation", "name": "Get All The Entries From Contentful", "description": "This workflow allows users to retrieve all content from a Contentful space with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/640-get-all-the-entries-from-contentful/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/640-get-all-the-entries-from-contentful/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Contentful"], "tags": ["all", "automation", "contentful", "entries", "get"]}, {"category": "automation", "name": "Get Information Of An Image", "description": "This workflow allows users to retrieve an image from a random image API, edit the image, and potentially perform further actions with the edited image.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/576-get-information-of-an-image/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/576-get-information-of-an-image/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Edit Image", "HTTP Request"], "tags": ["an", "automation", "get", "image", "information"]}, {"category": "automation", "name": "Split In Batches Node Noitemsleft Example", "description": "This workflow generates a set of 10 items, splits them into batches of 1, and checks if there are any items left to process, displaying a \"No Items Left\" message if the batch is empty.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/995-split-in-batches-node-noitemsleft-example/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/995-split-in-batches-node-noitemsleft-example/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Function", "SplitInBatches", "IF", "Set"], "tags": ["automation", "batches", "example", "node", "noitemsleft", "split"]}, {"category": "automation", "name": "Flux Ai Image Generator", "description": "This workflow generates unique, AI-powered images based on user-provided prompts and style preferences, and serves the generated images on a responsive web page.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2417-flux-ai-image-generator/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2417-flux-ai-image-generator/workflow.json", "nodeCount": 19, "nodeNames": ["Vivid Pop Explosion", "AI Dystopia", "Post-Analog Glitchscape", "Neon Fauvism", "None", "Serve webpage", "Respond with error", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Upload image to S3", "Sticky Note3", "Hyper-Surreal Escape", "Sticky Note4", "n8n Form Trigger", "Call hugginface inference api", "Sticky Note5", "Sticky Note6", "Route by style"], "tags": ["ai", "automation", "flux", "generator", "image"]}, {"category": "automation", "name": "Sample Error Workflow", "description": "This workflow sends notifications to <PERSON><PERSON> and <PERSON><PERSON><PERSON> when an error occurs in a workflow, providing details about the error and the workflow that encountered it.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/359-sample-error-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/359-sample-error-workflow/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON><PERSON>", "Mattermost", "<PERSON><PERSON><PERSON>"], "tags": ["automation", "error", "sample"]}, {"category": "automation", "name": "Send New Youtube Channel Videos To Telegram", "description": "This workflow automatically retrieves the latest YouTube videos from a specific channel, filters out previously posted videos, and sends a Telegram message with the details of the new videos.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1599-send-new-youtube-channel-videos-to-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1599-send-new-youtube-channel-videos-to-telegram/workflow.json", "nodeCount": 5, "nodeNames": ["Set", "Function", "CheckTime", "GetVideosYT", "SendVideo"], "tags": ["automation", "channel", "new", "send", "telegram", "videos", "youtube"]}, {"category": "automation", "name": "Send An Sms Using The Mocean Node", "description": "This workflow allows users to trigger the sending of SMS messages using the Mocean SMS API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/667-send-an-sms-using-the-mocean-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/667-send-an-sms-using-the-mocean-node/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Mocean"], "tags": ["an", "automation", "mocean", "node", "send", "sms", "using"]}, {"category": "automation", "name": "Turn On A Light To A Specific Color On Any Update In Github Repository", "description": "This workflow turns a light in a Home Assistant instance red whenever an update is made to a GitHub repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1856-turn-on-a-light-to-a-specific-color-on-any-update-in-github-repository/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1856-turn-on-a-light-to-a-specific-color-on-any-update-in-github-repository/workflow.json", "nodeCount": 4, "nodeNames": ["On any update in repository", "Turn a light red", "Note", "Note1"], "tags": ["any", "automation", "color", "github", "light", "repository", "specific", "turn", "update"]}, {"category": "automation", "name": "Transf Meeting Booking Into Notion S Task With Verified Information", "description": "This workflow automatically creates a new Notion database page for each new Calendly event, populating it with the event details and enriching the contact information using Dropcontact.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1325-transf-meeting-booking-into-notion-s-task-with-verified-information/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1325-transf-meeting-booking-into-notion-s-task-with-verified-information/workflow.json", "nodeCount": 3, "nodeNames": ["Notion", "Dropcontact", "<PERSON><PERSON><PERSON>"], "tags": ["automation", "booking", "information", "meeting", "notion", "s", "task", "transf", "verified"]}, {"category": "automation", "name": "Export N8N Cloud Execution Data To Csv", "description": "This workflow allows users to easily retrieve and export all n8n workflow executions as a CSV file, providing a convenient way to analyze and monitor workflow performance.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2295-export-n8n-cloud-execution-data-to-csv/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2295-export-n8n-cloud-execution-data-to-csv/workflow.json", "nodeCount": 7, "nodeNames": ["When clicking ‘Test workflow’", "n8n | Get all executions", "Convert to CSV", "<PERSON><PERSON>", "Sticky Note1", "No Operation", "do nothing", "Sticky Note2"], "tags": ["automation", "cloud", "csv", "data", "execution", "export", "n8n"]}, {"category": "automation", "name": "Sql To Xml Export With Xsl Template Formatting", "description": "This workflow retrieves a set of random products from a MySQL database, formats the data into an XML structure, and serves it as a dynamic web page with an associated XSL stylesheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1947-sql-to-xml-export-with-xsl-template-formatting/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1947-sql-to-xml-export-with-xsl-template-formatting/workflow.json", "nodeCount": 15, "nodeNames": ["Webhook", "Show 16 random products", "Define file structure", "Concatenate Items", "Convert to XML", "Create HTML", "Move Binary Data", "Respond to Webhook", "Get XSLT", "Respond to Webhook1", "Request xsl template", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3"], "tags": ["automation", "export", "formatting", "sql", "template", "xml", "xsl"]}, {"category": "automation", "name": "Insert And Retrieve Data From A Table In Stackby", "description": "This workflow allows users to create and retrieve data from a Stackby table.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/934-insert-and-retrieve-data-from-a-table-in-stackby/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/934-insert-and-retrieve-data-from-a-table-in-stackby/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Set", "Stackby", "Stackby1"], "tags": ["automation", "data", "insert", "retrieve", "stackby", "table"]}, {"category": "automation", "name": "Automate Droplet Snapshots On Digitalocean", "description": "This workflow automates the management of DigitalOcean Droplet snapshots by keeping the number of snapshots under a defined limit, deleting the oldest ones, and ensuring new snapshots are created at regular intervals.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2485-automate-droplet-snapshots-on-digitalocean/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2485-automate-droplet-snapshots-on-digitalocean/workflow.json", "nodeCount": 17, "nodeNames": ["Filter", "List Snapshots for a Droplet", "List All Droplets", "Delete a Snapshot", "Droplet Actions snapshot (n8n.optimus01.co.za)", "Runs every 48hrs", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Sticky Note6", "Sticky Note7", "Sticky Note1", "Sticky Note11", "Sticky Note14", "Sticky Note9", "Sticky Note15"], "tags": ["automate", "automation", "digitalocean", "droplet", "snapshots"]}, {"category": "automation", "name": "Set Credentials Dynamically Using Expressions", "description": "This workflow allows users to securely authenticate with the NASA API using a dynamic API key entered through a form, and then redirects them to the NASA picture of the day.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2223-set-credentials-dynamically-using-expressions/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2223-set-credentials-dynamically-using-expressions/workflow.json", "nodeCount": 7, "nodeNames": ["n8n Form Trigger", "NASA", "Respond to Webhook", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3"], "tags": ["automation", "credentials", "dynamically", "expressions", "set", "using"]}, {"category": "automation", "name": "Get The Value Of A Key From Redis", "description": "This workflow retrieves a value from a Redis database when a manual trigger is executed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/557-get-the-value-of-a-key-from-redis/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/557-get-the-value-of-a-key-from-redis/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Redis"], "tags": ["automation", "get", "key", "redis", "value"]}, {"category": "automation", "name": "Get Multiple Attachments From Gmail And Upload Them To Gdrive", "description": "This workflow automatically uploads email attachments to Google Drive, providing a convenient way to archive and access important files.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2348-get-multiple-attachments-from-gmail-and-upload-them-to-gdrive/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2348-get-multiple-attachments-from-gmail-and-upload-them-to-gdrive/workflow.json", "nodeCount": 3, "nodeNames": ["Trigger - <PERSON> Email", "attach binary data outputs", "upload files to google drive"], "tags": ["attachments", "automation", "gdrive", "get", "gmail", "multiple", "them", "upload"]}, {"category": "automation", "name": "Perform Speech To Text On Recorded Audio Clips Using Wit Ai", "description": "This workflow converts a local audio file into a format suitable for processing by a speech recognition API, allowing users to transcribe audio content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/437-perform-speech-to-text-on-recorded-audio-clips-using-wit-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/437-perform-speech-to-text-on-recorded-audio-clips-using-wit-ai/workflow.json", "nodeCount": 2, "nodeNames": ["Read Binary File", "HTTP Request"], "tags": ["ai", "audio", "automation", "clips", "perform", "recorded", "speech", "text", "using", "wit"]}, {"category": "automation", "name": "Track Changes Of Product Prices", "description": "This workflow monitors product prices on various websites, updates a local JSON file with the latest prices, and sends email notifications when a better price is found.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/837-track-changes-of-product-prices/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/837-track-changes-of-product-prices/workflow.json", "nodeCount": 25, "nodeNames": ["HTML Extract", "<PERSON><PERSON>", "getActualPrice", "fetchWeb", "FunctionItem", "Write Binary File1", "Move Binary Data1", "IF1", "checkifexists", "IF3", "SaveToFile", "JsonToBinary", "changeME", "initItem", "savedItems", "itemsToJSON", "IF", "initItem1", "IF2", "updateSavedItems", "updateSavedItems1", "cleanData", "IF4", "NotifyBetterPrice", "NotifyIncorrectPrice"], "tags": ["automation", "changes", "prices", "product", "track"]}, {"category": "automation", "name": "Get Data From Multiple Rss Feeds To Telegram", "description": "This workflow automatically retrieves and filters RSS feeds, then sends relevant updates to specific Telegram channels based on content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1554-get-data-from-multiple-rss-feeds-to-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1554-get-data-from-multiple-rss-feeds-to-telegram/workflow.json", "nodeCount": 11, "nodeNames": ["RSS Feed Read", "SplitInBatches", "<PERSON><PERSON>", "only get new RSS", "Telegram_IT", "Telegram_Security", "RSS Source", "Telegram_M365", "IF-2", "IF-1", "Clear Function"], "tags": ["automation", "data", "feeds", "get", "multiple", "rss", "telegram"]}, {"category": "automation", "name": "Extend N8N With Additional Tools", "description": "This workflow is a Telegram bot that allows users to request weather information for several European capitals, with the data displayed in an image generated using the R programming language.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1605-extend-n8n-with-additional-tools/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1605-extend-n8n-with-additional-tools/workflow.json", "nodeCount": 21, "nodeNames": ["Switch", "msg_greet", "msg_wrongcommand", "<PERSON>eg<PERSON>", "msg_getweather", "City List", "Convert API response", "Get weather data", "Spreadsheet File", "Write csv", "Filename", "msg_errorAPI", "Any errors API?", "msg_errorR", "Read Binary File", "R successful?", "<PERSON><PERSON>", "Merge1", "msg_pleasewait", "Merge2", "Run R script"], "tags": ["additional", "automation", "extend", "n8n", "tools"]}, {"category": "automation", "name": "Create A Board Lists And A Card In Wekan", "description": "This workflow automates the process of creating and managing tasks in a Wekan project management board.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/728-create-a-board-lists-and-a-card-in-wekan/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/728-create-a-board-lists-and-a-card-in-wekan/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Wekan1", "Wekan2", "Wekan3", "Wekan4"], "tags": ["automation", "board", "card", "create", "lists", "wekan"]}, {"category": "automation", "name": "Send N8N Automation Errors To A Monday Com Board", "description": "This workflow automatically creates a Monday.com board item to track and record errors that occur in a workflow, providing valuable insights for troubleshooting and process improvement.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2074-send-n8n-automation-errors-to-a-monday-com-board/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2074-send-n8n-automation-errors-to-a-monday-com-board/workflow.json", "nodeCount": 5, "nodeNames": ["Monday", "Date & Time", "<PERSON><PERSON><PERSON>", "Code", "UPDATE"], "tags": ["automation", "board", "com", "errors", "monday", "n8n", "send"]}, {"category": "automation", "name": "Rate Limiting And Waiting For External Events", "description": "This workflow sends personalized messages to customers, waits for external approval, and then continues with the rest of the workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1749-rate-limiting-and-waiting-for-external-events/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1749-rate-limiting-and-waiting-for-external-events/workflow.json", "nodeCount": 13, "nodeNames": ["On clicking 'execute'", "Note1", "Note", "Create approval URL", "Wait for external approval", "Rest of the workflow placeholder", "Customer Datastore", "SplitInBatches", "Note4", "Wait for time interval", "If - Are we Finished?", "Customer Messenger - Send URL to merchant", "Customer Messenger - Send message to client"], "tags": ["automation", "events", "external", "limiting", "rate", "waiting"]}, {"category": "automation", "name": "Invoke An Aws Lambda Function", "description": "This workflow allows users to trigger an AWS Lambda function with a single click, providing a simple and efficient way to execute custom serverless code.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/510-invoke-an-aws-lambda-function/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/510-invoke-an-aws-lambda-function/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "AWS Lambda"], "tags": ["an", "automation", "aws", "function", "invoke", "lambda"]}, {"category": "automation", "name": "Create A Table In Postgres And Insert Data", "description": "This workflow creates a simple database table and inserts a record into it.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/599-create-a-table-in-postgres-and-insert-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/599-create-a-table-in-postgres-and-insert-data/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Set", "Postgres", "Postgres1"], "tags": ["automation", "create", "data", "insert", "postgres", "table"]}, {"category": "automation", "name": "Create Update And Get A Product From Woocommerce", "description": "This workflow automates the process of creating a new product, updating its stock quantity, and retrieving the updated product details in a WooCommerce store.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/847-create-update-and-get-a-product-from-woocommerce/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/847-create-update-and-get-a-product-from-woocommerce/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "WooCommerce", "WooCommerce1", "WooCommerce2"], "tags": ["automation", "create", "get", "product", "update", "woocommerce"]}, {"category": "automation", "name": "Advanced Ai Demo Presented At Ai Developers 14 Meetup", "description": "This workflow uses AI and automation to categorize incoming emails, load PDF documents into a vector store, and provide a conversational interface to interact with the PDF content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2358-advanced-ai-demo-presented-at-ai-developers-14-meetup/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2358-advanced-ai-demo-presented-at-ai-developers-14-meetup/workflow.json", "nodeCount": 39, "nodeNames": ["Send message", "Sticky Note2", "<PERSON><PERSON>", "Execute JavaScript", "Sticky Note3", "Recursive Character Text Splitter", "Embeddings OpenAI", "Default Data Loader", "OpenAI Chat Model", "Embeddings OpenAI2", "Vector Store Retriever", "Download PDF", "PDFs to download", "Sticky Note4", "Sticky Note5", "Read Pinecone Vector Store", "Question and Answer Chain", "Sticky Note6", "Sticky Note7", "Anthropic <PERSON>", "Get calendar availability", "Appointment booking agent", "Sticky Note1", "Window Buffer Memory", "Sticky Note8", "Sticky Note9", "Sticky Note10", "Insert into Pinecone vector store", "Book appointment", "When chat message received", "Sticky Note11", "Sticky Note12", "OpenAI Chat Model1", "Add automation label", "On new email to <PERSON><PERSON>'s inbox", "Add music label", "Assign label with AI", "Webhook", "Whether email contains n8n"], "tags": ["14", "advanced", "ai", "at", "automation", "demo", "developers", "meetup", "presented"]}, {"category": "automation", "name": "De Activate N8N Workflows Using Telegram Commands", "description": "This workflow allows users to quickly activate or deactivate specific workflows in n8n through Telegram commands, providing a convenient way to manage workflows remotely.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1999-de-activate-n8n-workflows-using-telegram-commands/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1999-de-activate-n8n-workflows-using-telegram-commands/workflow.json", "nodeCount": 12, "nodeNames": ["<PERSON><PERSON>", "Keep only messages from a specific chat id", "Sticky Note1", "Deactivate the marketing workflow", "Deactivate the sales workflow", "Activate the marketing workflow", "Switch depending on content (activate)", "Sticky Note2", "Receive commands from Telegram", "Activate the sales workflow", "Switch depending on command", "Switch depending on content (deactivate)"], "tags": ["automation", "commands", "de", "n8n", "telegram", "using", "workflows"]}, {"category": "automation", "name": "Very Quick Quickstart", "description": "This workflow generates sample customer data and prepares it for further processing.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1700-very-quick-quickstart/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1700-very-quick-quickstart/workflow.json", "nodeCount": 6, "nodeNames": ["Customer Datastore", "Note", "Note1", "Note2", "When clicking \"Test Workflow\"", "Edit Fields1"], "tags": ["automation", "quick", "quickstart", "very"]}, {"category": "automation", "name": "Ai Summarize Podcast Episode And Enhance Using Wikipedia", "description": "This workflow takes a podcast transcript, summarizes it, extracts relevant topics and questions, researches and explains each topic using Wikipedia, and then sends a digest email with the summary, topics, and questions.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1956-ai-summarize-podcast-episode-and-enhance-using-wikipedia/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1956-ai-summarize-podcast-episode-and-enhance-using-wikipedia/workflow.json", "nodeCount": 19, "nodeNames": ["When clicking \"Execute Workflow\"", "Podcast Episode Transcript", "Workflow Input to JSON Document", "Recursive Character Text Splitter", "<PERSON><PERSON>", "Topics", "Summarize Transcript", "GPT 4 - Extract", "Wikipedia1", "Sticky Note1", "Send Digest", "Sticky Note3", "Format topic text & title", "Structured Output Parser", "Extract Topics & Questions", "GPT3.5 - Research", "GPT3.5 - Summarize", "Sticky Note4", "Research & Explain Topics"], "tags": ["ai", "automation", "enhance", "episode", "podcast", "summarize", "using", "wikipedia"]}, {"category": "automation", "name": "Rename A Key In N8N", "description": "This workflow allows users to set a key-value pair and then rename the key, providing a simple way to transform data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/582-rename-a-key-in-n8n/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/582-rename-a-key-in-n8n/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Set", "<PERSON><PERSON>"], "tags": ["automation", "key", "n8n", "rename"]}, {"category": "automation", "name": "<PERSON>i Ai Agent Apple Shortcuts Powered Voice Template", "description": "This workflow integrates with Apple Shortcuts to enable voice-controlled AI assistance, allowing users to ask questions and receive concise, contextual responses through <PERSON><PERSON>.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2436-siri-ai-agent-apple-shortcuts-powered-voice-template/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2436-siri-ai-agent-apple-shortcuts-powered-voice-template/workflow.json", "nodeCount": 7, "nodeNames": ["OpenAI Chat Model", "<PERSON><PERSON>", "Respond to Apple Shortcut", "Sticky Note1", "Sticky Note2", "AI Agent", "When called by Apple Shortcut"], "tags": ["agent", "ai", "apple", "automation", "powered", "shortcuts", "siri", "template", "voice"]}, {"category": "automation", "name": "Ai Data Analyst Agent For Spreadsheets With Nocodb", "description": "This workflow uses a conversational AI agent to help a user understand the structure and contents of a database table, leveraging a NoCoDb integration and OpenAI language model.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2653-ai-data-analyst-agent-for-spreadsheets-with-nocodb/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2653-ai-data-analyst-agent-for-spreadsheets-with-nocodb/workflow.json", "nodeCount": 10, "nodeNames": ["<PERSON><PERSON>", "Window Buffer Memory", "Extract_Columns", "NocoDB", "Settings", "nocodb_extract_table", "Data Analyst Agent", "<PERSON><PERSON>", "OpenAI Chat Model", "Sticky Note6"], "tags": ["agent", "ai", "analyst", "automation", "data", "nocodb", "spreadsheets"]}, {"category": "automation", "name": "Retrieve A Monday Com Row And All Data In A Single Node", "description": "This workflow pulls data from a Monday.com board, including item details, linked pulses, and related subitem details, and aggregates the data into a comprehensive JSON output.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2086-retrieve-a-monday-com-row-and-all-data-in-a-single-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2086-retrieve-a-monday-com-row-and-all-data-in-a-single-node/workflow.json", "nodeCount": 26, "nodeNames": ["GET ALL COLUMNS", "GET ALL RELATIONS", "PULL LINKEDPULSE1", "GET LINKEDPULSES1", "SPLIT LINKED PULSES1", "SPLIT SUBITEMS1", "GET EACH SUBITEM1", "GET ALL COLUMNS1", "GET ALL COLUMNS2", "Aggregate1", "PULL SUBITEMS", "GET ITEM", "GET ALL COLUMNS3", "Merge4", "Aggregate", "<PERSON><PERSON>", "Merge2", "Merge1", "Execute Workflow Trigger", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note4", "Execute Workflow", "<PERSON>", "Sticky Note3"], "tags": ["all", "automation", "com", "data", "monday", "node", "retrieve", "row", "single"]}, {"category": "automation", "name": "Search And Download Torrents Using Transmission Daemon", "description": "This workflow automatically searches for and downloads torrent files based on a title received via a webhook, and sends notifications to a Telegram chat.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1381-search-and-download-torrents-using-transmission-daemon/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1381-search-and-download-torrents-using-transmission-daemon/workflow.json", "nodeCount": 8, "nodeNames": ["Webhook", "SearchTorrent", "Start download", "IF", "Torrent not found", "Telegram1", "IF2", "Start download new token"], "tags": ["automation", "daemon", "download", "search", "torrents", "transmission", "using"]}, {"category": "automation", "name": "Receive Updates For Events In Clickup", "description": "This workflow automatically triggers actions in response to events in ClickUp, a popular project management and collaboration platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/487-receive-updates-for-events-in-clickup/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/487-receive-updates-for-events-in-clickup/workflow.json", "nodeCount": 1, "nodeNames": ["<PERSON><PERSON><PERSON><PERSON>"], "tags": ["automation", "clickup", "events", "receive", "updates"]}, {"category": "automation", "name": "Itemmatching Usage Example", "description": "This workflow retrieves customer data from a datastore, reduces the data to only the names, and then restores the email addresses for each customer.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1966-itemmatching-usage-example/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1966-itemmatching-usage-example/workflow.json", "nodeCount": 8, "nodeNames": ["When clicking \"Execute Workflow\"", "Customer Datastore (n8n training)", "Code", "<PERSON>", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3"], "tags": ["automation", "example", "itemmatching", "usage"]}, {"category": "automation", "name": "Receive Updates For Bitbucket Events", "description": "This workflow automatically triggers a process when a new commit is pushed to a Bitbucket repository, enabling efficient and streamlined code management.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/529-receive-updates-for-bitbucket-events/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/529-receive-updates-for-bitbucket-events/workflow.json", "nodeCount": 1, "nodeNames": ["Bitbucket Trigger"], "tags": ["automation", "bitbucket", "events", "receive", "updates"]}, {"category": "automation", "name": "Comparing Data With The Compare Datasets Node", "description": "This workflow compares two datasets and identifies differences between them, providing a valuable tool for data analysis and reconciliation.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1943-comparing-data-with-the-compare-datasets-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1943-comparing-data-with-the-compare-datasets-node/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking \"Execute Workflow\"", "Dataset 1", "Dataset 2", "Compare Datasets", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["automation", "compare", "comparing", "data", "datasets", "node"]}, {"category": "automation", "name": "Report N8N Workflow Errors To Telegram", "description": "This workflow sends a Telegram message when an error occurs in another workflow, providing a summary of the failed workflow and a link to the execution details.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2159-report-n8n-workflow-errors-to-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2159-report-n8n-workflow-errors-to-telegram/workflow.json", "nodeCount": 5, "nodeNames": ["On Error", "Set message", "Sticky Note3", "Telegram", "<PERSON><PERSON>"], "tags": ["automation", "errors", "n8n", "report", "telegram"]}, {"category": "automation", "name": "Easy Image Captioning With Gemini 1 5 Pro", "description": "This workflow takes an image, generates a caption for it using a language model, and then overlays the caption onto the image.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2418-easy-image-captioning-with-gemini-1-5-pro/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2418-easy-image-captioning-with-gemini-1-5-pro/workflow.json", "nodeCount": 16, "nodeNames": ["When clicking ‘Test workflow’", "Google Gemini Chat Model", "Structured Output Parser", "Get Info", "Resize For AI", "Calculate Positioning", "Apply Caption to Image", "<PERSON><PERSON>", "Merge Image & Caption", "Merge Caption & Positions", "Get Image", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Image Captioning Agent"], "tags": ["1", "5", "automation", "captioning", "easy", "gemini", "image", "pro"]}, {"category": "automation", "name": "Count The Items Returned By A Node", "description": "This workflow retrieves customer data from a training datastore and sets the count of retrieved items as a workflow variable.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1913-count-the-items-returned-by-a-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1913-count-the-items-returned-by-a-node/workflow.json", "nodeCount": 3, "nodeNames": ["When clicking \"Execute Workflow\"", "Customer Datastore (n8n training)", "Set"], "tags": ["automation", "count", "items", "node", "returned"]}, {"category": "automation", "name": "Serve A Static Html Page When A Link Is Accessed", "description": "This workflow sets up a webhook endpoint that responds with a simple HTML page when triggered.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1306-serve-a-static-html-page-when-a-link-is-accessed/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1306-serve-a-static-html-page-when-a-link-is-accessed/workflow.json", "nodeCount": 2, "nodeNames": ["Respond to Webhook", "Webhook"], "tags": ["accessed", "automation", "html", "link", "page", "serve", "static", "when"]}, {"category": "automation", "name": "Create Update And Get A Post Via Discourse", "description": "This workflow automates the process of creating, updating, and retrieving Discourse posts using the Discourse API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/930-create-update-and-get-a-post-via-discourse/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/930-create-update-and-get-a-post-via-discourse/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Discourse", "Discourse1", "Discourse2"], "tags": ["automation", "create", "discourse", "get", "post", "update", "via"]}, {"category": "automation", "name": "Receive Updates For The Position Of The Iss And Push It To A Firbase", "description": "This workflow periodically retrieves the current location of the International Space Station (ISS) and stores it in a Google Firebase Realtime Database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/787-receive-updates-for-the-position-of-the-iss-and-push-it-to-a-firbase/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/787-receive-updates-for-the-position-of-the-iss-and-push-it-to-a-firbase/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON>", "HTTP Request", "Set", "Google Cloud Realtime Database"], "tags": ["automation", "firbase", "iss", "it", "position", "push", "receive", "updates"]}, {"category": "automation", "name": "Split Out Binary Data", "description": "This workflow downloads a compressed file, decompresses it, and then splits the binary data into individual items, making it easier to process the contents.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1621-split-out-binary-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1621-split-out-binary-data/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Split Up Binary Data", "Download Example Data", "Decompress Example Data", "Note", "Note1"], "tags": ["automation", "binary", "data", "out", "split"]}, {"category": "automation", "name": "Write Json To Disk Binary", "description": "This workflow creates a binary file containing a JSON object with example data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/101-write-json-to-disk-binary/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/101-write-json-to-disk-binary/workflow.json", "nodeCount": 3, "nodeNames": ["Write Binary File", "Make Binary", "Create Example Data"], "tags": ["automation", "binary", "disk", "json", "write"]}, {"category": "automation", "name": "Rss Feed For Ard Audiothek Podcasts", "description": "This workflow generates an RSS feed for a podcast by extracting episode details from a website.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1799-rss-feed-for-ard-audiothek-podcasts/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1799-rss-feed-for-ard-audiothek-podcasts/workflow.json", "nodeCount": 11, "nodeNames": ["On clicking 'execute'", "Get overview page", "Extract links", "Remove duplicate links", "Split out lists", "Get episode page", "Extract script", "Parse JSON", "Define feed items", "Feed", "Serve feed"], "tags": ["ard", "audiothek", "automation", "feed", "podcasts", "rss"]}, {"category": "automation", "name": "Deploy Site When New Content Gets Added", "description": "This workflow enables automatic deployment of content to a Netlify site triggered by a webhook.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1254-deploy-site-when-new-content-gets-added/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1254-deploy-site-when-new-content-gets-added/workflow.json", "nodeCount": 2, "nodeNames": ["Webhook", "Netlify"], "tags": ["added", "automation", "content", "deploy", "gets", "new", "site", "when"]}, {"category": "automation", "name": "Create An Image Procedurally Using Bannerbear", "description": "This workflow allows users to quickly generate custom images using the Bannerbear API, providing a simple and efficient way to create visually appealing content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/544-create-an-image-procedurally-using-bannerbear/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/544-create-an-image-procedurally-using-bannerbear/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON><PERSON>"], "tags": ["an", "automation", "bannerbear", "create", "image", "procedurally", "using"]}, {"category": "automation", "name": "Getting Started", "description": "This workflow sets a variable every 2 hours and stores its value in the workflow data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/getting-started/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/getting-started/workflow.json", "nodeCount": 3, "nodeNames": ["FunctionItem", "2 hours Interval", "Set"], "tags": ["automation", "getting", "started"]}, {"category": "automation", "name": "Telegram Echo Bot", "description": "This workflow creates a Telegram bot that echoes back the JSON content of any message, file, or other input sent to it, making it useful for debugging and learning about the Telegram platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2007-telegram-echo-bot/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2007-telegram-echo-bot/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "Listen for incoming events", "Send back the JSON content of the message"], "tags": ["automation", "bot", "echo", "telegram"]}, {"category": "automation", "name": "Get All Posts From Wordpress", "description": "This workflow allows users to retrieve a list of all posts from a WordPress website with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/546-get-all-posts-from-wordpress/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/546-get-all-posts-from-wordpress/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Wordpress"], "tags": ["all", "automation", "get", "posts", "wordpress"]}, {"category": "automation", "name": "Split In Batches Node Currentrunindex Example", "description": "This workflow generates a set of 10 items, splits them into batches of 1, and checks if the current batch index is equal to 5, at which point it sets a \"Loop Ended\" message.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/996-split-in-batches-node-currentrunindex-example/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/996-split-in-batches-node-currentrunindex-example/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Function", "SplitInBatches", "IF", "Set"], "tags": ["automation", "batches", "currentrunindex", "example", "node", "split"]}, {"category": "automation", "name": "Merge Greetings With The Users Based On The Language", "description": "This workflow generates sample data with names, greetings, and languages, and then merges the data to create a combined output.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/655-merge-greetings-with-the-users-based-on-the-language/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/655-merge-greetings-with-the-users-based-on-the-language/workflow.json", "nodeCount": 4, "nodeNames": ["When clicking ‘Test workflow’", "Sample data (name + language)", "Sample data (greeting + language)", "Merge (name + language + greeting)"], "tags": ["automation", "based", "greetings", "language", "merge", "users"]}, {"category": "automation", "name": "Create A Table In Mysql And Insert Data", "description": "This workflow creates a simple MySQL table, inserts a row with a predefined name, and then retrieves the data from the table.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/598-create-a-table-in-mysql-and-insert-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/598-create-a-table-in-mysql-and-insert-data/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "MySQL", "MySQL1", "Set"], "tags": ["automation", "create", "data", "insert", "mysql", "table"]}, {"category": "automation", "name": "Execute An Sql Query In Microsoft Sql", "description": "This workflow allows users to execute custom SQL queries against a Microsoft SQL Server database on-demand.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/479-execute-an-sql-query-in-microsoft-sql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/479-execute-an-sql-query-in-microsoft-sql/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Microsoft SQL"], "tags": ["an", "automation", "execute", "microsoft", "query", "sql"]}, {"category": "automation", "name": "Access Data From Bubble Application", "description": "This workflow allows users to trigger an HTTP request to a specific API endpoint with a single click, providing a simple way to interact with external data sources.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/879-access-data-from-bubble-application/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/879-access-data-from-bubble-application/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "HTTP Request"], "tags": ["access", "application", "automation", "bubble", "data"]}, {"category": "automation", "name": "Create A New Issue In Jira", "description": "This workflow allows users to quickly create a new Jira issue with a predefined summary and issue type by triggering the workflow manually.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/459-create-a-new-issue-in-jira/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/459-create-a-new-issue-in-jira/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>"], "tags": ["automation", "create", "issue", "jira", "new"]}, {"category": "automation", "name": "Translate Instructions Using Lingvanex", "description": "This workflow generates a random cocktail recipe, translates the instructions to Italian, and presents the result to the user.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/797-translate-instructions-using-lingvanex/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/797-translate-instructions-using-lingvanex/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "LingvaNex", "HTTP Request"], "tags": ["automation", "instructions", "lingvanex", "translate", "using"]}, {"category": "automation", "name": "Post Rss Feed Items From Yesterday To Slack", "description": "This workflow automatically fetches the latest blog posts from the n8n.io RSS feed, filters them to include only those published since yesterday, and then posts a summary of the new posts to a Slack channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1469-post-rss-feed-items-from-yesterday-to-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1469-post-rss-feed-items-from-yesterday-to-slack/workflow.json", "nodeCount": 6, "nodeNames": ["Build our message", "Every Morning", "Get Yesterdays Date", "Get the RSS Feed", "If it was published after yesterday", "Post to Slack"], "tags": ["automation", "feed", "items", "post", "rss", "slack", "yesterday"]}, {"category": "automation", "name": "Compare Sql Datasets", "description": "This workflow compares sales data from two different time periods, allowing users to identify trends and insights that can inform business decisions.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1944-compare-sql-datasets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1944-compare-sql-datasets/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Execute Workflow\"", "Compare Datasets", "Orders from 2003 and 2004", "Orders from 2004 and 2005", "Change ordercount"], "tags": ["automation", "compare", "datasets", "sql"]}, {"category": "automation", "name": "Authenticate A User In A Workflow With Openid Connect", "description": "This workflow implements a secure OAuth 2.0 authentication flow, allowing users to log in to your application using an external identity provider.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1997-authenticate-a-user-in-a-workflow-with-openid-connect/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1997-authenticate-a-user-in-a-workflow-with-openid-connect/workflow.json", "nodeCount": 15, "nodeNames": ["Webhook", "Code", "user info", "send back login page", "IF token is present", "Welcome page", "send back welcome page", "IF user info ok", "login form", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Set variables : auth", "token", "userinfo", "client id", "scope", "IF we have code in URI and not in PKCE mode", "get access_token from /token endpoint with code"], "tags": ["authenticate", "automation", "connect", "openid", "user"]}, {"category": "automation", "name": "Create A Website Screenshot And Send Via Telegram Channel", "description": "This workflow captures a screenshot of a website and sends it to a Telegram chat upon manual execution.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/858-create-a-website-screenshot-and-send-via-telegram-channel/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/858-create-a-website-screenshot-and-send-via-telegram-channel/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Telegram", "uProc"], "tags": ["automation", "channel", "create", "screenshot", "send", "telegram", "via", "website"]}, {"category": "automation", "name": "Execute Set Node Based On Function Output", "description": "This workflow generates two JSON objects with different IDs, then uses an IF node to conditionally set the value of a \"name\" field based on the ID.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/581-execute-set-node-based-on-function-output/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/581-execute-set-node-based-on-function-output/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Function", "IF", "Set", "Set1"], "tags": ["automation", "based", "execute", "function", "node", "output", "set"]}, {"category": "automation", "name": "Create An Issue On Gitlab On Every Github Release", "description": "This workflow automatically checks for new releases in a GitHub repository, compares them to existing issues in a GitLab repository, and creates a new issue if a release is found without a corresponding issue.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1349-create-an-issue-on-gitlab-on-every-github-release/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1349-create-an-issue-on-gitlab-on-every-github-release/workflow.json", "nodeCount": 6, "nodeNames": ["Get latest release", "<PERSON><PERSON>", "<PERSON><PERSON>", "No issue for release?", "Create issue", "List issues"], "tags": ["an", "automation", "create", "every", "github", "gitlab", "issue", "release"]}, {"category": "automation", "name": "Create A Table In Cratedb And Insert Data", "description": "This workflow creates a table in a CrateDB database, sets the values for the table columns, and then inserts the data into the table.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/597-create-a-table-in-cratedb-and-insert-data/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/597-create-a-table-in-cratedb-and-insert-data/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "CrateDB", "CrateDB1", "Set"], "tags": ["automation", "cratedb", "create", "data", "insert", "table"]}, {"category": "automation", "name": "Create A New Team For A Project In Notion", "description": "This workflow creates a new project and user in Notion, and updates the user's associated semesters and projects.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1374-create-a-new-team-for-a-project-in-notion/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1374-create-a-new-team-for-a-project-in-notion/workflow.json", "nodeCount": 23, "nodeNames": ["Get Team Members", "<PERSON><PERSON>", "Query Current Semester", "Select Semester ID and Projects Count", "Use Default Name if Not Specified", "Select Project Showcase ID", "Get Project Name & Idea", "Create Project", "If user exists", "Create User", "Query for User", "Merge1", "Merge2", "Update Semester for User", "Query User", "Select Semester ID", "Update Project Relation", "Merge3", "Concatenate SemesterIDs", "Concatenate ProjectIDs", "Merge4", "<PERSON> Email", "Team Creation"], "tags": ["automation", "create", "new", "notion", "project", "team"]}, {"category": "automation", "name": "Bookmarking Urls In Your Browser And Save Them To Notion", "description": "This workflow allows users to easily save bookmarks to a Notion database using a simple webhook trigger.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2038-bookmarking-urls-in-your-browser-and-save-them-to-notion/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2038-bookmarking-urls-in-your-browser-and-save-them-to-notion/workflow.json", "nodeCount": 4, "nodeNames": ["POST", "Notion", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["automation", "bookmarking", "browser", "notion", "save", "them", "urls"]}, {"category": "automation", "name": "Send An Sms To A Number Whenever You Go Out", "description": "This workflow sends a text message to a specified phone number whenever a \"Leaving Home\" Pushcut action is triggered.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/870-send-an-sms-to-a-number-whenever-you-go-out/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/870-send-an-sms-to-a-number-whenever-you-go-out/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "tags": ["an", "automation", "go", "number", "out", "send", "sms", "whenever", "you"]}, {"category": "automation", "name": "Working With Dates And Times", "description": "This workflow demonstrates different ways to work with dates and times in n8n, providing a practical tool for users to manage and format date-related data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1744-working-with-dates-and-times/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1744-working-with-dates-and-times/workflow.json", "nodeCount": 9, "nodeNames": ["On clicking 'execute'", "Note", "Note3", "12 Hours from now", "Note1", "Note4", "Set times", "Edit times", "Format - MMMM DD YY"], "tags": ["automation", "dates", "times", "working"]}, {"category": "automation", "name": "Monday Com Useful Utilities", "description": "This workflow retrieves information from a Monday.com board, extracts and processes related data, and uploads files to the board.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2073-monday-com-useful-utilities/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2073-monday-com-useful-utilities/workflow.json", "nodeCount": 14, "nodeNames": ["When clicking \"Test workflow\"", "PULL SUBITEMS", "SPLIT SUBITEMS", "GET EACH SUBITEM", "MONDAY UPLOAD", "Convert to File", "<PERSON><PERSON>", "PULL LINKEDPULSE", "GET ITEM", "GET LINKEDPULSES", "GET BOARD RELATION", "COLUMN BY NAME", "COLUMN BY ID", "SPLIT LINKED PULSES"], "tags": ["automation", "com", "monday", "useful", "utilities"]}, {"category": "automation", "name": "Scrape And Store Data From Multiple Website Pages", "description": "This workflow scrapes SWIFT code data from a website, normalizes the country names, and stores the data in a MongoDB database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1073-scrape-and-store-data-from-multiple-website-pages/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1073-scrape-and-store-data-from-multiple-website-pages/workflow.json", "nodeCount": 23, "nodeNames": ["On clicking 'execute'", "HTTP Request", "HTML Extract", "SplitInBatches", "HTTP Request1", "HTML Extract1", "MongoDB1", "uProc", "Prepare Documents", "More Countries", "Set Page to <PERSON><PERSON><PERSON>", "More Pages", "Set More Pages", "Set", "Generate filename", "Read Binary File", "File exists?", "Write Binary File", "Read Binary File1", "Wait", "Prepare countries", "Create Directory", "MongoDB"], "tags": ["automation", "data", "multiple", "pages", "scrape", "store", "website"]}, {"category": "automation", "name": "Get A Pipeline In Circleci", "description": "This workflow allows users to trigger a CircleCI build on-demand, providing a simple way to initiate CI/CD processes.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/454-get-a-pipeline-in-circleci/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/454-get-a-pipeline-in-circleci/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "CircleCI"], "tags": ["automation", "<PERSON><PERSON>", "get", "pipeline"]}, {"category": "automation", "name": "Add Product Ideas To Notion Via A Slack Command", "description": "This workflow allows Slack users to submit new ideas to a Notion database by using a Slack slash command, and provides a helpful message with instructions for adding more details to the idea.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2140-add-product-ideas-to-notion-via-a-slack-command/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2140-add-product-ideas-to-notion-via-a-slack-command/workflow.json", "nodeCount": 8, "nodeNames": ["Webhook", "<PERSON><PERSON>", "Notion", "Switch", "Hidden message to add feature details", "Sticky Note1", "Set me up", "Sticky Note2"], "tags": ["add", "automation", "command", "ideas", "notion", "product", "slack", "via"]}, {"category": "automation", "name": "Notify A Team Channel About New Software Releases Via Slack And Github", "description": "This workflow automatically sends a notification to a Slack channel whenever a new release is available in a specific GitHub repository.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/371-notify-a-team-channel-about-new-software-releases-via-slack-and-github/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/371-notify-a-team-channel-about-new-software-releases-via-slack-and-github/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON>ck", "<PERSON><PERSON><PERSON>"], "tags": ["about", "automation", "channel", "github", "new", "notify", "releases", "slack", "software", "team", "via"]}, {"category": "automation", "name": "Receive Server Sent Events", "description": "This workflow uses the SSE Trigger node to monitor a specified URL for real-time updates, enabling users to respond to changes or events in a timely manner.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/639-receive-server-sent-events/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/639-receive-server-sent-events/workflow.json", "nodeCount": 1, "nodeNames": ["<PERSON><PERSON>"], "tags": ["automation", "events", "receive", "sent", "server"]}, {"category": "automation", "name": "Create Transcription Jobs Using Aws Transcribe", "description": "This workflow automatically transcribes audio files stored in an AWS S3 bucket using the AWS Transcribe service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1111-create-transcription-jobs-using-aws-transcribe/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1111-create-transcription-jobs-using-aws-transcribe/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "AWS Transcribe", "AWS S3"], "tags": ["automation", "aws", "create", "jobs", "transcribe", "transcription", "using"]}, {"category": "automation", "name": "Move Data Between Json And Spreadsheets", "description": "This workflow retrieves random user data, converts it to a CSV file, and then sends the CSV file as an attachment in an email while also appending the data to a Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1739-move-data-between-json-and-spreadsheets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1739-move-data-between-json-and-spreadsheets/workflow.json", "nodeCount": 14, "nodeNames": ["HTTP Request", "Google Sheets", "Set", "Spreadsheet File", "Spreadsheet File1", "Write Binary File", "Move Binary Data1", "Gmail1", "Google Sheets2", "Move Binary Data2", "Note", "Note1", "Note2", "Note3"], "tags": ["automation", "between", "data", "json", "move", "spreadsheets"]}, {"category": "automation", "name": "Build A Self Hosted Url Shortener With A Dashboard", "description": "This workflow generates short URLs, tracks their usage, and provides a dashboard with analytics.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1093-build-a-self-hosted-url-shortener-with-a-dashboard/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1093-build-a-self-hosted-url-shortener-with-a-dashboard/workflow.json", "nodeCount": 26, "nodeNames": ["Webhook", "Extract URL", "Check URL", "Crypto", "Airtable", "Set ID", "shortUrl", "longUrl", "Find by ID", "Already exists ?", "Set Output", "Set Error output", "Set Output1", "Set input", "Webhook1", "Set Error output1", "Check Id", "Find by ID1", "Already exists ?1", "Set Output2", "Extract Id", "404 Error", "Update clicks", "Prepare clicks count", "Webhook2", "Find by ID2", "Extract stats", "Set dashboard"], "tags": ["automation", "build", "dashboard", "hosted", "self", "shortener", "url"]}, {"category": "automation", "name": "Send The Astronomy Picture Of The Day Daily To A Telegram Channel", "description": "This workflow automatically retrieves the latest image from NASA's Astronomy Picture of the Day (APOD) and sends it to a Telegram chat.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/828-send-the-astronomy-picture-of-the-day-daily-to-a-telegram-channel/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/828-send-the-astronomy-picture-of-the-day-daily-to-a-telegram-channel/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "NASA", "Telegram"], "tags": ["astronomy", "automation", "channel", "daily", "day", "picture", "send", "telegram"]}, {"category": "automation", "name": "Extract And Store Text From Chat Images Using Aws S3", "description": "This workflow automates the process of extracting text from images uploaded via Telegram, storing the images in AWS S3, and appending the extracted text data to an Airtable database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1393-extract-and-store-text-from-chat-images-using-aws-s3/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1393-extract-and-store-text-from-chat-images-using-aws-s3/workflow.json", "nodeCount": 4, "nodeNames": ["AWS Textract", "<PERSON>eg<PERSON>", "Airtable", "AWS S3"], "tags": ["automation", "aws", "chat", "extract", "images", "s3", "store", "text", "using"]}, {"category": "automation", "name": "Detect And Store The Information About A Purchase Using The Image Of A Receipt", "description": "This workflow automates the process of capturing and storing expense receipts, providing valuable insights for expense tracking and management.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/739-detect-and-store-the-information-about-a-purchase-using-the-image-of-a-receipt/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/739-detect-and-store-the-information-about-a-purchase-using-the-image-of-a-receipt/workflow.json", "nodeCount": 4, "nodeNames": ["Webhook", "<PERSON><PERSON>", "Airtable", "Set"], "tags": ["about", "automation", "detect", "image", "information", "purchase", "receipt", "store", "using"]}, {"category": "automation", "name": "Back Up Your N8N Workflows To Github", "description": "This workflow automatically backs up all n8n workflows to a GitHub repository on a daily schedule, with the ability to detect and handle new, modified, or unchanged workflows.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1534-back-up-your-n8n-workflows-to-github/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1534-back-up-your-n8n-workflows-to-github/workflow.json", "nodeCount": 26, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Execute Workflow Trigger", "n8n", "Return", "Get File", "If file too large", "<PERSON><PERSON>", "isDiffOrNew", "Check Status", "Same file - Do nothing", "File is different", "File is new", "Create new file", "Edit existing file", "Loop Over Items", "Schedule Trigger", "Create sub path", "Sticky Note1", "Sticky Note2", "Starting Message", "Execute Workflow", "Completed Notification", "Failed Flows", "Get file data", "Config"], "tags": ["automation", "back", "github", "n8n", "up", "workflows"]}, {"category": "automation", "name": "Receive Updates When An Event Occurs In Taiga", "description": "This workflow automatically triggers an action in response to changes in a Taiga project, enabling efficient project management and task tracking.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/686-receive-updates-when-an-event-occurs-in-taiga/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/686-receive-updates-when-an-event-occurs-in-taiga/workflow.json", "nodeCount": 1, "nodeNames": ["<PERSON><PERSON>"], "tags": ["an", "automation", "event", "occurs", "receive", "taiga", "updates", "when"]}, {"category": "automation", "name": "Generate Seo Seed Keywords Using Ai", "description": "This workflow uses an AI-powered tool to generate a list of 15-20 seed keywords based on a user's ideal customer profile, providing a foundation for a comprehensive SEO strategy.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2473-generate-seo-seed-keywords-using-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2473-generate-seo-seed-keywords-using-ai/workflow.json", "nodeCount": 15, "nodeNames": ["<PERSON><PERSON>", "Sticky Note13", "Anthropic <PERSON>", "Split Out", "Sticky Note12", "Sticky Note17", "Sticky Note11", "Set Ideal Customer Profile (ICP)", "Aggregate for AI node", "Sticky Note14", "Sticky Note16", "AI Agent", "Connect to your own database", "Sticky Note15", "When clicking ‘Test workflow’"], "tags": ["ai", "automation", "generate", "keywords", "seed", "seo", "using"]}, {"category": "automation", "name": "Pattern For Parallel Sub Workflow Execution Followed By Wait For All Loop", "description": "This workflow orchestrates the asynchronous execution of multiple sub-workflows and waits for them to complete before continuing the main workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2536-pattern-for-parallel-sub-workflow-execution-followed-by-wait-for-all-loop/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2536-pattern-for-parallel-sub-workflow-execution-followed-by-wait-for-all-loop/workflow.json", "nodeCount": 18, "nodeNames": ["When clicking ‘Test workflow’", "Loop Over Items", "Webhook Callback Wait", "Update finishedSet", "Initialize finishedSet", "Simulate Multi-Item for Parallel Processing", "If All Finished", "Start Sub-Workflow via Webhook", "Acknowledge Finished", "<PERSON><PERSON>", "Sticky Note1", "Continue Workflow (noop)", "Sticky Note3", "Sticky Note2", "Wait", "Call Resume on Parent Workflow", "Respond to Webhook", "Webhook"], "tags": ["all", "automation", "execution", "followed", "loop", "parallel", "pattern", "sub", "wait"]}, {"category": "automation", "name": "<PERSON> N8N Personal Assistant", "description": "This workflow automatically generates an excuse email and sends it to the original sender, while also notifying the recipient's colleague on Slack.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/632-nathan-your-n8n-personal-assistant/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/632-nathan-your-n8n-personal-assistant/workflow.json", "nodeCount": 9, "nodeNames": ["<PERSON> Harvey's Email", "Who Is The Email From?", "Read Excuses File", "Retrieve Excuses Spreadsheet Data", "Generate Excuse", "Merge Excuse and Mail Data", "Send Email", "<PERSON><PERSON><PERSON> (Louis)", "<PERSON><PERSON><PERSON> (General)"], "tags": ["assistant", "automation", "n8n", "nathan", "personal"]}, {"category": "automation", "name": "Visualize Your Sql Agent Queries With Openai And Quickchart Io", "description": "This workflow combines an AI-powered SQL agent with dynamic chart generation capabilities to provide users with data-driven insights and visualizations.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2559-visualize-your-sql-agent-queries-with-openai-and-quickchart-io/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2559-visualize-your-sql-agent-queries-with-openai-and-quickchart-io/workflow.json", "nodeCount": 16, "nodeNames": ["OpenAI Chat Model", "Execute Workflow", "Execute \"Generate a chart\" tool", "OpenAI - Generate Chart definition with Structured Output", "Set response", "When chat message received", "Set Text output", "Set Text + Chart output", "AI Agent", "Window Buffer Memory", "Sticky Note1", "<PERSON><PERSON>", "Sticky Note2", "OpenAI Chat Model Classifier", "Sticky Note3", "Text Classifier - Chart required?"], "tags": ["agent", "automation", "io", "openai", "queries", "quickchart", "sql", "visualize"]}, {"category": "automation", "name": "Reddit Ai Digest", "description": "This workflow automatically scans Reddit for posts related to the n8n workflow automation tool, analyzes the content using OpenAI, and generates a summary and categorization of the posts.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1895-reddit-ai-digest/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1895-reddit-ai-digest/workflow.json", "nodeCount": 15, "nodeNames": ["When clicking \"Execute Workflow\"", "Reddit", "Sticky Note1", "Set", "IF", "Sticky Note2", "IF1", "<PERSON><PERSON>", "Merge1", "SetFinal", "Sticky Note3", "OpenAI Summary", "OpenAI Classify", "OpenAI Summary Backup", "<PERSON><PERSON>"], "tags": ["ai", "automation", "digest", "reddit"]}, {"category": "automation", "name": "Save Your Workflows Into A Github Repository", "description": "This workflow automatically backs up and synchronizes n8n workflows to a GitHub repository, detecting and handling changes to ensure consistency.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/817-save-your-workflows-into-a-github-repository/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/817-save-your-workflows-into-a-github-repository/workflow.json", "nodeCount": 16, "nodeNames": ["On clicking 'execute'", "dataArray", "N8N Workflows", "GitHub", "<PERSON><PERSON>", "N8N Workflow Detail", "github_status", "same", "different", "new", "GitHub Edit", "GitHub Create", "isDiffOrNew", "Daily @ 20:00", "OneAtATime", "Globals"], "tags": ["automation", "github", "repository", "save", "workflows"]}, {"category": "automation", "name": "Manage Changes Using The Git Node", "description": "This workflow automates the process of creating a new Git commit and pushing it to a repository with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1115-manage-changes-using-the-git-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1115-manage-changes-using-the-git-node/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Git", "Git1", "Git2", "Git3"], "tags": ["automation", "changes", "git", "manage", "node", "using"]}, {"category": "automation", "name": "Execute Set Node Based On Function Output", "description": "This workflow generates a set of JSON objects with unique IDs and sets their names based on the ID value using a switch statement.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/688-execute-set-node-based-on-function-output/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/688-execute-set-node-based-on-function-output/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Function", "Set", "Set1", "Switch", "Set2", "NoOp"], "tags": ["automation", "based", "execute", "function", "node", "output", "set"]}, {"category": "automation", "name": "Handle Errors From A Different Workflow", "description": "This workflow sends an email notification whenever an error occurs in the n8n workflow, providing details about the error and the workflow name.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/8-handle-errors-from-a-different-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/8-handle-errors-from-a-different-workflow/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON><PERSON>", "Mailgun"], "tags": ["automation", "different", "errors", "handle"]}, {"category": "automation", "name": "Get Only New Rss With Photo", "description": "This workflow automatically fetches and filters new articles from an RSS feed, extracting relevant data and images to provide a curated content feed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1309-get-only-new-rss-with-photo/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1309-get-only-new-rss-with-photo/workflow.json", "nodeCount": 5, "nodeNames": ["<PERSON><PERSON>", "RSS Feed Read", "Extract Image1", "Filter RSS Data", "Only get new RSS1"], "tags": ["automation", "get", "new", "only", "photo", "rss"]}, {"category": "automation", "name": "Create A New Card In Trello", "description": "This workflow allows users to create a new Trello card with a predefined title and description by manually triggering the workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/461-create-a-new-card-in-trello/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/461-create-a-new-card-in-trello/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Trello"], "tags": ["automation", "card", "create", "new", "trello"]}, {"category": "automation", "name": "Plex Automatic Qbittorent Throttler", "description": "This workflow automatically manages the throttling of a qBittorrent client based on media playback events (resume, play, pause, stop).", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1415-plex-automatic-qbittorent-throttler/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1415-plex-automatic-qbittorent-throttler/workflow.json", "nodeCount": 21, "nodeNames": ["Webhook", "Switch", "Resume", "Check if Local", "Play", "Don't Do Anything", "Pause", "Stop", "Get QB Cookie", "Get QB Cookie1", "Global Variables", "Check Throttle State", "Check if Throttled", "Do Nothing", "Check Throttle State2", "Check if Throttled1", "Do Nothing1", "Throttle Connection", "Resume Downloads", "Disable Throttle", "Enable Throttle"], "tags": ["automatic", "automation", "plex", "qbittorent", "throttler"]}, {"category": "automation", "name": "Standup Bot 3 4 Override Config", "description": "This workflow allows users to save a JSON configuration file to a local file system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1474-standup-bot-3-4-override-config/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1474-standup-bot-3-4-override-config/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Write Binary File", "Move Binary Data"], "tags": ["3", "4", "automation", "bot", "config", "override", "standup"]}, {"category": "automation", "name": "Get All The Stories And Publish Them In Storyblok", "description": "This workflow allows users to publish Storyblok content with a single click, streamlining the content publishing process.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/768-get-all-the-stories-and-publish-them-in-storyblok/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/768-get-all-the-stories-and-publish-them-in-storyblok/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Storyblok", "Storyblok1"], "tags": ["all", "automation", "get", "publish", "stories", "storyblok", "them"]}, {"category": "automation", "name": "Openai Examples Chatgpt Dalle 2 Whisper 1 5 In 1", "description": "This workflow uses OpenAI's language models (GPT-3 and DALL-E) to transcribe audio, summarize text, translate to German, generate HTML with SVG images, and provide quick email responses.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1900-openai-examples-chatgpt-dalle-2-whisper-1-5-in-1/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1900-openai-examples-chatgpt-dalle-2-whisper-1-5-in-1/workflow.json", "nodeCount": 27, "nodeNames": ["When clicking \"Execute Workflow\"", "davinci-003-complete", "ChatGPT-ex2", "davinci-003-edit", "ChatGPT-ex1.1", "ChatGPT-ex1.2", "Text-example", "Code-ex3.1", "ChatGPT-ex3.1", "ChatGPT-ex3.2", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "DALLE-ex3.3", "Sticky Note6", "ChatGPT-ex4", "Set-ex4", "HTML-ex4", "Sticky Note7", "Sticky Note8", "ChatGPT-ex", "LoadMP3", "Whisper-transcribe", "Sticky Note9"], "tags": ["1", "2", "5", "automation", "chatgpt", "dalle", "examples", "openai", "whisper"]}, {"category": "automation", "name": "Share Jokes On Twitter Automatically", "description": "This workflow automatically retrieves a random image joke from an API, downloads the image, and tweets it out at a scheduled time every day.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1376-share-jokes-on-twitter-automatically/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1376-share-jokes-on-twitter-automatically/workflow.json", "nodeCount": 4, "nodeNames": ["Request blablagues", "Recup image", "At 17H image jokes", "Tweet image jokes"], "tags": ["automatically", "automation", "jokes", "share", "twitter"]}, {"category": "automation", "name": "Trigger A Build Using The Travisci Node", "description": "This workflow allows users to manually trigger a Travis CI build for a specific repository and branch.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/658-trigger-a-build-using-the-travisci-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/658-trigger-a-build-using-the-travisci-node/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "TravisCI"], "tags": ["automation", "build", "node", "travis<PERSON>", "trigger", "using"]}, {"category": "automation", "name": "Create Update And Get A Task In Microsoft To Do", "description": "This workflow allows users to create, update, and view tasks in Microsoft To-Do, providing a simple and efficient way to manage their daily tasks.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1114-create-update-and-get-a-task-in-microsoft-to-do/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1114-create-update-and-get-a-task-in-microsoft-to-do/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Microsoft To Do", "Microsoft To Do1", "Microsoft To Do2"], "tags": ["automation", "create", "do", "get", "microsoft", "task", "update"]}, {"category": "automation", "name": "Send Updates About The Position Of The Iss Every Minute To A Topic In Activemq", "description": "This workflow periodically retrieves the current position of the International Space Station (ISS) and sends the data to an AMQP queue.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/762-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-activemq/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/762-send-updates-about-the-position-of-the-iss-every-minute-to-a-topic-in-activemq/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON>", "HTTP Request", "Set", "AMQP Sender"], "tags": ["about", "activemq", "automation", "every", "iss", "minute", "position", "send", "topic", "updates"]}, {"category": "automation", "name": "Join Data From Postgres And Mysql", "description": "This workflow retrieves product details, including product grade, fabric, and composition, based on a webhook trigger and generates a product label.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1363-join-data-from-postgres-and-mysql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1363-join-data-from-postgres-and-mysql/workflow.json", "nodeCount": 6, "nodeNames": ["emiti<PERSON><PERSON><PERSON><PERSON><PERSON>", "dadosProduto", "PegarConfiguracaoImpressao", "dadosRolo", "trataRetorno", "rolo<PERSON><PERSON><PERSON>"], "tags": ["automation", "data", "join", "mysql", "postgres"]}, {"category": "automation", "name": "Get All Releases In Sentry", "description": "This workflow allows users to create a new release in Sentry.io and retrieve a list of all existing releases.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/643-get-all-releases-in-sentry/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/643-get-all-releases-in-sentry/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Sentry.io", "Sentry.io1"], "tags": ["all", "automation", "get", "releases", "sentry"]}, {"category": "automation", "name": "Custom Langchain Agent Written In Javascript", "description": "This workflow leverages OpenAI's language models and custom LangChain tools to provide a conversational AI assistant that can answer questions and perform tasks.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1955-custom-langchain-agent-written-in-javascript/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1955-custom-langchain-agent-written-in-javascript/workflow.json", "nodeCount": 10, "nodeNames": ["OpenAI", "<PERSON><PERSON>", "When clicking \"Execute Workflow\"", "Set", "Set1", "Chat OpenAI", "Sticky Note1", "Custom - Wikipedia", "Custom - LLM Chain Node", "Agent"], "tags": ["agent", "automation", "custom", "javascript", "langchain", "written"]}, {"category": "automation", "name": "Create Add An Attachment And Send A Draft Using Microsoft Outlook", "description": "This workflow automates the process of creating and sending an email with an attached image using Microsoft Outlook.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/867-create-add-an-attachment-and-send-a-draft-using-microsoft-outlook/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/867-create-add-an-attachment-and-send-a-draft-using-microsoft-outlook/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Microsoft Outlook", "HTTP Request", "Microsoft Outlook1", "Microsoft Outlook2"], "tags": ["add", "an", "attachment", "automation", "create", "draft", "microsoft", "outlook", "send", "using"]}, {"category": "automation", "name": "Load Data Into Snowflake", "description": "This workflow retrieves a CSV file, processes the data, and stores it in a Snowflake database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1918-load-data-into-snowflake/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1918-load-data-into-snowflake/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Execute Workflow\"", "HTTP Request", "Spreadsheet File", "Set", "Snowflake"], "tags": ["automation", "data", "load", "snowflake"]}, {"category": "automation", "name": "Build An Endpoint To Perform Crud Operations With Multiple Http Methods", "description": "This workflow provides a comprehensive customer management system, allowing users to create, retrieve, update, and delete customer records in an Airtable database through a set of webhooks.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2490-build-an-endpoint-to-perform-crud-operations-with-multiple-http-methods/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2490-build-an-endpoint-to-perform-crud-operations-with-multiple-http-methods/workflow.json", "nodeCount": 18, "nodeNames": ["Respond to Webhook", "Respond to Webhook1", "Respond to Webhook2", "Respond to Webhook4", "<PERSON><PERSON>", "Create", "Get All", "Sticky Note1", "Sticky Note2", "Get Single", "Sticky Note3", "Airtable", "Respond to Webhook5", "Sticky Note4", "Airtable1", "Get Single1", "Webhook", "Webhook (with ID)"], "tags": ["an", "automation", "build", "crud", "endpoint", "http", "methods", "multiple", "operations", "perform"]}, {"category": "automation", "name": "Narrating Over A Video Using Multimodal Ai", "description": "This workflow takes a video, extracts frames from it, and uses a multimodal language model to generate a narration script, which is then used to create a voiceover clip that is uploaded to Google Drive.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2467-narrating-over-a-video-using-multimodal-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2467-narrating-over-a-video-using-multimodal-ai/workflow.json", "nodeCount": 21, "nodeNames": ["OpenAI Chat Model", "Capture Frames", "Split Out Frames", "Download Video", "Convert to <PERSON>ary", "When clicking ‘Test workflow’", "<PERSON><PERSON><PERSON>", "Upload to GDrive", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Stay Within Service Limits", "For Every 15 Frames", "Resize Frame", "Aggregate Frames", "Sticky Note5", "Use Text-to-Speech", "Sticky Note6", "Generate Narration Script"], "tags": ["ai", "automation", "multimodal", "narrating", "over", "using", "video"]}, {"category": "automation", "name": "Get Local Datetime Into Function Node Using Moment Js", "description": "This workflow provides the current local date and time, including detailed date and time components, when the \"execute\" button is manually triggered.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/695-get-local-datetime-into-function-node-using-moment-js/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/695-get-local-datetime-into-function-node-using-moment-js/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Get Local Datetime"], "tags": ["automation", "datetime", "function", "get", "js", "local", "moment", "node", "using"]}, {"category": "automation", "name": "Create Update And Get A Post In Ghost", "description": "This workflow automates the process of creating, updating, and retrieving blog posts on a Ghost CMS site.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/825-create-update-and-get-a-post-in-ghost/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/825-create-update-and-get-a-post-in-ghost/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Ghost", "Ghost1", "Ghost2"], "tags": ["automation", "create", "get", "ghost", "post", "update"]}, {"category": "automation", "name": "Add A Bug To Linear Via Slack Command", "description": "This workflow automates the process of creating a new issue in a project management tool (Linear) based on a bug report submitted through a Slack slash command.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2153-add-a-bug-to-linear-via-slack-command/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2153-add-a-bug-to-linear-via-slack-command/workflow.json", "nodeCount": 10, "nodeNames": ["<PERSON><PERSON>", "Hidden message to add bug details", "Create linear issue", "<PERSON><PERSON>", "Sticky Note1", "Set me up", "Get all linear teams", "Get linear labels for a team", "Set team ID", "Sticky Note2"], "tags": ["add", "automation", "bug", "command", "linear", "slack", "via"]}, {"category": "automation", "name": "Get The Job Details Using The Cortex Node", "description": "This workflow analyzes a URL for potential abuse using the Cortex threat intelligence platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/809-get-the-job-details-using-the-cortex-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/809-get-the-job-details-using-the-cortex-node/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "<PERSON>rtex", "Cortex1"], "tags": ["automation", "cortex", "details", "get", "job", "node", "using"]}, {"category": "automation", "name": "N8N Nodemation Basic Getting Started On The Workflow Canvas 1 3", "description": "This workflow periodically executes a custom function that sets two variables, and then uses the Set node to extract one of those variables and store it in the workflow's data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/18-n8n-nodemation-basic-getting-started-on-the-workflow-canvas-1-3/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/18-n8n-nodemation-basic-getting-started-on-the-workflow-canvas-1-3/workflow.json", "nodeCount": 3, "nodeNames": ["FunctionItem", "2 hours Interval", "Set"], "tags": ["1", "3", "automation", "basic", "canvas", "getting", "n8n", "nodemation", "started"]}, {"category": "automation", "name": "Demo Workflow How To Use Workflowstaticdata", "description": "This workflow manages the expiration and renewal of an access token, ensuring continuous access to a protected API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2538-demo-workflow-how-to-use-workflowstaticdata/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2538-demo-workflow-how-to-use-workflowstaticdata/workflow.json", "nodeCount": 9, "nodeNames": ["Webhook", "continue with valid token", "get new accessToken", "2. set token in static data", "Schedule Trigger", "<PERSON><PERSON>", "Sticky Note1", "if token is valid", "1. initiate static data"], "tags": ["automation", "demo", "how", "use", "workflowstaticdata"]}, {"category": "automation", "name": "Extract Post Titles From A Blog", "description": "This workflow extracts article titles and URLs from the Hacker Noon homepage, providing a simple way to stay up-to-date with the latest tech news and trends.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/434-extract-post-titles-from-a-blog/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/434-extract-post-titles-from-a-blog/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "HTTP Request", "HTML Extract", "HTML Extract1"], "tags": ["automation", "blog", "extract", "post", "titles"]}, {"category": "automation", "name": "Automate Image Validation Tasks Using Ai Vision", "description": "This workflow automates the process of verifying if a portrait photo meets the UK government's passport photo guidelines using an AI vision model.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2420-automate-image-validation-tasks-using-ai-vision/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2420-automate-image-validation-tasks-using-ai-vision/workflow.json", "nodeCount": 11, "nodeNames": ["When clicking ‘Test workflow’", "Structured Output Parser", "Photo URLs", "Photos To List", "Download Photos", "Resize For AI", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Passport Photo Validator", "Google Gemini Chat Model"], "tags": ["ai", "automate", "automation", "image", "tasks", "using", "validation", "vision"]}, {"category": "automation", "name": "Use Regex To Select Date", "description": "This workflow allows users to set a \"Close Date\" field and automatically update it based on certain conditions.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1328-use-regex-to-select-date/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1328-use-regex-to-select-date/workflow.json", "nodeCount": 6, "nodeNames": ["On clicking 'execute'", "Check for Close Date", "Set Close Date 3 Weeks Later", "NoOp", "Set Close Date", "Set Close Date To Original"], "tags": ["automation", "date", "regex", "select", "use"]}, {"category": "automation", "name": "Get All Members Of A Discord Server With A Specific Role", "description": "This workflow retrieves members from a Discord server, filters them based on a specific role, and saves their IDs to a Google Sheet, allowing for efficient tracking and management of server members.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2105-get-all-members-of-a-discord-server-with-a-specific-role/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2105-get-all-members-of-a-discord-server-with-a-specific-role/workflow.json", "nodeCount": 16, "nodeNames": ["When clicking \"Test workflow\"", "Delete ID", "SaveID", "Get ID", "<PERSON><PERSON>", "Check if we have more members left", "We're done", "Check if we have an ID", "Filter to only include members with role", "Get First 100 Members", "Get next 100 Members after last ID", "<PERSON><PERSON>", "Setup: Edit this to get started", "Webhook", "Send Response", "Sticky Note1"], "tags": ["all", "automation", "discord", "get", "members", "role", "server", "specific"]}, {"category": "automation", "name": "Tiny Tiny Rss New Stared Article Saved To Wallabag", "description": "This workflow automatically saves starred articles from a RSS feed to a Wallabag read-it-later service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1423-tiny-tiny-rss-new-stared-article-saved-to-wallabag/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1423-tiny-tiny-rss-new-stared-article-saved-to-wallabag/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "HTTP Request", "<PERSON><PERSON>", "Function", "IF", "NoOp", "<PERSON><PERSON>", "<PERSON><PERSON>", "Get stared articles", "Auth TTRss"], "tags": ["article", "automation", "new", "rss", "saved", "stared", "tiny", "wallabag"]}, {"category": "automation", "name": "Import Productboard Notes Companies And Features Into Snowflake", "description": "This workflow extracts data from Productboard, maps it to a Snowflake database, and sends a weekly update to Slack.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2576-import-productboard-notes-companies-and-features-into-snowflake/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2576-import-productboard-notes-companies-and-features-into-snowflake/workflow.json", "nodeCount": 35, "nodeNames": ["Sticky Note1", "Manual mapping feature", "get productboard companies", "Manual mapping companies", "get productboard notes", "Manual mapping notes", "Split features", "Split companies", "Split notes", "Split features in notes", "Combine Feature ID + Note ID", "get productboard features", "Update Productboard Notes", "Empty Table Productboard Notes", "[CREATE] PRODUCTBOARD_NOTES", "[CREATE] PRODUCTBOARD_COMPANIES", "Update Productboard Companies", "Manual mapping companies db", "Manual mapping notes db", "Empty Table Productboard Companies", "[CREATE] PRODUCTBOARD_NOTES_FEATURES", "Manual mapping feature note IDs db", "Update Productboard Note and Feature IDs", "Empty Table Productboard Note and Feature IDs", "Loop Over Items notes", "Loop Over Items features notes", "[CREATE] PRODUCTBOARD_FEATURES", "Empty Table Productboard Features", "Loop Over Items features", "Manual mapping features db", "Update Productboard Features", "Schedule Trigger", "<PERSON><PERSON>ck", "Count Notes Last 7 days and Unprocessed", "Sticky Note2"], "tags": ["automation", "companies", "features", "import", "notes", "productboard", "snowflake"]}, {"category": "automation", "name": "Backup Workflows To Github", "description": "This workflow automatically backs up and updates your n8n workflows in a GitHub repository, providing a reliable and versioned backup system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1222-backup-workflows-to-github/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1222-backup-workflows-to-github/workflow.json", "nodeCount": 11, "nodeNames": ["GitHub Edit", "Get Files", "Transform", "Create file", "<PERSON><PERSON>", "Get workflows", "Get workflow data", "Download Raw Content", "transform", "Daily at 23:59", "Merge1"], "tags": ["automation", "backup", "github", "workflows"]}, {"category": "automation", "name": "Add Articles To A Notion List By Accessing A Discord Slash Command", "description": "This workflow automatically extracts the title from a URL, adds the link and title to a Notion database, and sends a confirmation message to Discord.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1110-add-articles-to-a-notion-list-by-accessing-a-discord-slash-command/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1110-add-articles-to-a-notion-list-by-accessing-a-discord-slash-command/workflow.json", "nodeCount": 7, "nodeNames": ["Webhook", "HTTP Request", "Check type", "Extract Title", "Add Link to Notion", "Reply on Discord", "Register URL"], "tags": ["accessing", "add", "articles", "automation", "command", "discord", "list", "notion", "slash"]}, {"category": "automation", "name": "Get Top 5 Products On Product Hunt Every Hour", "description": "This workflow retrieves the top 5 Product Hunt projects posted in the last 24 hours, extracts key details, and sends a summary to a Discord channel on an hourly basis.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1298-get-top-5-products-on-product-hunt-every-hour/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1298-get-top-5-products-on-product-hunt-every-hour/workflow.json", "nodeCount": 5, "nodeNames": ["<PERSON><PERSON>", "Set", "GraphQL", "Item Lists", "Discord"], "tags": ["5", "automation", "every", "get", "hour", "hunt", "product", "products", "top"]}, {"category": "automation", "name": "Create Or Update A Post In Wordpress", "description": "This workflow creates a new WordPress post and then updates the content of that post.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/668-create-or-update-a-post-in-wordpress/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/668-create-or-update-a-post-in-wordpress/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Wordpress", "Wordpress1"], "tags": ["automation", "create", "or", "post", "update", "wordpress"]}, {"category": "automation", "name": "Streamline Your Zoom Meetings With Secure Automated Stripe Payments", "description": "This workflow automates the creation of a Zoom meeting, a Stripe payment link, and a Google Sheet to manage event participants, streamlining the event setup process.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2192-streamline-your-zoom-meetings-with-secure-automated-stripe-payments/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2192-streamline-your-zoom-meetings-with-secure-automated-stripe-payments/workflow.json", "nodeCount": 20, "nodeNames": ["Create Zoom meeting", "Create Stripe Product", "Config", "Send email to teacher", "Create participant list", "Add participant to list", "Send confirmation to participant", "Notify teacher", "Create payment link", "Format participant", "Format event", "Store event", "Creation Form", "On payment", "Sticky Note1", "Sticky Note2", "Sticky Note3", "if is creation flow", "<PERSON><PERSON>", "the end"], "tags": ["automated", "automation", "meetings", "payments", "secure", "streamline", "stripe", "zoom"]}, {"category": "automation", "name": "Get Execute Command Data And Transfer To Json", "description": "This workflow executes a command that returns a JSON object, parses the output, and then uses an if-statement to conditionally process the data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/156-get-execute-command-data-and-transfer-to-json/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/156-get-execute-command-data-and-transfer-to-json/workflow.json", "nodeCount": 3, "nodeNames": ["Execute Command", "IF", "To Flow Data"], "tags": ["automation", "command", "data", "execute", "get", "json", "transfer"]}, {"category": "automation", "name": "Create Update And Get An Issue On Taiga", "description": "This workflow allows users to create a new Taiga issue, update its description, and then retrieve the updated issue details.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/685-create-update-and-get-an-issue-on-taiga/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/685-create-update-and-get-an-issue-on-taiga/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Taiga", "Taiga1", "Taiga2"], "tags": ["an", "automation", "create", "get", "issue", "taiga", "update"]}, {"category": "automation", "name": "User Verification And Login Using Auth0", "description": "This workflow implements a secure user authentication process using the Auth0 platform, allowing users to log in with their existing accounts or create new ones.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2408-user-verification-and-login-using-auth0/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2408-user-verification-and-login-using-auth0/workflow.json", "nodeCount": 16, "nodeNames": ["Request Access Token", "Get Userinfo", "If", "No Code Found", "Open Auth Webpage", "<PERSON><PERSON>", "Set Application Details", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Set Application Details1", "/login", "/receive-token", "Sticky Note7"], "tags": ["auth0", "automation", "login", "user", "using", "verification"]}, {"category": "automation", "name": "Send Airtable Data As Tasks To Trello", "description": "This workflow automates the process of creating a Trello card with a custom-designed banner for conference speakers who have a high total score in Airtable.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/385-send-airtable-data-as-tasks-to-trello/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/385-send-airtable-data-as-tasks-to-trello/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Airtable", "<PERSON><PERSON><PERSON>", "Trello"], "tags": ["airtable", "as", "automation", "data", "send", "tasks", "trello"]}, {"category": "automation", "name": "Receive Updates For Gitlab Events", "description": "This workflow automatically triggers a process when a change is made to a specific GitLab repository, enabling streamlined and efficient project management.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/528-receive-updates-for-gitlab-events/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/528-receive-updates-for-gitlab-events/workflow.json", "nodeCount": 1, "nodeNames": ["<PERSON><PERSON><PERSON><PERSON>"], "tags": ["automation", "events", "gitlab", "receive", "updates"]}, {"category": "automation", "name": "Standup Bot 4 4 Worker", "description": "This workflow manages a Mattermost-based standup process, allowing users to configure and update their standup settings, and providing reminders and reporting capabilities.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1475-standup-bot-4-4-worker/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1475-standup-bot-4-4-worker/workflow.json", "nodeCount": 29, "nodeNames": ["publish report", "get user data", "open-standup-dialog?", "Action from MM", "Slash Cmd from MM", "config?", "open config dialog", "Prep Config Dialog", "callback ID?", "standup-config", "standup-answers", "Prep Config Override", "Override Config", "Read Config 1", "Read Config 2", "confirm success", "Read Config 3", "Filter Due Standups", "Prep Request Standup", "Create Channel", "Remind Users", "Get User", "Prep <PERSON>minder", "Prep Standup Dialog", "open standup dialog", "Prep Report", "Delete ReminderPost", "Update Post", "Every hour"], "tags": ["4", "automation", "bot", "standup", "worker"]}, {"category": "automation", "name": "Telegram User Registration Workflow", "description": "This workflow automates the process of onboarding new users by integrating with Google Sheets and Telegram to capture user information, store it in a database, and send personalized welcome messages.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2406-telegram-user-registration-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2406-telegram-user-registration-workflow/workflow.json", "nodeCount": 15, "nodeNames": ["Note3", "<PERSON><PERSON> Start", "Trigger_Data", "Note4", "Note2", "Note", "Find User", "Note1", "Data to Save", "Write to Data Base", "Welcome message", "Welcome back", "New?", "Update status", "Data Example"], "tags": ["automation", "registration", "telegram", "user"]}, {"category": "automation", "name": "Joining Different Datasets", "description": "This workflow demonstrates various use cases of the Merge node in n8n, allowing users to combine and enrich data from different sources.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1747-joining-different-datasets/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1747-joining-different-datasets/workflow.json", "nodeCount": 17, "nodeNames": ["On clicking 'execute'", "Note", "Note1", "Note2", "Note4", "Note6", "Note8", "Note9", "Ingredients in stock from recipe", "Super Band", "<PERSON><PERSON> Needed", "B. Ingredients in stock", "Merge recipe", "<PERSON><PERSON>", "B. Recipe quantities", "<PERSON><PERSON>", "<PERSON><PERSON>"], "tags": ["automation", "datasets", "different", "joining"]}, {"category": "automation", "name": "Merge Multiple Runs Into One", "description": "This workflow retrieves customer data from a datastore, processes it in batches, and merges the results.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1814-merge-multiple-runs-into-one/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1814-merge-multiple-runs-into-one/workflow.json", "nodeCount": 7, "nodeNames": ["On clicking 'execute'", "Customer Datastore", "Wait", "Done looping?", "Merge loop items", "NoOp", "Loop Over Items"], "tags": ["automation", "merge", "multiple", "one", "runs"]}, {"category": "automation", "name": "Add Liked Songs To A Spotify Monthly Playlist", "description": "This workflow automatically manages a monthly Spotify playlist by checking for new liked songs, adding them to the playlist, and ensuring the playlist is up-to-date in a NocoDb database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1074-add-liked-songs-to-a-spotify-monthly-playlist/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1074-add-liked-songs-to-a-spotify-monthly-playlist/workflow.json", "nodeCount": 30, "nodeNames": ["Get current date", "Sticky Note3", "Get last 10 liked tracks", "Check if track is saved", "Is not saved", "Create song entry", "Get all user playlist", "Sticky Note4", "Get monthly playlist", "Get playlist in DB", "Monthly playlist exist in Spotify ?", "Playlist exist  in DB ?", "Create playlist in Spotify", "Create playlist in DB1", "Create playlist in DB", "<PERSON><PERSON>", "Clean op", "Clean op2", "Get this month playlist in DB", "Get this month tracks in DB", "Add song to the playlist", "For each tracks in liked song", "For each monthly tracks in DB", "Get this month tracks in Spotify", "Filter1", "Song is not present in the playlist ?", "Clean op1", "Sticky Note5", "Schedule Trigger", "End"], "tags": ["add", "automation", "liked", "monthly", "playlist", "songs", "spotify"]}, {"category": "automation", "name": "Get Today S Date And Day Using The Function Node", "description": "This workflow allows users to easily retrieve the current date and day of the week with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/524-get-today-s-date-and-day-using-the-function-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/524-get-today-s-date-and-day-using-the-function-node/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Function"], "tags": ["automation", "date", "day", "function", "get", "node", "s", "today", "using"]}, {"category": "automation", "name": "Add New Clients From Notion To Clockify", "description": "This workflow automatically adds new clients from a Notion database to Clockify, a time tracking tool, streamlining the process of managing client information across different platforms.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2551-add-new-clients-from-notion-to-clockify/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2551-add-new-clients-from-notion-to-clockify/workflow.json", "nodeCount": 4, "nodeNames": ["Add client to Clockify", "Notion Trigger on new client", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["add", "automation", "clients", "clockify", "new", "notion"]}, {"category": "automation", "name": "Analyze Feedback Using Aws Comprehend And Send It To A Mattermost Channel", "description": "This workflow automatically analyzes feedback from a Typeform survey, detects the sentiment of the feedback, and sends a Mattermost message with the sentiment score and feedback text if the sentiment is negative.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/965-analyze-feedback-using-aws-comprehend-and-send-it-to-a-mattermost-channel/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/965-analyze-feedback-using-aws-comprehend-and-send-it-to-a-mattermost-channel/workflow.json", "nodeCount": 5, "nodeNames": ["Mattermost", "NoOp", "IF", "AWS Comprehend", "Typeform Trigger"], "tags": ["analyze", "automation", "aws", "channel", "comprehend", "feedback", "it", "mattermost", "send", "using"]}, {"category": "automation", "name": "Ai Agent With Charts Capabilities Using Openai Structured Output And Quickchart", "description": "This workflow integrates an AI agent with the ability to generate charts using OpenAI's structured output capabilities, providing a seamless way to incorporate data visualizations into conversational AI applications.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2400-ai-agent-with-charts-capabilities-using-openai-structured-output-and-quickchart/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2400-ai-agent-with-charts-capabilities-using-openai-structured-output-and-quickchart/workflow.json", "nodeCount": 11, "nodeNames": ["OpenAI Chat Model", "Window Buffer Memory", "Generate a chart", "Execute \"Generate a chart\" tool", "OpenAI - Generate Chart definition with Structured Output", "Set response", "Sticky Note1", "<PERSON><PERSON>", "Sticky Note2", "AI Agent", "When chat message received"], "tags": ["agent", "ai", "automation", "capabilities", "charts", "openai", "output", "quickchart", "structured", "using"]}, {"category": "automation", "name": "Purge N8N Execution History Located In Mysql", "description": "This workflow automatically deletes old execution records from a MySQL database on a daily schedule or when manually triggered.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/700-purge-n8n-execution-history-located-in-mysql/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/700-purge-n8n-execution-history-located-in-mysql/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "MySQL", "<PERSON><PERSON>"], "tags": ["automation", "execution", "history", "located", "mysql", "n8n", "purge"]}, {"category": "automation", "name": "Time Logging On Clockify Using Slack", "description": "This workflow is a Slack-integrated time tracking assistant that helps users manage their time entries on the Clockify platform, providing features like creating, updating, and deleting time entries, as well as retrieving client and project information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2604-time-logging-on-clockify-using-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2604-time-logging-on-clockify-using-slack/workflow.json", "nodeCount": 16, "nodeNames": ["OpenAI Chat Model", "Calculator", "Create New Time Entry", "GetClientsTool", "Get All Time Entries", "Current loggedin user", "GetProjectsTool", "Update Time Entry", "Delete Time Entry", "DateConverter", "ClockifyBlockia", "<PERSON><PERSON><PERSON>", "Execution Data", "Window Buffer Memory", "Add reaction", "Send reply"], "tags": ["automation", "clockify", "logging", "slack", "time", "using"]}, {"category": "automation", "name": "Get Synonyms Of A German Word", "description": "This workflow allows users to retrieve synonyms for a given word using the OpenThesaurus API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/806-get-synonyms-of-a-german-word/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/806-get-synonyms-of-a-german-word/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "OpenThesaurus"], "tags": ["automation", "german", "get", "synonyms", "word"]}, {"category": "automation", "name": "Get Entries From A Cockpit Collection", "description": "This workflow allows users to manually trigger a Cockpit API call to interact with a sample data collection.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/518-get-entries-from-a-cockpit-collection/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/518-get-entries-from-a-cockpit-collection/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Cockpit"], "tags": ["automation", "cockpit", "collection", "entries", "get"]}, {"category": "automation", "name": "Execute Another Workflow", "description": "This workflow allows users to manually trigger the execution of another workflow, providing a simple way to run specific workflows on-demand.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/588-execute-another-workflow/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/588-execute-another-workflow/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Execute Workflow"], "tags": ["another", "automation", "execute"]}, {"category": "automation", "name": "Kb Tool Confluence Knowledge Base", "description": "This workflow integrates with a knowledge base tool (Confluence) to enhance the IT support process by enabling sophisticated search and response capabilities via Slack.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2398-kb-tool-confluence-knowledge-base/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2398-kb-tool-confluence-knowledge-base/workflow.json", "nodeCount": 7, "nodeNames": ["Execute Workflow Trigger", "Query Confluence", "Return Tool Response", "<PERSON><PERSON>", "Sticky Note3", "Sticky Note4", "Sticky Note5"], "tags": ["automation", "base", "confluence", "kb", "knowledge", "tool"]}, {"category": "automation", "name": "Send A Random Recipe Once A Day To Telegram", "description": "This workflow automatically sends a random vegan recipe to Telegram users who have joined the bot, with the recipe's image and URL.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1425-send-a-random-recipe-once-a-day-to-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1425-send-a-random-recipe-once-a-day-to-telegram/workflow.json", "nodeCount": 15, "nodeNames": ["<PERSON><PERSON>", "Airtable2", "Set", "Recipe Photo", "Recipe URL", "IF", "Airtable", "Airtable1", "Telegram Recipe Image", "Telegram Recipe URL", "Set1", "Get recipes from API", "Get recipes", "Telegram Trigger - people join bot", "Telegram - Welcome Message"], "tags": ["automation", "day", "once", "random", "recipe", "send", "telegram"]}, {"category": "automation", "name": "Send Location Updates Of The Iss Every Minute To A Queue In Aws Sqs", "description": "This workflow retrieves the current location of the International Space Station (ISS) and sends the data to an AWS SQS queue at a regular interval.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1047-send-location-updates-of-the-iss-every-minute-to-a-queue-in-aws-sqs/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1047-send-location-updates-of-the-iss-every-minute-to-a-queue-in-aws-sqs/workflow.json", "nodeCount": 4, "nodeNames": ["AWS SQS", "Set", "HTTP Request", "<PERSON><PERSON>"], "tags": ["automation", "aws", "every", "iss", "location", "minute", "queue", "send", "sqs", "updates"]}, {"category": "automation", "name": "Create A Folder In Onedrive", "description": "This workflow creates a new folder in the user's Microsoft OneDrive storage when the manual trigger is executed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/565-create-a-folder-in-onedrive/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/565-create-a-folder-in-onedrive/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Microsoft OneDrive"], "tags": ["automation", "create", "folder", "onedrive"]}, {"category": "automation", "name": "<PERSON><PERSON><PERSON> Chatbot Powered By Ai", "description": "This workflow enables a Slack bot to engage in conversational interactions with users, leveraging AI language models and external tools to provide personalized and contextual responses.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1961-slack-chatbot-powered-by-ai/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1961-slack-chatbot-powered-by-ai/workflow.json", "nodeCount": 14, "nodeNames": ["Webhook", "<PERSON><PERSON>ck", "Chat OpenAI", "Window Buffer Memory", "Wikipedia", "No Operation", "do nothing", "Is user message", "<PERSON><PERSON>", "SerpAPI", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Sticky Note4", "Agent"], "tags": ["ai", "automation", "chatbot", "powered", "slack"]}, {"category": "automation", "name": "Create A New User In Notion Based On The Signup Form Submission", "description": "This workflow automates the process of signing up new users, checking if they already exist in a Notion database, and updating their associated semesters.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1373-create-a-new-user-in-notion-based-on-the-signup-form-submission/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/1373-create-a-new-user-in-notion-based-on-the-signup-form-submission/workflow.json", "nodeCount": 12, "nodeNames": ["Extract Name and Email", "Sign Up", "If user exists", "Create User", "Query for User", "Query Current Semester", "Select Semester ID", "Update Semester for User", "<PERSON><PERSON>", "Concatenate Semester IDs", "<PERSON><PERSON>", "Query User"], "tags": ["automation", "based", "create", "form", "new", "notion", "signup", "submission", "user"]}, {"category": "automation", "name": "Check For Preview For A Link", "description": "This workflow checks the availability of a website and provides different actions based on the result.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/935-check-for-preview-for-a-link/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/935-check-for-preview-for-a-link/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON><PERSON><PERSON>", "IF", "Peekalink1", "NoOp"], "tags": ["automation", "check", "link", "preview"]}, {"category": "automation", "name": "Ai Agent To Chat With Supabase Postgresql Db", "description": "This workflow enables users to interact with a Supabase/PostgreSQL database through an AI-powered conversational agent, allowing them to retrieve and analyze data without requiring SQL expertise.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2612-ai-agent-to-chat-with-supabase-postgresql-db/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2612-ai-agent-to-chat-with-supabase-postgresql-db/workflow.json", "nodeCount": 11, "nodeNames": ["Sticky Note3", "Sticky Note5", "Sticky Note6", "Sticky Note7", "When chat message received", "Run SQL query", "OpenAI Chat Model", "DB Schema", "Get table definition", "<PERSON><PERSON>", "AI Agent"], "tags": ["agent", "ai", "automation", "chat", "db", "postgresql", "supabase"]}, {"category": "automation", "name": "Create A Url On Bitly", "description": "This workflow generates a shortened Bitly link for a given long URL, providing a convenient way to share content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/442-create-a-url-on-bitly/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/442-create-a-url-on-bitly/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Bitly"], "tags": ["automation", "bitly", "create", "url"]}, {"category": "automation", "name": "Openai Assistant Workflow Upload File Create An Assistant Chat With It", "description": "This workflow automates the process of uploading a Google Drive document to OpenAI, creating a custom AI assistant based on the document, and enabling a chat interface to interact with the assistant.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2201-openai-assistant-workflow-upload-file-create-an-assistant-chat-with-it/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2201-openai-assistant-workflow-upload-file-create-an-assistant-chat-with-it/workflow.json", "nodeCount": 10, "nodeNames": ["When clicking \"Test workflow\"", "<PERSON><PERSON>", "Get File", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "OpenAI Assistant", "Upload File to OpenAI", "Create new Assistant"], "tags": ["an", "assistant", "automation", "chat", "create", "file", "it", "openai", "upload"]}, {"category": "automation", "name": "Store And Send Information About The Weather For Any City Via Sms", "description": "This workflow automates the process of collecting customer information, retrieving weather data, and sending weather updates via SMS.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/738-store-and-send-information-about-the-weather-for-any-city-via-sms/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/738-store-and-send-information-about-the-weather-for-any-city-via-sms/workflow.json", "nodeCount": 5, "nodeNames": ["Webhook", "Set", "Airtable", "OpenWeatherMap", "<PERSON><PERSON><PERSON>"], "tags": ["about", "any", "automation", "city", "information", "send", "sms", "store", "via", "weather"]}, {"category": "automation", "name": "Manage Projects In Clockify", "description": "This workflow automates the process of creating and managing Clockify time entries for a specific project and task, providing a streamlined way to track and report on time spent on documentation-related work.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/701-manage-projects-in-clockify/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/701-manage-projects-in-clockify/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Clockify", "Clockify1", "Clockify2", "Clockify3"], "tags": ["automation", "clockify", "manage", "projects"]}, {"category": "automation", "name": "Chatgpt Automatic Code Review In Gitlab Mr", "description": "This workflow automates the process of reviewing code changes in a GitLab merge request and providing feedback directly in the GitLab discussion thread.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2167-chatgpt-automatic-code-review-in-gitlab-mr/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2167-chatgpt-automatic-code-review-in-gitlab-mr/workflow.json", "nodeCount": 14, "nodeNames": ["<PERSON><PERSON>", "Sticky Note2", "Sticky Note3", "Webhook", "Code", "Split Out1", "OpenAI Chat Model1", "Get Changes1", "Skip File Change1", "Parse Last Diff Line1", "Post Discussions1", "Need Review1", "Basic LLM Chain1", "Sticky Note4"], "tags": ["automatic", "automation", "chatgpt", "code", "gitlab", "mr", "review"]}, {"category": "automation", "name": "N8N Nodemation Basic Creating Your First Simple Workflow 2 3", "description": "This workflow creates a webhook endpoint that generates a personalized greeting message and sends it to a specified webhook URL.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/19-n8n-nodemation-basic-creating-your-first-simple-workflow-2-3/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/19-n8n-nodemation-basic-creating-your-first-simple-workflow-2-3/workflow.json", "nodeCount": 3, "nodeNames": ["Webhook", "HTTP Request", "FunctionItem"], "tags": ["2", "3", "automation", "basic", "creating", "first", "n8n", "nodemation", "simple"]}, {"category": "automation", "name": "Create A Collection And Create Update And Get A Bookmark In Raindrop", "description": "This workflow automates the process of creating, updating, and retrieving bookmarks in the Raindrop.io service, providing a convenient way to manage online resources.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/959-create-a-collection-and-create-update-and-get-a-bookmark-in-raindrop/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/959-create-a-collection-and-create-update-and-get-a-bookmark-in-raindrop/workflow.json", "nodeCount": 4, "nodeNames": ["Raindrop", "Raindrop1", "Raindrop2", "Raindrop3"], "tags": ["automation", "bookmark", "collection", "create", "get", "raindrop", "update"]}, {"category": "automation", "name": "Supabase Insertion Upsertion Retrieval", "description": "This workflow enables users to seamlessly integrate their Google Drive content with a Supabase vector database, allowing for efficient storage, retrieval, and chatbot-powered question-answering capabilities.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2395-supabase-insertion-upsertion-retrieval/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2395-supabase-insertion-upsertion-retrieval/workflow.json", "nodeCount": 21, "nodeNames": ["Google Drive", "Default Data Loader", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3", "Question and Answer Chain", "OpenAI Chat Model", "Vector Store Retriever", "Recursive Character Text Splitter1", "Customize Response", "When chat message received", "Retrieve by Query", "Embeddings OpenAI Retrieval", "Embeddings OpenAI Insertion", "Placeholder (File/Content to Upsert)", "Embeddings OpenAI Upserting", "Insert Documents", "Retrieve Rows from Table", "Sticky Note4", "Update Documents"], "tags": ["automation", "insertion", "retrieval", "supabase", "upsertion"]}, {"category": "automation", "name": "Attach A Default Error Handler To All Active Workflows", "description": "This workflow automatically updates the error handling configuration for active n8n workflows that do not have a custom error handler set.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2312-attach-a-default-error-handler-to-all-active-workflows/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/automation/2312-attach-a-default-error-handler-to-all-active-workflows/workflow.json", "nodeCount": 11, "nodeNames": ["<PERSON><PERSON><PERSON>", "n8n", "Gmail", "get error handler", "n8n | update", "set fields", "<PERSON><PERSON>", "Sticky Note1", "Schedule Trigger", "Sticky Note2", "active && no error handler set && not this  handler workflow"], "tags": ["active", "all", "attach", "automation", "default", "error", "handler", "workflows"]}, {"category": "communication", "name": "Send Notification When Deployment Fails", "description": "This workflow automatically sends a notification to a Slack channel when a website deployment fails on Netlify.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1255-send-notification-when-deployment-fails/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1255-send-notification-when-deployment-fails/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON>", "<PERSON><PERSON>ck"], "tags": ["communication", "deployment", "fails", "notification", "send", "when"]}, {"category": "communication", "name": "Send Telegram Messages On Rss Feed Read", "description": "This workflow monitors an RSS feed, checks for new articles, and sends a Telegram message with the latest article's title and link.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1507-send-telegram-messages-on-rss-feed-read/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1507-send-telegram-messages-on-rss-feed-read/workflow.json", "nodeCount": 7, "nodeNames": ["Telegram", "RSS Feed Read", "Latest Read", "IF", "Write Latest Read", "NoOp", "<PERSON><PERSON>"], "tags": ["communication", "feed", "messages", "read", "rss", "send", "telegram"]}, {"category": "communication", "name": "Monitor Strava And Send Email Updates", "description": "This workflow automatically checks a user's Strava activity data daily and sends an email to their accountability partners if the user's activity level falls below a specified threshold.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/876-monitor-strava-and-send-email-updates/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/876-monitor-strava-and-send-email-updates/workflow.json", "nodeCount": 6, "nodeNames": ["Strava", "Accountability Settings", "Check Activity Level", "Enough Activity", "Send Email", "Check Daily at 11:AM"], "tags": ["communication", "email", "monitor", "send", "strava", "updates"]}, {"category": "communication", "name": "Send A Message Via Aws Sns", "description": "This workflow allows users to trigger an AWS SNS (Simple Notification Service) message with a custom message and subject.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/501-send-a-message-via-aws-sns/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/501-send-a-message-via-aws-sns/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "AWS SNS"], "tags": ["aws", "communication", "message", "send", "sns", "via"]}, {"category": "communication", "name": "Send An Email Template Using Mandrill", "description": "This workflow sends a welcome email to a specified email address using the Mandrill email service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/571-send-an-email-template-using-mandrill/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/571-send-an-email-template-using-mandrill/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Mandrill"], "tags": ["an", "communication", "email", "mandrill", "send", "template", "using"]}, {"category": "communication", "name": "Send A Discord Message When A Certain Onfleet Event Happens", "description": "This workflow automatically sends a notification to a Discord channel whenever a task is started in Onfleet, a delivery management platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1528-send-a-discord-message-when-a-certain-onfleet-event-happens/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1528-send-a-discord-message-when-a-certain-onfleet-event-happens/workflow.json", "nodeCount": 2, "nodeNames": ["Discord", "Onfleet Trigger"], "tags": ["certain", "communication", "discord", "event", "happens", "message", "onfleet", "send", "when"]}, {"category": "communication", "name": "Save Email Attachments To Nextcloud", "description": "This workflow automatically retrieves emails from an IMAP mailbox, extracts and sanitizes the attachments, and uploads them to a Nextcloud folder, organizing them by the email sender and date.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1344-save-email-attachments-to-nextcloud/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1344-save-email-attachments-to-nextcloud/workflow.json", "nodeCount": 3, "nodeNames": ["IMAP Email", "Nextcloud", "Map each attachment"], "tags": ["attachments", "communication", "email", "nextcloud", "save"]}, {"category": "communication", "name": "Send An Email", "description": "This workflow allows users to manually trigger the sending of an email with a pre-defined message.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/584-send-an-email/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/584-send-an-email/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Send Email"], "tags": ["an", "communication", "email", "send"]}, {"category": "communication", "name": "Generate Dynamic Contents For Emails Or Html Pages", "description": "This workflow generates customer invoices and sends them via email, as well as a summary email of new customers added in the last 24 hours.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1790-generate-dynamic-contents-for-emails-or-html-pages/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1790-generate-dynamic-contents-for-emails-or-html-pages/workflow.json", "nodeCount": 8, "nodeNames": ["On clicking 'execute'", "Customer Datastore", "Item Lists", "One item per template", "All items", "one template", "Add lines", "Send one TEXT email per item", "Send one HTML Email per list"], "tags": ["communication", "contents", "dynamic", "emails", "generate", "html", "or", "pages"]}, {"category": "communication", "name": "Send A Message To Telegram On A New Item Saved To Reader", "description": "This workflow automatically fetches new articles from the Readwise API, writes them to a binary file, and sends a Telegram message for each new article.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1416-send-a-message-to-telegram-on-a-new-item-saved-to-reader/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1416-send-a-message-to-telegram-on-a-new-item-saved-to-reader/workflow.json", "nodeCount": 11, "nodeNames": ["On clicking 'execute'", "Write Binary File", "Read Binary File", "HTTP Request", "Telegram", "Binary to json", "Json to binary", "Set new update time", "Split into baches", "<PERSON><PERSON>", "Config"], "tags": ["communication", "item", "message", "new", "reader", "saved", "send", "telegram"]}, {"category": "communication", "name": "Create Single New Masked Email Address With Fastmail", "description": "This n8n workflow allows users to create a disposable, masked email address using the Fastmail API, triggered by a webhook.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2471-create-single-new-masked-email-address-with-fastmail/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2471-create-single-new-masked-email-address-with-fastmail/workflow.json", "nodeCount": 7, "nodeNames": ["Session", "create random masked email", "Respond to Webhook", "<PERSON><PERSON>", "get fields for creation", "prepare output", "Webhook"], "tags": ["address", "communication", "create", "email", "fastmail", "masked", "new", "single"]}, {"category": "communication", "name": "Check To Do On Notion And Send Message On Slack", "description": "This workflow automatically checks for incomplete tasks assigned to a specific user in Notion, and sends a direct message to that user in Slack with the task details.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1105-check-to-do-on-notion-and-send-message-on-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1105-check-to-do-on-notion-and-send-message-on-slack/workflow.json", "nodeCount": 6, "nodeNames": ["<PERSON><PERSON>", "NoOp", "Get To Dos", "If task assigned to <PERSON><PERSON><PERSON>?", "Create a Direct Message", "Send a Direct Message"], "tags": ["check", "communication", "do", "message", "notion", "send", "slack"]}, {"category": "communication", "name": "Add Positive Feedback Messages To A Table In Notion", "description": "This workflow automates the process of analyzing customer feedback from a Typeform survey, categorizing it based on sentiment, and then taking appropriate actions such as creating a Notion page or a Trello card.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1109-add-positive-feedback-messages-to-a-table-in-notion/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1109-add-positive-feedback-messages-to-a-table-in-notion/workflow.json", "nodeCount": 6, "nodeNames": ["Typeform Trigger", "Google Cloud Natural Language", "IF", "Notion", "<PERSON><PERSON>ck", "Trello"], "tags": ["add", "communication", "feedback", "messages", "notion", "positive", "table"]}, {"category": "communication", "name": "Extract Url From An Email Address", "description": "This workflow extracts the domain name from a given email address, providing a simple way to parse and analyze email data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1377-extract-url-from-an-email-address/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1377-extract-url-from-an-email-address/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Extract domain name", "Sample email"], "tags": ["address", "an", "communication", "email", "extract", "url"]}, {"category": "communication", "name": "Send An Sms Whatsapp Message With Twilio", "description": "This workflow allows users to trigger a Twilio SMS message by manually executing the workflow.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/401-send-an-sms-whatsapp-message-with-twilio/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/401-send-an-sms-whatsapp-message-with-twilio/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON><PERSON>"], "tags": ["an", "communication", "message", "send", "sms", "twi<PERSON>", "whatsapp"]}, {"category": "communication", "name": "Receive Messages From A Queue Via Rabbitmq And Send An Sms", "description": "This workflow monitors a RabbitMQ queue, checks if the value of the \"temp\" parameter exceeds 50, and sends an alert via Vonage if the condition is met.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/845-receive-messages-from-a-queue-via-rabbitmq-and-send-an-sms/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/845-receive-messages-from-a-queue-via-rabbitmq-and-send-an-sms/workflow.json", "nodeCount": 4, "nodeNames": ["RabbitMQ", "IF", "Vonage", "NoOp"], "tags": ["an", "communication", "messages", "queue", "rabbitmq", "receive", "send", "sms", "via"]}, {"category": "communication", "name": "Send Dingtalk Message On New Azure Devops Pull Request", "description": "This workflow automates the process of sending a DingTalk message to the appropriate team members whenever a new pull request is created in Azure DevOps, ensuring timely notification and collaboration.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2034-send-dingtalk-message-on-new-azure-devops-pull-request/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2034-send-dingtalk-message-on-new-azure-devops-pull-request/workflow.json", "nodeCount": 5, "nodeNames": ["LoadDingTalkAccountMap", "ReceiveTfsPullRequestCreatedMessage", "BuildDingTalkWebHookData", "SendDingTalkMessageViaWebHook", "<PERSON><PERSON>"], "tags": ["azure", "communication", "devops", "<PERSON><PERSON><PERSON>", "message", "new", "pull", "request", "send"]}, {"category": "communication", "name": "Receive Messages From A Topic Via Kafka And Send An Sms", "description": "This workflow monitors a Kafka topic, checks if the temperature value exceeds a threshold, and sends a notification via the Vonage API if the condition is met.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/814-receive-messages-from-a-topic-via-kafka-and-send-an-sms/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/814-receive-messages-from-a-topic-via-kafka-and-send-an-sms/workflow.json", "nodeCount": 4, "nodeNames": ["<PERSON><PERSON><PERSON>", "IF", "Vonage", "NoOp"], "tags": ["an", "communication", "kafka", "messages", "receive", "send", "sms", "topic", "via"]}, {"category": "communication", "name": "Telegram Ai <PERSON> Assistant Ready Made Template For Voice Text Messages", "description": "This workflow is a Telegram chatbot that uses OpenAI's language model to engage in natural language conversations with users, providing helpful responses to both text and voice messages.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2534-telegram-ai-bot-assistant-ready-made-template-for-voice-text-messages/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2534-telegram-ai-bot-assistant-ready-made-template-for-voice-text-messages/workflow.json", "nodeCount": 15, "nodeNames": ["OpenAI Chat Model", "Window Buffer Memory", "Correct errors", "Listen for incoming events", "Download voice file", "Combine content and set properties", "Send final reply", "Send error message", "Convert audio to text", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Send Typing action", "AI Agent", "Determine content type"], "tags": ["ai", "assistant", "bot", "communication", "made", "messages", "ready", "telegram", "template", "text", "voice"]}, {"category": "communication", "name": "Get Slack Notifications When New Product Published On Woocommerce", "description": "This workflow automatically sends a notification to a Slack channel whenever a new product is added to a WooCommerce-powered website.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1765-get-slack-notifications-when-new-product-published-on-woocommerce/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1765-get-slack-notifications-when-new-product-published-on-woocommerce/workflow.json", "nodeCount": 3, "nodeNames": ["If URL has /product/", "Send message to slack", "On product creation"], "tags": ["communication", "get", "new", "notifications", "product", "published", "slack", "when", "woocommerce"]}, {"category": "communication", "name": "Send An Email Using Mailgun", "description": "This workflow allows users to manually trigger the sending of a test email using the Mailgun email service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/522-send-an-email-using-mailgun/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/522-send-an-email-using-mailgun/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Mailgun"], "tags": ["an", "communication", "email", "mailgun", "send", "using"]}, {"category": "communication", "name": "Zalando Price Patrol Monitor Price Evolution With Email Notification", "description": "This workflow automatically monitors Zalando product prices, updates a Google Sheet with the latest prices, and sends an email notification when a product's price drops below a user-specified alert price.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2212-zalando-price-patrol-monitor-price-evolution-with-email-notification/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2212-zalando-price-patrol-monitor-price-evolution-with-email-notification/workflow.json", "nodeCount": 14, "nodeNames": ["Add Log in History", "Update Products Infos", "If price below price alert", "List Products", "Format Product", "Schedule Trigger", "Scrap Product", "Notify Price Reduction", "Monitor Zalando Product", "Add Product", "Sticky Note1", "<PERSON><PERSON>", "Sticky Note2", "Sticky Note3"], "tags": ["communication", "email", "evolution", "monitor", "notification", "patrol", "price", "<PERSON><PERSON><PERSON>"]}, {"category": "communication", "name": "Create Screenshots With Uproc Save To Dropbox And Send By Email", "description": "This workflow generates website screenshots, uploads them to Dropbox, and sends an email with the screenshots to a specified email address.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/857-create-screenshots-with-uproc-save-to-dropbox-and-send-by-email/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/857-create-screenshots-with-uproc-save-to-dropbox-and-send-by-email/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "Create Web + Email Item", "Send Email", "Generate FullPage", "Generate Screenshot", "Get File", "Get File1", "<PERSON><PERSON>", "Upload Screenshot", "Upload fullpage"], "tags": ["communication", "create", "dropbox", "email", "save", "screenshots", "send", "uproc"]}, {"category": "communication", "name": "Poll Emails Using Jmap", "description": "This workflow fetches the user's Fastmail account and mailbox details, then retrieves the 3 most recent unread emails from the user's inbox.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2032-poll-emails-using-jmap/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2032-poll-emails-using-jmap/workflow.json", "nodeCount": 7, "nodeNames": ["When clicking \"Execute Workflow\"", "Get mailboxes", "Fetch API details", "Format results", "Get unread messages", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["communication", "emails", "jmap", "poll", "using"]}, {"category": "communication", "name": "Send An Email Using Aws Ses", "description": "This workflow allows users to easily send email notifications using the AWS Simple Email Service (SES) with a single click.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/507-send-an-email-using-aws-ses/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/507-send-an-email-using-aws-ses/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "AWS SES"], "tags": ["an", "aws", "communication", "email", "send", "ses", "using"]}, {"category": "communication", "name": "Send Daily Weather Updates Via A Message Using The Gotify Node", "description": "This workflow retrieves the current temperature in Berlin and sends a daily weather update notification to a Gotify messaging platform.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/774-send-daily-weather-updates-via-a-message-using-the-gotify-node/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/774-send-daily-weather-updates-via-a-message-using-the-gotify-node/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "OpenWeatherMap", "Gotify"], "tags": ["communication", "daily", "gotify", "message", "node", "send", "updates", "using", "via", "weather"]}, {"category": "communication", "name": "Create A Channel Add A Member And Post A Message To The Channel On Mattermost", "description": "This workflow automates the process of adding a user to a Mattermost channel and sending a welcome message to the channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/832-create-a-channel-add-a-member-and-post-a-message-to-the-channel-on-mattermost/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/832-create-a-channel-add-a-member-and-post-a-message-to-the-channel-on-mattermost/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Mattermost", "Mattermost1", "Mattermost2"], "tags": ["add", "channel", "communication", "create", "mattermost", "member", "message", "post"]}, {"category": "communication", "name": "Receive A Mattermost Message When New Data Gets Added To Airtable", "description": "This workflow automatically triggers a Mattermost message whenever new data is added to an Airtable table, providing real-time updates and visibility into the Airtable data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/799-receive-a-mattermost-message-when-new-data-gets-added-to-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/799-receive-a-mattermost-message-when-new-data-gets-added-to-airtable/workflow.json", "nodeCount": 2, "nodeNames": ["Airtable Trigger", "Mattermost"], "tags": ["added", "airtable", "communication", "data", "gets", "mattermost", "message", "new", "receive", "when"]}, {"category": "communication", "name": "Message On Website Content Changed In Telegram", "description": "This workflow periodically checks a website for changes and sends a notification via Telegram if any changes are detected.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1471-message-on-website-content-changed-in-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1471-message-on-website-content-changed-in-telegram/workflow.json", "nodeCount": 7, "nodeNames": ["HTTP Request", "Wait", "HTTP Request1", "IF", "<PERSON><PERSON>", "Telegram1", "NoOp"], "tags": ["changed", "communication", "content", "message", "telegram", "website"]}, {"category": "communication", "name": "Send An Email Using Mailjet", "description": "This workflow allows users to manually trigger the sending of a test email using the Mailjet email service.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/520-send-an-email-using-mailjet/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/520-send-an-email-using-mailjet/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Mailjet"], "tags": ["an", "communication", "email", "mailjet", "send", "using"]}, {"category": "communication", "name": "Send Bulk Messages To Chats In Telegram", "description": "This workflow retrieves data from a Google Sheet, splits it into batches, and sends each batch as a Telegram message with a 30-second delay between batches.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/772-send-bulk-messages-to-chats-in-telegram/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/772-send-bulk-messages-to-chats-in-telegram/workflow.json", "nodeCount": 5, "nodeNames": ["On clicking 'execute'", "Telegram", "Google Sheets", "SplitInBatches", "Wait1"], "tags": ["bulk", "chats", "communication", "messages", "send", "telegram"]}, {"category": "communication", "name": "Send Daily Weather Updates Via A Push Notification Using Spontit", "description": "This workflow automatically retrieves the current temperature in Berlin and sends a push notification with the weather update to the user's device every day at 9 AM.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/796-send-daily-weather-updates-via-a-push-notification-using-spontit/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/796-send-daily-weather-updates-via-a-push-notification-using-spontit/workflow.json", "nodeCount": 3, "nodeNames": ["<PERSON><PERSON>", "OpenWeatherMap", "Spontit"], "tags": ["communication", "daily", "notification", "push", "send", "spontit", "updates", "using", "via", "weather"]}, {"category": "communication", "name": "Listen On New Emails On A Imap Mailbox", "description": "This workflow retrieves email attachments, converts them to XML format, and then sends the XML data to an API endpoint for further processing.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/154-listen-on-new-emails-on-a-imap-mailbox/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/154-listen-on-new-emails-on-a-imap-mailbox/workflow.json", "nodeCount": 5, "nodeNames": ["IMAP Email", "Move Binary Data", "XML", "HTTP Request", "Set"], "tags": ["communication", "emails", "imap", "listen", "mailbox", "new"]}, {"category": "communication", "name": "Post A Message To A Channel In Rocketchat", "description": "This workflow allows users to manually trigger a message to be sent to a Rocket.Chat channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/462-post-a-message-to-a-channel-in-rocketchat/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/462-post-a-message-to-a-channel-in-rocketchat/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Rocketchat"], "tags": ["channel", "communication", "message", "post", "rocketchat"]}, {"category": "communication", "name": "Parse Email Body Message", "description": "This workflow allows users to extract key information from an email body by parsing it based on predefined labels.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1453-parse-email-body-message/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1453-parse-email-body-message/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Set values"], "tags": ["body", "communication", "email", "message", "parse"]}, {"category": "communication", "name": "Send Onfleet Driver Signup Messages In Slack", "description": "This workflow automatically sends a notification to a Slack channel whenever a new driver is created in the Onfleet platform, enabling efficient team communication and onboarding.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1532-send-onfleet-driver-signup-messages-in-slack/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/1532-send-onfleet-driver-signup-messages-in-slack/workflow.json", "nodeCount": 2, "nodeNames": ["Onfleet Trigger", "<PERSON><PERSON>ck"], "tags": ["communication", "driver", "messages", "onfleet", "send", "signup", "slack"]}, {"category": "communication", "name": "Send A Message Via A Lark Bot", "description": "This workflow allows users to send a message to a specific Lark chat using a predefined message content and chat ID.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2478-send-a-message-via-a-lark-bot/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2478-send-a-message-via-a-lark-bot/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking ‘Test workflow’", "Get Lark Token", "Input", "<PERSON><PERSON>", "Sticky Note1", "Send Message"], "tags": ["bot", "communication", "lark", "message", "send", "via"]}, {"category": "communication", "name": "Create Update And Send A Message To A Channel In Microsoft Teams", "description": "This workflow automates the process of creating a new Microsoft Teams channel, updating its name, and sending a message to the channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/680-create-update-and-send-a-message-to-a-channel-in-microsoft-teams/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/680-create-update-and-send-a-message-to-a-channel-in-microsoft-teams/workflow.json", "nodeCount": 4, "nodeNames": ["On clicking 'execute'", "Microsoft Teams", "Microsoft Teams1", "Microsoft Teams2"], "tags": ["channel", "communication", "create", "message", "microsoft", "send", "teams", "update"]}, {"category": "communication", "name": "Send A Message With An Inline Embedded Image With Gmail", "description": "This workflow automates the process of sending an email with an embedded image using Gmail.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2280-send-a-message-with-an-inline-embedded-image-with-gmail/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2280-send-a-message-with-an-inline-embedded-image-with-gmail/workflow.json", "nodeCount": 10, "nodeNames": ["When clicking \"Test workflow\"", "Compose message", "Send message", "Get image", "Message settings", "Convert image to base64", "<PERSON><PERSON>", "Sticky Note1", "Sticky Note2", "Sticky Note3"], "tags": ["an", "communication", "embedded", "gmail", "image", "inline", "message", "send"]}, {"category": "communication", "name": "Extract Domain And Verify Email Syntax On The Go", "description": "This workflow validates email addresses and extracts the domain from the email, providing a simple way to process email data.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2239-extract-domain-and-verify-email-syntax-on-the-go/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/2239-extract-domain-and-verify-email-syntax-on-the-go/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking \"Test workflow\"", "<PERSON><PERSON>", "Set these fields to extract domain", "Generate random data", "Sticky Note1"], "tags": ["communication", "domain", "email", "extract", "go", "syntax", "verify"]}, {"category": "communication", "name": "Verify Email Deliverability With Hunter", "description": "This workflow allows users to verify the validity of an email address using the Hunter email verification API.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/519-verify-email-deliverability-with-hunter/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/communication/519-verify-email-deliverability-with-hunter/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "<PERSON>"], "tags": ["communication", "deliverability", "email", "hunter", "verify"]}, {"category": "data-transformation", "name": "Convert Image Files Jpg Png Jpeg To Urls And Reduce File Size With Resmush It And Imgbb", "description": "This workflow automates the process of uploading images to ImgBB, optimizing them using ReSmush.it, and optionally generating images using OpenAI, providing optimized image URLs ready for use.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2513-convert-image-files-jpg-png-jpeg-to-urls-and-reduce-file-size-with-resmush-it-and-imgbb/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2513-convert-image-files-jpg-png-jpeg-to-urls-and-reduce-file-size-with-resmush-it-and-imgbb/workflow.json", "nodeCount": 11, "nodeNames": ["Upload Img to ImgBB for URL", "ReSmush.it Image Optimisation", "Store Optimised Image ImgBB", "Sticky Note50", "Sticky Note51", "Sticky Note52", "Sticky Note53", "Sticky Note54", "Set image description", "Generate Image", "No Operation", "do nothing"], "tags": ["convert", "data-transformation", "file", "files", "image", "imgbb", "it", "jpeg", "jpg", "png", "reduce", "re<PERSON><PERSON>", "size", "urls"]}, {"category": "data-transformation", "name": "Convert Xlsx To Pdf Using Convertapi", "description": "This workflow allows users to convert an XLSX file to a PDF file and save the resulting PDF to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2304-convert-xlsx-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2304-convert-xlsx-to-pdf-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download XLSX File", "File conversion to PDF"], "tags": ["convert", "convertapi", "data-transformation", "pdf", "using", "xlsx"]}, {"category": "data-transformation", "name": "Convert A Date From One Format To Another", "description": "This workflow allows users to easily retrieve the date and time for a specific date.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/575-convert-a-date-from-one-format-to-another/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/575-convert-a-date-from-one-format-to-another/workflow.json", "nodeCount": 2, "nodeNames": ["On clicking 'execute'", "Date & Time"], "tags": ["another", "convert", "data-transformation", "date", "format", "one"]}, {"category": "data-transformation", "name": "Transform Xml Data And Upload To Dropbox", "description": "This workflow retrieves an XML file, converts it to JSON, modifies the title, converts it back to XML, and saves the updated file to Dropbox.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/13-transform-xml-data-and-upload-to-dropbox/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/13-transform-xml-data-and-upload-to-dropbox/workflow.json", "nodeCount": 5, "nodeNames": ["To JSON", "Change title", "Get XML Data", "Dropbox", "To XML"], "tags": ["data", "data-transformation", "dropbox", "transform", "upload", "xml"]}, {"category": "data-transformation", "name": "Xml To Dropbox", "description": "This workflow retrieves an XML file, converts it to JSON, modifies the title, converts it back to XML, and saves the updated XML file to Dropbox.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/xml-to-dropbox/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/xml-to-dropbox/workflow.json", "nodeCount": 5, "nodeNames": ["To JSON", "Change title", "Get XML Data", "Dropbox", "To XML"], "tags": ["data-transformation", "dropbox", "xml"]}, {"category": "data-transformation", "name": "Convert Pptx To Pdf Using Convertapi", "description": "This workflow converts a PowerPoint (PPTX) file to a PDF file and saves the result to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2305-convert-pptx-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2305-convert-pptx-to-pdf-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download PPTX File", "File conversion to PDF"], "tags": ["convert", "convertapi", "data-transformation", "pdf", "pptx", "using"]}, {"category": "data-transformation", "name": "Etl Pipeline For Text Processing", "description": "This workflow automatically collects tweets with the \"#OnThisDay\" hashtag, analyzes their sentiment using Google Cloud Natural Language, and posts the most positive tweets to a Slack channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1045-etl-pipeline-for-text-processing/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1045-etl-pipeline-for-text-processing/workflow.json", "nodeCount": 9, "nodeNames": ["Twitter", "Postgres", "MongoDB", "<PERSON><PERSON>ck", "IF", "NoOp", "Google Cloud Natural Language", "Set", "<PERSON><PERSON>"], "tags": ["data-transformation", "etl", "pipeline", "processing", "text"]}, {"category": "data-transformation", "name": "Convert The Json Data Received From The Cocktaildb Api In Xml", "description": "This workflow retrieves a random cocktail recipe from an API and converts the response to XML format.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/661-convert-the-json-data-received-from-the-cocktaildb-api-in-xml/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/661-convert-the-json-data-received-from-the-cocktaildb-api-in-xml/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "HTTP Request", "XML"], "tags": ["api", "cocktaildb", "convert", "data", "data-transformation", "json", "received", "xml"]}, {"category": "data-transformation", "name": "Convert Baserow Rich Text Markdown Field To Html", "description": "This workflow automatically updates video descriptions in a Baserow database based on incoming webhook data, with the ability to handle both single record and bulk updates.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2345-convert-baserow-rich-text-markdown-field-to-html/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2345-convert-baserow-rich-text-markdown-field-to-html/workflow.json", "nodeCount": 9, "nodeNames": ["Get single record from baserow", "Update single record in baserow", "Update all records in baserow", "Check if it's 1 record or all records - Baserow", "Get all records from baserow", "Baserow sync video description", "Convert markdown to HTML (single)", "Convert markdown to HTML (all records)", "<PERSON><PERSON>"], "tags": ["baserow", "convert", "data-transformation", "field", "html", "markdown", "rich", "text"]}, {"category": "data-transformation", "name": "Convert Pdf To Pdfa Using Convertapi", "description": "This workflow downloads a PDF file, converts it to a PDF/A format, and saves the converted file to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2317-convert-pdf-to-pdfa-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2317-convert-pdf-to-pdfa-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download PDF File", "File conversion to PDFA"], "tags": ["convert", "convertapi", "data-transformation", "pdf", "pdfa", "using"]}, {"category": "data-transformation", "name": "Merge Pdf Files Using Convertapi", "description": "This workflow downloads two PDF files, merges them using a PDF merge API, and saves the resulting PDF file to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2298-merge-pdf-files-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2298-merge-pdf-files-using-convertapi/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download first remote PDF File", "Download second PDF File", "PDF merge API HTTP Request"], "tags": ["convertapi", "data-transformation", "files", "merge", "pdf", "using"]}, {"category": "data-transformation", "name": "Convert An Xml File To Json Via Webhook Call", "description": "This workflow provides a webhook-based API for converting XML data to JSON format, with error handling and Slack notifications.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2222-convert-an-xml-file-to-json-via-webhook-call/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2222-convert-an-xml-file-to-json-via-webhook-call/workflow.json", "nodeCount": 12, "nodeNames": ["Sticky Note1", "Sticky Note3", "Extract From File", "Error Response", "POST", "XML", "Success Response", "Already JSON", "Change Field", "Sticky Note4", "Switch", "Send to Error Channel"], "tags": ["an", "call", "convert", "data-transformation", "file", "json", "via", "webhook", "xml"]}, {"category": "data-transformation", "name": "Convert Web Page To Pdf Using Convertapi", "description": "This workflow converts a web page to a PDF file and saves it to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2310-convert-web-page-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2310-convert-web-page-to-pdf-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["Convert web page to PDF", "When clicking ‘Test workflow’", "Read/Write Files from Disk", "<PERSON><PERSON>", "Sticky Note1"], "tags": ["convert", "convertapi", "data-transformation", "page", "pdf", "using", "web"]}, {"category": "data-transformation", "name": "Convert Postgresql Table To Csv", "description": "This workflow exports data from a Postgres database table to a CSV file.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1902-convert-postgresql-table-to-csv/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1902-convert-postgresql-table-to-csv/workflow.json", "nodeCount": 4, "nodeNames": ["When clicking \"Execute Workflow\"", "Spreadsheet File", "TableName", "Postgres"], "tags": ["convert", "csv", "data-transformation", "postgresql", "table"]}, {"category": "data-transformation", "name": "Protect Pdf With The Password Using Convertapi", "description": "This workflow allows users to download a PDF file, protect it with a password, and save the protected file to their local disk and Google Drive.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2306-protect-pdf-with-the-password-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2306-protect-pdf-with-the-password-using-convertapi/workflow.json", "nodeCount": 7, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download PDF File", "Protect File with Password", "Sticky Note1", "Google Drive"], "tags": ["convertapi", "data-transformation", "password", "pdf", "protect", "using"]}, {"category": "data-transformation", "name": "Convert Docx To Pdf Using Convertapi", "description": "This workflow converts a Microsoft Word document (DOCX) to a PDF file and saves the result to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2294-convert-docx-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2294-convert-docx-to-pdf-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download File", "File conversion"], "tags": ["convert", "convertapi", "data-transformation", "docx", "pdf", "using"]}, {"category": "data-transformation", "name": "Convert Filemaker Data Api To Flat File Array", "description": "This workflow retrieves contact data from a FileMaker Data API and returns the contact information.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1537-convert-filemaker-data-api-to-flat-file-array/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/1537-convert-filemaker-data-api-to-flat-file-array/workflow.json", "nodeCount": 3, "nodeNames": ["FileMaker response.data", "Return item.fieldData", "FileMaker Data API Contacts"], "tags": ["api", "array", "convert", "data", "data-transformation", "file", "filemaker", "flat"]}, {"category": "data-transformation", "name": "Convert Airtable Rich Text Markdown Field To Html", "description": "This workflow automatically updates the HTML version of video descriptions in an Airtable base, either for a single record or for all records, based on the incoming webhook request.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2308-convert-airtable-rich-text-markdown-field-to-html/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2308-convert-airtable-rich-text-markdown-field-to-html/workflow.json", "nodeCount": 9, "nodeNames": ["Check if it's 1 record or all records - Airtable", "Get single record from airtable", "Convert markdown to HTML1", "Convert markdown to HTML2", "Update single record in airtable", "Update all records in airtable", "Get all records from airtable", "Airtable sync video description", "<PERSON><PERSON>"], "tags": ["airtable", "convert", "data-transformation", "field", "html", "markdown", "rich", "text"]}, {"category": "data-transformation", "name": "Convert Image Urls To An Uploaded Attachment In Airtable", "description": "This workflow automatically uploads image URLs as attachments in an Airtable database.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2037-convert-image-urls-to-an-uploaded-attachment-in-airtable/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2037-convert-image-urls-to-an-uploaded-attachment-in-airtable/workflow.json", "nodeCount": 4, "nodeNames": ["When clicking \"Execute Workflow\"", "Get all records with an image URL", "Update attachment field with images", "<PERSON><PERSON>"], "tags": ["airtable", "an", "attachment", "convert", "data-transformation", "image", "uploaded", "urls"]}, {"category": "data-transformation", "name": "Convert An Array Into An Array Of Objects", "description": "This workflow generates a list of data items and then maps each item to a new JSON object.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/763-convert-an-array-into-an-array-of-objects/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/763-convert-an-array-into-an-array-of-objects/workflow.json", "nodeCount": 2, "nodeNames": ["<PERSON><PERSON>", "Function"], "tags": ["an", "array", "convert", "data-transformation", "objects"]}, {"category": "data-transformation", "name": "Convert Image To Pdf Using Convertapi", "description": "This workflow converts an image file to a PDF document and saves the result to disk.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2316-convert-image-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2316-convert-image-to-pdf-using-convertapi/workflow.json", "nodeCount": 5, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Download Image", "File conversion to PDF"], "tags": ["convert", "convertapi", "data-transformation", "image", "pdf", "using"]}, {"category": "data-transformation", "name": "Convert Docx From Url To Pdf Using Convertapi", "description": "This workflow converts a Microsoft Word document (DOCX) to a PDF file and saves it to the local file system.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2297-convert-docx-from-url-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2297-convert-docx-from-url-to-pdf-using-convertapi/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking ‘Test workflow’", "HTTP Request", "Read/Write Files from Disk", "<PERSON><PERSON>", "Config", "Sticky Note1"], "tags": ["convert", "convertapi", "data-transformation", "docx", "pdf", "url", "using"]}, {"category": "data-transformation", "name": "Convert <PERSON>l To <PERSON>", "description": "This workflow allows users to convert a hardcoded XML string into a structured JSON object, providing a simple way to work with XML data in n8n.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/160-convert-xml-to-json/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/160-convert-xml-to-json/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "Set", "XML"], "tags": ["convert", "data-transformation", "json", "xml"]}, {"category": "data-transformation", "name": "Convert Html To Pdf Using Convertapi", "description": "This workflow converts an HTML document to a PDF file and saves it to disk, providing a simple way to generate PDF documents from HTML content.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2314-convert-html-to-pdf-using-convertapi/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/data-transformation/2314-convert-html-to-pdf-using-convertapi/workflow.json", "nodeCount": 6, "nodeNames": ["When clicking ‘Test workflow’", "Write Result File to Disk", "<PERSON><PERSON>", "Create HTML", "Convert HTML to File", "Convert File to PDF"], "tags": ["convert", "convertapi", "data-transformation", "html", "pdf", "using"]}, {"category": "analytics", "name": "Markdown Report Generation", "description": "This workflow generates a formatted HTML timesheet report, including user avatars, and allows the report to be emailed.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1690-markdown-report-generation/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1690-markdown-report-generation/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "SortElements", "<PERSON><PERSON>", "CreateMDReport", "Send Email", "GetImg", "ImgBinary", "Merge2", "Move Binary Data1", "GetTimesheetRecords"], "tags": ["analytics", "generation", "markdown", "report"]}, {"category": "analytics", "name": "Create A Short Url And Get The Statistics Of The Url", "description": "This workflow creates a short URL for a given URL and provides statistics on the short URL's usage.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/815-create-a-short-url-and-get-the-statistics-of-the-url/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/815-create-a-short-url-and-get-the-statistics-of-the-url/workflow.json", "nodeCount": 3, "nodeNames": ["On clicking 'execute'", "<PERSON><PERSON>", "Yourls1"], "tags": ["analytics", "create", "get", "short", "statistics", "url"]}, {"category": "analytics", "name": "Markdown Timesheet Report Generation", "description": "This workflow generates a formatted HTML timesheet report from user-submitted timesheet data, which can be emailed to stakeholders.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1692-markdown-timesheet-report-generation/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1692-markdown-timesheet-report-generation/workflow.json", "nodeCount": 10, "nodeNames": ["On clicking 'execute'", "SortElements", "<PERSON><PERSON>", "CreateMDReport", "Send Email", "GetImg", "ImgBinary", "Merge2", "Move Binary Data1", "GetTimesheetRecords"], "tags": ["analytics", "generation", "markdown", "report", "timesheet"]}, {"category": "analytics", "name": "Ai Customer Feedback Sentiment Analysis", "description": "This workflow automates the process of collecting customer feedback, analyzing the sentiment using OpenAI, and then storing the feedback along with the sentiment analysis in a Google Sheets spreadsheet.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1996-ai-customer-feedback-sentiment-analysis/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1996-ai-customer-feedback-sentiment-analysis/workflow.json", "nodeCount": 9, "nodeNames": ["Sticky Note2", "Sticky Note3", "Sticky Note4", "Sticky Note5", "Sticky Note6", "Add customer feedback to Google Sheets", "Merge sentiment with form content", "Classify feedback with OpenAI", "Submit form with customer feedback"], "tags": ["ai", "analysis", "analytics", "customer", "feedback", "sentiment"]}, {"category": "analytics", "name": "Send Instagram Statistics To Mattermost", "description": "This workflow automatically retrieves Instagram account statistics, formats the data, and sends a daily update message to a Mattermost channel.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/812-send-instagram-statistics-to-mattermost/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/812-send-instagram-statistics-to-mattermost/workflow.json", "nodeCount": 5, "nodeNames": ["Mattermost", "Date & Time", "<PERSON><PERSON>", "Get the date today", "Read data on Google Sheets"], "tags": ["analytics", "instagram", "mattermost", "send", "statistics"]}, {"category": "analytics", "name": "Report Number Of Weekly Created Records In An App", "description": "This workflow automatically retrieves new product ideas from a Notion database, filters and summarizes the data, and then sends a Slack message with the number of new UX ideas added in the last 7 days.", "readmeLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1931-report-number-of-weekly-created-records-in-an-app/README.md", "jsonLink": "https://github.com/aruntemme/n8n-workflows/blob/main/analytics/1931-report-number-of-weekly-created-records-in-an-app/workflow.json", "nodeCount": 11, "nodeNames": ["Schedule Trigger", "Notion", "<PERSON><PERSON>ck", "Item Lists", "<PERSON><PERSON>", "Sticky Note2", "Sticky Note1", "Sticky Note3", "Sticky Note8", "Filter", "Code"], "tags": ["an", "analytics", "app", "created", "number", "records", "report", "weekly"]}]