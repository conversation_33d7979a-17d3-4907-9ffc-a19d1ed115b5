# 🔍 Guide de Recherche Internet - WeMa IA

Guide complet pour utiliser la recherche internet dans WeMa IA avec SearXNG.

## 🚀 Fonctionnalités

### **1. Recherche Automatique**
WeMa IA détecte automatiquement quand vous avez besoin d'informations récentes :

```
Utilisateur: "Quelles sont les dernières actualités sur l'IA ?"
WeMa IA: [Détecte automatiquement] → Recherche d'actualités
```

### **2. Recherche Manuelle**
Utilisez le bouton 🔍 ou des commandes explicites :

```
Utilisateur: "Recherche les cours du Bitcoin aujourd'hui"
Utilisateur: [Clique sur le bouton 🔍]
```

### **3. Types de Recherche**

#### **Recherche Générale**
- Questions générales
- Informations factuelles
- Définitions

```
"Qu'est-ce que le machine learning ?"
"Comment fonctionne la blockchain ?"
```

#### **Actualités**
- Événements récents
- News du jour/mois
- Informations temporelles

```
"Actualités sur l'intelligence artificielle"
"Que s'est-il passé aujourd'hui en politique ?"
```

#### **Recherche Technique**
- Questions de programmation
- Documentation technique
- Solutions de code

```
"Comment utiliser React hooks ?"
"Erreur Python import module"
```

#### **Recherche Scientifique**
- Publications académiques
- Études scientifiques
- Recherche universitaire

```
"Études récentes sur le changement climatique"
"Publications sur l'informatique quantique"
```

## 🎯 Détection Automatique

### **Mots-clés Déclencheurs**

#### **Recherche Explicite**
- "recherche", "cherche", "trouve"
- "regarde", "vérifier", "google"

#### **Questions d'Information**
- "quoi", "que", "qu'est-ce", "comment"
- "pourquoi", "où", "quand", "qui"

#### **Actualités**
- "actualité", "news", "récent"
- "aujourd'hui", "hier", "cette semaine"

#### **Technique**
- "code", "programmation", "bug"
- "javascript", "python", "react"

### **Exemples de Détection**

| Message | Type Détecté | Confiance |
|---------|--------------|-----------|
| "Recherche les prix de l'immobilier" | Général | 90% |
| "Quelles sont les actualités du jour ?" | Actualités | 85% |
| "Comment débugger React ?" | Technique | 80% |
| "Études sur l'IA en médecine" | Scientifique | 75% |

## 🔧 Interface Utilisateur

### **1. Suggestion Automatique**
Quand WeMa IA détecte une intention de recherche :

```
┌─────────────────────────────────────┐
│ 🔍 Rechercher: "prix Bitcoin"      │
│                    [Rechercher]     │
└─────────────────────────────────────┘
```

### **2. Bouton de Recherche**
- **Icône** : 🔍 dans la barre d'outils
- **État** : Actif quand il y a du texte
- **Animation** : Spinner pendant la recherche

### **3. Résultats dans le Chat**
Les résultats apparaissent directement dans votre message :

```
🔍 **Recherche actualités:** "IA en médecine"

**Résumé des 5 résultat(s) trouvé(s):**

1. **L'IA révolutionne le diagnostic médical**
   Nouvelle technologie permet de détecter...
   📅 2024-01-15

2. **Robots chirurgicaux autonomes**
   Premier robot entièrement autonome...

🔗 Sources principales:
1. https://example.com/ai-medical
2. https://example.com/robot-surgery

_Recherche effectuée en 1250ms_
```

## ⚙️ Configuration

### **1. Activer la Recherche**
1. Aller dans **Paramètres** → **Services**
2. Activer **Recherche Internet - SearXNG**
3. Configurer l'URL : `http://localhost:8888`
4. Tester la connexion

### **2. Paramètres Avancés**
- **Catégories** : Général, Actualités, Science, IT
- **Langue** : Français par défaut
- **Filtrage** : Modéré, Strict, Désactivé
- **Résultats max** : 5-10 recommandé

### **3. Installation SearXNG**
Voir le guide détaillé : [SEARXNG_SETUP.md](./SEARXNG_SETUP.md)

## 🎨 Intégration au Contexte

### **Ajout Automatique**
Les résultats de recherche sont automatiquement :
- ✅ **Ajoutés au contexte** de la conversation
- ✅ **Formatés pour l'IA** avec métadonnées
- ✅ **Horodatés** pour la fraîcheur
- ✅ **Sourcés** avec URLs vérifiées

### **Format du Contexte**
```
[RECHERCHE ACTUALITÉS] "IA médecine" - 5 résultat(s):

1. L'IA révolutionne le diagnostic médical
   Nouvelle technologie permet de détecter...
   Source: https://example.com/ai-medical

2. Robots chirurgicaux autonomes
   Premier robot entièrement autonome...
   Source: https://example.com/robot-surgery
```

## 🚀 Utilisation Optimale

### **Bonnes Pratiques**

#### **1. Questions Précises**
```
❌ "Parle-moi de l'IA"
✅ "Quelles sont les dernières avancées en IA médicale ?"
```

#### **2. Contexte Temporel**
```
❌ "Actualités"
✅ "Actualités de cette semaine sur la tech"
```

#### **3. Domaine Spécifique**
```
❌ "Comment programmer ?"
✅ "Comment utiliser les hooks React en 2024 ?"
```

### **Commandes Utiles**

| Commande | Effet |
|----------|-------|
| "Recherche [sujet]" | Recherche générale |
| "Actualités [sujet]" | Recherche d'actualités |
| "Comment [technique] ?" | Recherche technique |
| "Études sur [science]" | Recherche scientifique |

## 🔍 Exemples Pratiques

### **Actualités**
```
Utilisateur: "Quelles sont les actualités du jour en tech ?"
WeMa IA: [Recherche automatique] → Résultats récents
```

### **Technique**
```
Utilisateur: "Erreur 'module not found' en Python"
WeMa IA: [Recherche StackOverflow] → Solutions
```

### **Général**
```
Utilisateur: "Recherche les meilleurs restaurants à Paris"
WeMa IA: [Recherche générale] → Recommandations
```

### **Scientifique**
```
Utilisateur: "Études récentes sur l'efficacité des vaccins"
WeMa IA: [Recherche académique] → Publications
```

## 🛠️ Dépannage

### **Problèmes Courants**

#### **"Service non disponible"**
- Vérifier que SearXNG est démarré
- Tester l'URL dans le navigateur
- Vérifier la configuration

#### **"Aucun résultat"**
- Reformuler la requête
- Essayer des mots-clés différents
- Vérifier la connexion internet

#### **Résultats de mauvaise qualité**
- Être plus spécifique
- Utiliser le bon type de recherche
- Ajuster les catégories

### **Logs de Debug**
```javascript
// Console du navigateur
🔍 Recherche internet: "IA médecine"
🔍 Intention détectée: {type: "general", confidence: 0.8}
✅ Résultats de recherche ajoutés au contexte
```

## 📊 Performance

### **Métriques Typiques**
- **Temps de recherche** : 1-3 secondes
- **Résultats** : 5-10 par recherche
- **Sources** : 3-5 moteurs agrégés
- **Précision** : 85-95% selon le type

### **Optimisations**
- Cache des résultats récents
- Compression intelligente du contexte
- Parallélisation des requêtes
- Filtrage de la qualité

---

**Note** : La recherche internet nécessite SearXNG configuré et fonctionnel. Voir la documentation d'installation pour plus de détails.
