/**
 * 🧪 TEST RÉEL DE LA COMPRESSION INTELLIGENTE
 *
 * Ce script teste vraiment la compression avec de vrais messages
 */

import { intelligentCompressor } from './src/services/api/context/IntelligentCompressor.js';

// Messages de test réalistes
const testMessages = [
  {
    id: 'msg-1',
    role: 'user',
    content: 'Salut ! Je veux créer une application React avec TypeScript.',
    timestamp: new Date('2024-01-01T10:00:00Z')
  },
  {
    id: 'msg-2',
    role: 'assistant',
    content: 'Parfait ! Je vais t\'aider à créer une application React avec TypeScript. Voici les étapes :\n\n1. Installer Node.js\n2. Créer le projet avec `npx create-react-app mon-app --template typescript`\n3. Configurer les dépendances\n\nCommençons par la première étape.',
    timestamp: new Date('2024-01-01T10:01:00Z')
  },
  {
    id: 'msg-3',
    role: 'user',
    content: 'OK j\'ai installé Node.js. Comment je fais pour créer le projet ?',
    timestamp: new Date('2024-01-01T10:02:00Z')
  },
  {
    id: 'msg-4',
    role: 'assistant',
    content: 'Excellent ! Maintenant, ouvre ton terminal et exécute cette commande :\n\n```bash\nnpx create-react-app mon-app --template typescript\ncd mon-app\nnpm start\n```\n\nCela va créer ton projet React avec TypeScript et le démarrer.',
    timestamp: new Date('2024-01-01T10:03:00Z')
  },
  {
    id: 'msg-5',
    role: 'user',
    content: 'Ça marche ! L\'application se lance. Maintenant je veux ajouter une base de données.',
    timestamp: new Date('2024-01-01T10:05:00Z')
  },
  {
    id: 'msg-6',
    role: 'assistant',
    content: 'Super ! Pour la base de données, tu as plusieurs options :\n\n1. **SQLite** - Simple, parfait pour débuter\n2. **PostgreSQL** - Plus robuste pour la production\n3. **MongoDB** - Base NoSQL\n\nQuelle option préfères-tu ? Et quel type de données veux-tu stocker ?',
    timestamp: new Date('2024-01-01T10:06:00Z')
  },
  {
    id: 'msg-7',
    role: 'user',
    content: 'Je pense que PostgreSQL c\'est bien. Je veux stocker des utilisateurs et leurs profils.',
    timestamp: new Date('2024-01-01T10:07:00Z')
  },
  {
    id: 'msg-8',
    role: 'assistant',
    content: 'Excellent choix ! PostgreSQL est parfait pour ça. Voici comment procéder :\n\n1. **Installer PostgreSQL** sur ton système\n2. **Installer Prisma** comme ORM : `npm install prisma @prisma/client`\n3. **Configurer le schéma** pour les utilisateurs\n\nVeux-tu que je te guide pour l\'installation de PostgreSQL ?',
    timestamp: new Date('2024-01-01T10:08:00Z')
  },
  {
    id: 'msg-9',
    role: 'user',
    content: 'Oui s\'il te plaît, guide-moi pour l\'installation.',
    timestamp: new Date('2024-01-01T10:09:00Z')
  },
  {
    id: 'msg-10',
    role: 'assistant',
    content: 'Parfait ! Voici les étapes d\'installation de PostgreSQL :\n\n**Sur Windows :**\n1. Télécharge PostgreSQL depuis postgresql.org\n2. Lance l\'installateur\n3. Note bien le mot de passe que tu définis\n\n**Sur Mac :**\n```bash\nbrew install postgresql\nbrew services start postgresql\n```\n\n**Sur Linux :**\n```bash\nsudo apt update\nsudo apt install postgresql postgresql-contrib\n```\n\nSur quel système es-tu ?',
    timestamp: new Date('2024-01-01T10:10:00Z')
  },
  {
    id: 'msg-11',
    role: 'user',
    content: 'Je suis sur Windows. J\'ai téléchargé et installé PostgreSQL. Maintenant quoi ?',
    timestamp: new Date('2024-01-01T10:15:00Z')
  },
  {
    id: 'msg-12',
    role: 'assistant',
    content: 'Parfait ! Maintenant configurons Prisma dans ton projet React :\n\n1. **Installer Prisma :**\n```bash\nnpm install prisma @prisma/client\nnpx prisma init\n```\n\n2. **Configurer la base :** Édite le fichier `.env` :\n```\nDATABASE_URL="postgresql://postgres:tonmotdepasse@localhost:5432/monapp"\n```\n\n3. **Créer le schéma :** Dans `prisma/schema.prisma` :\n```prisma\nmodel User {\n  id    Int     @id @default(autoincrement())\n  email String  @unique\n  name  String?\n  profile Profile?\n}\n\nmodel Profile {\n  id     Int    @id @default(autoincrement())\n  bio    String?\n  userId Int    @unique\n  user   User   @relation(fields: [userId], references: [id])\n}\n```\n\nExécute ces commandes dans ton projet.',
    timestamp: new Date('2024-01-01T10:16:00Z')
  },
  {
    id: 'msg-13',
    role: 'user',
    content: 'C\'est fait ! Prisma est configuré. Comment je teste que ça marche ?',
    timestamp: new Date('2024-01-01T10:20:00Z')
  },
  {
    id: 'msg-14',
    role: 'assistant',
    content: 'Excellent ! Maintenant testons la connexion :\n\n1. **Générer le client Prisma :**\n```bash\nnpx prisma generate\nnpx prisma db push\n```\n\n2. **Créer un fichier de test** `test-db.js` :\n```javascript\nconst { PrismaClient } = require(\'@prisma/client\')\nconst prisma = new PrismaClient()\n\nasync function main() {\n  const user = await prisma.user.create({\n    data: {\n      email: \'<EMAIL>\',\n      name: \'Test User\',\n      profile: {\n        create: {\n          bio: \'Je suis un utilisateur de test\'\n        }\n      }\n    },\n    include: {\n      profile: true\n    }\n  })\n  console.log(\'Utilisateur créé:\', user)\n}\n\nmain().catch(console.error).finally(() => prisma.$disconnect())\n```\n\n3. **Exécuter le test :**\n```bash\nnode test-db.js\n```\n\nEssaie ça !',
    timestamp: new Date('2024-01-01T10:21:00Z')
  },
  {
    id: 'msg-15',
    role: 'user',
    content: 'Ça marche parfaitement ! L\'utilisateur a été créé. Maintenant je veux intégrer ça dans mon composant React.',
    timestamp: new Date('2024-01-01T10:25:00Z')
  },
  {
    id: 'msg-16',
    role: 'assistant',
    content: 'Super ! Pour intégrer Prisma dans React, tu vas créer une API. Voici comment :\n\n1. **Créer un dossier API :** `src/api/`\n2. **Créer un service utilisateur :** `src/api/userService.ts`\n\n```typescript\nimport { PrismaClient } from \'@prisma/client\'\n\nconst prisma = new PrismaClient()\n\nexport const userService = {\n  async createUser(email: string, name: string, bio?: string) {\n    return await prisma.user.create({\n      data: {\n        email,\n        name,\n        profile: bio ? {\n          create: { bio }\n        } : undefined\n      },\n      include: { profile: true }\n    })\n  },\n\n  async getUsers() {\n    return await prisma.user.findMany({\n      include: { profile: true }\n    })\n  }\n}\n```\n\nMaintenant tu peux utiliser ce service dans tes composants React !',
    timestamp: new Date('2024-01-01T10:26:00Z')
  },
  {
    id: 'msg-17',
    role: 'user',
    content: 'Parfait ! Maintenant je veux créer un formulaire pour ajouter des utilisateurs.',
    timestamp: new Date('2024-01-01T10:30:00Z')
  },
  {
    id: 'msg-18',
    role: 'assistant',
    content: 'Excellente idée ! Créons un formulaire React avec TypeScript :\n\n```typescript\nimport React, { useState } from \'react\'\nimport { userService } from \'../api/userService\'\n\ninterface UserFormProps {\n  onUserCreated?: () => void\n}\n\nconst UserForm: React.FC<UserFormProps> = ({ onUserCreated }) => {\n  const [email, setEmail] = useState(\'\')\n  const [name, setName] = useState(\'\')\n  const [bio, setBio] = useState(\'\')\n  const [loading, setLoading] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    \n    try {\n      await userService.createUser(email, name, bio)\n      setEmail(\'\')\n      setName(\'\')\n      setBio(\'\')\n      onUserCreated?.()\n      alert(\'Utilisateur créé avec succès !\')\n    } catch (error) {\n      alert(\'Erreur lors de la création\')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <div>\n        <label>Email:</label>\n        <input \n          type="email" \n          value={email} \n          onChange={(e) => setEmail(e.target.value)}\n          required \n        />\n      </div>\n      <div>\n        <label>Nom:</label>\n        <input \n          type="text" \n          value={name} \n          onChange={(e) => setName(e.target.value)}\n          required \n        />\n      </div>\n      <div>\n        <label>Bio:</label>\n        <textarea \n          value={bio} \n          onChange={(e) => setBio(e.target.value)}\n        />\n      </div>\n      <button type="submit" disabled={loading}>\n        {loading ? \'Création...\' : \'Créer utilisateur\'}\n      </button>\n    </form>\n  )\n}\n\nexport default UserForm\n```\n\nCe formulaire est prêt à utiliser !',
    timestamp: new Date('2024-01-01T10:31:00Z')
  }
];

async function testCompression() {
  console.log('🧪 DÉBUT DU TEST DE COMPRESSION INTELLIGENTE');
  console.log(`📊 Messages de test: ${testMessages.length}`);
  
  const totalChars = testMessages.reduce((sum, msg) => sum + msg.content.length, 0);
  console.log(`📊 Caractères totaux: ${totalChars}`);
  
  try {
    console.log('\n🚀 Lancement de la compression intelligente...');
    
    const compressedMessages = await intelligentCompressor.smartCompress(
      testMessages,
      {
        targetContextWindow: 8000
      },
      'test-session'
    );
    
    console.log('\n✅ RÉSULTATS DE LA COMPRESSION:');
    console.log(`📊 Messages originaux: ${testMessages.length}`);
    console.log(`📊 Messages compressés: ${compressedMessages.length}`);
    
    const compressedChars = compressedMessages.reduce((sum, msg) => sum + msg.content.length, 0);
    console.log(`📊 Caractères originaux: ${totalChars}`);
    console.log(`📊 Caractères compressés: ${compressedChars}`);
    console.log(`📊 Ratio de compression: ${(totalChars / compressedChars).toFixed(2)}x`);
    
    console.log('\n📋 MESSAGES COMPRESSÉS:');
    compressedMessages.forEach((msg, index) => {
      console.log(`\n[${index + 1}] ${msg.role} (${msg.id}):`);
      console.log(msg.content.substring(0, 200) + (msg.content.length > 200 ? '...' : ''));
      if (msg.metadata?.isCompressed) {
        console.log('🔄 [COMPRESSÉ]');
      }
    });
    
  } catch (error) {
    console.error('❌ ERREUR LORS DU TEST:', error);
  }
}

// Lancer le test
testCompression();
