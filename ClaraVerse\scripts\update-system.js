#!/usr/bin/env node
/**
 * 🔄 Système de mise à jour WeMa IA
 * Gère les mises à jour backend et frontend en réseau local
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const UPDATE_SERVER_PORT = 8001; // Port pour serveur de mise à jour
const VERSION_FILE = 'version.json';

// Utilitaires
const log = (message) => console.log(`🔄 ${new Date().toLocaleTimeString()} - ${message}`);
const error = (message) => console.error(`❌ ${new Date().toLocaleTimeString()} - ${message}`);

// Lire la version actuelle
function getCurrentVersion() {
  try {
    if (fs.existsSync(VERSION_FILE)) {
      return JSON.parse(fs.readFileSync(VERSION_FILE, 'utf8'));
    }
  } catch (e) {
    error(`Erreur lecture version: ${e.message}`);
  }
  
  return {
    version: '1.0.0',
    backend: '1.0.0',
    frontend: '1.0.0',
    lastUpdate: new Date().toISOString()
  };
}

// Sauvegarder la version
function saveVersion(versionInfo) {
  try {
    fs.writeFileSync(VERSION_FILE, JSON.stringify(versionInfo, null, 2));
    log(`Version sauvegardée: ${versionInfo.version}`);
  } catch (e) {
    error(`Erreur sauvegarde version: ${e.message}`);
  }
}

// Créer un package de mise à jour
function createUpdatePackage() {
  log('Création du package de mise à jour...');
  
  const version = getCurrentVersion();
  const newVersion = {
    ...version,
    version: incrementVersion(version.version),
    lastUpdate: new Date().toISOString()
  };
  
  const updateDir = 'updates';
  const packageDir = path.join(updateDir, `v${newVersion.version}`);
  
  // Créer les dossiers
  if (!fs.existsSync(updateDir)) fs.mkdirSync(updateDir);
  if (fs.existsSync(packageDir)) fs.rmSync(packageDir, { recursive: true });
  fs.mkdirSync(packageDir);
  
  // 1. Build frontend
  log('Build frontend...');
  try {
    execSync('npm run build:server', { stdio: 'inherit' });
    
    // Copier le build frontend
    const frontendSrc = 'dist';
    const frontendDest = path.join(packageDir, 'frontend');
    if (fs.existsSync(frontendSrc)) {
      fs.cpSync(frontendSrc, frontendDest, { recursive: true });
      newVersion.frontend = newVersion.version;
      log('✅ Frontend packagé');
    }
  } catch (e) {
    error(`Erreur build frontend: ${e.message}`);
  }
  
  // 2. Copier backend
  log('Package backend...');
  try {
    const backendSrc = 'py_backend';
    const backendDest = path.join(packageDir, 'backend');
    
    // Copier seulement les fichiers nécessaires
    fs.mkdirSync(backendDest);
    
    const filesToCopy = [
      'main.py',
      'start_server.py',
      'requirements.txt',
      'rag_premium_service.py',
      'ocr_service.py',
      'langfuse_service.py'
    ];
    
    filesToCopy.forEach(file => {
      const src = path.join(backendSrc, file);
      const dest = path.join(backendDest, file);
      if (fs.existsSync(src)) {
        fs.copyFileSync(src, dest);
      }
    });
    
    newVersion.backend = newVersion.version;
    log('✅ Backend packagé');
  } catch (e) {
    error(`Erreur package backend: ${e.message}`);
  }
  
  // 3. Créer script de mise à jour
  const updateScript = `@echo off
echo 🔄 Mise à jour WeMa IA vers v${newVersion.version}

REM Arrêter le serveur
taskkill /F /IM python.exe 2>nul

REM Sauvegarder la configuration
if exist "backend\\config.env" copy "backend\\config.env" "config.env.backup"
if exist "backend\\data" xcopy "backend\\data" "data.backup\\" /E /I /Y

REM Mettre à jour backend
if exist "backend" rmdir /S /Q "backend"
xcopy "backend" "..\\backend\\" /E /I /Y

REM Mettre à jour frontend
if exist "frontend" (
    if exist "..\\frontend" rmdir /S /Q "..\\frontend"
    xcopy "frontend" "..\\frontend\\" /E /I /Y
)

REM Restaurer la configuration
if exist "config.env.backup" copy "config.env.backup" "..\\backend\\config.env"
if exist "data.backup" xcopy "data.backup" "..\\backend\\data\\" /E /I /Y

REM Redémarrer le serveur
cd ..\\backend
call venv\\Scripts\\activate
python start_server.py --production --host 0.0.0.0 --port 8000

echo ✅ Mise à jour terminée !
pause`;
  
  fs.writeFileSync(path.join(packageDir, 'update.bat'), updateScript);
  
  // 4. Créer fichier de version
  fs.writeFileSync(path.join(packageDir, 'version.json'), JSON.stringify(newVersion, null, 2));
  
  // 5. Créer archive ZIP
  log('Création archive...');
  try {
    const archiveName = `wema-ia-v${newVersion.version}.zip`;
    execSync(`powershell Compress-Archive -Path "${packageDir}\\*" -DestinationPath "${updateDir}\\${archiveName}" -Force`);
    log(`✅ Archive créée: ${archiveName}`);
  } catch (e) {
    error(`Erreur création archive: ${e.message}`);
  }
  
  // Sauvegarder la nouvelle version
  saveVersion(newVersion);
  
  log(`🎉 Package de mise à jour v${newVersion.version} créé !`);
  return newVersion;
}

// Incrémenter version
function incrementVersion(version) {
  const parts = version.split('.').map(Number);
  parts[2]++; // Incrémenter patch
  return parts.join('.');
}

// Serveur de mise à jour simple
function startUpdateServer() {
  const http = require('http');
  const url = require('url');
  
  const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    
    // CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }
    
    // API endpoints
    if (parsedUrl.pathname === '/version') {
      // Retourner version actuelle
      const version = getCurrentVersion();
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(version));
      
    } else if (parsedUrl.pathname === '/check-update') {
      // Vérifier s'il y a une mise à jour
      const currentVersion = getCurrentVersion();
      const clientVersion = parsedUrl.query.version || '0.0.0';
      
      const hasUpdate = compareVersions(currentVersion.version, clientVersion) > 0;
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        hasUpdate,
        currentVersion: currentVersion.version,
        clientVersion,
        downloadUrl: hasUpdate ? `/download/wema-ia-v${currentVersion.version}.zip` : null
      }));
      
    } else if (parsedUrl.pathname.startsWith('/download/')) {
      // Télécharger mise à jour
      const filename = path.basename(parsedUrl.pathname);
      const filePath = path.join('updates', filename);
      
      if (fs.existsSync(filePath)) {
        res.writeHead(200, {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${filename}"`
        });
        fs.createReadStream(filePath).pipe(res);
      } else {
        res.writeHead(404);
        res.end('Fichier non trouvé');
      }
      
    } else {
      res.writeHead(404);
      res.end('Endpoint non trouvé');
    }
  });
  
  server.listen(UPDATE_SERVER_PORT, '0.0.0.0', () => {
    log(`🌐 Serveur de mise à jour démarré sur port ${UPDATE_SERVER_PORT}`);
    log(`📡 Accessible sur: http://*************:${UPDATE_SERVER_PORT}`);
  });
}

// Comparer versions
function compareVersions(a, b) {
  const aParts = a.split('.').map(Number);
  const bParts = b.split('.').map(Number);
  
  for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
    const aPart = aParts[i] || 0;
    const bPart = bParts[i] || 0;
    
    if (aPart > bPart) return 1;
    if (aPart < bPart) return -1;
  }
  
  return 0;
}

// CLI
const command = process.argv[2];

switch (command) {
  case 'create':
    createUpdatePackage();
    break;
    
  case 'server':
    startUpdateServer();
    break;
    
  case 'build-and-serve':
    createUpdatePackage();
    setTimeout(() => startUpdateServer(), 1000);
    break;
    
  default:
    console.log(`
🔄 Système de mise à jour WeMa IA

Commandes:
  create              Créer un package de mise à jour
  server              Démarrer le serveur de mise à jour
  build-and-serve     Créer package et démarrer serveur

Exemples:
  node scripts/update-system.js create
  node scripts/update-system.js server
  node scripts/update-system.js build-and-serve
`);
}
