/**
 * SERVICE SUGGESTIONS INTELLIGENTES DE DOCUMENTS
 * Suggestions automatiques basées sur le contexte et l'historique
 */

import { DocumentInfo } from '../types/gdpr-types';
import { ClaraMessage } from '../types/clara-types';
import { hierarchicalMemory } from './hierarchicalMemoryService';

export interface DocumentSuggestion {
  document: DocumentInfo;
  relevanceScore: number;
  reasons: string[];
  confidence: number;
  lastUsed?: number;
  usageCount?: number;
}

export interface SuggestionContext {
  query: string;
  conversationHistory: ClaraMessage[];
  currentDocuments: string[];
  userPreferences: Record<string, any>;
}

export class IntelligentSuggestionsService {
  private documentUsageHistory: Map<string, {
    count: number;
    lastUsed: number;
    contexts: string[];
    effectiveness: number;
  }> = new Map();

  private queryDocumentPairs: Map<string, string[]> = new Map();

  /**
   * Obtenir des suggestions de documents pour une requête
   */
  public async getSuggestions(
    context: SuggestionContext,
    availableDocuments: DocumentInfo[]
  ): Promise<DocumentSuggestion[]> {
    const suggestions: DocumentSuggestion[] = [];

    for (const document of availableDocuments) {
      const suggestion = await this.evaluateDocument(document, context);
      if (suggestion.relevanceScore > 0.3) { // Seuil de pertinence
        suggestions.push(suggestion);
      }
    }

    // Trier par score de pertinence
    suggestions.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // Retourner les 5 meilleures suggestions
    return suggestions.slice(0, 5);
  }

  /**
   * Évaluer la pertinence d'un document pour un contexte donné
   */
  private async evaluateDocument(
    document: DocumentInfo,
    context: SuggestionContext
  ): Promise<DocumentSuggestion> {
    const reasons: string[] = [];
    let relevanceScore = 0;
    let confidence = 0.5;

    // 1. Analyse sémantique du nom de fichier
    const filenameScore = this.analyzeFilename(document.filename, context.query);
    if (filenameScore > 0.5) {
      relevanceScore += filenameScore * 0.3;
      reasons.push(`Nom de fichier pertinent (${Math.round(filenameScore * 100)}%)`);
      confidence += 0.1;
    }

    // 2. Analyse du contenu (si disponible)
    if (document.content) {
      const contentScore = this.analyzeContent(document.content, context.query);
      if (contentScore > 0.3) {
        relevanceScore += contentScore * 0.4;
        reasons.push(`Contenu pertinent (${Math.round(contentScore * 100)}%)`);
        confidence += 0.2;
      }
    }

    // 3. Historique d'utilisation
    const usageScore = this.analyzeUsageHistory(document.filename, context);
    if (usageScore > 0) {
      relevanceScore += usageScore * 0.2;
      reasons.push(`Utilisé récemment dans un contexte similaire`);
      confidence += 0.1;
    }

    // 4. Analyse de la collection
    const collectionScore = this.analyzeCollection(document.collectionName, context);
    if (collectionScore > 0) {
      relevanceScore += collectionScore * 0.1;
      reasons.push(`Collection pertinente: ${document.collectionName}`);
    }

    // 5. Métadonnées
    const metadataScore = this.analyzeMetadata(document.metadata, context);
    if (metadataScore > 0) {
      relevanceScore += metadataScore * 0.1;
      reasons.push(`Métadonnées correspondantes`);
    }

    // 6. Contexte conversationnel
    const conversationScore = this.analyzeConversationContext(document, context);
    if (conversationScore > 0) {
      relevanceScore += conversationScore * 0.2;
      reasons.push(`Pertinent pour la conversation en cours`);
      confidence += 0.1;
    }

    // 7. Documents déjà sélectionnés (éviter les doublons)
    if (context.currentDocuments.includes(document.filename)) {
      relevanceScore *= 0.1; // Fortement pénaliser les doublons
      reasons.push(`Déjà sélectionné`);
    }

    const usage = this.documentUsageHistory.get(document.filename);

    return {
      document,
      relevanceScore: Math.min(relevanceScore, 1.0),
      reasons,
      confidence: Math.min(confidence, 1.0),
      lastUsed: usage?.lastUsed,
      usageCount: usage?.count || 0
    };
  }

  /**
   * Analyser la pertinence du nom de fichier
   */
  private analyzeFilename(filename: string, query: string): number {
    const queryWords = this.extractKeywords(query.toLowerCase());
    const filenameWords = this.extractKeywords(filename.toLowerCase());

    let matches = 0;
    for (const queryWord of queryWords) {
      for (const filenameWord of filenameWords) {
        if (filenameWord.includes(queryWord) || queryWord.includes(filenameWord)) {
          matches++;
          break;
        }
      }
    }

    return queryWords.length > 0 ? matches / queryWords.length : 0;
  }

  /**
   * Analyser la pertinence du contenu
   */
  private analyzeContent(content: string, query: string): number {
    const queryWords = this.extractKeywords(query.toLowerCase());
    const contentWords = this.extractKeywords(content.toLowerCase());

    let matches = 0;
    let totalRelevance = 0;

    for (const queryWord of queryWords) {
      const wordCount = contentWords.filter(word => 
        word.includes(queryWord) || queryWord.includes(word)
      ).length;
      
      if (wordCount > 0) {
        matches++;
        totalRelevance += Math.min(wordCount / 10, 1); // Normaliser
      }
    }

    return queryWords.length > 0 ? totalRelevance / queryWords.length : 0;
  }

  /**
   * Analyser l'historique d'utilisation
   */
  private analyzeUsageHistory(filename: string, context: SuggestionContext): number {
    const usage = this.documentUsageHistory.get(filename);
    if (!usage) return 0;

    let score = 0;

    // Récence d'utilisation
    const daysSinceLastUse = (Date.now() - usage.lastUsed) / (1000 * 60 * 60 * 24);
    if (daysSinceLastUse < 7) {
      score += 0.5 * (1 - daysSinceLastUse / 7);
    }

    // Fréquence d'utilisation
    score += Math.min(usage.count / 10, 0.3);

    // Efficacité passée
    score += usage.effectiveness * 0.2;

    // Contexte similaire
    const queryKeywords = this.extractKeywords(context.query);
    for (const pastContext of usage.contexts) {
      const pastKeywords = this.extractKeywords(pastContext);
      const similarity = this.calculateSimilarity(queryKeywords, pastKeywords);
      if (similarity > 0.5) {
        score += 0.3;
        break;
      }
    }

    return Math.min(score, 1.0);
  }

  /**
   * Analyser la pertinence de la collection
   */
  private analyzeCollection(collectionName: string, context: SuggestionContext): number {
    const queryWords = this.extractKeywords(context.query.toLowerCase());
    const collectionWords = this.extractKeywords(collectionName.toLowerCase());

    let matches = 0;
    for (const queryWord of queryWords) {
      if (collectionWords.some(word => word.includes(queryWord))) {
        matches++;
      }
    }

    return queryWords.length > 0 ? matches / queryWords.length * 0.5 : 0;
  }

  /**
   * Analyser les métadonnées
   */
  private analyzeMetadata(metadata: Record<string, any>, context: SuggestionContext): number {
    let score = 0;
    const queryWords = this.extractKeywords(context.query.toLowerCase());

    // Analyser les valeurs des métadonnées
    for (const [key, value] of Object.entries(metadata)) {
      if (typeof value === 'string') {
        const metaWords = this.extractKeywords(value.toLowerCase());
        for (const queryWord of queryWords) {
          if (metaWords.some(word => word.includes(queryWord))) {
            score += 0.1;
          }
        }
      }
    }

    return Math.min(score, 0.5);
  }

  /**
   * Analyser le contexte conversationnel
   */
  private analyzeConversationContext(document: DocumentInfo, context: SuggestionContext): number {
    let score = 0;

    // Analyser les derniers messages pour détecter des patterns
    const recentMessages = context.conversationHistory.slice(-5);
    const conversationText = recentMessages.map(m => m.content).join(' ').toLowerCase();

    // Mots-clés du document dans la conversation
    const docKeywords = this.extractKeywords(document.filename.toLowerCase());
    if (document.content) {
      docKeywords.push(...this.extractKeywords(document.content.toLowerCase()).slice(0, 10));
    }

    for (const keyword of docKeywords) {
      if (conversationText.includes(keyword)) {
        score += 0.1;
      }
    }

    // Sujets de conversation récurrents
    const memoryContext = hierarchicalMemory.getMemoryContext(context.query);
    for (const topic of memoryContext.relevantTopics) {
      if (topic.relatedDocuments.includes(document.filename)) {
        score += 0.3;
      }
    }

    return Math.min(score, 1.0);
  }

  /**
   * Extraire les mots-clés d'un texte
   */
  private extractKeywords(text: string): string[] {
    // Mots vides à ignorer
    const stopWords = new Set([
      'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais',
      'pour', 'avec', 'dans', 'sur', 'par', 'ce', 'cette', 'ces', 'que', 'qui',
      'the', 'a', 'an', 'and', 'or', 'but', 'for', 'with', 'in', 'on', 'by',
      'this', 'that', 'these', 'those', 'what', 'which', 'who'
    ]);

    return text
      .split(/\s+/)
      .map(word => word.replace(/[^\w]/g, ''))
      .filter(word => word.length > 2 && !stopWords.has(word.toLowerCase()))
      .map(word => word.toLowerCase());
  }

  /**
   * Calculer la similarité entre deux ensembles de mots-clés
   */
  private calculateSimilarity(keywords1: string[], keywords2: string[]): number {
    if (keywords1.length === 0 || keywords2.length === 0) return 0;

    const set1 = new Set(keywords1);
    const set2 = new Set(keywords2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));

    return intersection.size / Math.max(set1.size, set2.size);
  }

  /**
   * Enregistrer l'utilisation d'un document
   */
  public recordDocumentUsage(
    filename: string,
    context: string,
    effectiveness: number = 5
  ): void {
    const usage = this.documentUsageHistory.get(filename) || {
      count: 0,
      lastUsed: 0,
      contexts: [],
      effectiveness: 5
    };

    usage.count++;
    usage.lastUsed = Date.now();
    usage.contexts.push(context);
    usage.effectiveness = (usage.effectiveness + effectiveness) / 2;

    // Limiter l'historique des contextes
    if (usage.contexts.length > 10) {
      usage.contexts = usage.contexts.slice(-10);
    }

    this.documentUsageHistory.set(filename, usage);
  }

  /**
   * Enregistrer une paire requête-document pour l'apprentissage
   */
  public recordQueryDocumentPair(query: string, documents: string[]): void {
    const queryKey = this.extractKeywords(query).join(' ');
    const existingDocs = this.queryDocumentPairs.get(queryKey) || [];
    
    // Fusionner avec les documents existants
    const allDocs = [...new Set([...existingDocs, ...documents])];
    this.queryDocumentPairs.set(queryKey, allDocs);
  }

  /**
   * Obtenir des suggestions basées sur des requêtes similaires
   */
  public getSuggestionsFromSimilarQueries(query: string): string[] {
    const queryKeywords = this.extractKeywords(query);
    const suggestions: { docs: string[], similarity: number }[] = [];

    for (const [pastQuery, docs] of this.queryDocumentPairs.entries()) {
      const pastKeywords = this.extractKeywords(pastQuery);
      const similarity = this.calculateSimilarity(queryKeywords, pastKeywords);
      
      if (similarity > 0.3) {
        suggestions.push({ docs, similarity });
      }
    }

    // Trier par similarité et retourner les documents
    suggestions.sort((a, b) => b.similarity - a.similarity);
    
    const allSuggestions = suggestions.flatMap(s => s.docs);
    return [...new Set(allSuggestions)].slice(0, 5);
  }

  /**
   * Obtenir les statistiques d'utilisation
   */
  public getUsageStats(): {
    totalDocuments: number;
    totalUsages: number;
    averageEffectiveness: number;
    mostUsedDocuments: Array<{ filename: string; count: number }>;
  } {
    const stats = {
      totalDocuments: this.documentUsageHistory.size,
      totalUsages: 0,
      averageEffectiveness: 0,
      mostUsedDocuments: [] as Array<{ filename: string; count: number }>
    };

    let totalEffectiveness = 0;
    const usageArray: Array<{ filename: string; count: number }> = [];

    for (const [filename, usage] of this.documentUsageHistory.entries()) {
      stats.totalUsages += usage.count;
      totalEffectiveness += usage.effectiveness;
      usageArray.push({ filename, count: usage.count });
    }

    if (this.documentUsageHistory.size > 0) {
      stats.averageEffectiveness = totalEffectiveness / this.documentUsageHistory.size;
    }

    stats.mostUsedDocuments = usageArray
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return stats;
  }

  /**
   * Réinitialiser l'historique d'utilisation
   */
  public clearUsageHistory(): void {
    this.documentUsageHistory.clear();
    this.queryDocumentPairs.clear();
  }

  /**
   * Exporter l'historique pour sauvegarde
   */
  public exportHistory(): {
    usage: Array<[string, any]>;
    pairs: Array<[string, string[]]>;
  } {
    return {
      usage: Array.from(this.documentUsageHistory.entries()),
      pairs: Array.from(this.queryDocumentPairs.entries())
    };
  }

  /**
   * Importer l'historique depuis une sauvegarde
   */
  public importHistory(data: {
    usage: Array<[string, any]>;
    pairs: Array<[string, string[]]>;
  }): void {
    this.documentUsageHistory = new Map(data.usage);
    this.queryDocumentPairs = new Map(data.pairs);
  }
}

// Instance globale
export const intelligentSuggestions = new IntelligentSuggestionsService();
