/**
 * Gestionnaire principal du système RAG
 * 
 * Responsabilités :
 * - Détection automatique du mode (rapide/performant)
 * - Orchestration du traitement des documents
 * - Interface unifiée pour claraApiService
 */

import { FastMode } from './modes/FastMode';
import { PerformantMode } from './modes/PerformantMode';
import {
  RagDocument,
  RagProcessingResult,
  RagConfig,
  OnContentChunk,
  RagModeInterface
} from './types/RagTypes';
import { getRagConfigForMode } from '../../config/constants';
import { getRagModeForSession } from '../session/SessionRagModeService';
import type { ClaraSessionConfig } from '../../types/clara_assistant_types';

export class RagManager {
  private fastMode: RagModeInterface;
  private performantMode: RagModeInterface;

  constructor() {
    this.fastMode = new FastMode();
    this.performantMode = new PerformantMode();
  }

  /**
   * Traite les documents selon le mode détecté par session
   */
  async processDocuments(
    documents: RagDocument[],
    userQuery: string,
    onContentChunk?: OnContentChunk,
    sessionId?: string,
    sessionConfig?: ClaraSessionConfig
  ): Promise<RagProcessingResult> {
    const config = this.detectRagMode(sessionId, sessionConfig);
    
    console.log(`🔍 RAG Manager: Mode détecté →`, config.mode === 'fast' ? 'RAPIDE ⚡' : 'PERFORMANT 🧠');

    const selectedMode = config.mode === 'fast' ? this.fastMode : this.performantMode;
    
    try {
      const result = await selectedMode.processDocuments(documents, userQuery, onContentChunk);
      
      console.log(`✅ RAG Manager: Traitement terminé en ${result.processingStats.processingTime}ms`);
      
      return result;
    } catch (error) {
      console.error('❌ RAG Manager: Erreur de traitement:', error);
      
      // Fallback vers mode rapide en cas d'erreur
      if (config.mode === 'performant') {
        console.log('🔄 RAG Manager: Fallback vers mode rapide');
        return await this.fastMode.processDocuments(documents, userQuery, onContentChunk);
      }
      
      throw error;
    }
  }

  /**
   * Détecte le mode RAG par session (nouveau système professionnel)
   */
  private detectRagMode(sessionId?: string, sessionConfig?: ClaraSessionConfig): RagConfig {
    try {
      // Vérification de l'environnement browser
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        console.log('🔍 RAG Manager: Environnement serveur détecté → Mode performant par défaut');
        return {
          mode: 'performant',
          threshold: 20000,
          maxContextLength: 100000,
          enableContextManagement: true
        };
      }

      let ragFastMode = false;

      // 🚀 NOUVEAU : Utiliser le mode par session si disponible
      if (sessionId) {
        const sessionRagMode = getRagModeForSession(sessionId, sessionConfig);
        ragFastMode = sessionRagMode === 'fast';
        console.log(`🎯 RAG Manager: Mode détecté pour session ${sessionId} → ${sessionRagMode.toUpperCase()}`);
      } else {
        // Fallback vers localStorage pour compatibilité
        const ragFastModeStr = localStorage.getItem('clara-rag-fast-mode');
        ragFastMode = ragFastModeStr ? JSON.parse(ragFastModeStr) : false;
        console.log(`🎯 RAG Manager: Mode global détecté → ${ragFastMode ? 'FAST' : 'PERFORMANT'} (no session)`);
      }

      // Utiliser la configuration unifiée
      const configForMode = getRagConfigForMode(ragFastMode);

      return {
        mode: ragFastMode ? 'fast' : 'performant',
        threshold: configForMode.THRESHOLD,
        maxContextLength: configForMode.MAX_CONTEXT,
        enableContextManagement: configForMode.ENABLE_CONTEXT_MGMT
      };
    } catch (error) {
      console.warn('⚠️ RAG Manager: Erreur de détection du mode, utilisation du mode performant par défaut');
      return {
        mode: 'performant',
        threshold: 20000,
        maxContextLength: 100000,
        enableContextManagement: true
      };
    }
  }

  /**
   * Convertit les documents du format Clara vers le format RAG
   */
  static convertDocuments(claraDocuments: any[]): RagDocument[] {
    return claraDocuments.map(doc => ({
      id: doc.id || `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      filename: doc.filename || 'Unknown Document',
      content: doc.content || '',
      fileType: doc.fileType || 'document',
      collectionName: doc.collectionName
    }));
  }

  /**
   * Obtient les statistiques du dernier traitement
   */
  getLastProcessingStats(): any {
    // Pourrait être étendu pour garder un historique des traitements
    return {
      lastMode: this.detectRagMode().mode,
      timestamp: new Date().toISOString()
    };
  }
}
