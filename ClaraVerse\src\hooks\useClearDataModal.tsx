import { useState } from 'react';
import { clearClaraDataWithConfirmation, executeClearClaraData } from '../utils/clearClaraData';

interface ClearDataStats {
  totalSessions: number;
  totalMessages: number;
  totalFiles: number;
}

interface UseClearDataModalReturn {
  isModalOpen: boolean;
  stats: ClearDataStats | null;
  isClearing: boolean;
  openModal: () => void;
  closeModal: () => void;
  confirmClear: () => Promise<void>;
}

export const useClearDataModal = (): UseClearDataModalReturn => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [stats, setStats] = useState<ClearDataStats | null>(null);
  const [isClearing, setIsClearing] = useState(false);

  const openModal = async () => {
    try {
      await clearClaraDataWithConfirmation(
        (statsData) => {
          setStats(statsData);
          setIsModalOpen(true);
        },
        () => {
          // User cancelled
          setIsModalOpen(false);
        }
      );
    } catch (error) {
      console.error('Error opening clear data modal:', error);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setStats(null);
  };

  const confirmClear = async () => {
    setIsClearing(true);
    try {
      const success = await executeClearClaraData();
      if (success) {
        // The page will reload automatically
        closeModal();
      }
    } catch (error) {
      console.error('Error clearing data:', error);
    } finally {
      setIsClearing(false);
    }
  };

  return {
    isModalOpen,
    stats,
    isClearing,
    openModal,
    closeModal,
    confirmClear
  };
};
