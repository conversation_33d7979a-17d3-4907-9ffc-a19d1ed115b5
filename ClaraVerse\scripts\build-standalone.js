#!/usr/bin/env node
/**
 * 🚀 Script de build pour app standalone
 * Crée une version standalone de WeMa IA pour déploiement client-serveur
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const BUILD_DIR = 'dist-standalone';
const BACKEND_DIR = 'py_backend';

console.log('🚀 Build WeMa IA Standalone...');

// 1. Nettoyer le dossier de build
console.log('🧹 Nettoyage...');
if (fs.existsSync(BUILD_DIR)) {
  fs.rmSync(BUILD_DIR, { recursive: true });
}
fs.mkdirSync(BUILD_DIR);

// 2. Build du frontend en mode production
console.log('⚛️ Build frontend...');
process.env.NODE_ENV = 'production';
process.env.REACT_APP_SERVER_MODE = 'true';

try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Frontend build réussi');
} catch (error) {
  console.error('❌ Erreur build frontend:', error.message);
  process.exit(1);
}

// 3. Copier le build frontend
console.log('📁 Copie frontend...');
const frontendBuildPath = path.join(__dirname, '..', 'dist');
const frontendDestPath = path.join(BUILD_DIR, 'frontend');

if (fs.existsSync(frontendBuildPath)) {
  fs.cpSync(frontendBuildPath, frontendDestPath, { recursive: true });
  console.log('✅ Frontend copié');
} else {
  console.error('❌ Dossier dist introuvable');
  process.exit(1);
}

// 4. Copier le backend
console.log('🐍 Copie backend...');
const backendSrcPath = path.join(__dirname, '..', BACKEND_DIR);
const backendDestPath = path.join(BUILD_DIR, 'backend');

if (fs.existsSync(backendSrcPath)) {
  fs.cpSync(backendSrcPath, backendDestPath, { recursive: true });
  console.log('✅ Backend copié');
} else {
  console.error('❌ Dossier backend introuvable');
  process.exit(1);
}

// 5. Créer les scripts de démarrage
console.log('📝 Création scripts de démarrage...');

// Script Windows
const windowsScript = `@echo off
echo 🚀 Demarrage WeMa IA Server...

REM Verifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouve. Installez Python 3.8+
    pause
    exit /b 1
)

REM Installer les dependances Python si necessaire
if not exist "backend\\venv" (
    echo 📦 Creation environnement virtuel...
    cd backend
    python -m venv venv
    call venv\\Scripts\\activate
    pip install -r requirements.txt
    cd ..
)

REM Demarrer le backend
echo 🐍 Demarrage backend...
cd backend
call venv\\Scripts\\activate
python start_server.py --production --host 0.0.0.0 --port 8000

pause`;

fs.writeFileSync(path.join(BUILD_DIR, 'start-server.bat'), windowsScript);

// Script Linux/Mac
const unixScript = `#!/bin/bash
echo "🚀 Démarrage WeMa IA Server..."

# Vérifier Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trouvé. Installez Python 3.8+"
    exit 1
fi

# Installer les dépendances Python si nécessaire
if [ ! -d "backend/venv" ]; then
    echo "📦 Création environnement virtuel..."
    cd backend
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
fi

# Démarrer le backend
echo "🐍 Démarrage backend..."
cd backend
source venv/bin/activate
python start_server.py --production --host 0.0.0.0 --port 8000`;

fs.writeFileSync(path.join(BUILD_DIR, 'start-server.sh'), unixScript);
fs.chmodSync(path.join(BUILD_DIR, 'start-server.sh'), '755');

// 6. Créer le fichier de configuration
const configFile = `# Configuration WeMa IA Server
# Modifiez ces valeurs selon votre environnement

# Backend
CLARA_HOST=0.0.0.0
CLARA_PORT=8000
CLARA_LOG_LEVEL=INFO

# Frontend (si servi par le backend)
FRONTEND_PORT=3000

# LM Studio (optionnel - peut être sur une autre machine)
LM_STUDIO_HOST=localhost
LM_STUDIO_PORT=1234

# Base de données
DATABASE_PATH=./data/clara.db

# Sécurité (pour production)
# SECRET_KEY=your-secret-key-here
# ALLOWED_HOSTS=your-domain.com,localhost`;

fs.writeFileSync(path.join(BUILD_DIR, 'config.env'), configFile);

// 7. Créer le README
const readmeContent = `# WeMa IA - Déploiement Serveur

## 🚀 Installation

### Prérequis
- Python 3.8+ avec pip
- 4GB RAM minimum
- 10GB espace disque

### Démarrage rapide

#### Windows
\`\`\`
start-server.bat
\`\`\`

#### Linux/Mac
\`\`\`
chmod +x start-server.sh
./start-server.sh
\`\`\`

## 🔧 Configuration

Modifiez \`config.env\` pour adapter à votre environnement.

### Variables importantes :
- \`CLARA_HOST\`: IP d'écoute (0.0.0.0 pour toutes les interfaces)
- \`CLARA_PORT\`: Port du backend (défaut: 8000)
- \`LM_STUDIO_HOST\`: IP de LM Studio si séparé

## 🌐 Accès

Une fois démarré :
- Backend API: http://votre-ip:8000
- Frontend: Servez le dossier \`frontend/\` avec un serveur web

## 📱 Frontend séparé

Pour servir le frontend séparément :

1. **Serveur web simple :**
   \`\`\`
   cd frontend
   python -m http.server 3000
   \`\`\`

2. **Nginx/Apache :** Pointez vers le dossier \`frontend/\`

3. **Variables d'environnement :**
   - \`REACT_APP_BACKEND_URL=http://votre-serveur:8000\`
   - \`REACT_APP_LM_STUDIO_URL=http://votre-serveur:1234\`

## 🔧 Dépannage

### Backend ne démarre pas
- Vérifiez Python 3.8+
- Installez les dépendances : \`pip install -r requirements.txt\`

### Frontend ne se connecte pas
- Vérifiez l'URL du backend dans la configuration
- Vérifiez les CORS si domaines différents

### LM Studio inaccessible
- Démarrez LM Studio avec l'option serveur
- Vérifiez l'IP et le port dans la configuration
`;

fs.writeFileSync(path.join(BUILD_DIR, 'README.md'), readmeContent);

console.log('✅ Build standalone terminé !');
console.log(`📁 Dossier: ${BUILD_DIR}/`);
console.log('🚀 Prêt pour déploiement serveur !');
