/**
 * GDPR API Service
 * Handles all API communications for GDPR document processing
 */

import {
  OCRRequest,
  OCRResult,
  AnonymizationRequest,
  AnonymizationResult,
  AnonymizationPreview,
  DeanonymizationRequest,
  DocumentProcessRequest,
  DocumentProcessResponse,
  APIResponse,
  Collection
} from '../types/gdpr-types';

class GDPRApiService {
  private baseUrl: string;

  constructor() {
    // Use unified backend URL (localhost pour cohérence)
    this.baseUrl = 'http://localhost:8000';
  }

  /**
   * Convert file to base64 string
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix (e.g., "data:application/pdf;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Handle API responses with error checking
   */
  private async handleResponse<T>(response: Response): Promise<APIResponse<T>> {
    try {
      console.log('🌐 Response status:', response.status, response.statusText);
      const data = await response.json();
      console.log('📥 Response data:', data);

      if (!response.ok) {
        console.error('❌ Response not OK:', data);
        return {
          success: false,
          error: data.detail || data.error || `HTTP ${response.status}`,
          data: undefined
        };
      }

      console.log('✅ Response OK, returning data');
      return {
        success: true,
        data: data,
        error: undefined
      };
    } catch (error) {
      console.error('❌ Failed to parse response:', error);
      return {
        success: false,
        error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // OCR API Methods
  // ============================================================================

  /**
   * Process document with OCR
   */
  async processOCR(file: File, options: Partial<OCRRequest> = {}): Promise<APIResponse<OCRResult>> {
    try {
      console.log('🔍 Processing OCR for file:', file.name, 'Size:', file.size);

      const fileBase64 = await this.fileToBase64(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';

      console.log('📄 File type:', fileType, 'Base64 length:', fileBase64.length);

      const request = {
        file_base64: fileBase64,
        file_type: fileType,
        preserve_layout: options.preserve_layout ?? true,
        language: options.language ?? 'eng+fra',
        dpi: options.dpi ?? 300,
        detect_tables: options.detect_tables ?? true,
        table_mode: options.table_mode ?? false
      };

      console.log('📤 Sending OCR request:', { ...request, file_base64: `[${fileBase64.length} chars]` });
      console.log('🌐 OCR URL:', `${this.baseUrl}/ocr/process`);

      const response = await fetch(`${this.baseUrl}/ocr/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<OCRResult>(response);
    } catch (error) {
      return {
        success: false,
        error: `OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // GDPR Anonymization API Methods
  // ============================================================================

  /**
   * Anonymize text for GDPR compliance
   */
  async anonymizeText(request: AnonymizationRequest): Promise<APIResponse<AnonymizationResult>> {
    try {
      console.log('🛡️ Anonymizing text:', { ...request, text: `[${request.text?.length || 0} chars]` });

      const response = await fetch(`${this.baseUrl}/gdpr/anonymize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<AnonymizationResult>(response);
    } catch (error) {
      return {
        success: false,
        error: `Anonymization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Preview anonymization without actually anonymizing
   */
  async previewAnonymization(text: string, language: string = 'en'): Promise<APIResponse<AnonymizationPreview>> {
    try {
      const response = await fetch(`${this.baseUrl}/gdpr/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text, language })
      });

      return this.handleResponse<{ preview: AnonymizationPreview }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Preview failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Restore original text from anonymized version
   */
  async deanonymizeText(request: DeanonymizationRequest): Promise<APIResponse<{ restoredText: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/gdpr/deanonymize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<{ restoredText: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Deanonymization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // Combined Document Processing API Methods
  // ============================================================================

  /**
   * Process document with OCR and optional GDPR anonymization
   */
  async processDocumentWithGDPR(
    file: File, 
    options: Partial<DocumentProcessRequest> = {}
  ): Promise<APIResponse<DocumentProcessResponse>> {
    try {
      const fileBase64 = await this.fileToBase64(file);
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';

      const request = {
        file_base64: fileBase64,
        file_type: fileType,
        filename: file.name,
        collection_name: options.collectionName ?? 'default_collection',
        anonymize: options.anonymize ?? true,
        preserve_layout: options.preserveLayout ?? true,
        ocr_language: options.ocrLanguage ?? 'eng+fra',
        anonymization_language: options.anonymizationLanguage ?? 'en'
      };

      const response = await fetch(`${this.baseUrl}/documents/process-with-gdpr`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      return this.handleResponse<DocumentProcessResponse>(response);
    } catch (error) {
      return {
        success: false,
        error: `Document processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  // ============================================================================
  // Collection Management API Methods
  // ============================================================================

  /**
   * Get all collections
   */
  async getCollections(): Promise<APIResponse<{ collections: Collection[] }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections`);
      return this.handleResponse<{ collections: Collection[] }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to fetch collections: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Create a new collection
   */
  async createCollection(name: string, description?: string): Promise<APIResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, description })
      });

      return this.handleResponse<{ message: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to create collection: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }

  /**
   * Delete a collection
   */
  async deleteCollection(collectionName: string): Promise<APIResponse<{ message: string }>> {
    try {
      const response = await fetch(`${this.baseUrl}/collections/${collectionName}`, {
        method: 'DELETE'
      });

      return this.handleResponse<{ message: string }>(response);
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete collection: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: undefined
      };
    }
  }



  // ============================================================================
  // Document Management Methods
  // ============================================================================

  /**
   * Get all documents, optionally filtered by collection
   */
  async getDocuments(collectionName?: string): Promise<APIResponse<DocumentInfo[]>> {
    try {
      const url = collectionName
        ? `${this.baseUrl}/documents?collection_name=${encodeURIComponent(collectionName)}`
        : `${this.baseUrl}/documents`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Ensure all documents have required properties
      const documents = (data.documents || []).map((doc: any) => ({
        id: doc.id || 0,
        filename: doc.filename || 'Unknown Document',
        fileType: doc.file_type || doc.fileType || 'unknown',
        collectionName: doc.collection_name || doc.collectionName || 'undefined',
        metadata: doc.metadata || {},
        createdAt: doc.created_at || doc.createdAt || new Date().toISOString(),
        chunkCount: doc.chunk_count || doc.chunkCount || 0,
        isAnonymized: doc.is_anonymized || doc.isAnonymized || false,
        originalContent: doc.original_content || doc.originalContent,
        anonymizedContent: doc.anonymized_content || doc.anonymizedContent,
        anonymizationMappings: doc.anonymization_mappings || doc.anonymizationMappings
      }));

      return {
        success: true,
        data: documents
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: []
      };
    }
  }

  /**
   * Get a specific document by ID
   */
  async getDocument(documentId: number): Promise<APIResponse<DocumentInfo>> {
    try {
      const response = await fetch(`${this.baseUrl}/documents/${documentId}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: {} as DocumentInfo
      };
    }
  }

  /**
   * Update document content
   */
  async updateDocument(request: DocumentEditRequest): Promise<APIResponse<DocumentInfo>> {
    try {
      const response = await fetch(`${this.baseUrl}/documents/${request.documentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          new_content: request.newContent,
          preserve_anonymization: request.preserveAnonymization || false
        }),
      });

      return this.handleResponse<DocumentInfo>(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: {} as DocumentInfo
      };
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: number, collectionName: string): Promise<APIResponse<void>> {
    try {
      const response = await fetch(`${this.baseUrl}/documents/${documentId}?collection_name=${encodeURIComponent(collectionName)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: undefined
      };
    }
  }

  /**
   * Search documents
   */
  async searchDocuments(
    query: string,
    collectionName: string,
    limit: number = 10
  ): Promise<APIResponse<DocumentSearchResult[]>> {
    try {
      const response = await fetch(`${this.baseUrl}/documents/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          collection_name: collectionName,
          k: limit
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Transform the search results to match our interface
      const searchResults: DocumentSearchResult[] = data.results?.map((result: any) => ({
        document: {
          id: 0, // We'll need to enhance the backend to return document info
          filename: result.metadata?.source_file || 'Unknown',
          fileType: result.metadata?.file_type || 'unknown',
          collectionName: collectionName,
          metadata: result.metadata || {},
          createdAt: result.metadata?.created_at || new Date().toISOString(),
          chunkCount: 1,
          isAnonymized: result.metadata?.anonymized || false
        } as DocumentInfo,
        relevantChunks: [{
          content: result.content,
          metadata: result.metadata,
          score: result.score
        }]
      })) || [];

      return {
        success: true,
        data: searchResults
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: []
      };
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Check if GDPR/OCR services are available
   */
  async checkServiceAvailability(): Promise<APIResponse<{ available: boolean; services: string[] }>> {
    try {
      // Test both OCR and GDPR endpoints with minimal requests
      const testRequests = await Promise.allSettled([
        fetch(`${this.baseUrl}/gdpr/preview`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: 'test', language: 'en' })
        }),
        fetch(`${this.baseUrl}/ocr/process`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            fileBase64: 'dGVzdA==', // base64 for "test"
            fileType: 'txt' 
          })
        })
      ]);

      const availableServices: string[] = [];
      
      if (testRequests[0].status === 'fulfilled' && testRequests[0].value.status !== 503) {
        availableServices.push('GDPR');
      }
      
      if (testRequests[1].status === 'fulfilled' && testRequests[1].value.status !== 503) {
        availableServices.push('OCR');
      }

      return {
        success: true,
        data: {
          available: availableServices.length > 0,
          services: availableServices
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Service check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { available: false, services: [] }
      };
    }
  }

  /**
   * Get supported file formats
   */
  getSupportedFormats(): string[] {
    return [
      'pdf',   // Scanned PDFs
      'png', 'jpg', 'jpeg', 'tiff', 'bmp', 'gif',  // Images
      'docx',  // Word documents
      'txt', 'md'  // Text files
    ];
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedFormats();
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      return {
        valid: false,
        error: `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`
      };
    }

    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  }
}

// Export singleton instance
export const gdprApiService = new GDPRApiService();
export default gdprApiService;
