"""
🚀 UNIFIED RAG SERVICE - LightRAG + BGE-M3 + Qdrant
Architecture optimale pour serveur local avec performance maximale

Features:
- LightRAG pour graph-based retrieval
- BGE-M3 embeddings multilingues
- Qdrant vector database
- Cache Redis intelligent
- Monitoring performance
- Fallback ChromaDB
"""

import asyncio
import logging
import time
import json
import hashlib
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import os

# Core RAG
try:
    from lightrag import LightRAG, QueryParam
    # Try different import paths for LightRAG LLM functions
    try:
        from lightrag.llm.ollama_impl import ollama_complete, ollama_embedding
    except ImportError:
        try:
            from lightrag.llm import ollama_complete, ollama_embedding
        except ImportError:
            # Fallback - we'll implement our own Ollama integration
            ollama_complete = None
            ollama_embedding = None

    LIGHTRAG_AVAILABLE = True
    print("✅ LightRAG imported successfully")
except ImportError as e:
    LIGHTRAG_AVAILABLE = False
    ollama_complete = None
    ollama_embedding = None
    print(f"❌ LightRAG import failed: {e}")
    logging.warning(f"LightRAG not available: {e}, using fallback")

# Vector Databases
try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    logging.warning("Qdrant not available, using ChromaDB fallback")

# Embeddings
try:
    from FlagEmbedding import BGEM3FlagModel
    BGE_AVAILABLE = True
except ImportError:
    BGE_AVAILABLE = False
    logging.warning("BGE-M3 not available, using Ollama embeddings")

# Cache
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available, using memory cache")

# Fallback imports
import chromadb
from langchain_ollama import OllamaEmbeddings
from langchain_chroma import Chroma

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# OLLAMA INTEGRATION FOR LIGHTRAG
# ============================================================================

async def custom_ollama_complete(prompt, model="gemma3:4b", **kwargs):
    """Custom Ollama completion function for LightRAG"""
    import aiohttp

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    **kwargs
                }
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("response", "")
                else:
                    logger.error(f"Ollama completion failed: {response.status}")
                    return ""
    except Exception as e:
        logger.error(f"Ollama completion error: {e}")
        return ""

async def custom_ollama_embedding(text, model="mxbai-embed-large", **kwargs):
    """Custom Ollama embedding function for LightRAG"""
    import aiohttp

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:11434/api/embeddings",
                json={
                    "model": model,
                    "prompt": text,
                    **kwargs
                }
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get("embedding", [])
                else:
                    logger.error(f"Ollama embedding failed: {response.status}")
                    return []
    except Exception as e:
        logger.error(f"Ollama embedding error: {e}")
        return []

# Use custom functions if official ones are not available
if ollama_complete is None:
    ollama_complete = custom_ollama_complete
if ollama_embedding is None:
    ollama_embedding = custom_ollama_embedding

# ============================================================================
# CONFIGURATION
# ============================================================================

@dataclass
class RAGConfig:
    """Configuration centralisée pour le service RAG"""
    
    # LightRAG Configuration
    lightrag_dir: str = "./lightrag_storage"
    
    # Qdrant Configuration
    qdrant_url: str = "http://localhost:6333"
    qdrant_collection: str = "wema_documents"
    
    # BGE-M3 Configuration
    bge_model_name: str = "BAAI/bge-m3"
    bge_device: str = "cpu"  # ou "cuda" si GPU disponible
    
    # Ollama Configuration - DÉLÉGATION D'INFÉRENCE AU SERVEUR CENTRAL
    ollama_base_url: str = "http://***********:11434"  # SERVEUR CENTRAL pour inférence IA
    ollama_embedding_model: str = "mxbai-embed-large"
    ollama_llm_model: str = "magistral"  # Modèle par défaut selon les préférences utilisateur

    # Fallback local si serveur central indisponible
    ollama_fallback_url: str = "http://localhost:11434"
    enable_fallback: bool = True
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379"
    cache_ttl: int = 3600  # 1 heure
    
    # Performance Configuration
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_results: int = 8
    score_threshold: float = 0.1
    
    # ChromaDB Fallback
    chroma_persist_dir: str = "./chroma_fallback"

# ============================================================================
# CACHE MANAGER
# ============================================================================

class CacheManager:
    """Gestionnaire de cache intelligent avec Redis + fallback mémoire"""
    
    def __init__(self, config: RAGConfig):
        self.config = config
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {"hits": 0, "misses": 0}
        
    async def initialize(self):
        """Initialiser la connexion Redis"""
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(self.config.redis_url)
                await self.redis_client.ping()
                logger.info("✅ Redis cache initialized")
            except Exception as e:
                logger.warning(f"Redis connection failed: {e}, using memory cache")
                self.redis_client = None
        
    def _generate_key(self, query: str, params: Dict[str, Any]) -> str:
        """Générer une clé de cache unique avec normalisation"""
        # Normalize query (lowercase, strip, remove extra spaces)
        normalized_query = " ".join(query.lower().strip().split())

        # Create stable key with sorted params
        key_data = f"{normalized_query}:{json.dumps(params, sort_keys=True)}"
        return f"rag:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    async def get(self, query: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Récupérer du cache"""
        key = self._generate_key(query, params)
        
        # Try Redis first
        if self.redis_client:
            try:
                cached = await self.redis_client.get(key)
                if cached:
                    self.cache_stats["hits"] += 1
                    logger.debug(f"🎯 Cache HIT (Redis): {query[:30]}...")
                    return json.loads(cached)
            except Exception as e:
                logger.warning(f"Redis get error: {e}")
        
        # Fallback to memory cache
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            if time.time() - entry["timestamp"] < self.config.cache_ttl:
                self.cache_stats["hits"] += 1
                logger.debug(f"🎯 Cache HIT (Memory): {query[:30]}...")
                return entry["data"]
            else:
                del self.memory_cache[key]
        
        self.cache_stats["misses"] += 1
        return None
    
    async def set(self, query: str, params: Dict[str, Any], data: Dict[str, Any]):
        """Stocker en cache"""
        key = self._generate_key(query, params)
        
        # Try Redis first
        if self.redis_client:
            try:
                await self.redis_client.setex(
                    key, 
                    self.config.cache_ttl, 
                    json.dumps(data)
                )
                logger.debug(f"💾 Cache SET (Redis): {query[:30]}...")
                return
            except Exception as e:
                logger.warning(f"Redis set error: {e}")
        
        # Fallback to memory cache
        self.memory_cache[key] = {
            "data": data,
            "timestamp": time.time()
        }
        
        # Cleanup old entries
        current_time = time.time()
        expired_keys = [
            k for k, v in self.memory_cache.items()
            if current_time - v["timestamp"] > self.config.cache_ttl
        ]
        for k in expired_keys:
            del self.memory_cache[k]
        
        logger.debug(f"💾 Cache SET (Memory): {query[:30]}...")
    
    def get_stats(self) -> Dict[str, Any]:
        """Statistiques du cache"""
        total = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total * 100) if total > 0 else 0
        
        return {
            "hits": self.cache_stats["hits"],
            "misses": self.cache_stats["misses"],
            "hit_rate": round(hit_rate, 2),
            "backend": "redis" if self.redis_client else "memory",
            "memory_entries": len(self.memory_cache)
        }

# ============================================================================
# EMBEDDING MANAGER
# ============================================================================

class EmbeddingManager:
    """Gestionnaire d'embeddings avec BGE-M3 + fallback Ollama"""
    
    def __init__(self, config: RAGConfig):
        self.config = config
        self.bge_model = None
        self.ollama_embeddings = None
        
    async def initialize(self):
        """Initialiser les modèles d'embeddings"""
        # Initialize both BGE-M3 and Ollama (don't return early)
        bge_success = False
        ollama_success = False

        # Try BGE-M3 first
        if self.config.bge_device == "disabled" or os.getenv('DISABLE_BGE') == '1':
            logger.info("🚫 BGE-M3 disabled by configuration")
        elif BGE_AVAILABLE:
            try:
                logger.info("🔄 Loading BGE-M3 model...")
                self.bge_model = BGEM3FlagModel(
                    self.config.bge_model_name,
                    device=self.config.bge_device,
                    use_fp16=True,  # Optimize for speed
                    max_length=512  # Optimize for performance
                )
                logger.info("✅ BGE-M3 model loaded with optimizations")
                bge_success = True
            except Exception as e:
                logger.warning(f"BGE-M3 loading failed: {e}")

        # Always try to initialize Ollama as well
        try:
            logger.info("🔄 Initializing Ollama embeddings...")
            self.ollama_embeddings = OllamaEmbeddings(
                base_url=self.config.ollama_base_url,
                model=self.config.ollama_embedding_model
                # Remove invalid parameters for newer versions
            )
            logger.info("✅ Ollama embeddings initialized with optimizations")
            ollama_success = True
        except Exception as e:
            logger.warning(f"Ollama initialization failed: {e}")

        # Check if at least one embedding model is available
        if not bge_success and not ollama_success:
            logger.error("❌ No embedding models available!")
            raise RuntimeError("Failed to initialize any embedding model")

        logger.info(f"📊 Embedding models available: BGE-M3={bge_success}, Ollama={ollama_success}")
    
    async def embed_query(self, text: str) -> List[float]:
        """Générer embedding pour une requête"""
        if not text or not text.strip():
            logger.error("Empty text provided for embedding")
            raise RuntimeError("Empty text provided for embedding")

        text = text.strip()
        logger.info(f"🔤 Embedding text: {text[:50]}...")

        # Try Ollama FIRST (more reliable)
        logger.info(f"🔍 Ollama embeddings available: {self.ollama_embeddings is not None}")
        if self.ollama_embeddings:
            try:
                logger.info("🔄 Using Ollama embeddings...")
                embedding = await asyncio.to_thread(self.ollama_embeddings.embed_query, text)
                if embedding and len(embedding) > 0:
                    logger.info(f"✅ Ollama embedding successful: {len(embedding)} dimensions")
                    return embedding
                else:
                    logger.warning("⚠️ Ollama returned empty embeddings")
            except Exception as e:
                logger.error(f"❌ Ollama embedding failed: {e}")
        else:
            logger.warning("⚠️ Ollama embeddings not initialized")

        # Fallback to BGE-M3
        if self.bge_model:
            try:
                logger.info("🔄 Fallback to BGE-M3...")
                # Use dense embedding only for better compatibility
                embeddings = self.bge_model.encode([text], return_dense=True, return_sparse=False, return_colbert_vecs=False)

                if embeddings is not None and len(embeddings) > 0:
                    # Handle different return formats
                    if hasattr(embeddings, 'dense_vecs') and embeddings.dense_vecs is not None:
                        result = embeddings.dense_vecs[0].tolist()
                    elif isinstance(embeddings, list) and len(embeddings) > 0:
                        result = embeddings[0].tolist() if hasattr(embeddings[0], 'tolist') else embeddings[0]
                    else:
                        result = embeddings.tolist() if hasattr(embeddings, 'tolist') else embeddings

                    if result and len(result) > 0:
                        logger.info(f"✅ BGE-M3 embedding successful: {len(result)} dimensions")
                        return result
                    else:
                        logger.warning("⚠️ BGE-M3 returned empty result after processing")
                else:
                    logger.warning("⚠️ BGE-M3 returned None or empty embeddings")
            except Exception as e:
                logger.warning(f"❌ BGE-M3 embedding failed: {e}")
                import traceback
                logger.debug(f"BGE-M3 traceback: {traceback.format_exc()}")

        raise RuntimeError("No embedding model available")
    
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Générer embeddings pour plusieurs documents"""
        logger.info(f"🔤 Embedding {len(texts)} documents...")

        # Try Ollama FIRST (more reliable)
        if self.ollama_embeddings:
            try:
                logger.info("🔄 Using Ollama for batch embeddings...")
                result = await asyncio.to_thread(self.ollama_embeddings.embed_documents, texts)
                if result and len(result) > 0:
                    logger.info(f"✅ Ollama batch embedding successful: {len(result)} embeddings")
                    return result
                else:
                    logger.warning("⚠️ Ollama returned empty batch embeddings")
            except Exception as e:
                logger.error(f"❌ Ollama batch embedding failed: {e}")

        # Fallback to BGE-M3
        if self.bge_model:
            try:
                logger.info("🔄 Fallback to BGE-M3 for batch embeddings...")
                embeddings = self.bge_model.encode(texts)
                if embeddings is not None and len(embeddings) > 0:
                    result = [emb.tolist() for emb in embeddings]
                    logger.info(f"✅ BGE-M3 batch embedding successful: {len(result)} embeddings")
                    return result
                else:
                    logger.warning("⚠️ BGE-M3 returned empty batch embeddings")
            except Exception as e:
                logger.warning(f"❌ BGE-M3 batch embedding failed: {e}")

        raise RuntimeError("No embedding model available")

# ============================================================================
# VECTOR STORE MANAGER
# ============================================================================

class VectorStoreManager:
    """Gestionnaire de base vectorielle avec Qdrant + fallback ChromaDB"""
    
    def __init__(self, config: RAGConfig, embedding_manager: EmbeddingManager):
        self.config = config
        self.embedding_manager = embedding_manager
        self.qdrant_client = None
        self.chroma_client = None
        self.vector_store = None
        
    async def initialize(self):
        """Initialiser la base vectorielle"""
        # Try Qdrant first
        if QDRANT_AVAILABLE:
            try:
                self.qdrant_client = QdrantClient(url=self.config.qdrant_url)
                
                # Create collection if not exists
                collections = await asyncio.to_thread(self.qdrant_client.get_collections)
                collection_names = [c.name for c in collections.collections]
                
                if self.config.qdrant_collection not in collection_names:
                    await asyncio.to_thread(
                        self.qdrant_client.create_collection,
                        collection_name=self.config.qdrant_collection,
                        vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
                    )
                
                logger.info("✅ Qdrant vector store initialized")
                return
            except Exception as e:
                logger.warning(f"Qdrant initialization failed: {e}")
        
        # Fallback to ChromaDB
        try:
            self.chroma_client = chromadb.PersistentClient(
                path=self.config.chroma_persist_dir
            )
            logger.info("✅ ChromaDB fallback initialized")
        except Exception as e:
            logger.error(f"Vector store initialization failed: {e}")
            # Create a minimal in-memory client as last resort
            try:
                self.chroma_client = chromadb.Client()
                logger.info("✅ ChromaDB in-memory fallback initialized")
            except Exception as e2:
                logger.error(f"All vector store initialization failed: {e2}")
                # Don't raise - allow service to continue without vector store
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """Ajouter des documents à la base vectorielle"""
        # Try Qdrant first if available
        if self.qdrant_client:
            try:
                result = await self._add_to_qdrant(documents)
                if result:
                    logger.info("✅ Documents added to Qdrant successfully")
                    return True
                else:
                    logger.warning("⚠️ Qdrant add failed, trying ChromaDB fallback")
            except Exception as e:
                logger.warning(f"❌ Qdrant add failed: {e}, trying ChromaDB fallback")

        # Fallback to ChromaDB
        if self.chroma_client:
            try:
                result = await self._add_to_chroma(documents)
                if result:
                    logger.info("✅ Documents added to ChromaDB successfully")
                    return True
                else:
                    logger.error("❌ ChromaDB add failed")
            except Exception as e:
                logger.error(f"❌ ChromaDB add failed: {e}")

        logger.error("❌ No vector store available for adding documents")
        return False
    
    async def search(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Rechercher dans la base vectorielle"""
        limit = limit or self.config.max_results

        # Try Qdrant first if available
        if self.qdrant_client:
            try:
                results = await self._search_qdrant(query, limit)
                if results:
                    logger.info(f"✅ Qdrant search found {len(results)} results")
                    return results
                else:
                    logger.warning("⚠️ Qdrant search returned no results, trying ChromaDB")
            except Exception as e:
                logger.warning(f"❌ Qdrant search failed: {e}, trying ChromaDB fallback")

        # Fallback to ChromaDB
        if self.chroma_client:
            try:
                results = await self._search_chroma(query, limit)
                if results:
                    logger.info(f"✅ ChromaDB search found {len(results)} results")
                    return results
                else:
                    logger.warning("⚠️ ChromaDB search returned no results")
            except Exception as e:
                logger.error(f"❌ ChromaDB search failed: {e}")

        logger.warning("❌ No vector store available or all searches failed")
        return []
    
    async def _add_to_qdrant(self, documents: List[Dict[str, Any]]) -> bool:
        """Ajouter à Qdrant"""
        try:
            # Prepare points for Qdrant
            points = []

            for i, doc in enumerate(documents):
                # Get content from document (handle different formats)
                content = doc.get("content") or doc.get("text") or str(doc)
                if not content:
                    logger.warning(f"Document {i} has no content, skipping")
                    continue

                # Generate embedding
                embedding = await self.embedding_manager.embed_query(content)

                # Create point
                point = PointStruct(
                    id=int(time.time() * 1000) + i,  # Unique ID
                    vector=embedding,
                    payload={
                        "content": content,
                        "metadata": doc.get("metadata", {}),
                        "timestamp": time.time()
                    }
                )
                points.append(point)

            # Upload to Qdrant
            await asyncio.to_thread(
                self.qdrant_client.upsert,
                collection_name=self.config.qdrant_collection,
                points=points
            )

            logger.info(f"✅ Added {len(documents)} documents to Qdrant")
            return True

        except Exception as e:
            logger.error(f"Failed to add documents to Qdrant: {e}")
            return False
    
    async def _search_qdrant(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Rechercher dans Qdrant"""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_manager.embed_query(query)

            # Search in Qdrant
            search_result = await asyncio.to_thread(
                self.qdrant_client.search,
                collection_name=self.config.qdrant_collection,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=self.config.score_threshold
            )

            # Format results
            formatted_results = []
            for point in search_result:
                formatted_results.append({
                    "content": point.payload.get("content", ""),
                    "metadata": point.payload.get("metadata", {}),
                    "score": point.score
                })

            return formatted_results

        except Exception as e:
            logger.error(f"Qdrant search failed: {e}")
            return []
    
    async def _add_to_chroma(self, documents: List[Dict[str, Any]]) -> bool:
        """Ajouter à ChromaDB"""
        try:
            # Get or create collection (use consistent name)
            collection_name = "wema_documents"  # Fixed collection name
            collection = self.chroma_client.get_or_create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"}
            )

            logger.info(f"📄 Adding to ChromaDB collection: {collection_name}")

            # Prepare data (handle different content formats)
            texts = []
            metadatas = []
            for doc in documents:
                content = doc.get("content") or doc.get("text") or str(doc)
                if content:
                    texts.append(content)
                    metadatas.append(doc.get("metadata", {}))

            if not texts:
                logger.warning("No valid texts found in documents")
                return False

            ids = [f"doc_{i}_{int(time.time())}" for i in range(len(texts))]

            # Generate embeddings
            embeddings = await self.embedding_manager.embed_documents(texts)

            # Add to collection
            collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )

            logger.info(f"✅ Added {len(documents)} documents to ChromaDB")
            return True

        except Exception as e:
            logger.error(f"Failed to add documents to ChromaDB: {e}")
            return False
    
    async def _search_chroma(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Rechercher dans ChromaDB"""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_manager.embed_query(query)

            # Get or create collection (use consistent name)
            collection_name = "wema_documents"  # Fixed collection name
            collection = self.chroma_client.get_or_create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"}
            )

            logger.info(f"🔍 Searching in ChromaDB collection: {collection_name}")
            collection_count = collection.count()
            logger.info(f"📊 Collection count: {collection_count}")

            if collection_count == 0:
                logger.warning("⚠️ Collection is empty! No documents to search.")
                return []

            # List all collections for debug
            all_collections = self.chroma_client.list_collections()
            logger.info(f"🗂️ All collections: {[c.name for c in all_collections]}")

            # Check if documents exist in any collection
            for coll in all_collections:
                count = coll.count()
                logger.info(f"   Collection '{coll.name}': {count} documents")

            # Search
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                include=["documents", "metadatas", "distances"]
            )

            # Format results
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    formatted_results.append({
                        "content": doc,
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "score": 1 - results["distances"][0][i] if results["distances"] else 0.0
                    })

            return formatted_results

        except Exception as e:
            logger.error(f"ChromaDB search failed: {e}")
            return []

# ============================================================================
# LIGHTRAG MANAGER
# ============================================================================

class LightRAGManager:
    """Gestionnaire LightRAG pour graph-based retrieval"""

    def __init__(self, config: RAGConfig):
        self.config = config
        self.lightrag = None

    async def initialize(self):
        """Initialiser LightRAG"""
        if not LIGHTRAG_AVAILABLE:
            logger.warning("LightRAG not available, skipping initialization")
            return False

        try:
            # Create storage directory
            os.makedirs(self.config.lightrag_dir, exist_ok=True)

            # Initialize LightRAG
            self.lightrag = LightRAG(
                working_dir=self.config.lightrag_dir,
                llm_model_func=ollama_complete,
                llm_model_name=self.config.ollama_llm_model,
                llm_model_max_async=4,
                llm_model_max_token_size=32768,
                llm_model_kwargs={
                    "host": self.config.ollama_base_url,
                    "options": {"temperature": 0}
                },
                embedding_func=ollama_embedding,
                embedding_model_name=self.config.ollama_embedding_model,
                embedding_model_kwargs={
                    "host": self.config.ollama_base_url
                }
            )

            logger.info("✅ LightRAG initialized")
            return True

        except Exception as e:
            logger.error(f"LightRAG initialization failed: {e}")
            return False

    async def add_document(self, text: str, metadata: Dict[str, Any] = None) -> bool:
        """Ajouter un document à LightRAG"""
        if not self.lightrag:
            return False

        try:
            await self.lightrag.ainsert(text)
            logger.info(f"✅ Document added to LightRAG: {len(text)} chars")
            return True
        except Exception as e:
            logger.error(f"Failed to add document to LightRAG: {e}")
            return False

    async def search(self, query: str, mode: str = "hybrid") -> str:
        """Rechercher avec LightRAG"""
        if not self.lightrag:
            return ""

        try:
            result = await self.lightrag.aquery(
                query,
                param=QueryParam(mode=mode)
            )
            return result or ""
        except Exception as e:
            logger.error(f"LightRAG search failed: {e}")
            return ""

# ============================================================================
# UNIFIED RAG SERVICE
# ============================================================================

class UnifiedRAGService:
    """Service RAG unifié combinant tous les composants optimaux"""

    def __init__(self, config: RAGConfig = None):
        self.config = config or RAGConfig()

        # Initialize components
        self.cache_manager = CacheManager(self.config)
        self.embedding_manager = EmbeddingManager(self.config)
        self.vector_store_manager = VectorStoreManager(self.config, self.embedding_manager)
        self.lightrag_manager = LightRAGManager(self.config)

        # Performance metrics
        self.metrics = {
            "total_queries": 0,
            "total_documents": 0,
            "average_response_time": 0,
            "cache_hit_rate": 0,
            "active_since": time.time(),
            "ollama_server_status": "unknown",
            "ollama_active_url": self.config.ollama_base_url
        }

        self.initialized = False

    async def _test_ollama_connectivity(self):
        """Tester la connectivité Ollama avec fallback automatique"""
        import requests

        # Test serveur central d'abord
        try:
            response = requests.get(f"{self.config.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.metrics["ollama_server_status"] = "central_server_ok"
                self.metrics["ollama_active_url"] = self.config.ollama_base_url
                logger.info(f"✅ Ollama serveur central accessible: {self.config.ollama_base_url}")
                return True
        except Exception as e:
            logger.warning(f"⚠️ Serveur central Ollama inaccessible: {e}")

        # Fallback vers serveur local si activé
        if hasattr(self.config, 'enable_fallback') and self.config.enable_fallback:
            try:
                fallback_url = getattr(self.config, 'ollama_fallback_url', 'http://localhost:11434')
                response = requests.get(f"{fallback_url}/api/tags", timeout=3)
                if response.status_code == 200:
                    self.metrics["ollama_server_status"] = "fallback_local_ok"
                    self.metrics["ollama_active_url"] = fallback_url
                    # Mettre à jour la config pour utiliser le fallback
                    self.config.ollama_base_url = fallback_url
                    logger.info(f"✅ Ollama fallback local accessible: {fallback_url}")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ Ollama fallback local inaccessible: {e}")

        self.metrics["ollama_server_status"] = "unavailable"
        logger.error("❌ Aucun serveur Ollama accessible (central ou local)")
        return False

    async def initialize(self):
        """Initialiser tous les composants"""
        if self.initialized:
            return True

        logger.info("🚀 Initializing Unified RAG Service...")

        try:
            # Test connectivité Ollama en premier
            await self._test_ollama_connectivity()

            # Initialize all components
            await self.cache_manager.initialize()
            await self.embedding_manager.initialize()
            await self.vector_store_manager.initialize()
            await self.lightrag_manager.initialize()

            self.initialized = True
            logger.info("✅ Unified RAG Service initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize RAG service: {e}")
            return False

    async def add_document(
        self,
        text: str,
        metadata: Dict[str, Any] = None,
        use_lightrag: bool = True,
        use_vector_store: bool = True
    ) -> Dict[str, Any]:
        """Ajouter un document au système RAG"""
        if not self.initialized:
            await self.initialize()

        start_time = time.time()
        results = {"lightrag": False, "vector_store": False}

        try:
            # Add to LightRAG for graph-based retrieval
            if use_lightrag:
                results["lightrag"] = await self.lightrag_manager.add_document(text, metadata)

            # Add to vector store for similarity search
            if use_vector_store:
                documents = [{
                    "text": text,
                    "metadata": metadata or {}
                }]
                results["vector_store"] = await self.vector_store_manager.add_documents(documents)

            self.metrics["total_documents"] += 1
            processing_time = time.time() - start_time

            logger.info(f"📄 Document added in {processing_time:.2f}s: {results}")

            return {
                "success": any(results.values()),
                "results": results,
                "processing_time": processing_time
            }

        except Exception as e:
            logger.error(f"Failed to add document: {e}")
            return {"success": False, "error": str(e)}

    async def search(
        self,
        query: str,
        use_cache: bool = True,
        use_lightrag: bool = True,
        use_vector_store: bool = True,
        limit: int = None,
        fast_mode: bool = False
    ) -> Dict[str, Any]:
        """Recherche unifiée avec cache intelligent"""
        if not self.initialized:
            await self.initialize()

        start_time = time.time()
        self.metrics["total_queries"] += 1

        # 🚀 FAST MODE: Optimisations pour vitesse
        if fast_mode:
            limit = min(limit or 5, 5)  # Max 5 résultats en mode rapide
            use_lightrag = False        # Désactiver LightRAG (plus lent)

        # Prepare search parameters
        search_params = {
            "use_lightrag": use_lightrag,
            "use_vector_store": use_vector_store,
            "limit": limit or self.config.max_results,
            "fast_mode": fast_mode
        }

        # Try cache first
        if use_cache:
            cached_result = await self.cache_manager.get(query, search_params)
            if cached_result:
                self._update_metrics(time.time() - start_time, from_cache=True)
                logger.info(f"🎯 Cache HIT for query: {query[:30]}...")
                return cached_result

        try:
            results = {
                "lightrag_results": "",
                "vector_results": [],
                "combined_response": "",
                "sources": []
            }

            # Search with LightRAG (graph-based)
            if use_lightrag:
                lightrag_response = await self.lightrag_manager.search(query)
                results["lightrag_results"] = lightrag_response

            # Search with vector store (similarity-based)
            if use_vector_store:
                vector_results = await self.vector_store_manager.search(query, limit)
                results["vector_results"] = vector_results or []

            # Combine results intelligently
            results["combined_response"] = self._combine_results(
                results["lightrag_results"],
                results["vector_results"]
            )

            # Extract sources
            results["sources"] = self._extract_sources(results["vector_results"])

            # Add metadata
            processing_time = time.time() - start_time
            results.update({
                "query": query,
                "processing_time": processing_time,
                "timestamp": time.time(),
                "from_cache": False
            })

            # Cache the result
            if use_cache:
                await self.cache_manager.set(query, search_params, results)

            self._update_metrics(processing_time, from_cache=False)

            logger.info(f"🔍 Search completed in {processing_time:.2f}s")
            return results

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return {
                "error": str(e),
                "query": query,
                "processing_time": time.time() - start_time
            }

    def _combine_results(self, lightrag_response: str, vector_results: List[Dict]) -> str:
        """Combiner intelligemment les résultats LightRAG et vectoriels"""
        combined = []

        # Add LightRAG graph-based context (prioritized)
        if lightrag_response and lightrag_response.strip():
            combined.append(f"📊 Graph-based Analysis:\n{lightrag_response.strip()}")

        # Add top vector search results with relevance scores
        if vector_results and len(vector_results) > 0:
            # Sort by score and take top results
            sorted_results = sorted(vector_results, key=lambda x: x.get('score', 0), reverse=True)
            top_results = sorted_results[:5]  # Top 5 for better context

            vector_sections = []
            for i, result in enumerate(top_results, 1):
                score = result.get('score', 0)
                source = result.get('metadata', {}).get('source_file', 'Unknown')
                content = result.get('content', '').strip()

                if content and score > 0.1:  # Filter low-relevance results
                    vector_sections.append(
                        f"📄 Document {i} ({source}) - Relevance: {score:.2f}\n{content}"
                    )

            if vector_sections:
                combined.append(f"🔍 Relevant Documents:\n\n" + "\n\n".join(vector_sections))

        # Fallback if no results
        if not combined:
            return "No relevant information found in the knowledge base."

        return "\n\n" + "="*50 + "\n\n".join(combined)

    def _extract_sources(self, vector_results: List[Dict]) -> List[str]:
        """Extraire les sources des résultats vectoriels"""
        sources = []
        if vector_results:
            for result in vector_results:
                source = result.get('metadata', {}).get('source')
                if source and source not in sources:
                    sources.append(source)
        return sources

    def _update_metrics(self, response_time: float, from_cache: bool = False):
        """Mettre à jour les métriques de performance"""
        # Update average response time
        current_avg = self.metrics["average_response_time"]
        total_queries = self.metrics["total_queries"]

        self.metrics["average_response_time"] = (
            (current_avg * (total_queries - 1) + response_time) / total_queries
        )

        # Update cache hit rate
        cache_stats = self.cache_manager.get_stats()
        self.metrics["cache_hit_rate"] = cache_stats["hit_rate"]

    def get_metrics(self) -> Dict[str, Any]:
        """Obtenir les métriques de performance"""
        uptime = time.time() - self.metrics["active_since"]
        cache_stats = self.cache_manager.get_stats()

        return {
            **self.metrics,
            "uptime_seconds": round(uptime, 2),
            "cache_stats": cache_stats,
            "components_status": {
                "lightrag": self.lightrag_manager.lightrag is not None,
                "vector_store": self.vector_store_manager.qdrant_client is not None or
                               self.vector_store_manager.chroma_client is not None,
                "embeddings": self.embedding_manager.bge_model is not None or
                             self.embedding_manager.ollama_embeddings is not None,
                "cache": self.cache_manager.redis_client is not None
            }
        }

    async def list_all_documents(self) -> List[Dict[str, Any]]:
        """Lister tous les documents disponibles dans le RAG (OPTIMISÉ POUR LA VITESSE)"""
        start_time = time.time()
        documents = []

        try:
            # OPTIMISATION : Essayer ChromaDB d'abord (plus rapide que Qdrant)
            if self.vector_store_manager.chroma_client:
                try:
                    collection_name = "wema_documents"
                    collection = self.vector_store_manager.chroma_client.get_collection(collection_name)

                    # OPTIMISATION : Récupérer seulement les métadonnées (pas le contenu)
                    result = collection.get(
                        include=['metadatas'],  # Pas de 'documents' pour plus de vitesse
                        limit=1000  # Limite raisonnable
                    )

                    # Traiter les résultats ChromaDB RAPIDEMENT
                    seen_files = set()
                    for doc_id, metadata in zip(result['ids'], result['metadatas']):
                        source_file = metadata.get('source_file', f'Document_{doc_id}')

                        # Éviter les doublons
                        if source_file not in seen_files:
                            seen_files.add(source_file)
                            documents.append({
                                'id': doc_id,
                                'filename': source_file,
                                'metadata': metadata,
                                'source': 'chromadb'
                            })

                    elapsed = time.time() - start_time
                    logger.info(f"⚡ Found {len(documents)} documents in ChromaDB in {elapsed:.2f}s")
                    return documents  # Retour immédiat si ChromaDB fonctionne

                except Exception as e:
                    logger.warning(f"ChromaDB listing failed: {e}")

            # Fallback Qdrant seulement si ChromaDB échoue
            if self.vector_store_manager.qdrant_client:
                try:
                    # OPTIMISATION : Pas d'import dans la fonction
                    scroll_result = self.vector_store_manager.qdrant_client.scroll(
                        collection_name=self.config.qdrant_collection,
                        limit=1000,
                        with_payload=True,
                        with_vectors=False  # OPTIMISATION : Pas de vecteurs
                    )

                    # Traitement rapide
                    seen_files = set()
                    for point in scroll_result[0]:
                        payload = point.payload or {}
                        source_file = payload.get('source_file', f'Document_{point.id}')

                        if source_file not in seen_files:
                            seen_files.add(source_file)
                            documents.append({
                                'id': str(point.id),
                                'filename': source_file,
                                'metadata': payload,
                                'source': 'qdrant'
                            })

                    elapsed = time.time() - start_time
                    logger.info(f"⚡ Found {len(documents)} documents in Qdrant in {elapsed:.2f}s")

                except Exception as e:
                    logger.warning(f"Qdrant listing failed: {e}")

            elapsed = time.time() - start_time
            logger.info(f"⚡ Document listing completed in {elapsed:.2f}s")
            return documents

        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"Failed to list documents in {elapsed:.2f}s: {e}")
            return []

    async def health_check(self) -> Dict[str, Any]:
        """Vérification de santé du service"""
        if not self.initialized:
            return {"status": "not_initialized"}

        try:
            # Test search functionality
            test_result = await self.search("test query", use_cache=False)

            return {
                "status": "healthy",
                "metrics": self.get_metrics(),
                "test_search_time": test_result.get("processing_time", 0)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

# ============================================================================
# SINGLETON INSTANCE
# ============================================================================

# Global instance
_rag_service_instance = None

async def get_rag_service() -> UnifiedRAGService:
    """Obtenir l'instance singleton du service RAG"""
    global _rag_service_instance

    if _rag_service_instance is None:
        logger.info("🔄 Creating new RAG service instance...")
        try:
            _rag_service_instance = UnifiedRAGService()
            logger.info("🔄 Initializing RAG service...")
            success = await _rag_service_instance.initialize()
            if success:
                logger.info("✅ RAG service initialized successfully")
            else:
                logger.warning("⚠️ RAG service initialization completed with warnings")
        except Exception as e:
            logger.error(f"❌ RAG service initialization failed: {e}")
            # Create a minimal service that can handle basic operations
            _rag_service_instance = UnifiedRAGService()
            _rag_service_instance.initialized = True

    return _rag_service_instance
