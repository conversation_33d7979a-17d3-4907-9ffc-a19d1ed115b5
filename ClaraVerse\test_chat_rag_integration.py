#!/usr/bin/env python3
"""
Test script pour vérifier l'intégration Chat + RAG
Vérifie que les documents uploadés via chat sont bien intégrés dans le RAG Premium
"""

import asyncio
import aiohttp
import json
import tempfile
import os
from pathlib import Path

# Configuration
BACKEND_URL = "http://localhost:8000"
TEST_DOCUMENT_CONTENT = """
# Test Document pour Chat Upload

Ceci est un document de test pour vérifier l'intégration entre:
- Upload via chat interface
- Traitement OCR
- Intégration RAG Premium
- Recherche unifiée

## Informations importantes
- Nom: Document Test Chat
- Type: Test d'intégration
- Collection: chat_uploads
- Contenu: Texte de test pour recherche RAG

## Données de test
Ce document contient des informations spécifiques qui devraient être trouvées lors de la recherche RAG:
- Mot-clé unique: INTEGRATION_TEST_KEYWORD_2024
- Phrase unique: "Cette phrase est unique pour tester la recherche RAG"
- Données numériques: 12345, 67890
"""

async def test_chat_upload_rag_integration():
    """Test complet de l'intégration Chat Upload + RAG"""
    print("🧪 Test d'intégration Chat Upload + RAG Premium")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # 1. Créer un fichier temporaire
        print("📄 1. Création du document de test...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(TEST_DOCUMENT_CONTENT)
            temp_file_path = f.name
        
        try:
            # 2. Upload via l'endpoint documents (simule upload chat)
            print("📤 2. Upload du document via chat interface...")
            
            with open(temp_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='test_chat_document.txt', content_type='text/plain')
                data.add_field('collection_name', 'chat_uploads')
                data.add_field('use_ocr', 'false')  # Pas besoin d'OCR pour un fichier texte
                data.add_field('metadata', json.dumps({
                    'uploaded_via': 'chat_interface',
                    'test_document': True,
                    'session_id': 'test_session_123'
                }))
                
                async with session.post(f"{BACKEND_URL}/documents/upload", data=data) as response:
                    if response.status == 200:
                        upload_result = await response.json()
                        print(f"✅ Upload réussi: {upload_result}")
                        document_id = upload_result.get('document_id')
                    else:
                        error_text = await response.text()
                        print(f"❌ Échec upload: {response.status} - {error_text}")
                        return False
            
            # 3. Attendre un peu pour que l'indexation RAG se termine
            print("⏳ 3. Attente de l'indexation RAG...")
            await asyncio.sleep(2)
            
            # 4. Tester la recherche RAG Premium
            print("🔍 4. Test de recherche RAG Premium...")
            
            search_queries = [
                "INTEGRATION_TEST_KEYWORD_2024",
                "phrase unique pour tester",
                "Document Test Chat",
                "données numériques 12345"
            ]
            
            for query in search_queries:
                print(f"   🔎 Recherche: '{query}'")
                
                search_payload = {
                    "query": query,
                    "use_lightrag": True,
                    "use_vector_store": True,
                    "limit": 5
                }
                
                async with session.post(f"{BACKEND_URL}/rag/search", json=search_payload) as response:
                    if response.status == 200:
                        search_result = await response.json()
                        vector_results = search_result.get('vector_results', [])
                        lightrag_results = search_result.get('lightrag_results', '')
                        
                        print(f"      📊 Résultats vectoriels: {len(vector_results)}")
                        print(f"      🧠 Résultats LightRAG: {len(lightrag_results)} chars")
                        
                        # Vérifier si notre document est trouvé
                        found_our_doc = False
                        for result in vector_results:
                            if 'test_chat_document.txt' in result.get('metadata', {}).get('source_file', ''):
                                found_our_doc = True
                                print(f"      ✅ Document trouvé avec score: {result.get('score', 0):.3f}")
                                break
                        
                        if not found_our_doc and vector_results:
                            print(f"      ⚠️  Document non trouvé dans les résultats vectoriels")
                            for i, result in enumerate(vector_results[:2]):
                                source = result.get('metadata', {}).get('source_file', 'unknown')
                                print(f"         - Résultat {i+1}: {source}")
                        
                        if lightrag_results and query.lower() in lightrag_results.lower():
                            print(f"      ✅ Trouvé dans LightRAG")
                        
                    else:
                        error_text = await response.text()
                        print(f"      ❌ Échec recherche: {response.status} - {error_text}")
            
            # 5. Tester la recherche via l'endpoint legacy (pour comparaison)
            print("🔍 5. Test de recherche legacy...")
            
            legacy_payload = {
                "query": "INTEGRATION_TEST_KEYWORD_2024",
                "collection_name": "chat_uploads",
                "k": 5
            }
            
            async with session.post(f"{BACKEND_URL}/documents/search", json=legacy_payload) as response:
                if response.status == 200:
                    legacy_result = await response.json()
                    legacy_results = legacy_result.get('results', [])
                    print(f"   📊 Résultats legacy: {len(legacy_results)}")
                    
                    for result in legacy_results:
                        source = result.get('metadata', {}).get('source_file', 'unknown')
                        score = result.get('score', 0)
                        print(f"      - {source}: {score:.3f}")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Échec recherche legacy: {response.status} - {error_text}")
            
            # 6. Vérifier la santé du RAG
            print("🏥 6. Vérification santé RAG...")
            async with session.get(f"{BACKEND_URL}/rag/health") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"   ✅ RAG Status: {health.get('status', 'unknown')}")
                    print(f"   🧠 LightRAG: {health.get('lightrag_available', False)}")
                    print(f"   🔢 Qdrant: {health.get('qdrant_available', False)}")
                else:
                    print(f"   ❌ Échec vérification santé: {response.status}")
            
            # 7. Nettoyer - supprimer le document de test
            print("🧹 7. Nettoyage...")
            if document_id:
                async with session.delete(f"{BACKEND_URL}/documents/{document_id}") as response:
                    if response.status == 200:
                        print("   ✅ Document de test supprimé")
                    else:
                        print(f"   ⚠️  Échec suppression: {response.status}")
            
            print("\n🎉 Test d'intégration terminé!")
            return True
            
        finally:
            # Supprimer le fichier temporaire
            try:
                os.unlink(temp_file_path)
            except:
                pass

async def main():
    """Point d'entrée principal"""
    try:
        success = await test_chat_upload_rag_integration()
        if success:
            print("\n✅ SUCCÈS: L'intégration Chat + RAG fonctionne correctement!")
        else:
            print("\n❌ ÉCHEC: Problèmes détectés dans l'intégration")
    except Exception as e:
        print(f"\n💥 ERREUR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
