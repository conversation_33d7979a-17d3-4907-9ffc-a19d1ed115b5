/**
 * 🌐 Composant de Statut de Connectivité WeMa IA
 * 
 * Affiche le statut de connectivité en temps réel :
 * - Backend local (natif)
 * - Serveur Ollama central (délégation inférence)
 * - LM Studio local (fallback)
 */

import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Server, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { connectivityService, ConnectivityStatus } from '../../services/connectivity/ConnectivityService';

interface ConnectivityStatusProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const ConnectivityStatusComponent: React.FC<ConnectivityStatusProps> = ({
  className = '',
  showDetails = false,
  autoRefresh = true,
  refreshInterval = 60000 // 1 minute
}) => {
  const [status, setStatus] = useState<ConnectivityStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Test initial et auto-refresh
  useEffect(() => {
    checkConnectivity();

    if (autoRefresh) {
      const interval = setInterval(checkConnectivity, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const checkConnectivity = async () => {
    setIsLoading(true);
    try {
      const newStatus = await connectivityService.checkConnectivity();
      setStatus(newStatus);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('❌ Connectivity check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (available: boolean, isLoading: boolean) => {
    if (isLoading) return <Clock className="w-4 h-4 animate-spin" />;
    return available ? 
      <CheckCircle className="w-4 h-4 text-green-500" /> : 
      <AlertTriangle className="w-4 h-4 text-red-500" />;
  };

  const getOverallStatusColor = (status: ConnectivityStatus['overall']['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (!status) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Clock className="w-4 h-4 animate-spin text-blue-500" />
        <span className="text-sm text-gray-600">Vérification connectivité...</span>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Statut global compact */}
      <div className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${getOverallStatusColor(status.overall.status)}`}>
        {status.overall.status === 'healthy' ? (
          <Wifi className="w-4 h-4" />
        ) : (
          <WifiOff className="w-4 h-4" />
        )}
        <span className="text-sm font-medium">
          {status.overall.status === 'healthy' ? 'Connecté' : 
           status.overall.status === 'degraded' ? 'Mode dégradé' : 'Déconnecté'}
        </span>
        
        {lastUpdate && (
          <span className="text-xs opacity-70">
            {lastUpdate.toLocaleTimeString()}
          </span>
        )}
        
        <button
          onClick={checkConnectivity}
          disabled={isLoading}
          className="ml-auto p-1 hover:bg-black/5 rounded transition-colors"
          title="Actualiser"
        >
          <Clock className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Détails des services */}
      {showDetails && (
        <div className="mt-3 space-y-2">
          {/* Backend local */}
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.backend.available, isLoading)}
              <span className="text-sm font-medium">Backend Local</span>
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-600">{status.backend.url}</div>
              {status.backend.responseTime && (
                <div className="text-xs text-gray-500">{status.backend.responseTime}ms</div>
              )}
              {status.backend.error && (
                <div className="text-xs text-red-500">{status.backend.error}</div>
              )}
            </div>
          </div>

          {/* Ollama serveur central */}
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.ollama.available, isLoading)}
              <span className="text-sm font-medium">Ollama Central</span>
              <span className="text-xs text-blue-600">(Inférence IA)</span>
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-600">{status.ollama.url}</div>
              {status.ollama.responseTime && (
                <div className="text-xs text-gray-500">{status.ollama.responseTime}ms</div>
              )}
              {status.ollama.models && status.ollama.models.length > 0 && (
                <div className="text-xs text-green-600">{status.ollama.models.length} modèles</div>
              )}
              {status.ollama.error && (
                <div className="text-xs text-red-500">{status.ollama.error}</div>
              )}
            </div>
          </div>

          {/* LM Studio local */}
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.lmstudio.available, isLoading)}
              <span className="text-sm font-medium">LM Studio Local</span>
              <span className="text-xs text-orange-600">(Fallback)</span>
            </div>
            <div className="text-right">
              <div className="text-xs text-gray-600">{status.lmstudio.url}</div>
              {status.lmstudio.responseTime && (
                <div className="text-xs text-gray-500">{status.lmstudio.responseTime}ms</div>
              )}
              {status.lmstudio.models && status.lmstudio.models.length > 0 && (
                <div className="text-xs text-green-600">{status.lmstudio.models.length} modèles</div>
              )}
              {status.lmstudio.error && (
                <div className="text-xs text-red-500">{status.lmstudio.error}</div>
              )}
            </div>
          </div>

          {/* Message global */}
          <div className="p-2 bg-blue-50 rounded">
            <p className="text-xs text-blue-700">{status.overall.message}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConnectivityStatusComponent;
