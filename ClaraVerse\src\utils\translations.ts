/**
 * 🇫🇷 SYSTÈME DE TRADUCTION WEMA IA
 * 
 * Centralise toutes les traductions de l'application
 * Remplace progressivement tous les textes anglais par du français
 */

export const translations = {
  // === NAVIGATION ET MENU ===
  navigation: {
    dashboard: 'Tableau de bord',
    chat: 'Chat IA',
    agents: 'Agents IA',
    documentManager: 'Gestionnaire de documents',
    imageGen: 'Génération d\'images',
    workflows: 'Flux de travail',
    settings: 'Paramètres',
    help: 'Aide',
  },

  // === CHAT ET MESSAGES ===
  chat: {
    placeholder: 'Posez-moi n\'importe quelle question...',
    send: 'Envoyer',
    stop: 'Arrêter',
    newChat: 'Nouvelle conversation',
    uploadImages: 'Télécharger des images',
    uploadDocuments: 'Télécharger documents et code',
    dropFiles: 'Déposez vos fichiers ici pour les télécharger',
    sendMessage: 'Envoyer le message (Entrée)',
    stopGenerating: 'Arrêter la génération',
    thinking: 'Réflexion...',
    generating: 'Génération en cours...',
  },

  // === NOTIFICATIONS ===
  notifications: {
    // Moteur IA
    engineStarting: 'Démarrage du moteur WeMa IA...',
    engineStartingDesc: 'WeMa IA démarre son service d\'intelligence artificielle local. Cela peut prendre un moment.',
    engineStartFailed: 'Échec du démarrage du moteur WeMa IA',
    engineStartFailedDesc: 'Impossible de démarrer le service d\'IA local. Veuillez vérifier votre installation.',
    engineReady: 'Moteur WeMa IA prêt',
    engineReadyDesc: 'Votre service d\'IA local fonctionne et est prêt à discuter !',
    
    // Modèles
    modelReady: 'Modèle IA prêt',
    modelLoaded: 'chargé !',
    noModelSelected: 'Aucun modèle sélectionné',
    noModelSelectedDesc: 'Veuillez sélectionner au moins un modèle pour le fournisseur actuel dans les Options avancées, ou allez dans Paramètres → Gestionnaire de modèles pour télécharger des modèles.',
    
    // Streaming
    streamingActive: 'Mode streaming actif',
    streamingActiveDesc: 'Fonctionnalités autonomes automatiquement désactivées pour une expérience de streaming fluide.',
    
    // Erreurs
    chatError: 'Erreur de chat',
    chatErrorDesc: 'Échec de la génération de réponse. Veuillez réessayer.',
    visionModelRequired: 'Modèle de vision requis',
    visionModelRequiredDesc: 'Veuillez configurer un modèle de vision/multimodal pour traiter les images.',
    
    // Connexion
    testingProvider: 'Test du fournisseur',
    testingProviderDesc: 'Vérification de la connexion à',
    providerConnectionFailed: 'Échec de connexion au fournisseur',
    providerConnectionFailedDesc: 'ne répond pas. Veuillez vérifier si le service fonctionne ou essayer un autre fournisseur.',
    providerConnected: 'Fournisseur connecté',
    providerConnectedDesc: 'Connexion réussie à',
  },

  // === PARAMÈTRES ET CONFIGURATION ===
  settings: {
    systemPrompt: 'Prompt système',
    systemPromptPlaceholder: 'Entrez un prompt système personnalisé pour ce fournisseur...',
    temperature: 'Température',
    maxTokens: 'Tokens maximum',
    enableTools: 'Activer les outils',
    enableRAG: 'Activer RAG',
    enableStreaming: 'Activer le streaming',
    autoModelSelection: 'Sélection automatique de modèle',
    enableMCP: 'Activer MCP',
    autonomousAgent: 'Agent autonome',
    maxRetries: 'Tentatives maximum',
    maxToolCalls: 'Appels d\'outils maximum',
    enableSelfCorrection: 'Activer l\'auto-correction',
    enableProgressTracking: 'Activer le suivi de progression',
    enableChainOfThought: 'Activer la chaîne de pensée',
  },

  // === TYPES DE FICHIERS ===
  fileTypes: {
    image: 'Image',
    pdf: 'PDF',
    text: 'Texte',
    code: 'Code',
    json: 'JSON',
    csv: 'CSV',
    document: 'Document',
  },

  // === ÉTATS ET STATUTS ===
  status: {
    online: 'En ligne',
    offline: 'Hors ligne',
    connecting: 'Connexion...',
    loading: 'Chargement...',
    processing: 'Traitement...',
    complete: 'Terminé',
    error: 'Erreur',
    ready: 'Prêt',
    uploading: 'Téléchargement...',
    indexing: 'Indexation...',
  },

  // === ACTIONS ===
  actions: {
    save: 'Sauvegarder',
    cancel: 'Annuler',
    delete: 'Supprimer',
    edit: 'Modifier',
    copy: 'Copier',
    download: 'Télécharger',
    upload: 'Télécharger',
    refresh: 'Actualiser',
    retry: 'Réessayer',
    close: 'Fermer',
    open: 'Ouvrir',
    select: 'Sélectionner',
    deselect: 'Désélectionner',
    clear: 'Effacer',
    reset: 'Réinitialiser',
  },

  // === TEMPS ET DATES ===
  time: {
    now: 'Maintenant',
    today: 'Aujourd\'hui',
    yesterday: 'Hier',
    thisWeek: 'Cette semaine',
    lastWeek: 'La semaine dernière',
    thisMonth: 'Ce mois',
    lastMonth: 'Le mois dernier',
    minutes: 'minutes',
    hours: 'heures',
    days: 'jours',
    weeks: 'semaines',
    months: 'mois',
    ago: 'il y a',
  },

  // === MESSAGES D'ERREUR COMMUNS ===
  errors: {
    networkError: 'Erreur de réseau',
    serverError: 'Erreur serveur',
    unknownError: 'Erreur inconnue',
    fileTooBig: 'Fichier trop volumineux',
    fileTypeNotSupported: 'Type de fichier non supporté',
    uploadFailed: 'Échec du téléchargement',
    processingFailed: 'Échec du traitement',
    connectionLost: 'Connexion perdue',
    timeout: 'Délai d\'attente dépassé',
  },

  // === CONFIRMATIONS ===
  confirmations: {
    deleteConversation: 'Supprimer la conversation ?',
    deleteDocument: 'Supprimer le document ?',
    clearHistory: 'Effacer l\'historique ?',
    resetSettings: 'Réinitialiser les paramètres ?',
    unsavedChanges: 'Modifications non sauvegardées',
    areYouSure: 'Êtes-vous sûr ?',
  },

  // === TOOLTIPS ET AIDE ===
  tooltips: {
    ragMode: 'Mode RAG : Utilise les documents sélectionnés pour enrichir les réponses',
    fastMode: 'Mode rapide : Traitement direct sans RAG complexe',
    performanceMode: 'Mode performance : Traitement RAG complet pour de meilleurs résultats',
    voiceChat: 'Chat vocal : Parlez directement avec l\'IA',
    modelSelection: 'Sélection de modèle : Choisissez le modèle IA approprié',
    providerHealth: 'État du fournisseur : Indique si le service IA est disponible',
  },

  // === BRANDING WEMA IA ===
  branding: {
    appName: 'WeMa IA',
    tagline: 'Votre assistant intelligent personnel',
    welcome: 'Bienvenue dans WeMa IA',
    poweredBy: 'Propulsé par WeMa IA',
    version: 'Version',
    copyright: '© 2024 WeMa IA. Tous droits réservés.',
  },

  // === MODES ET FONCTIONNALITÉS ===
  modes: {
    ragFast: 'RAG Rapide',
    ragPerformance: 'RAG Performance',
    streaming: 'Streaming',
    autonomous: 'Autonome',
    voice: 'Vocal',
    vision: 'Vision',
    code: 'Code',
  },
};

/**
 * Fonction utilitaire pour obtenir une traduction
 */
export function t(key: string): string {
  const keys = key.split('.');
  let value: any = translations;
  
  for (const k of keys) {
    value = value?.[k];
    if (value === undefined) {
      console.warn(`Translation missing for key: ${key}`);
      return key; // Retourner la clé si traduction manquante
    }
  }
  
  return value;
}

/**
 * Fonction pour traduire avec des paramètres
 */
export function tWithParams(key: string, params: Record<string, string>): string {
  let text = t(key);
  
  for (const [param, value] of Object.entries(params)) {
    text = text.replace(`{${param}}`, value);
  }
  
  return text;
}

export default translations;
