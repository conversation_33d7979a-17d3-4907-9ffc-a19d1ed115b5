/**
 * 🛠️ TOOL EXECUTOR
 * Gère l'exécution des outils de manière isolée
 */

import { defaultTools, executeTool } from '../../utils/claraTools';
import { db } from '../../db';
import type { Tool } from '../../db';
import { claraMCPService } from '../claraMCPService';

export interface ToolExecutionResult {
  toolName: string;
  success: boolean;
  result?: any;
  error?: string;
}

export interface ToolExecutionAttempt {
  attempt: number;
  toolName: string;
  arguments: any;
  error?: string;
  success: boolean;
  timestamp: Date;
}

export class ToolExecutor {
  /**
   * Execute multiple tool calls
   */
  public async executeToolCalls(toolCalls: any[]): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];

    for (const toolCall of toolCalls) {
      try {
        const functionName = toolCall.function?.name;
        
        console.log(`🔍 [DEBUG] Raw tool call object:`, JSON.stringify(toolCall, null, 2));
        console.log(`🔍 [DEBUG] Function name:`, functionName);
        console.log(`🔍 [DEBUG] Raw arguments:`, toolCall.function?.arguments);

        // Parse arguments
        let args = {};
        try {
          if (typeof toolCall.function?.arguments === 'string') {
            const argsString = toolCall.function.arguments.trim();
            console.log(`🔍 [DEBUG] Arguments string (trimmed):`, argsString);
            
            if (argsString === '' || argsString === 'null' || argsString === 'undefined') {
              args = {};
            } else {
              args = JSON.parse(argsString);
            }
          } else if (toolCall.function?.arguments && typeof toolCall.function.arguments === 'object') {
            args = toolCall.function.arguments;
            console.log(`🔍 [DEBUG] Using object arguments directly:`, args);
          } else {
            args = {};
          }
        } catch (parseError) {
          console.warn(`⚠️ Failed to parse tool arguments for ${functionName}:`, parseError);
          console.warn(`⚠️ Raw arguments:`, toolCall.function?.arguments);
          args = {};
        }

        // Check for malformed tool calls
        if (!functionName || functionName.trim() === '') {
          console.warn('⚠️ Skipping malformed tool call with empty function name:', toolCall);
          const result: ToolExecutionResult = {
            toolName: 'unknown',
            success: false,
            error: 'Tool call has empty or missing function name'
          };
          results.push(result);
          continue;
        }

        console.log(`🔧 Executing tool: ${functionName} with args:`, args);

        // Execute the tool
        const result = await this.executeSingleTool(functionName, args);
        results.push(result);

      } catch (error) {
        const result: ToolExecutionResult = {
          toolName: toolCall.function?.name || 'unknown',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        console.log(`❌ Tool execution error for ${toolCall.function?.name || 'unknown'}:`, result);
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Execute a single tool with retry logic
   */
  public async executeSingleTool(functionName: string, args: any): Promise<ToolExecutionResult> {
    // Check if this is an MCP tool call
    if (functionName?.startsWith('mcp_')) {
      return this.executeMCPTool(functionName, args);
    }

    // Try Clara tools first
    const claraTool = defaultTools.find(tool => tool.name === functionName || tool.id === functionName);
    if (claraTool) {
      try {
        const result = await executeTool(claraTool.id, args);
        console.log(`✅ Clara tool ${functionName} result:`, result);
        return {
          toolName: functionName,
          success: result.success,
          result: result.result,
          error: result.error
        };
      } catch (error) {
        return {
          toolName: functionName,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    // Try database tools
    try {
      const dbTools = await db.getEnabledTools();
      const dbTool = dbTools.find(tool => tool.name === functionName);
      
      if (dbTool) {
        try {
          const funcBody = `return (async () => {
            ${dbTool.implementation}
            return { success: true, result: 'Tool executed successfully' };
          })();`;
          const testFunc = new Function('args', funcBody);
          const result = await testFunc(args);
          
          console.log(`✅ Database tool ${functionName} result:`, result);
          return {
            toolName: functionName,
            success: true,
            result: result
          };
        } catch (dbError) {
          return {
            toolName: functionName,
            success: false,
            error: dbError instanceof Error ? dbError.message : 'Database tool execution failed'
          };
        }
      } else {
        return {
          toolName: functionName,
          success: false,
          error: `Tool '${functionName}' not found`
        };
      }
    } catch (error) {
      return {
        toolName: functionName,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute MCP tool
   */
  private async executeMCPTool(functionName: string, args: any): Promise<ToolExecutionResult> {
    console.log(`🔧 [API] Processing MCP tool call: ${functionName}`);
    try {
      const mcpToolCalls = claraMCPService.parseOpenAIToolCalls([{
        id: `tool_${Date.now()}`,
        type: 'function',
        function: { name: functionName, arguments: JSON.stringify(args) }
      }]);

      if (mcpToolCalls.length > 0) {
        const mcpResult = await claraMCPService.executeToolCall(mcpToolCalls[0]);
        
        // Process the MCP result comprehensively
        const processedResult = this.processMCPToolResult(mcpResult, functionName);
        
        return {
          toolName: functionName,
          success: mcpResult.success,
          result: processedResult,
          error: mcpResult.success ? undefined : mcpResult.error
        };
      } else {
        return {
          toolName: functionName,
          success: false,
          error: 'Failed to parse MCP tool call'
        };
      }
    } catch (mcpError) {
      return {
        toolName: functionName,
        success: false,
        error: mcpError instanceof Error ? mcpError.message : 'MCP tool execution failed'
      };
    }
  }

  /**
   * Process MCP tool result
   */
  private processMCPToolResult(mcpResult: any, functionName: string): any {
    if (!mcpResult.success) {
      return { error: mcpResult.error || 'MCP tool execution failed' };
    }

    // Handle different types of MCP results
    if (mcpResult.result) {
      if (typeof mcpResult.result === 'string') {
        return { content: mcpResult.result };
      } else if (typeof mcpResult.result === 'object') {
        return mcpResult.result;
      }
    }

    return { success: true, message: `Tool ${functionName} executed successfully` };
  }

  /**
   * Validate tool structure
   */
  public validateTool(tool: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check top-level structure
    if (!tool.type || tool.type !== 'function') {
      errors.push('Tool must have type "function"');
    }

    if (!tool.function) {
      errors.push('Tool must have a function property');
      return { isValid: false, errors };
    }

    const func = tool.function;

    // Check function properties
    if (!func.name || typeof func.name !== 'string' || func.name.trim() === '') {
      errors.push('Function must have a valid name');
    }

    if (!func.description || typeof func.description !== 'string') {
      errors.push('Function must have a description');
    }

    if (!func.parameters) {
      errors.push('Function must have parameters');
      return { isValid: false, errors };
    }

    // Check parameters structure
    if (typeof func.parameters !== 'object') {
      errors.push('Parameters must be an object');
    } else {
      if (!func.parameters.type || func.parameters.type !== 'object') {
        errors.push('Parameters must have type "object"');
      }

      if (!func.parameters.properties || typeof func.parameters.properties !== 'object') {
        errors.push('Parameters must have properties object');
      }
    }

    return { isValid: errors.length === 0, errors };
  }
}

// Export singleton instance
export const toolExecutor = new ToolExecutor();
