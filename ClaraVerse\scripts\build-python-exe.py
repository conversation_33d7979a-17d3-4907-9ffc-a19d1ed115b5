#!/usr/bin/env python3
"""
🐍 Builder EXE Python pur - Sans Electron
Crée un EXE autonome avec PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

class PythonExeBuilder:
    def __init__(self):
        self.build_dir = Path("python-build")
        self.dist_dir = Path("dist-python")
        
    def build_exe(self):
        """Créer l'EXE Python autonome"""
        print("🐍 Build EXE Python pur WeMa IA")
        print("=" * 50)
        
        # 1. Préparer l'environnement
        self.prepare_build()
        
        # 2. Créer l'app Python avec interface web
        self.create_python_app()
        
        # 3. Build avec PyInstaller
        self.build_with_pyinstaller()
        
        print("🎉 EXE Python créé !")
        print(f"📁 Fichier: {self.dist_dir}/WeMa-IA.exe")
        print(f"📊 Taille: ~30-50MB (vs 80-120MB Electron)")
        
    def prepare_build(self):
        """Préparer les dossiers"""
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            
        self.build_dir.mkdir()
        self.dist_dir.mkdir()
        
    def create_python_app(self):
        """Créer l'application Python avec serveur web intégré"""
        
        # App principale
        main_app = '''
import os
import sys
import threading
import webbrowser
import time
from pathlib import Path

# Ajouter le dossier py_backend au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'py_backend'))

def start_backend():
    """Démarrer le backend Python"""
    try:
        from main import app
        import uvicorn
        
        # Démarrer le serveur backend
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
    except Exception as e:
        print(f"Erreur backend: {e}")

def start_frontend():
    """Démarrer le serveur frontend"""
    try:
        import http.server
        import socketserver
        import os
        
        # Changer vers le dossier build
        build_dir = os.path.join(os.path.dirname(__file__), 'build')
        os.chdir(build_dir)
        
        # Serveur HTTP simple
        handler = http.server.SimpleHTTPRequestHandler
        with socketserver.TCPServer(("127.0.0.1", 3000), handler) as httpd:
            print("🌐 Frontend démarré sur http://127.0.0.1:3000")
            httpd.serve_forever()
            
    except Exception as e:
        print(f"Erreur frontend: {e}")

def main():
    """Point d'entrée principal"""
    print("🚀 Démarrage WeMa IA")
    
    # Démarrer le backend en arrière-plan
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Attendre que le backend soit prêt
    time.sleep(3)
    
    # Démarrer le frontend en arrière-plan
    frontend_thread = threading.Thread(target=start_frontend, daemon=True)
    frontend_thread.start()
    
    # Attendre que le frontend soit prêt
    time.sleep(2)
    
    # Ouvrir le navigateur
    print("🌐 Ouverture de l'interface...")
    webbrowser.open("http://127.0.0.1:3000")
    
    # Garder l'application vivante
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("🛑 Arrêt de WeMa IA")
        sys.exit(0)

if __name__ == "__main__":
    main()
'''
        
        # Sauvegarder l'app principale
        with open(self.build_dir / "wema_ia.py", "w", encoding="utf-8") as f:
            f.write(main_app)
            
        # Copier les sources
        self.copy_sources()
        
    def copy_sources(self):
        """Copier les sources nécessaires"""
        sources = [
            ("py_backend", "py_backend"),
            ("build", "build")  # Build React déjà créé
        ]
        
        for src, dst in sources:
            src_path = Path(src)
            dst_path = self.build_dir / dst
            
            if src_path.exists():
                if src_path.is_dir():
                    shutil.copytree(src_path, dst_path)
                else:
                    shutil.copy2(src_path, dst_path)
                    
        print("✅ Sources copiées")
        
    def build_with_pyinstaller(self):
        """Build avec PyInstaller"""
        
        # Créer le fichier spec
        spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['wema_ia.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('py_backend', 'py_backend'),
        ('build', 'build'),
    ],
    hiddenimports=[
        'uvicorn',
        'fastapi',
        'pydantic',
        'sqlite3',
        'requests',
        'json',
        'pathlib',
        'threading',
        'webbrowser',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WeMa-IA',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Pas de console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons/wema-ia.ico'
)
'''
        
        spec_file = self.build_dir / "wema_ia.spec"
        with open(spec_file, "w", encoding="utf-8") as f:
            f.write(spec_content)
            
        # Lancer PyInstaller
        try:
            print("🔨 Build avec PyInstaller...")
            subprocess.run([
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm", 
                str(spec_file)
            ], cwd=self.build_dir, check=True)
            
            # Copier l'EXE final
            exe_src = self.build_dir / "dist" / "WeMa-IA.exe"
            exe_dst = self.dist_dir / "WeMa-IA.exe"
            
            if exe_src.exists():
                shutil.copy2(exe_src, exe_dst)
                print("✅ EXE créé avec succès")
            else:
                print("❌ EXE non trouvé")
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur PyInstaller: {e}")
            
    def install_dependencies(self):
        """Installer les dépendances nécessaires"""
        deps = [
            "pyinstaller",
            "uvicorn", 
            "fastapi",
            "pydantic"
        ]
        
        for dep in deps:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
                print(f"✅ {dep} installé")
            except subprocess.CalledProcessError:
                print(f"❌ Erreur installation {dep}")

if __name__ == "__main__":
    builder = PythonExeBuilder()
    
    if len(sys.argv) > 1 and sys.argv[1] == "install":
        builder.install_dependencies()
    else:
        builder.build_exe()
