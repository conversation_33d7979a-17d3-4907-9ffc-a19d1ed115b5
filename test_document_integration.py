#!/usr/bin/env python3
"""
Test de l'intégration des documents dans le chat
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_document_integration():
    print("🔗 Test de l'Intégration Documents Chat")
    print("=" * 50)
    
    # 1. Test récupération des collections
    print("1. 📚 Test Collections...")
    try:
        response = requests.get(f"{BASE_URL}/collections")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            collections = response.json()
            print(f"   ✅ Collections trouvées: {len(collections)}")
            for collection in collections:
                print(f"      - {collection.get('name', 'Unknown')}: {collection.get('document_count', 0)} docs")
        else:
            print(f"   ❌ Erreur collections: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # 2. Test récupération des documents
    print("\n2. 📄 Test Documents...")
    try:
        response = requests.get(f"{BASE_URL}/documents")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"   ✅ Documents trouvés: {len(documents)}")
            
            for doc in documents[:3]:  # Afficher les 3 premiers
                print(f"      - ID: {doc.get('id', 'N/A')}")
                print(f"        Nom: {doc.get('filename', 'Unknown')}")
                print(f"        Type: {doc.get('file_type', 'unknown')}")
                print(f"        Collection: {doc.get('collection_name', 'undefined')}")
                print(f"        Chunks: {doc.get('chunk_count', 0)}")
                print(f"        Anonymisé: {doc.get('is_anonymized', False)}")
                print()
                
        else:
            print(f"   ❌ Erreur documents: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # 3. Test structure des données
    print("3. 🔍 Test Structure des Données...")
    try:
        response = requests.get(f"{BASE_URL}/documents")
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            
            if documents:
                doc = documents[0]
                required_fields = ['id', 'filename', 'file_type', 'collection_name']
                missing_fields = []
                
                for field in required_fields:
                    if field not in doc or doc[field] is None:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"   ⚠️ Champs manquants: {missing_fields}")
                else:
                    print("   ✅ Structure des données correcte")
                    
                # Vérifier les types
                print("   📊 Types de données:")
                for key, value in doc.items():
                    print(f"      {key}: {type(value).__name__} = {value}")
            else:
                print("   ⚠️ Aucun document pour tester la structure")
                
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def main():
    test_document_integration()
    
    print("\n" + "=" * 50)
    print("🎯 Test terminé")
    print("\n📋 Pour tester l'interface:")
    print("1. Ouvrir http://localhost:5174")
    print("2. Aller dans le chat principal")
    print("3. Cliquer sur l'icône RAG (base de données)")
    print("4. Vérifier que les documents s'affichent sans erreur")
    print("\n🔧 Backend: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
