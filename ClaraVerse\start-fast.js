#!/usr/bin/env node
/**
 * 🚀 DÉMARRAGE RAPIDE - WeMa IA
 * Script pour lancer l'application sans les services lents
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage rapide WeMa IA...');

// Variables d'environnement pour désactiver les services lents
const env = {
  ...process.env,
  NODE_ENV: 'development',
  ELECTRON_HOT_RELOAD: 'true',
  SKIP_PYTHON_BACKEND: 'true',
  SKIP_DOCKER_SERVICES: 'true',
  SKIP_LLAMA_BINARIES: 'true',
  FAST_START: 'true'
};

// Lancer Vite en premier
console.log('📦 Démarrage du serveur Vite...');
const vite = spawn('npm', ['run', 'dev'], {
  env,
  stdio: 'inherit',
  shell: true
});

// Attendre 3 secondes puis lancer Electron
setTimeout(() => {
  console.log('⚡ Démarrage d\'Electron...');
  const electron = spawn('electron', ['.'], {
    env,
    stdio: 'inherit',
    shell: true
  });

  electron.on('close', (code) => {
    console.log(`Electron fermé avec le code ${code}`);
    vite.kill();
    process.exit(code);
  });
}, 3000);

vite.on('close', (code) => {
  console.log(`Vite fermé avec le code ${code}`);
  process.exit(code);
});

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt en cours...');
  vite.kill();
  process.exit(0);
});
