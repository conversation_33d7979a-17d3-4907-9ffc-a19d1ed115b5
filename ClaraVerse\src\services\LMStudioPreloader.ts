/**
 * 🚀 LM STUDIO PRELOADER - SIMPLE ET EFFICACE
 *
 * Logique simple :
 * - Précharge UNE SEULE FOIS par modèle
 * - Précharge seulement si nécessaire
 * - Pas de complexité inutile
 */

import { logger, LogCategory } from '../utils/logger';

export class LMStudioPreloader {
  private currentModel: string | null = null;
  private isPreloaded = false;
  private isPreloading = false;
  private readonly PRELOAD_TIMEOUT = 8000; // 8 secondes max

  /**
   * 🎯 PRÉCHARGEMENT SIMPLE - Précharge seulement si nécessaire
   */
  public async preload(modelId: string = 'auto'): Promise<boolean> {
    // 🎯 LOGIQUE SIMPLE : Si c'est le même modèle et déjà préchargé → SKIP
    if (this.currentModel === modelId && this.isPreloaded) {
      logger.debug(LogCategory.PERFORMANCE, `Model ${modelId} already preloaded, skipping`);
      return true;
    }

    // Éviter les préchargements simultanés
    if (this.isPreloading) {
      logger.debug(LogCategory.PERFORMANCE, 'Preload already in progress, skipping');
      return false;
    }

    this.isPreloading = true;
    logger.info(LogCategory.PERFORMANCE, `🚀 Preloading LM Studio model: ${modelId}`);

    try {
      // 🚀 MÉTHODE 1: Backend proxy
      let success = await this.preloadViaBackend();

      if (!success) {
        // 🔄 MÉTHODE 2: Direct fallback
        logger.debug(LogCategory.PERFORMANCE, 'Backend failed, trying direct...');
        success = await this.preloadDirectly();
      }

      if (success) {
        this.currentModel = modelId;
        this.isPreloaded = true;
        logger.info(LogCategory.PERFORMANCE, `✅ Model ${modelId} preloaded successfully`);
      } else {
        logger.warn(LogCategory.PERFORMANCE, `⚠️ Failed to preload model ${modelId}`);
      }

      return success;
    } catch (error) {
      logger.error(LogCategory.PERFORMANCE, 'Preload error', error);
      return false;
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * 🔄 FORCER LE PRÉCHARGEMENT (quand le modèle change)
   */
  public async forcePreload(modelId: string = 'auto'): Promise<boolean> {
    logger.info(LogCategory.PERFORMANCE, `🔄 Force preloading model: ${modelId}`);

    // Réinitialiser l'état
    this.isPreloaded = false;
    this.currentModel = null;

    return this.preload(modelId);
  }

  /**
   * Préchargement via le backend proxy - DÉSACTIVÉ car endpoint n'existe pas
   */
  private async preloadViaBackend(): Promise<boolean> {
    // L'endpoint /proxy/lmstudio/preload n'existe pas dans le backend
    // On utilise directement la méthode directe
    return false;
  }

  /**
   * Préchargement direct vers LM Studio
   */
  private async preloadDirectly(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.PRELOAD_TIMEOUT);

      const response = await fetch('http://localhost:1234/v1/chat/completions', {
        method: 'POST',
        signal: controller.signal,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'auto',
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 1,
          temperature: 0
        })
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 🎯 ÉTAT SIMPLE
   */
  public isCurrentlyPreloaded(modelId: string = 'auto'): boolean {
    return this.currentModel === modelId && this.isPreloaded;
  }

  /**
   * 🧪 VÉRIFIER DISPONIBILITÉ LM STUDIO
   */
  public async isLMStudioAvailable(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:1234/v1/models', {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 🧹 RESET COMPLET
   */
  public reset(): void {
    this.currentModel = null;
    this.isPreloaded = false;
    this.isPreloading = false;
    logger.debug(LogCategory.PERFORMANCE, 'Preloader reset');
  }
}

// 🎯 INSTANCE SINGLETON SIMPLE
export const lmStudioPreloader = new LMStudioPreloader();

// Debug en développement
if (import.meta.env.DEV) {
  (window as any).lmStudioPreloader = lmStudioPreloader;
}
