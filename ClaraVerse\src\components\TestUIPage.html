<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Immersive UI Demo</title>
  <style>
    /* CSS will be injected from the project */
    :root {
      --primary-color: #FF6B93;
      --secondary-color: #6499E9;
      --accent-color: #A6F6FF;
      --text-color: #333;
      --light-color: #fff;
      --dark-color: #1E1E1E;
      --success-color: #4CAF50;
      --warning-color: #FFC107;
      --error-color: #F44336;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    body {
      background-color: var(--dark-color);
      color: var(--light-color);
      height: 100vh;
      width: 100%;
      overflow: hidden;
    }
    
    .app-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }
    
    .hero-section {
      height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      background: linear-gradient(135deg, #FF6B93 0%, #6499E9 100%);
      position: relative;
      overflow: hidden;
      z-index: 1;
    }
    
    .hero-section::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
      opacity: 0.3;
      z-index: -1;
    }
    
    .hero-content {
      max-width: 800px;
      padding: 0 20px;
      z-index: 2;
    }
    
    h1 {
      font-size: 3.5rem;
      margin-bottom: 20px;
      color: var(--light-color);
      text-shadow: 0 2px 10px rgba(0,0,0,0.2);
      animation: fadeInUp 1s ease-out forwards;
    }
    
    p.subtitle {
      font-size: 1.5rem;
      margin-bottom: 40px;
      color: rgba(255,255,255,0.9);
      animation: fadeInUp 1s ease-out 0.3s forwards;
      opacity: 0;
    }
    
    .cta-buttons {
      display: flex;
      gap: 20px;
      margin-top: 20px;
      animation: fadeInUp 1s ease-out 0.6s forwards;
      opacity: 0;
    }
    
    .btn {
      padding: 15px 30px;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      outline: none;
    }
    
    .btn-primary {
      background-color: var(--light-color);
      color: var(--primary-color);
    }
    
    .btn-primary:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    }
    
    .btn-secondary {
      background-color: transparent;
      color: var(--light-color);
      border: 2px solid var(--light-color);
    }
    
    .btn-secondary:hover {
      background-color: rgba(255,255,255,0.1);
      transform: translateY(-5px);
    }
    
    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }
    
    .shape {
      position: absolute;
      background-color: rgba(255,255,255,0.1);
      border-radius: 50%;
      animation: float 15s infinite ease-in-out;
    }
    
    .shape:nth-child(1) {
      width: 150px;
      height: 150px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }
    
    .shape:nth-child(2) {
      width: 100px;
      height: 100px;
      top: 30%;
      right: 10%;
      animation-delay: 2s;
    }
    
    .shape:nth-child(3) {
      width: 200px;
      height: 200px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
    
    .shape:nth-child(4) {
      width: 80px;
      height: 80px;
      bottom: 30%;
      right: 20%;
      animation-delay: 6s;
    }
    
    @keyframes float {
      0% {
        transform: translateY(0) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(5deg);
      }
      100% {
        transform: translateY(0) rotate(0deg);
      }
    }
    
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }
    
    .modal.active {
      opacity: 1;
      visibility: visible;
    }
    
    .modal-content {
      background-color: var(--light-color);
      color: var(--text-color);
      max-width: 500px;
      width: 90%;
      border-radius: 10px;
      padding: 30px;
      animation: modalScale 0.3s ease-out forwards;
      transform: scale(0.7);
    }
    
    .modal.active .modal-content {
      transform: scale(1);
    }
    
    .modal-header {
      margin-bottom: 20px;
    }
    
    .modal-title {
      font-size: 1.8rem;
      color: var(--primary-color);
    }
    
    .modal-body {
      margin-bottom: 20px;
    }
    
    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
    
    .modal-btn {
      padding: 10px 20px;
      border-radius: 5px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
    }
    
    .btn-confirm {
      background-color: var(--primary-color);
      color: var(--light-color);
    }
    
    .btn-cancel {
      background-color: #eee;
      color: var(--text-color);
    }
    
    @keyframes modalScale {
      from {
        transform: scale(0.7);
      }
      to {
        transform: scale(1);
      }
    }
    
    @media (max-width: 768px) {
      h1 {
        font-size: 2.5rem;
      }
      
      p.subtitle {
        font-size: 1.2rem;
      }
      
      .cta-buttons {
        flex-direction: column;
        gap: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <div class="hero-section">
      <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
      </div>
      
      <div class="hero-content">
        <h1>Experience Immersive UI</h1>
        <p class="subtitle">This is a completely distraction-free, immersive UI experience that focuses entirely on your content.</p>
        
        <div class="cta-buttons">
          <button class="btn btn-primary" id="showModal">Get Started</button>
          <button class="btn btn-secondary">Learn More</button>
        </div>
      </div>
    </div>
    
    <div class="modal" id="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Welcome to ClaraVerse</h2>
        </div>
        <div class="modal-body">
          <p>This is an example of an interactive modal within your immersive UI. Your content can include any interactive elements you'd like.</p>
        </div>
        <div class="modal-footer">
          <button class="modal-btn btn-cancel" id="closeModal">Close</button>
          <button class="modal-btn btn-confirm">Confirm</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Add interactivity to the demo page
    const showModalButton = document.getElementById('showModal');
    const modal = document.getElementById('modal');
    const closeModalButton = document.getElementById('closeModal');
    
    showModalButton.addEventListener('click', () => {
      modal.classList.add('active');
    });
    
    closeModalButton.addEventListener('click', () => {
      modal.classList.remove('active');
    });
    
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.classList.remove('active');
      }
    });
    
    // Add some extra interactivity
    document.querySelectorAll('.btn').forEach(button => {
      button.addEventListener('mouseover', () => {
        button.style.transition = 'all 0.3s ease';
        button.style.transform = 'translateY(-5px)';
        button.style.boxShadow = '0 10px 20px rgba(0,0,0,0.2)';
      });
      
      button.addEventListener('mouseout', () => {
        button.style.transition = 'all 0.3s ease';
        button.style.transform = '';
        button.style.boxShadow = '';
      });
    });
    
    // Add a welcome message in the console
    console.log('Welcome to the Immersive UI Demo!');
    
    // Create dynamic effects
    function createWaveEffect() {
      const shapes = document.querySelectorAll('.shape');
      shapes.forEach((shape, index) => {
        const delay = index * 2;
        shape.style.animationDelay = `${delay}s`;
      });
    }
    
    createWaveEffect();
  </script>
</body>
</html> 