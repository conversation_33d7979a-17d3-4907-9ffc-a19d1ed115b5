# 🛠️ Tool Use Intelligent - Guide d'utilisation

## Vue d'ensemble

Le système Tool Use Intelligent permet aux LLMs (Qwen3, LM Studio) de décider automatiquement quand et comment utiliser des outils externes, notamment la recherche internet. Il remplace l'ancien système de recherche manuelle par une approche autonome et intelligente.

## Fonctionnalités principales

### 🔍 Mode Recherche Forcé
- **Activation** : Cliquer sur le bouton de recherche dans l'interface
- **Comportement** : Le LLM DOIT utiliser la recherche internet pour chaque réponse
- **Usage** : Idéal pour des questions nécessitant des informations récentes

### 🧠 Mode Autonome
- **Activation** : Mode par défaut quand la recherche n'est pas forcée
- **Comportement** : Le LLM PEUT décider d'utiliser la recherche s'il le juge nécessaire
- **Usage** : Conversation normale avec recherche intelligente automatique

## Architecture technique

### Composants principaux

#### 1. InternetSearchTool (`src/services/api/tools/InternetSearchTool.ts`)
```typescript
// Configuration du tool search_internet
export const INTERNET_SEARCH_TOOL: Tool = {
  id: 'search_internet',
  name: 'search_internet',
  description: 'Search the internet for current, accurate information...',
  parameters: [
    {
      name: 'query',
      type: 'string',
      description: 'Search query optimized to find relevant information',
      required: true
    },
    {
      name: 'search_type',
      type: 'string',
      description: 'Type of search: general, technical, news, shopping',
      required: false
    }
  ]
};
```

#### 2. ToolUseManager (`src/services/api/tools/ToolUseManager.ts`)
```typescript
// Gestionnaire intelligent des tools
export class ToolUseManager {
  // Génère le prompt système selon la configuration
  generateSystemPrompt(config: ToolUseConfig): string
  
  // Exécute un tool call
  executeToolCall(toolCall: any): Promise<ToolCallResult>
  
  // Obtient les tools disponibles
  getAvailableTools(config: ToolUseConfig): Promise<any[]>
}
```

#### 3. ChatManager - Nouvelle méthode
```typescript
// Nouvelle méthode avec tool use intelligent
public async sendMessageWithIntelligentTools(
  context: ChatContext,
  onContentChunk?: (content: string) => void,
  onSearchProgress?: (sources: string[], action: string) => void
): Promise<ClaraMessage>
```

### Flux de traitement

1. **Détection du mode** : L'interface détermine si le mode recherche est forcé
2. **Configuration** : Création de la `ToolUseConfig` appropriée
3. **Prompt système** : Génération du prompt avec instructions tool use
4. **Envoi LLM** : Le LLM reçoit les tools disponibles et décide de les utiliser
5. **Exécution** : Si le LLM fait un tool call, exécution automatique
6. **Intégration** : Les résultats sont intégrés dans la réponse finale

## Utilisation pratique

### Interface utilisateur

#### Bouton de recherche
- **État normal** : Mode autonome activé
- **État bleu** : Mode recherche forcé activé
- **Animation** : Progression de recherche en temps réel

#### Messages avec recherche
- **Sources** : Affichées pendant la recherche
- **Intégration** : Résultats invisibles pour l'utilisateur final
- **Qualité** : Réponses enrichies avec informations récentes

### Exemples d'usage

#### Mode recherche forcé
```
Utilisateur: "Quel est le prix du Bitcoin aujourd'hui ?"
Système: [Active automatiquement search_internet]
LLM: Appelle search_internet("prix Bitcoin aujourd'hui")
Résultat: Réponse avec prix actuel et sources
```

#### Mode autonome
```
Utilisateur: "Comment faire une omelette ?"
LLM: [Décide de ne pas chercher - connaissance suffisante]
Résultat: Réponse directe sans recherche

Utilisateur: "Quelles sont les nouvelles de Tesla cette semaine ?"
LLM: [Décide de chercher - informations récentes nécessaires]
Résultat: Appelle search_internet automatiquement
```

## Configuration

### Types de recherche supportés
- **general** : Recherche générale (défaut)
- **technical** : Recherche technique/IT
- **news** : Actualités
- **shopping** : Produits/prix

### Paramètres configurables
```typescript
interface ToolUseConfig {
  forceSearchMode: boolean;    // Mode recherche forcé
  autonomousMode: boolean;     // Mode autonome
  provider: 'ollama' | 'lm_studio';
  model: string;
}
```

## Compatibilité

### Providers supportés
- ✅ **Ollama** (Qwen3 avec tool use)
- ✅ **LM Studio** (Compatible OpenAI tools)

### Modèles recommandés
- **Qwen3** : Support natif du tool use
- **Autres modèles** : Compatibilité via format OpenAI

## Avantages

### Par rapport à l'ancien système
1. **Autonomie** : Le LLM décide quand chercher
2. **Qualité** : Requêtes de recherche optimisées par le LLM
3. **Intégration** : Résultats seamless dans la conversation
4. **Performance** : Pas de recherche inutile
5. **UX** : Interface plus fluide et naturelle

### Bénéfices utilisateur
- Réponses plus précises et à jour
- Pas besoin d'activer manuellement la recherche
- Sources automatiquement intégrées
- Conversation plus naturelle

## Dépannage

### Problèmes courants

#### Tool use ne fonctionne pas
- Vérifier que le provider supporte les tools
- Vérifier la configuration du modèle
- Consulter les logs pour les erreurs

#### Recherche trop fréquente
- Ajuster les prompts système
- Modifier les triggers de détection
- Utiliser le mode normal au lieu du mode forcé

#### Pas de recherche en mode autonome
- Vérifier que le modèle supporte le tool use
- Reformuler la question pour être plus explicite
- Activer le mode recherche forcé si nécessaire

## Logs et monitoring

### Catégories de logs
- `LogCategory.TOOLS` : Exécution des tools
- `LogCategory.PROVIDERS` : Communication avec les providers
- `LogCategory.SEARCH` : Opérations de recherche

### Métriques importantes
- Nombre de tool calls par conversation
- Temps d'exécution des recherches
- Taux de succès des tool calls
- Qualité des réponses avec/sans tools
