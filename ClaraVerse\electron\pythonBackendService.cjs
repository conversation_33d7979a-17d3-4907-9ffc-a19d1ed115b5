const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');
const log = require('electron-log');

class PythonBackendService {
  constructor() {
    this.process = null;
    this.isRunning = false;
    this.port = 5001;
    this.backendPath = path.join(__dirname, '..', 'py_backend');
    this.mainScript = path.join(this.backendPath, 'main.py');
  }

  async start() {
    try {
      log.info('🐍 Starting Python backend service...');
      
      // Vérifier que le script existe
      if (!fs.existsSync(this.mainScript)) {
        throw new Error(`Python backend script not found: ${this.mainScript}`);
      }

      // Vérifier que Python est disponible
      await this.checkPython();

      // Démarrer le processus Python
      this.process = spawn('python', ['main.py'], {
        cwd: this.backendPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONUNBUFFERED: '1',
          CLARA_PORT: this.port.toString(),
          CLARA_HOST: '127.0.0.1'
        }
      });

      // Gérer les logs
      this.process.stdout.on('data', (data) => {
        log.info(`[Python Backend] ${data.toString().trim()}`);
      });

      this.process.stderr.on('data', (data) => {
        log.error(`[Python Backend Error] ${data.toString().trim()}`);
      });

      this.process.on('close', (code) => {
        log.info(`[Python Backend] Process exited with code ${code}`);
        this.isRunning = false;
        this.process = null;
      });

      this.process.on('error', (error) => {
        log.error(`[Python Backend] Process error:`, error);
        this.isRunning = false;
        this.process = null;
      });

      // Attendre que le service soit prêt
      await this.waitForReady();
      
      this.isRunning = true;
      log.info('✅ Python backend service started successfully');
      
      return true;
    } catch (error) {
      log.error('❌ Failed to start Python backend service:', error);
      throw error;
    }
  }

  async stop() {
    if (this.process) {
      log.info('🛑 Stopping Python backend service...');
      this.process.kill('SIGTERM');
      
      // Attendre un peu pour un arrêt propre
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Forcer l'arrêt si nécessaire
      if (this.process && !this.process.killed) {
        this.process.kill('SIGKILL');
      }
      
      this.process = null;
      this.isRunning = false;
      log.info('✅ Python backend service stopped');
    }
  }

  async checkPython() {
    return new Promise((resolve, reject) => {
      const pythonCheck = spawn('python', ['--version'], { stdio: 'pipe' });
      
      pythonCheck.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error('Python not found. Please install Python 3.8+'));
        }
      });

      pythonCheck.on('error', (error) => {
        reject(new Error('Python not found. Please install Python 3.8+'));
      });
    });
  }

  async waitForReady(maxAttempts = 45, delay = 1000) {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const isHealthy = await this.checkHealth();
        if (isHealthy) {
          return true;
        }
      } catch (error) {
        // Continuer à essayer
      }
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    throw new Error('Python backend failed to start within timeout');
  }

  async checkHealth() {
    return new Promise((resolve) => {
      const req = http.get(`http://localhost:${this.port}/health`, (res) => {
        resolve(res.statusCode === 200);
      });

      req.on('error', () => {
        resolve(false);
      });

      req.setTimeout(2000, () => {
        req.destroy();
        resolve(false);
      });
    });
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      port: this.port,
      pid: this.process ? this.process.pid : null
    };
  }

  async restart() {
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 1000));
    return await this.start();
  }
}

module.exports = PythonBackendService;
