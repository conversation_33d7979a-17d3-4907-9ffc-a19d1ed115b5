import React from 'react';
import { CheckCircle, FileText, Database, Zap } from 'lucide-react';

export interface DocumentProcessingStatus {
  filename: string;
  stage: 'selecting' | 'adding' | 'indexing' | 'complete' | 'uploading' | 'ocr';
  progress: number;
  source?: 'chat_upload' | 'rag_selection' | 'document_manager';
}

interface DocumentProcessingAnimationProps {
  status: DocumentProcessingStatus | null;
  isVisible: boolean;
  onComplete?: () => void;
}

const DocumentProcessingAnimation: React.FC<DocumentProcessingAnimationProps> = ({
  status,
  isVisible,
  onComplete
}) => {
  if (!isVisible || !status) return null;

  const getStageInfo = (stage: string) => {
    switch (stage) {
      case 'selecting':
        return {
          icon: FileText,
          text: 'Sélection du document...',
          color: 'text-blue-600 dark:text-blue-400'
        };
      case 'uploading':
        return {
          icon: FileText,
          text: 'Upload en cours...',
          color: 'text-blue-600 dark:text-blue-400'
        };
      case 'ocr':
        return {
          icon: Zap,
          text: 'Traitement OCR...',
          color: 'text-purple-600 dark:text-purple-400'
        };
      case 'adding':
        return {
          icon: Database,
          text: 'Ajout au RAG...',
          color: 'text-wema-600 dark:text-wema-400'
        };
      case 'indexing':
        return {
          icon: Database,
          text: 'Indexation RAG...',
          color: 'text-wema-600 dark:text-wema-400'
        };
      case 'complete':
        return {
          icon: CheckCircle,
          text: 'Document prêt !',
          color: 'text-green-600 dark:text-green-400'
        };
      default:
        return {
          icon: FileText,
          text: 'Traitement...',
          color: 'text-gray-600 dark:text-gray-400'
        };
    }
  };

  const stageInfo = getStageInfo(status.stage);
  const IconComponent = stageInfo.icon;

  const getSourceLabel = (source?: string) => {
    switch (source) {
      case 'chat_upload':
        return 'Upload Chat';
      case 'rag_selection':
        return 'Sélection RAG';
      case 'document_manager':
        return 'Document Manager';
      default:
        return 'Document';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] animate-in slide-in-from-right-5 duration-300">
      {/* Header */}
      <div className="flex items-center gap-3 mb-3">
        <div className={`p-2 rounded-lg bg-gray-50 dark:bg-gray-700 ${stageInfo.color}`}>
          <IconComponent className="w-5 h-5" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
            {status.filename}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {getSourceLabel(status.source)}
          </p>
        </div>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className={`${stageInfo.color} font-medium`}>
            {stageInfo.text}
          </span>
          <span className="text-gray-500 dark:text-gray-400 text-xs">
            {status.progress}%
          </span>
        </div>
        
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              status.stage === 'complete'
                ? 'bg-green-500'
                : status.stage === 'selecting' || status.stage === 'uploading'
                ? 'bg-blue-500'
                : status.stage === 'ocr'
                ? 'bg-purple-500'
                : status.stage === 'adding' || status.stage === 'indexing'
                ? 'bg-wema-500'
                : 'bg-gray-500'
            }`}
            style={{ width: `${status.progress}%` }}
          />
        </div>
      </div>

      {/* Success message */}
      {status.stage === 'complete' && (
        <div className="mt-3 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-2 text-green-700 dark:text-green-300 text-sm">
            <CheckCircle className="w-4 h-4" />
            <span>Document disponible pour le RAG</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentProcessingAnimation;
