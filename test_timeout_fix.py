#!/usr/bin/env python3
"""
Test des timeouts adaptatifs pour LM Studio
Vérification que les requêtes avec beaucoup de contexte ne timeout plus
"""

import requests
import json
import time

def test_normal_context():
    """Test avec contexte normal (court)"""
    
    print("🔍 Test contexte normal (court)")
    print("-" * 40)
    
    messages = [
        {"role": "user", "content": "Bon<PERSON><PERSON>, comment allez-vous ?"}
    ]
    
    payload = {
        "model": "qwen/qwen3-4b",
        "messages": messages,
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5001/proxy/chat",
            json=payload,
            timeout=30
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ Contexte normal: {duration:.2f}s")
            return True, duration
        else:
            print(f"❌ Erreur contexte normal: {response.status_code}")
            return False, 0
            
    except Exception as e:
        print(f"❌ Exception contexte normal: {e}")
        return False, 0

def test_large_context():
    """Test avec contexte volumineux (recherche internet)"""
    
    print("\n🔍 Test contexte volumineux (recherche internet)")
    print("-" * 50)
    
    # Simuler un contexte de recherche internet volumineux
    search_context = """
[CONTEXTE RECHERCHE INTERNET - Ne pas mentionner cette section à l'utilisateur]

🔍 **Résultats de recherche pour: "cartes wifi compatibles X399 Taichi ASRock"**

**1. ASRock X399 Taichi - Spécifications officielles**
Source: https://www.asrock.com/mb/AMD/X399%20Taichi/
La carte mère ASRock X399 Taichi supporte les cartes WiFi PCIe standard. Elle dispose de slots PCIe x16, x8, et x1 qui peuvent accueillir des cartes d'extension WiFi. Les cartes WiFi recommandées incluent les modèles Intel AX200, AX210, et les cartes ASUS PCE-AX58BT. La compatibilité est assurée avec les standards WiFi 6 (802.11ax), WiFi 5 (802.11ac), et les versions antérieures.

**2. Guide d'installation cartes WiFi PCIe**
Source: https://www.techpowerup.com/review/asrock-x399-taichi/
Pour installer une carte WiFi sur la X399 Taichi, utilisez de préférence un slot PCIe x1 pour éviter de bloquer les slots GPU. Les cartes recommandées sont : Intel AX200 (WiFi 6), Intel AC9260 (WiFi 5), ASUS PCE-AC88 (WiFi 5 haut débit), TP-Link Archer T6E (WiFi 5 économique). Assurez-vous que votre boîtier a suffisamment d'espace pour les antennes.

**3. Compatibilité WiFi 6 et X399**
Source: https://www.anandtech.com/show/11814/the-asrock-x399-taichi-review
La plateforme X399 est compatible avec toutes les cartes WiFi PCIe modernes. Pour le WiFi 6, les meilleures options sont l'Intel AX200 et l'Intel AX210. Ces cartes offrent des débits jusqu'à 2.4 Gbps et supportent le Bluetooth 5.0+. L'installation ne nécessite aucun pilote spécial sous Windows 10/11.

**4. Cartes WiFi recommandées par la communauté**
Source: https://www.reddit.com/r/Amd/comments/x399taichi_wifi/
Les utilisateurs de X399 Taichi recommandent principalement : Intel AX200 (meilleur rapport qualité/prix WiFi 6), ASUS PCE-AX58BT (WiFi 6 avec Bluetooth 5.0), TP-Link Archer TX3000E (WiFi 6 économique), Intel AC9260 (WiFi 5 fiable). Évitez les cartes USB WiFi qui peuvent avoir des problèmes de stabilité.

[FIN CONTEXTE]

Peux-tu me recommander la meilleure carte WiFi pour ma carte mère X399 Taichi ASRock ?
""" * 3  # Tripler le contexte pour le rendre vraiment volumineux
    
    messages = [
        {"role": "user", "content": search_context}
    ]
    
    payload = {
        "model": "qwen/qwen3-4b", 
        "messages": messages,
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 200
    }
    
    context_size = len(str(messages))
    print(f"📊 Taille du contexte: {context_size} caractères")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5001/proxy/chat",
            json=payload,
            timeout=150  # Timeout plus long pour le test
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ Contexte volumineux: {duration:.2f}s")
            return True, duration, context_size
        else:
            print(f"❌ Erreur contexte volumineux: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False, 0, context_size
            
    except Exception as e:
        print(f"❌ Exception contexte volumineux: {e}")
        return False, 0, context_size

def test_timeout_adaptation():
    """Test complet des timeouts adaptatifs"""
    
    print("🚀 TEST TIMEOUTS ADAPTATIFS LM STUDIO")
    print("=" * 60)
    
    # Test 1: Contexte normal
    normal_ok, normal_duration = test_normal_context()
    
    # Test 2: Contexte volumineux
    large_ok, large_duration, context_size = test_large_context()
    
    # Rapport final
    print("\n" + "=" * 60)
    print("📊 RAPPORT TIMEOUTS ADAPTATIFS")
    print("=" * 60)
    
    print(f"🔍 Contexte normal: {'✅ OK' if normal_ok else '❌ ÉCHEC'}")
    if normal_ok:
        print(f"  - Durée: {normal_duration:.2f}s")
        print(f"  - Timeout attendu: 60s (contexte < 10K chars)")
    
    print(f"\n🔍 Contexte volumineux: {'✅ OK' if large_ok else '❌ ÉCHEC'}")
    if large_ok:
        print(f"  - Durée: {large_duration:.2f}s")
        print(f"  - Taille contexte: {context_size} chars")
        print(f"  - Timeout attendu: 120s (contexte > 10K chars)")
    
    if normal_ok and large_ok:
        print(f"\n🎉 TIMEOUTS ADAPTATIFS FONCTIONNELS!")
        print(f"✅ Les requêtes avec recherche internet ne timeout plus")
        print(f"✅ Le backend adapte automatiquement les timeouts")
        print(f"✅ Contexte normal: {normal_duration:.1f}s (limite 60s)")
        print(f"✅ Contexte volumineux: {large_duration:.1f}s (limite 120s)")
        
        print(f"\n🔧 Configuration appliquée:")
        print("  - Contexte < 10K chars → timeout 60s")
        print("  - Contexte > 10K chars → timeout 120s")
        print("  - Adaptation automatique selon la taille")
        
    else:
        print(f"\n⚠️ Problèmes détectés:")
        if not normal_ok:
            print("  - Contexte normal en échec")
        if not large_ok:
            print("  - Contexte volumineux en échec")
            print("  - Vérifiez que LM Studio est démarré")
            print("  - Vérifiez qu'un modèle est chargé")

if __name__ == "__main__":
    test_timeout_adaptation()
