#!/usr/bin/env node

/**
 * 🧪 Test CORS Direct - Vérification que LM Studio fonctionne en CORS direct
 * Ce script teste la nouvelle architecture sans proxy backend
 */

import { performance } from 'perf_hooks';

console.log('🧪 Test CORS Direct - LM Studio');
console.log('===============================');

// Test 1: Vérifier que LM Studio est disponible en CORS direct
async function testLMStudioDirectCORS() {
  console.log('\n📡 Test 1: LM Studio Direct CORS');
  
  try {
    const response = await fetch('http://localhost:1234/v1/models', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ LM Studio CORS direct fonctionne !');
      console.log(`📊 Trouvé ${data.data?.length || 0} modèles`);
      
      if (data.data && data.data.length > 0) {
        console.log('📝 Modèles disponibles:');
        data.data.forEach((model, index) => {
          console.log(`   ${index + 1}. ${model.id}`);
        });
      }
      
      return true;
    } else {
      console.log('❌ LM Studio a répondu avec une erreur:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ LM Studio n\'est pas accessible en CORS direct:', error.message);
    return false;
  }
}

// Test 2: Vérifier que l'ancien proxy backend n'est plus utilisé
async function testOldProxyRemoved() {
  console.log('\n🗑️ Test 2: Vérification suppression proxy');
  
  try {
    const response = await fetch('http://localhost:8000/proxy/lmstudio/models', {
      method: 'GET',
      mode: 'cors'
    });
    
    if (response.status === 404) {
      console.log('✅ Ancien endpoint proxy correctement supprimé');
      return true;
    } else {
      console.log('⚠️ Ancien endpoint proxy encore actif (peut être normal)');
      return true; // Pas critique
    }
  } catch (error) {
    console.log('✅ Backend proxy non accessible (normal après suppression)');
    return true;
  }
}

// Test 3: Test de performance CORS vs Proxy
async function testPerformanceComparison() {
  console.log('\n⚡ Test 3: Performance CORS Direct');
  
  const startTime = performance.now();
  
  try {
    const response = await fetch('http://localhost:1234/v1/models', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      mode: 'cors'
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (response.ok) {
      console.log(`✅ CORS Direct: ${duration.toFixed(2)}ms`);
      console.log('🚀 Performance optimale - pas de proxy intermédiaire !');
      return true;
    } else {
      console.log('❌ Échec du test de performance');
      return false;
    }
  } catch (error) {
    console.log('❌ Erreur test performance:', error.message);
    return false;
  }
}

// Exécuter tous les tests
async function runAllTests() {
  console.log('🚀 Démarrage des tests CORS Direct...\n');
  
  const results = {
    corsDirectTest: await testLMStudioDirectCORS(),
    proxyRemovalTest: await testOldProxyRemoved(),
    performanceTest: await testPerformanceComparison()
  };
  
  console.log('\n📊 Résultats des tests:');
  console.log('========================');
  console.log(`CORS Direct:      ${results.corsDirectTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Proxy supprimé:   ${results.proxyRemovalTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Performance:      ${results.performanceTest ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 Tous les tests CORS Direct sont passés !');
    console.log('✅ Migration vers CORS direct réussie');
  } else {
    console.log('\n⚠️ Certains tests ont échoué');
    console.log('🔧 Vérifiez que LM Studio est démarré et accessible');
  }
  
  return allPassed;
}

// Exécuter directement
runAllTests().catch(console.error);
