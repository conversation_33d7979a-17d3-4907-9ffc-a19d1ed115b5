import React, { memo } from 'react';
import { ClaraMessage } from '../types/clara_assistant_types';

interface OptimizedMessageProps {
  message: ClaraMessage;
  isStreaming?: boolean;
}

/**
 * 🚀 COMPOSANT MESSAGE OPTIMISÉ
 * Utilise React.memo pour éviter les re-renders inutiles
 */
const OptimizedMessage = memo<OptimizedMessageProps>(({ message, isStreaming }) => {
  return (
    <div className={`message ${message.role} ${isStreaming ? 'streaming' : ''}`}>
      <div className="message-content">
        {message.content}
        {isStreaming && <span className="streaming-cursor">▋</span>}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // 🚀 OPTIMISATION: Comparaison personnalisée pour éviter les re-renders
  // Ne re-render que si le contenu ou le statut streaming change vraiment
  return (
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.id === nextProps.message.id &&
    prevProps.isStreaming === nextProps.isStreaming
  );
});

OptimizedMessage.displayName = 'OptimizedMessage';

export default OptimizedMessage;
