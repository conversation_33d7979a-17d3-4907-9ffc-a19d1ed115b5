@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap');
@import 'prismjs/themes/prism.css';
@import 'prismjs/themes/prism-dark.css';

/* Import global scrollbar hiding first to ensure it takes precedence */
@import './styles/scrollbar-global.css';

/* WeMa IA specific styles */
@import './styles/wemaSidebar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables - WeMa IA Theme */
:root {
  --wema-50: #eff6ff;
  --wema-100: #dbeafe;
  --wema-200: #bfdbfe;
  --wema-300: #93c5fd;
  --wema-400: #60a5fa;
  --wema-500: #3b82f6;
  --wema-600: #2563eb;
  --wema-700: #1d4ed8;
  --wema-800: #1e40af;
  --wema-900: #1e3a8a;
}

@layer base {
  :root {
    --wema-50: #eff6ff;
    --wema-100: #dbeafe;
    --wema-200: #bfdbfe;
    --wema-300: #93c5fd;
    --wema-400: #60a5fa;
    --wema-500: #3b82f6;
    --wema-600: #2563eb;
    font-family: 'Quicksand', sans-serif;
  }

  .dark {
    --wema-50: #0f172a;
    --wema-100: #1e293b;
    --wema-200: #334155;
    --wema-300: #475569;
    --wema-400: #64748b;
    --wema-500: #3b82f6;
    --wema-600: #60a5fa;
  }
}

@layer components {
  .prose-wema {
    @apply prose dark:prose-invert;
  }

  .prose-wema a {
    @apply text-wema-500 transition-colors;
  }

  .prose-wema a:hover {
    color: var(--wema-600);
  }

  .dark .prose-wema a {
    @apply text-wema-400;
  }

  .dark .prose-wema a:hover {
    @apply text-wema-500;
  }

  .glassmorphic {
    @apply bg-white/70 backdrop-blur-lg border border-white/20 dark:bg-gray-900/70 dark:border-gray-800/20;
  }

  .glassmorphic-card {
    @apply bg-white/60 dark:bg-gray-900/60 backdrop-blur-md border border-white/30 dark:border-gray-700/30 shadow-xl;
  }
}

/* Complete cleanup of code blocks in markdown for both light and dark mode */
.prose pre, 
.prose code,
.dark .prose pre,
.dark .prose code {
  border: none !important;
  box-shadow: none !important;
}

/* Completely remove code syntax highlighting for all spans */
.prose pre code span,
.dark .prose pre code span {
  background: transparent !important;
  color: #e5e7eb !important;
  border: none !important;
  font-weight: normal !important;
  text-decoration: none !important;
}

/* Make code blocks consistent in both modes */
.prose pre,
.dark .prose pre {
  background-color: #1E1E1E !important;
  color: #e5e7eb !important;
  border-radius: 4px !important;
  padding: 0.75rem 1rem !important;
  border: none !important;
}

/* Ensure code text is visible in both modes with no highlighting */
.prose pre code,
.dark .prose pre code {
  color: #e5e7eb !important;
  background-color: transparent !important;
  padding: 0 !important;
}

/* Inline code styling for both modes */
.prose :not(pre) > code,
.dark .prose :not(pre) > code {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
  padding: 0.2em 0.4em !important;
  border-radius: 0.25rem !important;
  border: none !important;
}

/* Dark mode inline code */
.dark .prose :not(pre) > code {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

/* Override any theme-specific syntax highlighting */
.prose pre [class*="language-"],
.prose code [class*="language-"],
.dark .prose pre [class*="language-"],
.dark .prose code [class*="language-"] {
  color: #e5e7eb !important;
  background: transparent !important;
  text-shadow: none !important;
  font-family: monospace !important;
}

/* Smooth width transitions */
.w-0 {
  width: 0;
}

.w-auto {
  width: auto;
}

/* Scrollbar styling - Global hiding with optional thin scrollbar utility */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-gray-400/50 dark:bg-gray-600/50 rounded-full;
}

/* Global scrollbar hiding - Override any existing scrollbar styles */
* {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

/* Hide scrollbar but maintain functionality - Enhanced version */
.scrollbar-none {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.scrollbar-none::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

/* Smooth scrolling */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Animation delay utilities */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

/* App runner animations */
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-slide-up {
  animation: slideInUp 0.4s ease-out forwards;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite ease-in-out;
}

/* Auto-resize textarea */
textarea {
  min-height: 2.5rem;
  transition: height 0.2s ease;
}

/* Ensure markdown content has proper colors in dark mode */
.dark .markdown-wrapper {
  color-scheme: dark;
}

.dark-mode-prose {
  color-scheme: dark;
}

.dark .dark-mode-prose {
  --tw-prose-body: #e5e7eb;
  --tw-prose-headings: #f9fafb;
  --tw-prose-lead: #d1d5db;
  --tw-prose-links: #93c5fd;
  --tw-prose-bold: #f9fafb;
  --tw-prose-counters: #d1d5db;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #374151;
  --tw-prose-quotes: #f3f4f6;
  --tw-prose-quote-borders: #374151;
  --tw-prose-captions: #9ca3af;
  --tw-prose-code: #f9fafb;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #374151;
  --tw-prose-td-borders: #374151;
}

/* Code blocks in dark mode */
.dark .prose pre {
  background-color: #1f2937;
  border: 1px solid #374151;
}

.dark .prose code {
  color: #f9fafb;
  background-color: #374151;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-weight: 500;
}

.dark .prose a {
  color: #93c5fd;
  text-decoration: underline;
}

/* Lists in dark mode */
.dark .prose ul, 
.dark .prose ol {
  color: #e5e7eb;
}

/* Table styling in dark mode */
.dark .prose table {
  border-color: #374151;
}

.dark .prose thead {
  color: #f9fafb;
  border-bottom-color: #4b5563;
}

.dark .prose tbody tr {
  border-bottom-color: #374151;
}

.prose h2 {
  @apply text-2xl font-bold mb-4 text-gray-900 dark:text-white mt-8;
}

.prose h3 {
  @apply text-xl font-semibold mb-3 text-gray-800 dark:text-gray-100 mt-6;
}

.prose p {
  @apply mb-4 text-gray-700 dark:text-gray-300;
}

/* Complete cleanup of code blocks in markdown for both light and dark mode */
.bg-gradient-to-br {
  background: linear-gradient(to bottom right, #ffffff, #f9fafb);
}

.dark .bg-gradient-to-br {
  background: #000000;
}

/* ⚡ OPTIMISATION : Sidebar icon transitions avec GPU acceleration */
.sidebar-icon {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  width: 20px;
  height: 20px;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: opacity; /* Optimise les changements d'opacité */
}

.sidebar-expanded .sidebar-icon,
.glassmorphic:hover .sidebar-icon {
  opacity: 1;
}

/* ⚡ OPTIMISATION : Transitions fluides pour tous les panels */
.glassmorphic {
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: width, transform; /* Optimise les changements de taille */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Courbe plus fluide */
}

/* ⚡ OPTIMISATION : Réduire les repaints pendant les animations */
.transition-all {
  transform: translateZ(0); /* Force GPU acceleration */
  backface-visibility: hidden; /* Évite les flickering */
}

/* ⚡ PERFORMANCE : Optimisations globales pour animations fluides */
* {
  /* Améliore les performances des transitions */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ⚡ OPTIMISATION : Conteneurs avec beaucoup de contenu */
.chat-container, .conversation-list, .panel-container {
  contain: layout style paint; /* Isole les repaints */
  transform: translateZ(0); /* GPU acceleration */
}

/* ⚡ PERFORMANCE : Éviter les re-layouts pendant les animations */
.animate-slide, .animate-expand, .animate-collapse {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* ⚡ SIDEBAR OPTIMISÉE : Transitions fluides sans recalculs */
.sidebar-container {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  will-change: width;
}

.sidebar-button {
  @apply w-full flex items-center rounded-lg h-10 relative;
  @apply text-gray-700 dark:text-gray-300;
  transition: background-color 0.2s ease, color 0.2s ease;
  transform: translateZ(0);
}

.sidebar-expanded .sidebar-button {
  @apply px-4 justify-start gap-3;
}

.sidebar-collapsed .sidebar-button {
  @apply justify-center px-0;
}

.sidebar-button:hover {
  @apply bg-wema-50 text-wema-500 dark:bg-wema-100/10;
}

.sidebar-button.active {
  @apply bg-wema-100 text-wema-500 dark:bg-wema-100/10;
}

.sidebar-text {
  @apply whitespace-nowrap overflow-hidden;
  transition: opacity 0.3s ease, width 0.3s ease, margin 0.3s ease;
  transform: translateZ(0);
}

.sidebar-expanded .sidebar-text {
  @apply opacity-100 w-auto ml-3;
}

.sidebar-collapsed .sidebar-text {
  @apply opacity-0 w-0 ml-0;
}

/* ⚡ OPTIMISATION : Indicateur d'activité avec animation GPU */
.activity-indicator {
  @apply absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full;
  animation: smooth-pulse 2s ease-in-out infinite;
  transform: translateZ(0);
}

@keyframes smooth-pulse {
  0%, 100% {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateZ(0) scale(1.1);
  }
}

/* Enhanced prose styling for markdown content */
.prose {
  @apply text-gray-800 dark:text-gray-200;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  @apply text-gray-900 dark:text-gray-100 font-bold;
  @apply border-b border-gray-200 dark:border-gray-700 pb-2 mb-4;
}

.prose h1 { @apply text-2xl; }
.prose h2 { @apply text-xl; }
.prose h3 { @apply text-lg; }

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul, .prose ol {
  @apply mb-4;
}

.prose li {
  @apply mb-2 pl-2;
}

/* Ensure proper list alignment and spacing */
.prose ol {
  @apply list-decimal mb-4 space-y-2 pl-6;
  list-style-position: outside;
}

.prose ul {
  @apply list-disc mb-4 space-y-2 pl-6;
  list-style-position: outside;
}

/* Nested list styling */
.prose ol ol,
.prose ul ul,
.prose ol ul,
.prose ul ol {
  @apply mt-2 mb-2;
}

/* List item content alignment */
.prose li > p {
  @apply mb-2;
}

.prose li > p:first-child {
  @apply mt-0;
}

.prose li > p:last-child {
  @apply mb-0;
}

.prose table {
  @apply border-collapse border border-gray-300 dark:border-gray-600;
}

.prose th, .prose td {
  @apply border border-gray-300 dark:border-gray-600 px-4 py-2;
}

.prose th {
  @apply bg-gray-100 dark:bg-gray-800 font-semibold;
}

/* Code block enhancements */
.prose pre {
  @apply bg-transparent p-0 m-0;
}

.prose :not(pre) > code {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1.5 py-0.5 rounded text-sm font-mono;
}

/* Enhanced glassmorphism effects for voice chat */
.glassmorphic-card {
  @apply bg-white/60 dark:bg-gray-900/60 backdrop-blur-md border border-white/30 dark:border-gray-700/30 shadow-xl;
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
}

/* Voice chat specific animations */
@keyframes voicePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes voiceRipple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes wemaGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5), 0 0 60px rgba(59, 130, 246, 0.2);
  }
}

@keyframes floatingParticle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

.animate-voice-pulse {
  animation: voicePulse 2s ease-in-out infinite;
}

.animate-voice-ripple {
  animation: voiceRipple 1.5s ease-out infinite;
}

.animate-wema-glow {
  animation: wemaGlow 3s ease-in-out infinite;
}

.animate-floating-particle {
  animation: floatingParticle 4s ease-in-out infinite;
}

/* Enhanced button hover effects */
.voice-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.voice-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.voice-button:hover::before {
  left: 100%;
}

/* Breathing animation for idle states */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.animate-breathe {
  animation: breathe 4s ease-in-out infinite;
}

/* Status indicator animations */
@keyframes statusPing {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-status-ping {
  animation: statusPing 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Gradient text animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-text {
  background: linear-gradient(-45deg, #ec4899, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Gradient text animation */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

/* Enhanced card animations */
@keyframes shine {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.animate-shine {
  animation: shine 1.5s ease-in-out infinite;
}