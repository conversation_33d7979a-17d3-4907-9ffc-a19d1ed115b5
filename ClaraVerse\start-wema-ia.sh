#!/bin/bash

echo ""
echo "========================================"
echo "🚀 DÉMARRAGE AUTOMATIQUE WEMA IA"
echo "========================================"
echo ""

# Aller dans le répertoire du script
cd "$(dirname "$0")"

echo "📁 Répertoire de travail: $(pwd)"
echo ""

echo "🔧 Démarrage du backend (avec auto-start Docker + SearXNG)..."
cd py_backend

# Démarrer le backend en arrière-plan
python main.py &
BACKEND_PID=$!

echo "⏳ Attente du démarrage du backend..."
sleep 10

echo "🌐 Démarrage du frontend..."
cd ..

# Démarrer le frontend en arrière-plan
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ WeMa IA en cours de démarrage !"
echo ""
echo "📖 Le backend démarre automatiquement :"
echo "   - <PERSON>er"
echo "   - SearXNG (recherche internet)"
echo "   - Tous les services"
echo ""
echo "🌐 Interfaces :"
echo "   - Frontend: http://localhost:5173 ou 5174"
echo "   - Backend: http://localhost:5001"
echo "   - SearXNG: http://localhost:8888"
echo ""
echo "📊 Statut démarrage: http://localhost:5001/startup/status"
echo ""
echo "🛑 Pour arrêter WeMa IA, appuyez sur Ctrl+C"
echo ""

# Fonction de nettoyage
cleanup() {
    echo ""
    echo "🛑 Arrêt de WeMa IA..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ WeMa IA arrêté"
    exit 0
}

# Capturer Ctrl+C
trap cleanup SIGINT

# Attendre indéfiniment
wait
