/**
 * 🔄 Service de mise à jour automatique
 * Vérifie et applique les mises à jour depuis le serveur local
 */

import { SERVER_CONFIG, getBackendUrl } from '../config/server.config';

export interface VersionInfo {
  version: string;
  backend: string;
  frontend: string;
  lastUpdate: string;
}

export interface UpdateCheck {
  hasUpdate: boolean;
  currentVersion: string;
  clientVersion: string;
  downloadUrl?: string;
}

class UpdateService {
  private updateServerUrl: string;
  private currentVersion: string;
  private checkInterval: number = 5 * 60 * 1000; // 5 minutes
  private intervalId?: NodeJS.Timeout;

  constructor() {
    // Serveur de mise à jour sur port 8001
    const backendUrl = SERVER_CONFIG.BACKEND_URL.replace(':8000', ':8001');
    this.updateServerUrl = backendUrl;
    this.currentVersion = this.getLocalVersion();
  }

  /**
   * Obtenir la version locale
   */
  private getLocalVersion(): string {
    try {
      // En mode Electron, lire depuis package.json
      if (window.electronAPI) {
        return window.electronAPI.getVersion?.() || '1.0.0';
      }
      
      // En mode web, utiliser variable d'environnement ou localStorage
      return localStorage.getItem('wema_version') || '1.0.0';
    } catch {
      return '1.0.0';
    }
  }

  /**
   * Sauvegarder la version locale
   */
  private saveLocalVersion(version: string): void {
    try {
      localStorage.setItem('wema_version', version);
      localStorage.setItem('wema_last_update', new Date().toISOString());
    } catch (error) {
      console.warn('Impossible de sauvegarder la version:', error);
    }
  }

  /**
   * Vérifier s'il y a une mise à jour disponible
   */
  public async checkForUpdates(): Promise<UpdateCheck | null> {
    try {
      const response = await fetch(`${this.updateServerUrl}/check-update?version=${this.currentVersion}`, {
        method: 'GET',
        timeout: 10000
      });

      if (!response.ok) {
        throw new Error(`Erreur serveur: ${response.status}`);
      }

      const updateInfo: UpdateCheck = await response.json();
      
      console.log('🔄 Vérification mise à jour:', updateInfo);
      
      return updateInfo;
    } catch (error) {
      console.warn('Impossible de vérifier les mises à jour:', error);
      return null;
    }
  }

  /**
   * Télécharger et appliquer une mise à jour
   */
  public async downloadUpdate(downloadUrl: string): Promise<boolean> {
    try {
      console.log('📥 Téléchargement mise à jour...');
      
      // En mode Electron, utiliser l'API native
      if (window.electronAPI?.downloadUpdate) {
        const success = await window.electronAPI.downloadUpdate(downloadUrl);
        if (success) {
          console.log('✅ Mise à jour téléchargée, redémarrage requis');
          return true;
        }
        return false;
      }
      
      // En mode web, rediriger vers la nouvelle version
      console.log('🌐 Mode web: redirection vers nouvelle version');
      this.showUpdateNotification(downloadUrl);
      return true;
      
    } catch (error) {
      console.error('❌ Erreur téléchargement mise à jour:', error);
      return false;
    }
  }

  /**
   * Afficher notification de mise à jour
   */
  private showUpdateNotification(downloadUrl: string): void {
    // Créer une notification personnalisée
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 10000;
      max-width: 350px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    notification.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <span style="font-size: 24px; margin-right: 10px;">🔄</span>
        <strong>Mise à jour disponible</strong>
      </div>
      <p style="margin: 10px 0; opacity: 0.9;">
        Une nouvelle version de WeMa IA est disponible.
      </p>
      <div style="display: flex; gap: 10px; margin-top: 15px;">
        <button id="update-now" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 5px;
          cursor: pointer;
          font-weight: bold;
        ">Mettre à jour</button>
        <button id="update-later" style="
          background: transparent;
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 5px;
          cursor: pointer;
        ">Plus tard</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Gestionnaires d'événements
    notification.querySelector('#update-now')?.addEventListener('click', () => {
      window.open(downloadUrl, '_blank');
      notification.remove();
    });
    
    notification.querySelector('#update-later')?.addEventListener('click', () => {
      notification.remove();
    });
    
    // Auto-suppression après 30 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 30000);
  }

  /**
   * Démarrer la vérification automatique
   */
  public startAutoCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    // Vérification immédiate
    this.checkForUpdates().then(updateInfo => {
      if (updateInfo?.hasUpdate && updateInfo.downloadUrl) {
        this.showUpdateNotification(updateInfo.downloadUrl);
      }
    });
    
    // Vérifications périodiques
    this.intervalId = setInterval(async () => {
      const updateInfo = await this.checkForUpdates();
      if (updateInfo?.hasUpdate && updateInfo.downloadUrl) {
        this.showUpdateNotification(updateInfo.downloadUrl);
      }
    }, this.checkInterval);
    
    console.log('🔄 Vérification automatique des mises à jour activée');
  }

  /**
   * Arrêter la vérification automatique
   */
  public stopAutoCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
    console.log('🔄 Vérification automatique des mises à jour désactivée');
  }

  /**
   * Obtenir les informations de version du serveur
   */
  public async getServerVersion(): Promise<VersionInfo | null> {
    try {
      const response = await fetch(`${this.updateServerUrl}/version`, {
        method: 'GET',
        timeout: 5000
      });

      if (!response.ok) {
        throw new Error(`Erreur serveur: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Impossible d\'obtenir la version du serveur:', error);
      return null;
    }
  }

  /**
   * Forcer une vérification manuelle
   */
  public async manualCheck(): Promise<void> {
    console.log('🔄 Vérification manuelle des mises à jour...');
    
    const updateInfo = await this.checkForUpdates();
    
    if (updateInfo?.hasUpdate && updateInfo.downloadUrl) {
      this.showUpdateNotification(updateInfo.downloadUrl);
    } else {
      // Afficher message "pas de mise à jour"
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;
      
      notification.innerHTML = `
        <div style="display: flex; align-items: center;">
          <span style="margin-right: 10px;">✅</span>
          <span>Vous avez la dernière version</span>
        </div>
      `;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 3000);
    }
  }
}

// Export singleton
export const updateService = new UpdateService();
