<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clara Flow SDK - CDN Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #495057;
            margin-top: 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .output {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        
        input[type="file"] {
            margin: 10px 0;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .flow-builder {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .node-list {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        
        .node-item {
            background: #e9ecef;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .node-item:hover {
            border-color: #007bff;
        }
        
        .node-item.selected {
            background: #cce5ff;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Clara Flow SDK - Browser Example</h1>
        
        <!-- Basic Usage -->
        <div class="section">
            <h2>1. Basic SDK Usage</h2>
            <p>Test basic SDK functionality and validation.</p>
            <button onclick="testBasicSDK()">Test Basic SDK</button>
            <button onclick="validateSampleFlow()">Validate Sample Flow</button>
            <div id="basic-output" class="output"></div>
        </div>
        
        <!-- Flow Execution -->
        <div class="section">
            <h2>2. Flow Execution</h2>
            <p>Execute a simple text processing flow.</p>
            <button onclick="executeSimpleFlow()">Execute Simple Flow</button>
            <button onclick="executeAdvancedFlow()">Execute Advanced Flow</button>
            <div id="flow-output" class="output"></div>
        </div>
        
        <!-- File Upload Testing -->
        <div class="section">
            <h2>3. File Upload Testing</h2>
            <p>Test browser file upload functionality.</p>
            <input type="file" id="file-input" accept="*/*">
            <br>
            <button onclick="testFileUpload('base64')">Upload as Base64</button>
            <button onclick="testFileUpload('text')">Upload as Text</button>
            <button onclick="testFileUpload('url')">Upload as URL</button>
            <div id="file-output" class="output"></div>
        </div>
        
        <!-- Flow Import/Export -->
        <div class="section">
            <h2>4. Flow Import/Export</h2>
            <p>Import and export flows using browser APIs.</p>
            <input type="file" id="flow-input" accept=".json">
            <br>
            <button onclick="importFlow()">Import Flow</button>
            <button onclick="exportSampleFlow()">Export Sample Flow</button>
            <button onclick="loadFlowFromURL()">Load Flow from URL</button>
            <div id="import-output" class="output"></div>
        </div>
        
        <!-- Interactive Flow Builder -->
        <div class="section">
            <h2>5. Interactive Flow Builder</h2>
            <p>Build and test flows interactively.</p>
            <div class="flow-builder">
                <div>
                    <h3>Available Nodes</h3>
                    <div class="node-list">
                        <div class="node-item" data-type="input">📥 Input Node</div>
                        <div class="node-item" data-type="combine-text">🔗 Combine Text</div>
                        <div class="node-item" data-type="json-parse">🔧 JSON Parser</div>
                        <div class="node-item" data-type="if-else">🔀 If/Else</div>
                        <div class="node-item" data-type="output">📤 Output Node</div>
                    </div>
                </div>
                <div>
                    <h3>Flow Definition</h3>
                    <textarea id="flow-definition" placeholder="Flow JSON will appear here..."></textarea>
                    <button onclick="buildFlow()">Build Flow</button>
                    <button onclick="executeCustomFlow()">Execute Flow</button>
                </div>
            </div>
            <div id="builder-output" class="output"></div>
        </div>
        
        <!-- Browser Utilities -->
        <div class="section">
            <h2>6. Browser Utilities</h2>
            <p>Test browser-specific utilities and features.</p>
            <button onclick="getBrowserInfo()">Get Browser Info</button>
            <button onclick="testBrowserUtils()">Test Browser Utils</button>
            <div id="utils-output" class="output"></div>
        </div>
    </div>

    <!-- Load Clara Flow SDK from local build (in production, use CDN) -->
    <script src="../dist/clara-flow-sdk.umd.js"></script>
    
    <script>
        // Global variables
        let flowRunner;
        let currentFlow = null;
        
        // Initialize SDK
        function initSDK() {
            try {
                flowRunner = new ClaraFlowSDK.ClaraFlowRunner({
                    enableLogging: true,
                    timeout: 30000
                });
                log('basic-output', 'SDK initialized successfully!', 'success');
                return true;
            } catch (error) {
                log('basic-output', `Failed to initialize SDK: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Utility function for logging
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        // Clear output
        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        // Test basic SDK functionality
        function testBasicSDK() {
            clearOutput('basic-output');
            
            if (!initSDK()) return;
            
            // Test browser detection
            const isBrowser = ClaraFlowSDK.BrowserUtils.isBrowser();
            log('basic-output', `Running in browser: ${isBrowser}`, 'info');
            
            // Test SDK info
            log('basic-output', `SDK loaded successfully`, 'success');
            log('basic-output', `Available exports: ${Object.keys(ClaraFlowSDK).join(', ')}`, 'info');
        }
        
        // Validate sample flow
        function validateSampleFlow() {
            clearOutput('basic-output');
            
            if (!flowRunner) {
                if (!initSDK()) return;
            }
            
            const sampleFlow = {
                name: "Sample Validation Flow",
                version: "1.0.0",
                nodes: [
                    {
                        id: "input-1",
                        type: "input",
                        data: { label: "Text Input", inputType: "string" }
                    },
                    {
                        id: "output-1", 
                        type: "output",
                        data: { label: "Result Output" }
                    }
                ],
                connections: [
                    { source: "input-1", target: "output-1", sourceHandle: "output", targetHandle: "input" }
                ]
            };
            
            try {
                const validation = flowRunner.validateFlow(sampleFlow);
                log('basic-output', `Validation result: ${JSON.stringify(validation, null, 2)}`, 'info');
            } catch (error) {
                log('basic-output', `Validation failed: ${error.message}`, 'error');
            }
        }
        
        // Execute simple flow
        function executeSimpleFlow() {
            clearOutput('flow-output');
            
            if (!flowRunner) {
                if (!initSDK()) return;
            }
            
            const simpleFlow = {
                name: "Simple Text Flow",
                version: "1.0.0",
                nodes: [
                    {
                        id: "input-1",
                        type: "input",
                        data: { label: "Text Input", inputType: "string", defaultValue: "Hello" }
                    },
                    {
                        id: "combine-1",
                        type: "combine-text",
                        data: { mode: "space", addSpaces: true }
                    },
                    {
                        id: "output-1",
                        type: "output", 
                        data: { label: "Result" }
                    }
                ],
                connections: [
                    { source: "input-1", target: "combine-1", sourceHandle: "output", targetHandle: "text1" },
                    { source: "combine-1", target: "output-1", sourceHandle: "output", targetHandle: "input" }
                ]
            };
            
            const inputs = {
                "Text Input": "Hello",
                "combine-1": { text2: "World!" }
            };
            
            log('flow-output', 'Executing simple flow...', 'info');
            
            flowRunner.executeFlow(simpleFlow, inputs)
                .then(result => {
                    log('flow-output', `Flow executed successfully!`, 'success');
                    log('flow-output', `Result: ${JSON.stringify(result, null, 2)}`, 'info');
                })
                .catch(error => {
                    log('flow-output', `Flow execution failed: ${error.message}`, 'error');
                });
        }
        
        // Execute advanced flow
        function executeAdvancedFlow() {
            clearOutput('flow-output');
            
            if (!flowRunner) {
                if (!initSDK()) return;
            }
            
            const advancedFlow = {
                name: "Advanced Processing Flow",
                version: "1.0.0", 
                nodes: [
                    {
                        id: "input-1",
                        type: "input",
                        data: { label: "JSON Input", inputType: "json", defaultValue: '{"name": "Clara", "version": "1.4.0"}' }
                    },
                    {
                        id: "json-1",
                        type: "json-parse",
                        data: { field: "name" }
                    },
                    {
                        id: "combine-1", 
                        type: "combine-text",
                        data: { mode: "custom", customSeparator: " v" }
                    },
                    {
                        id: "output-1",
                        type: "output",
                        data: { label: "Formatted Output" }
                    }
                ],
                connections: [
                    { source: "input-1", target: "json-1", sourceHandle: "output", targetHandle: "input" },
                    { source: "json-1", target: "combine-1", sourceHandle: "output", targetHandle: "text1" },
                    { source: "input-1", target: "combine-1", sourceHandle: "output", targetHandle: "text2" },
                    { source: "combine-1", target: "output-1", sourceHandle: "output", targetHandle: "input" }
                ]
            };
            
            log('flow-output', 'Executing advanced flow...', 'info');
            
            flowRunner.executeFlow(advancedFlow, {})
                .then(result => {
                    log('flow-output', `Advanced flow executed successfully!`, 'success');
                    log('flow-output', `Result: ${JSON.stringify(result, null, 2)}`, 'info');
                })
                .catch(error => {
                    log('flow-output', `Advanced flow execution failed: ${error.message}`, 'error');
                });
        }
        
        // Test file upload
        function testFileUpload(format) {
            clearOutput('file-output');
            
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];
            
            if (!file) {
                log('file-output', 'Please select a file first', 'error');
                return;
            }
            
            if (!flowRunner) {
                if (!initSDK()) return;
            }
            
            log('file-output', `Uploading file: ${file.name} (${file.size} bytes) as ${format}`, 'info');
            
            flowRunner.handleFileUpload(file, { outputFormat: format })
                .then(result => {
                    log('file-output', `File uploaded successfully as ${format}!`, 'success');
                    if (format === 'url') {
                        log('file-output', `Object URL: ${result}`, 'info');
                    } else if (format === 'base64') {
                        log('file-output', `Base64 length: ${result.length} characters`, 'info');
                        log('file-output', `Preview: ${result.substring(0, 100)}...`, 'info');
                    } else {
                        log('file-output', `Content preview: ${String(result).substring(0, 200)}...`, 'info');
                    }
                })
                .catch(error => {
                    log('file-output', `File upload failed: ${error.message}`, 'error');
                });
        }
        
        // Import flow
        function importFlow() {
            clearOutput('import-output');
            
            const fileInput = document.getElementById('flow-input');
            
            ClaraFlowSDK.BrowserUtils.loadFlowFromFileInput(fileInput)
                .then(flowData => {
                    currentFlow = flowData;
                    log('import-output', 'Flow imported successfully!', 'success');
                    log('import-output', `Flow name: ${flowData.name || 'Unnamed'}`, 'info');
                    log('import-output', `Nodes: ${flowData.nodes?.length || 0}`, 'info');
                    log('import-output', `Flow data: ${JSON.stringify(flowData, null, 2)}`, 'info');
                })
                .catch(error => {
                    log('import-output', `Import failed: ${error.message}`, 'error');
                });
        }
        
        // Export sample flow
        function exportSampleFlow() {
            const sampleFlow = {
                name: "Exported Sample Flow",
                version: "1.0.0",
                description: "A sample flow exported from the browser",
                nodes: [
                    {
                        id: "input-1",
                        type: "input",
                        data: { label: "Sample Input", inputType: "string" }
                    },
                    {
                        id: "output-1",
                        type: "output", 
                        data: { label: "Sample Output" }
                    }
                ],
                connections: [
                    { source: "input-1", target: "output-1", sourceHandle: "output", targetHandle: "input" }
                ]
            };
            
            ClaraFlowSDK.BrowserUtils.downloadFlow(sampleFlow, 'sample-flow.json');
            log('import-output', 'Sample flow exported successfully!', 'success');
        }
        
        // Load flow from URL (demo)
        function loadFlowFromURL() {
            clearOutput('import-output');
            log('import-output', 'Loading flow from URL is available but requires a valid JSON endpoint', 'info');
            log('import-output', 'Example: flowRunner.loadFlowFromUrl("https://example.com/flow.json")', 'info');
        }
        
        // Build flow interactively
        function buildFlow() {
            const flowDef = document.getElementById('flow-definition');
            
            // Simple flow template
            const template = {
                name: "Custom Flow",
                version: "1.0.0",
                nodes: [
                    {
                        id: "input-1",
                        type: "input",
                        data: { label: "Input", inputType: "string", defaultValue: "Hello World" }
                    },
                    {
                        id: "output-1",
                        type: "output",
                        data: { label: "Output" }
                    }
                ],
                connections: [
                    { source: "input-1", target: "output-1", sourceHandle: "output", targetHandle: "input" }
                ]
            };
            
            flowDef.value = JSON.stringify(template, null, 2);
            log('builder-output', 'Flow template generated!', 'success');
        }
        
        // Execute custom flow
        function executeCustomFlow() {
            clearOutput('builder-output');
            
            if (!flowRunner) {
                if (!initSDK()) return;
            }
            
            const flowDef = document.getElementById('flow-definition');
            
            try {
                const flowData = JSON.parse(flowDef.value);
                
                log('builder-output', 'Executing custom flow...', 'info');
                
                flowRunner.executeFlow(flowData, {})
                    .then(result => {
                        log('builder-output', 'Custom flow executed successfully!', 'success');
                        log('builder-output', `Result: ${JSON.stringify(result, null, 2)}`, 'info');
                    })
                    .catch(error => {
                        log('builder-output', `Custom flow execution failed: ${error.message}`, 'error');
                    });
                    
            } catch (error) {
                log('builder-output', `Invalid JSON: ${error.message}`, 'error');
            }
        }
        
        // Get browser info
        function getBrowserInfo() {
            clearOutput('utils-output');
            
            const browserInfo = ClaraFlowSDK.BrowserUtils.getBrowserInfo();
            log('utils-output', `Browser Info: ${JSON.stringify(browserInfo, null, 2)}`, 'info');
            
            const isBrowser = ClaraFlowSDK.BrowserUtils.isBrowser();
            log('utils-output', `Is Browser: ${isBrowser}`, 'info');
        }
        
        // Test browser utilities
        function testBrowserUtils() {
            clearOutput('utils-output');
            
            log('utils-output', 'Testing browser utilities...', 'info');
            
            // Test utility functions
            const utils = ClaraFlowSDK.BrowserUtils;
            log('utils-output', `Available utilities: ${Object.keys(utils).join(', ')}`, 'info');
            
            // Test flow validation utility
            const validationResult = ClaraFlowSDK.validateFlow({
                name: "Test Flow",
                nodes: [],
                connections: []
            });
            
            log('utils-output', `Validation utility test: ${JSON.stringify(validationResult, null, 2)}`, 'info');
        }
        
        // Initialize on page load
        window.addEventListener('load', function() {
            log('basic-output', 'Page loaded. Clara Flow SDK CDN example ready!', 'success');
            log('basic-output', 'Click "Test Basic SDK" to initialize the SDK.', 'info');
        });
        
        // Node selection for flow builder
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('node-item')) {
                // Remove previous selection
                document.querySelectorAll('.node-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                // Add selection to clicked item
                e.target.classList.add('selected');
                
                const nodeType = e.target.dataset.type;
                log('builder-output', `Selected node type: ${nodeType}`, 'info');
            }
        });
    </script>
</body>
</html> 