/**
 * 🧪 TESTS POUR STREAM OPTIMIZER
 * Tests pour vérifier que le streaming accumule correctement le contenu
 */

import { StreamOptimizer } from './streamOptimizer';

describe('StreamOptimizer', () => {
  let receivedContent: string[] = [];
  let optimizer: StreamOptimizer;

  beforeEach(() => {
    receivedContent = [];
    optimizer = new StreamOptimizer((content) => {
      receivedContent.push(content);
    });
  });

  afterEach(() => {
    optimizer.cleanup();
  });

  test('should accumulate content correctly', () => {
    // Simuler un streaming avec plusieurs chunks
    optimizer.addContent('Hello ');
    optimizer.addContent('world ');
    optimizer.addContent('from ');
    optimizer.addContent('Clara!');
    
    // Forcer le flush final
    optimizer.flushFinal();
    
    // Vérifier que le contenu final est complet
    const finalContent = receivedContent[receivedContent.length - 1];
    expect(finalContent).toBe('Hello world from Clara!');
  });

  test('should handle thinking content correctly', () => {
    // Simuler un streaming avec thinking
    optimizer.addContent('<thinking>');
    optimizer.addContent('Let me think about this...');
    optimizer.addContent('</thinking>');
    optimizer.addContent('\n\nHello! ');
    optimizer.addContent('I can help you with that.');
    
    // Forcer le flush final
    optimizer.flushFinal();
    
    // Vérifier que le contenu final est complet
    const finalContent = receivedContent[receivedContent.length - 1];
    expect(finalContent).toBe('<thinking>Let me think about this...</thinking>\n\nHello! I can help you with that.');
  });

  test('should not lose content during streaming', () => {
    // Simuler un streaming rapide
    const chunks = ['The ', 'quick ', 'brown ', 'fox ', 'jumps ', 'over ', 'the ', 'lazy ', 'dog.'];
    
    chunks.forEach(chunk => {
      optimizer.addContent(chunk);
    });
    
    // Forcer le flush final
    optimizer.flushFinal();
    
    // Vérifier que tout le contenu est présent
    const finalContent = receivedContent[receivedContent.length - 1];
    expect(finalContent).toBe('The quick brown fox jumps over the lazy dog.');
  });

  test('should handle empty chunks gracefully', () => {
    optimizer.addContent('Start ');
    optimizer.addContent(''); // Chunk vide
    optimizer.addContent('middle ');
    optimizer.addContent(''); // Chunk vide
    optimizer.addContent('end');
    
    optimizer.flushFinal();
    
    const finalContent = receivedContent[receivedContent.length - 1];
    expect(finalContent).toBe('Start middle end');
  });
});

// Test manuel pour vérifier visuellement
export const testStreamingVisually = () => {
  console.log('🧪 Testing StreamOptimizer visually...');
  
  let accumulatedContent = '';
  const optimizer = new StreamOptimizer((content) => {
    accumulatedContent = content;
    console.log('📝 Received:', content);
  });

  // Simuler un streaming progressif
  const chunks = [
    '<thinking>',
    'I need to analyze this request carefully. ',
    'The user is asking about streaming optimization. ',
    'Let me provide a comprehensive answer.',
    '</thinking>',
    '\n\n',
    'Hello! ',
    'I understand you\'re having issues with streaming. ',
    'The problem was that content was disappearing instead of accumulating. ',
    'This has now been fixed! 🎉'
  ];

  let index = 0;
  const interval = setInterval(() => {
    if (index < chunks.length) {
      optimizer.addContent(chunks[index]);
      index++;
    } else {
      optimizer.flushFinal();
      clearInterval(interval);
      console.log('✅ Final content:', accumulatedContent);
      optimizer.cleanup();
    }
  }, 200);
};

// Exporter pour utilisation dans la console
if (typeof window !== 'undefined') {
  (window as any).testStreamingVisually = testStreamingVisually;
}
