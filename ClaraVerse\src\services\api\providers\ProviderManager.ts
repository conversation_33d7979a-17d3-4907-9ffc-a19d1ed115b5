/**
 * 🔧 Provider Manager - Gestion centralisée des fournisseurs AI
 * 
 * Responsabilités :
 * - Découverte automatique des providers
 * - Cache intelligent des providers et modèles
 * - Gestion des connexions et santé des providers
 * - Sélection automatique du meilleur provider
 */

import { OllamaClient } from '../../../utils/OllamaClient';
import type {
  ClaraProvider,
  ClaraModel,
  ClaraProviderType
} from '../../../types/clara_assistant_types';
import { logger, LogCategory } from '../../../utils/logger';

interface ProviderCache {
  data: ClaraProvider[];
  timestamp: number;
}

interface ModelCache {
  data: ClaraModel[];
  timestamp: number;
}

export class ProviderManager {
  private providersCache: ProviderCache | null = null;
  private modelsCache: Map<string, ModelCache> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 secondes
  private healthCheckCache: Map<string, { healthy: boolean; timestamp: number }> = new Map();
  private readonly HEALTH_CHECK_DURATION = 60000; // 1 minute

  /**
   * Obtenir tous les providers disponibles avec cache intelligent
   */
  public async getProviders(): Promise<ClaraProvider[]> {
    try {
      // 🚀 Vérifier le cache d'abord
      if (this.providersCache && this.isCacheValid(this.providersCache.timestamp)) {
        logger.debug(LogCategory.PROVIDERS, 'Providers récupérés depuis le cache');
        return this.providersCache.data;
      }

      // 🔍 Découverte automatique via backend proxy
      const providers = await this.discoverProviders();

      // 🚀 Mettre en cache
      this.providersCache = {
        data: providers,
        timestamp: Date.now()
      };

      logger.info(LogCategory.PROVIDERS, `${providers.length} providers découverts et mis en cache`);
      return providers;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des providers:', error);
      return [];
    }
  }

  /**
   * Obtenir les modèles d'un ou tous les providers
   */
  public async getModels(providerId?: string): Promise<ClaraModel[]> {
    const cacheKey = providerId || 'all';

    // 🚀 Vérifier le cache (réactivé après migration CORS)
    if (this.modelsCache.has(cacheKey)) {
      const cached = this.modelsCache.get(cacheKey)!;
      if (this.isCacheValid(cached.timestamp)) {
        console.log(`✅ Modèles récupérés depuis le cache pour: ${cacheKey}`);
        return cached.data;
      }
    }

    const models: ClaraModel[] = [];
    const providers = await this.getProviders();

    // Filtrer les providers selon le paramètre
    const targetProviders = providerId
      ? providers.filter(p => p.id === providerId && p.isEnabled)
      : providers.filter(p => p.isEnabled);

    // Récupérer les modèles de chaque provider
    for (const provider of targetProviders) {
      try {
        const providerModels = await this.getProviderModels(provider);
        models.push(...providerModels);
      } catch (error) {
        console.warn(`⚠️ Échec récupération modèles pour ${provider.name}:`, error);
      }
    }

    // 🚀 Mettre en cache
    this.modelsCache.set(cacheKey, {
      data: models,
      timestamp: Date.now()
    });

    console.log(`✅ ${models.length} modèles mis en cache pour: ${cacheKey}`);
    return models;
  }

  /**
   * Obtenir le provider primaire depuis la configuration
   */
  public async getPrimaryProvider(): Promise<ClaraProvider | null> {
    try {
      const providers = await this.getProviders();
      
      // Chercher le provider marqué comme primaire
      const primary = providers.find(p => p.isPrimary && p.isEnabled);
      if (primary) {
        return primary;
      }

      // Fallback : premier provider activé
      const firstEnabled = providers.find(p => p.isEnabled);
      if (firstEnabled) {
        console.log(`🔄 Utilisation du premier provider activé: ${firstEnabled.name}`);
        return firstEnabled;
      }

      console.warn('⚠️ Aucun provider primaire trouvé');
      return null;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du provider primaire:', error);
      return null;
    }
  }

  /**
   * Récupérer les modèles de LM Studio via API OpenAI standard
   */
  private async getLMStudioModels(provider: ClaraProvider): Promise<any[]> {
    try {
      const response = await fetch(`${provider.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(provider.apiKey && { 'Authorization': `Bearer ${provider.apiKey}` })
        },
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.warn(`⚠️ Error loading LM Studio models:`, error);
      return [];
    }
  }

  /**
   * Récupérer les modèles des providers génériques (OpenAI, OpenRouter, etc.)
   */
  private async getGenericProviderModels(provider: ClaraProvider): Promise<any[]> {
    try {
      const response = await fetch(`${provider.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(provider.apiKey && { 'Authorization': `Bearer ${provider.apiKey}` })
        },
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.warn(`⚠️ Error loading models for ${provider.name}:`, error);
      return [];
    }
  }

  /**
   * Vérifier la santé d'un provider
   */
  public async checkProviderHealth(provider: ClaraProvider): Promise<boolean> {
    const cacheKey = provider.id;
    
    // Vérifier le cache de santé
    if (this.healthCheckCache.has(cacheKey)) {
      const cached = this.healthCheckCache.get(cacheKey)!;
      if (this.isCacheValid(cached.timestamp, this.HEALTH_CHECK_DURATION)) {
        return cached.healthy;
      }
    }

    try {
      console.log(`🔍 Vérification santé provider: ${provider.name}`);

      let isHealthy = false;

      // 🚀 LOGIQUE SPÉCIFIQUE PAR TYPE DE PROVIDER
      if (provider.type === 'lmstudio') {
        // Test LM Studio via API OpenAI standard
        isHealthy = await this.testLMStudioHealth(provider);
      } else if (provider.type === 'ollama') {
        // Test Ollama via son client spécifique
        const client = new OllamaClient(provider.baseUrl || '', {
          apiKey: provider.apiKey || '',
          type: provider.type,
          providerId: provider.id
        });
        await client.listModels();
        isHealthy = true;
      } else {
        // Test providers génériques
        isHealthy = await this.testGenericProviderHealth(provider);
      }

      // Mettre en cache le résultat
      this.healthCheckCache.set(cacheKey, {
        healthy: isHealthy,
        timestamp: Date.now()
      });

      if (isHealthy) {
        console.log(`✅ Provider ${provider.name} en bonne santé`);
      }
      return isHealthy;
    } catch (error) {
      console.warn(`⚠️ Provider ${provider.name} non disponible:`, error);

      // Mettre en cache le résultat négatif
      this.healthCheckCache.set(cacheKey, {
        healthy: false,
        timestamp: Date.now()
      });

      return false;
    }
  }

  /**
   * Tester la santé de LM Studio
   */
  private async testLMStudioHealth(provider: ClaraProvider): Promise<boolean> {
    try {
      const response = await fetch(`${provider.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(provider.apiKey && { 'Authorization': `Bearer ${provider.apiKey}` })
        },
        signal: AbortSignal.timeout(5000)
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Tester la santé des providers génériques
   */
  private async testGenericProviderHealth(provider: ClaraProvider): Promise<boolean> {
    try {
      const response = await fetch(`${provider.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(provider.apiKey && { 'Authorization': `Bearer ${provider.apiKey}` })
        },
        signal: AbortSignal.timeout(5000)
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Trouver le meilleur provider pour un type de tâche
   */
  public async findBestProvider(
    task: 'text' | 'vision' | 'code' | 'tools',
    requirements?: { minModels?: number; preferLocal?: boolean }
  ): Promise<ClaraProvider | null> {
    const providers = await this.getProviders();
    const healthyProviders = [];

    // Vérifier la santé de chaque provider
    for (const provider of providers.filter(p => p.isEnabled)) {
      const isHealthy = await this.checkProviderHealth(provider);
      if (isHealthy) {
        healthyProviders.push(provider);
      }
    }

    if (healthyProviders.length === 0) {
      console.warn('⚠️ Aucun provider en bonne santé trouvé');
      return null;
    }

    // Logique de sélection selon la tâche
    let bestProvider = healthyProviders[0];

    // Préférer les providers locaux si demandé
    if (requirements?.preferLocal) {
      const localProvider = healthyProviders.find(p => 
        p.baseUrl?.includes('localhost') || 
        p.baseUrl?.includes('127.0.0.1')
      );
      if (localProvider) {
        bestProvider = localProvider;
      }
    }

    console.log(`🎯 Meilleur provider pour ${task}: ${bestProvider.name}`);
    return bestProvider;
  }

  /**
   * Nettoyer tous les caches
   */
  public clearCache(): void {
    console.log('🗑️ Nettoyage des caches ProviderManager');
    this.providersCache = null;
    this.modelsCache.clear();
    this.healthCheckCache.clear();
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Découvrir les providers via la base de données locale
   */
  private async discoverProviders(): Promise<ClaraProvider[]> {
    try {
      logger.debug(LogCategory.PROVIDERS, 'Loading providers from local database...');

      // Import dynamique pour éviter les dépendances circulaires
      const { db } = await import('../../../db');

      // Initialiser les providers par défaut si nécessaire
      await db.initializeDefaultProviders();

      // Récupérer tous les providers depuis la base de données
      const dbProviders = await db.getAllProviders();

      if (dbProviders.length > 0) {
        console.log(`✅ ${dbProviders.length} providers loaded from local database`);
        return dbProviders;
      } else {
        console.warn('⚠️ No providers found in local database');
        return [];
      }
    } catch (error) {
      console.warn('⚠️ Error loading providers from database:', error);
      return [];
    }
  }

  /**
   * Récupérer les modèles d'un provider spécifique via appel direct (CORS activé)
   */
  private async getProviderModels(provider: ClaraProvider): Promise<ClaraModel[]> {
    try {
      logger.group(LogCategory.PROVIDERS, `models-${provider.id}`, `Loading models for ${provider.name}`);

      let rawModels: any[] = [];

      // 🚀 LOGIQUE SPÉCIFIQUE PAR TYPE DE PROVIDER
      if (provider.type === 'lmstudio') {
        // LM Studio utilise l'API OpenAI standard
        rawModels = await this.getLMStudioModels(provider);
      } else if (provider.type === 'ollama') {
        // Ollama utilise son client spécifique
        const client = new OllamaClient(provider.baseUrl || '', {
          apiKey: provider.apiKey || '',
          type: provider.type,
          providerId: provider.id
        });
        rawModels = await client.listModels();
      } else {
        // Autres providers (OpenAI, OpenRouter, etc.)
        rawModels = await this.getGenericProviderModels(provider);
      }

      console.log(`🔍 Raw models from ${provider.name}:`, rawModels);

      const models: ClaraModel[] = [];

      for (const model of rawModels) {
        const claraModel: ClaraModel = {
          id: `${provider.id}:${model.id}`,
          name: model.name || model.id,
          provider: provider.id,
          type: this.detectModelType(model.name || model.id),
          size: model.size,
          supportsVision: this.supportsVision(model.name || model.id),
          supportsCode: this.supportsCode(model.name || model.id),
          supportsTools: this.supportsTools(model.name || model.id),
          metadata: {
            digest: model.digest,
            modified_at: model.modified_at
          }
        };

        console.log(`📝 Processed model: ${claraModel.id} (${claraModel.name})`);
        models.push(claraModel);
      }

      console.log(`✅ Loaded ${models.length} models from ${provider.name}`);
      return models;
    } catch (error) {
      console.warn(`⚠️ Error loading models for ${provider.name}:`, error);
      return [];
    }
  }

  /**
   * Vérifier si le cache est encore valide
   */
  private isCacheValid(timestamp: number, duration: number = this.CACHE_DURATION): boolean {
    return (Date.now() - timestamp) < duration;
  }

  /**
   * Détecter le type de modèle basé sur son nom
   */
  private detectModelType(modelName: string): 'text' | 'vision' | 'code' | 'embedding' {
    const name = modelName.toLowerCase();
    
    if (name.includes('vision') || name.includes('llava') || name.includes('gpt-4-vision')) {
      return 'vision';
    }
    
    if (name.includes('code') || name.includes('codellama') || name.includes('starcoder')) {
      return 'code';
    }
    
    if (name.includes('embed') || name.includes('bge')) {
      return 'embedding';
    }
    
    return 'text';
  }

  /**
   * Vérifier si le modèle supporte la vision
   */
  private supportsVision(modelName: string): boolean {
    const name = modelName.toLowerCase();
    return name.includes('vision') || name.includes('llava') || name.includes('gpt-4-vision');
  }

  /**
   * Vérifier si le modèle supporte le code
   */
  private supportsCode(modelName: string): boolean {
    const name = modelName.toLowerCase();
    return name.includes('code') || name.includes('codellama') || name.includes('starcoder') || 
           name.includes('qwen') || name.includes('deepseek');
  }

  /**
   * Vérifier si le modèle supporte les outils
   */
  private supportsTools(modelName: string): boolean {
    const name = modelName.toLowerCase();
    // La plupart des modèles modernes supportent les outils
    return !name.includes('embed') && !name.includes('vision-only');
  }
}

// Export singleton
export const providerManager = new ProviderManager();
