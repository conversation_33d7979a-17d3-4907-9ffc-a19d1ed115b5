/**
 * 🚀 API Services - Point d'entrée principal
 * 
 * Exports centralisés pour tous les services API refactorisés
 */

// Service principal
export { ClaraApiService, claraApiService } from './ClaraApiService';

// Modules spécialisés
export { providerManager } from './providers/ProviderManager';
export { chatManager } from './chat/ChatManager';
export { toolManager } from './tools/ToolManager';
export { contextManager } from './context/ContextManager';
export { attachmentProcessor } from './context/AttachmentProcessor';

// Types
export type { ChatContext } from './chat/ChatManager';
export type { ToolExecutionResult, ToolExecutionOptions } from './tools/ToolManager';
export type { ContextOptimizationResult, ContextLimits } from './context/ContextManager';
export type { ProcessingOptions } from './context/AttachmentProcessor';

// Utilitaires
export const API_VERSION = '2.0.0';
export const ARCHITECTURE_VERSION = 'modular-refactored';

/**
 * Fonction utilitaire pour créer une instance du service
 */
export function createClaraApiService(): ClaraApiService {
  return new ClaraApiService();
}

/**
 * Vérifier la santé de tous les modules
 */
export async function checkModulesHealth(): Promise<{
  providers: boolean;
  chat: boolean;
  tools: boolean;
  context: boolean;
  attachments: boolean;
}> {
  try {
    // Test basique de chaque module
    const providers = await providerManager.getProviders();
    const providersHealthy = Array.isArray(providers);

    return {
      providers: providersHealthy,
      chat: true, // ChatManager est toujours disponible
      tools: true, // ToolManager est toujours disponible
      context: true, // ContextManager est toujours disponible
      attachments: true // AttachmentProcessor est toujours disponible
    };
  } catch (error) {
    console.error('❌ Erreur vérification santé modules:', error);
    return {
      providers: false,
      chat: false,
      tools: false,
      context: false,
      attachments: false
    };
  }
}

/**
 * Nettoyer tous les caches des modules
 */
export function clearAllCaches(): void {
  console.log('🗑️ Nettoyage de tous les caches des modules');
  
  try {
    providerManager.clearCache();
    toolManager.clearCache();
    console.log('✅ Tous les caches nettoyés');
  } catch (error) {
    console.error('❌ Erreur nettoyage caches:', error);
  }
}

/**
 * Obtenir les statistiques de tous les modules
 */
export async function getModulesStats(): Promise<{
  api: any;
  providers: any;
  tools: any;
  context: any;
}> {
  try {
    return {
      api: claraApiService.getServiceStats(),
      providers: {
        // Statistiques du ProviderManager
        cacheSize: 'N/A' // À implémenter si nécessaire
      },
      tools: {
        // Statistiques du ToolManager
        cacheSize: 'N/A' // À implémenter si nécessaire
      },
      context: {
        // Statistiques du ContextManager
        optimizationsCount: 'N/A' // À implémenter si nécessaire
      }
    };
  } catch (error) {
    console.error('❌ Erreur récupération statistiques:', error);
    return {
      api: null,
      providers: null,
      tools: null,
      context: null
    };
  }
}
