#!/usr/bin/env python3
"""
Test du système OCR avec détection de tableaux
"""

import requests
import base64
import json
from PIL import Image, ImageDraw, ImageFont
import io

BASE_URL = "http://127.0.0.1:5000"

def create_test_table_image():
    """Crée une image de test avec un tableau"""
    # Créer une image blanche
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Essayer d'utiliser une police par défaut
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        title_font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # Titre du document
    draw.text((50, 30), "COMPTE DE RÉSULTAT 2024", fill='black', font=title_font)
    
    # Dessiner un tableau simple
    table_x = 50
    table_y = 80
    col_width = 150
    row_height = 30
    
    # Headers du tableau
    headers = ["Poste", "2024", "2023", "Variation"]
    for i, header in enumerate(headers):
        x = table_x + i * col_width
        y = table_y
        # Dessiner la cellule
        draw.rectangle([x, y, x + col_width, y + row_height], outline='black', width=2)
        # Texte centré
        text_x = x + 10
        text_y = y + 8
        draw.text((text_x, text_y), header, fill='black', font=font)
    
    # Données du tableau
    data = [
        ["Chiffre d'affaires", "1,250,000", "1,180,000", "****%"],
        ["Charges externes", "450,000", "420,000", "****%"],
        ["Charges personnel", "380,000", "360,000", "****%"],
        ["Résultat net", "285,000", "265,000", "****%"]
    ]
    
    for row_idx, row_data in enumerate(data):
        for col_idx, cell_data in enumerate(row_data):
            x = table_x + col_idx * col_width
            y = table_y + (row_idx + 1) * row_height
            # Dessiner la cellule
            draw.rectangle([x, y, x + col_width, y + row_height], outline='black', width=1)
            # Texte
            text_x = x + 10
            text_y = y + 8
            draw.text((text_x, text_y), cell_data, fill='black', font=font)
    
    # Ajouter du texte normal en dessous
    draw.text((50, table_y + 200), "Notes:", fill='black', font=font)
    draw.text((50, table_y + 230), "- Exercice clos le 31/12/2024", fill='black', font=font)
    draw.text((50, table_y + 260), "- Montants en euros", fill='black', font=font)
    
    return img

def image_to_base64(image):
    """Convertit une image PIL en base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def test_table_detection():
    """Test de la détection de tableaux"""
    print("🔍 Test de Détection de Tableaux...")
    
    try:
        # Créer une image avec tableau
        test_image = create_test_table_image()
        image_base64 = image_to_base64(test_image)
        
        print(f"   📄 Image avec tableau créée: {test_image.size}")
        
        # Test avec détection de tableaux activée
        ocr_request = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "detect_tables": True
        }
        
        response = requests.post(f"{BASE_URL}/ocr/process", json=ocr_request)
        
        print(f"   📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ OCR réussi: {result.get('success', False)}")
            
            # Analyser les tableaux détectés
            tables = result.get('tables', [])
            print(f"   📋 Tableaux détectés: {len(tables)}")
            
            for i, table in enumerate(tables):
                print(f"      Table {i+1}:")
                print(f"         - Position: ({table['x']}, {table['y']})")
                print(f"         - Taille: {table['width']}x{table['height']}")
                print(f"         - Dimensions: {table['rows']} lignes x {table['cols']} colonnes")
                print(f"         - Confiance: {table['confidence']:.2f}")
                
                # Afficher le contenu du tableau
                print(f"         - Contenu:")
                for row_idx, row in enumerate(table['cells']):
                    row_text = " | ".join(str(cell) for cell in row)
                    print(f"           [{row_idx}] {row_text}")
            
            # Analyser les blocs de texte
            text_blocks = result.get('text_blocks', [])
            table_cells = [b for b in text_blocks if b.get('is_table_cell', False)]
            regular_blocks = [b for b in text_blocks if not b.get('is_table_cell', False)]
            
            print(f"   📦 Blocs de texte total: {len(text_blocks)}")
            print(f"      - Cellules de tableau: {len(table_cells)}")
            print(f"      - Texte normal: {len(regular_blocks)}")
            
            # Afficher le texte formaté
            formatted_text = result.get('formatted_text', '')
            print(f"   📝 Texte formaté avec tableaux:")
            print("   " + "="*50)
            for line in formatted_text.split('\n')[:20]:  # Premières 20 lignes
                print(f"   {line}")
            if len(formatted_text.split('\n')) > 20:
                print("   ...")
            print("   " + "="*50)
            
            # Métadonnées
            metadata = result.get('metadata', {})
            print(f"   📊 Métadonnées:")
            for key, value in metadata.items():
                print(f"      - {key}: {value}")
            
            return len(tables) > 0
        else:
            print(f"   ❌ OCR échoué: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def test_table_vs_no_table():
    """Compare OCR avec et sans détection de tableaux"""
    print("\n🔄 Comparaison Avec/Sans Détection de Tableaux...")
    
    try:
        test_image = create_test_table_image()
        image_base64 = image_to_base64(test_image)
        
        # Test SANS détection de tableaux
        print("   📊 Test SANS détection de tableaux:")
        request_no_tables = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "detect_tables": False
        }
        
        response_no_tables = requests.post(f"{BASE_URL}/ocr/process", json=request_no_tables)
        
        if response_no_tables.status_code == 200:
            result_no_tables = response_no_tables.json()
            tables_no = len(result_no_tables.get('tables', []))
            blocks_no = len(result_no_tables.get('text_blocks', []))
            print(f"      - Tableaux: {tables_no}")
            print(f"      - Blocs de texte: {blocks_no}")
        
        # Test AVEC détection de tableaux
        print("   📋 Test AVEC détection de tableaux:")
        request_with_tables = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "detect_tables": True
        }
        
        response_with_tables = requests.post(f"{BASE_URL}/ocr/process", json=request_with_tables)
        
        if response_with_tables.status_code == 200:
            result_with_tables = response_with_tables.json()
            tables_with = len(result_with_tables.get('tables', []))
            blocks_with = len(result_with_tables.get('text_blocks', []))
            print(f"      - Tableaux: {tables_with}")
            print(f"      - Blocs de texte: {blocks_with}")
            
            return tables_with > tables_no
        
        return False
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    """Test principal de la détection de tableaux"""
    print("🚀 TEST DE DÉTECTION DE TABLEAUX")
    print("=" * 50)
    
    # Test de détection de tableaux
    table_detected = test_table_detection()
    
    # Test comparatif
    improvement = test_table_vs_no_table()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print(f"   📋 Tableaux détectés: {'✅' if table_detected else '❌'}")
    print(f"   🔄 Amélioration vs sans détection: {'✅' if improvement else '❌'}")
    
    if table_detected:
        print("\n🎉 DÉTECTION DE TABLEAUX OPÉRATIONNELLE !")
        print("   Le système peut maintenant:")
        print("   - Détecter automatiquement les tableaux")
        print("   - Extraire la structure tabulaire")
        print("   - Préserver le formatage des tableaux")
        print("   - Identifier les cellules individuelles")
        print("\n📋 PARFAIT POUR LES COMPTES SOCIAUX PAPPERS !")
        print("   - Tableaux financiers structurés")
        print("   - Données comptables organisées")
        print("   - Format adapté pour l'analyse")
    else:
        print("\n⚠️ DÉTECTION DE TABLEAUX À AMÉLIORER")
        print("   Vérifiez les dépendances OpenCV et pdfplumber")
    
    print("\n📋 UTILISATION:")
    print("   1. Uploadez vos comptes sociaux PDF")
    print("   2. Activez 'detect_tables: true' dans la requête")
    print("   3. Les tableaux seront automatiquement structurés")
    print("   4. Format parfait pour l'anonymisation GDPR")

if __name__ == "__main__":
    main()
