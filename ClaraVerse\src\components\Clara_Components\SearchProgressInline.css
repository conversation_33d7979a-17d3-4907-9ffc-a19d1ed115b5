/* Animations pour SearchProgressInline */

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideUp {
  0%, 100% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
}

/* Classes utilitaires pour les animations */
.search-source-animate {
  animation: fadeInScale 0.6s ease-out forwards;
}

.search-progress-bar {
  animation: slideUp 2s ease-in-out infinite;
}
