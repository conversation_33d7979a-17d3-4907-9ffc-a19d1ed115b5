/**
 * Document Chat Indicator Component
 * Shows selected documents in the chat interface with visual indicators
 */

import React from 'react';
import {
  FileText,
  Shield,
  X,
  Database,
  ChevronDown,
  ChevronUp,
  Info
} from 'lucide-react';

import { DocumentInfo } from '../types/gdpr-types';

interface DocumentChatIndicatorProps {
  selectedDocuments: DocumentInfo[];
  onRemoveDocument: (documentId: number) => void;
  onClearAll: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const DocumentChatIndicator: React.FC<DocumentChatIndicatorProps> = ({
  selectedDocuments,
  onRemoveDocument,
  onClearAll,
  isCollapsed = false,
  onToggleCollapse
}) => {
  if (selectedDocuments.length === 0) {
    return null;
  }

  const anonymizedCount = selectedDocuments.filter(doc => doc.isAnonymized).length;
  const collections = [...new Set(selectedDocuments.map(doc => doc.collectionName))];

  return (
    <div className="bg-gradient-to-r from-wema-50 to-wema-100 dark:from-wema-900/20 dark:to-wema-800/20 border border-wema-200 dark:border-wema-700 rounded-xl p-3 mb-4 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Database className="w-4 h-4 text-wema-600 dark:text-wema-400" />
          <span className="text-sm font-medium text-wema-700 dark:text-wema-300">
            RAG Mode • {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''}
          </span>
          {anonymizedCount > 0 && (
            <span className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
              <Shield className="w-3 h-3" />
              {anonymizedCount} GDPR
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {onToggleCollapse && (
            <button
              onClick={onToggleCollapse}
              className="p-1 hover:bg-wema-100 dark:hover:bg-wema-800/30 rounded transition-colors"
              title={isCollapsed ? "Expand" : "Collapse"}
            >
              {isCollapsed ? (
                <ChevronDown className="w-4 h-4 text-wema-600 dark:text-wema-400" />
              ) : (
                <ChevronUp className="w-4 h-4 text-wema-600 dark:text-wema-400" />
              )}
            </button>
          )}
          
          <button
            onClick={onClearAll}
            className="p-1 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors"
            title="Remove all documents"
          >
            <X className="w-4 h-4 text-red-600 dark:text-red-400" />
          </button>
        </div>
      </div>

      {/* Collections Summary */}
      {!isCollapsed && collections.length > 1 && (
        <div className="mb-2">
          <div className="flex items-center gap-1 text-xs text-wema-600 dark:text-wema-400">
            <Info className="w-3 h-3" />
            <span>Collections: {collections.join(', ')}</span>
          </div>
        </div>
      )}

      {/* Document List */}
      {!isCollapsed && (
        <div className="space-y-1">
          {selectedDocuments.map((document) => (
            <div
              key={document.id}
              className="flex items-center justify-between p-2 bg-white/80 dark:bg-gray-800/80 rounded-lg border border-wema-200 dark:border-wema-600 backdrop-blur-sm"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {document.filename}
                  </div>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    <span>{document.fileType.toUpperCase()}</span>
                    <span>{document.collectionName}</span>
                    <span>{document.chunkCount} chunks</span>
                    {document.isAnonymized && (
                      <span className="flex items-center gap-1 text-green-600 dark:text-green-400">
                        <Shield className="w-3 h-3" />
                        GDPR
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <button
                onClick={() => onRemoveDocument(document.id)}
                className="p-1 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors flex-shrink-0"
                title="Remove document"
              >
                <X className="w-3 h-3 text-red-600 dark:text-red-400" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Collapsed View */}
      {isCollapsed && (
        <div className="flex items-center gap-2 text-sm text-wema-600 dark:text-wema-400">
          <div className="flex -space-x-1">
            {selectedDocuments.slice(0, 3).map((doc, index) => (
              <div
                key={doc.id}
                className="w-6 h-6 bg-wema-100 dark:bg-wema-800 border-2 border-white dark:border-gray-800 rounded-full flex items-center justify-center"
                title={doc.filename}
              >
                <FileText className="w-3 h-3" />
              </div>
            ))}
            {selectedDocuments.length > 3 && (
              <div className="w-6 h-6 bg-wema-200 dark:bg-wema-700 border-2 border-white dark:border-gray-800 rounded-full flex items-center justify-center text-xs font-medium">
                +{selectedDocuments.length - 3}
              </div>
            )}
          </div>
          <span>in {collections.length} collection{collections.length !== 1 ? 's' : ''}</span>
        </div>
      )}

      {/* Context Information */}
      {!isCollapsed && (
        <div className="mt-2 pt-2 border-t border-wema-200 dark:border-wema-700">
          <div className="flex items-center gap-4 text-xs text-wema-600 dark:text-wema-400">
            <span>📊 Total chunks: {selectedDocuments.reduce((sum, doc) => sum + doc.chunkCount, 0)}</span>
            <span>📁 Collections: {collections.length}</span>
            {anonymizedCount > 0 && (
              <span>🛡️ GDPR protected: {anonymizedCount}</span>
            )}
          </div>
          
          <div className="mt-1 text-xs text-wema-600 dark:text-wema-400 flex items-center gap-1">
            <span className="w-1.5 h-1.5 bg-wema-500 rounded-full animate-pulse"></span>
            Clara will search through these documents to answer your questions
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentChatIndicator;
