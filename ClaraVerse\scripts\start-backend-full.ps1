# 🚀 Script de Démarrage Complet Backend WeMa IA
# 
# <PERSON><PERSON><PERSON><PERSON> le backend en mode complet avec tous les services RAG

Write-Host "🚀 Démarrage Backend WeMa IA - Mode Complet" -ForegroundColor Cyan
Write-Host "🧠 Tous les services RAG activés (démarrage plus lent)" -ForegroundColor Yellow
Write-Host ""

# Définir les variables d'environnement
$env:FAST_MODE = "false"
$env:INFERENCE_SERVER_IP = "*********"
$env:INFERENCE_SERVER_PORT = "11434"

# Aller dans le répertoire backend
Set-Location "py_backend"

Write-Host "📂 Répertoire: $(Get-Location)" -ForegroundColor Gray
Write-Host "🌐 Serveur d'inférence: $env:INFERENCE_SERVER_IP`:$env:INFERENCE_SERVER_PORT" -ForegroundColor Gray
Write-Host ""

# Démarrer le backend
Write-Host "🚀 Lancement du backend..." -ForegroundColor Green
Write-Host "⏳ Chargement des services RAG (PyTorch, BGE-M3, Qdrant)..." -ForegroundColor Yellow
python -m uvicorn main:app --host 0.0.0.0 --port 5001 --reload
