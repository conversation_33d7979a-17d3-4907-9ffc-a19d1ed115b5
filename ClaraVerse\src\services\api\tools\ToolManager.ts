/**
 * 🛠️ Tool Manager - Gestion centralisée des outils
 * 
 * Responsabilités :
 * - Récupération des outils disponibles (DB + MCP)
 * - Exécution des appels d'outils
 * - Gestion des erreurs et retry
 * - Intégration MCP
 * - Validation des paramètres
 */

import { db } from '../../../db';
import type { Tool } from '../../../db';
import { claraMCPService } from '../../claraMCPService';
import { defaultTools, executeTool } from '../../../utils/claraTools';
import type {
  ClaraAIConfig,
  ClaraMCPToolResult
} from '../../../types/clara_assistant_types';

export interface ToolExecutionResult {
  toolName: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  toolMessage?: any; // Pour les outils MCP avec contenu riche
}

export interface ToolExecutionOptions {
  maxRetries?: number;
  timeout?: number;
  enableMCP?: boolean;
}

export class ToolManager {
  private executionCache: Map<string, ToolExecutionResult> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Obtenir tous les outils disponibles (DB + MCP)
   */
  public async getAvailableTools(config: ClaraAIConfig): Promise<Tool[]> {
    const tools: Tool[] = [];

    try {
      // 1. Outils de la base de données
      const dbTools = await db.getEnabledTools();
      tools.push(...dbTools);
      console.log(`🔧 ${dbTools.length} outils DB récupérés`);

      // 2. Outils MCP si activés
      if (config.features.enableMCP && config.mcp?.enableTools) {
        const mcpTools = await this.getMCPTools(config);
        tools.push(...mcpTools);
        console.log(`🔧 ${mcpTools.length} outils MCP récupérés`);
      }

      console.log(`✅ Total: ${tools.length} outils disponibles`);
      return tools;
    } catch (error) {
      console.error('❌ Erreur récupération outils:', error);
      return [];
    }
  }

  /**
   * Exécuter une liste d'appels d'outils
   */
  public async executeToolCalls(
    toolCalls: any[],
    options: ToolExecutionOptions = {}
  ): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];
    const { maxRetries = 1, timeout = 30000 } = options;

    console.log(`🔧 Exécution de ${toolCalls.length} appels d'outils`);

    for (const toolCall of toolCalls) {
      const startTime = Date.now();
      
      try {
        // Valider l'appel d'outil
        if (!this.validateToolCall(toolCall)) {
          results.push({
            toolName: toolCall.function?.name || 'unknown',
            success: false,
            error: 'Appel d\'outil invalide',
            executionTime: Date.now() - startTime
          });
          continue;
        }

        const toolName = toolCall.function.name;
        const args = this.parseToolArguments(toolCall.function.arguments);

        // Vérifier le cache
        const cacheKey = this.generateCacheKey(toolName, args);
        const cached = this.getCachedResult(cacheKey);
        if (cached) {
          console.log(`⚡ Résultat en cache pour ${toolName}`);
          results.push(cached);
          continue;
        }

        // Exécuter l'outil avec retry
        let result: ToolExecutionResult | null = null;
        let lastError: Error | null = null;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
          try {
            result = await this.executeToolWithTimeout(toolName, args, timeout);
            break; // Succès, sortir de la boucle de retry
          } catch (error) {
            lastError = error as Error;
            console.warn(`⚠️ Tentative ${attempt + 1}/${maxRetries + 1} échouée pour ${toolName}:`, error);
            
            if (attempt < maxRetries) {
              await this.delay(1000 * (attempt + 1)); // Délai exponentiel
            }
          }
        }

        if (result) {
          // Mettre en cache le résultat
          this.cacheResult(cacheKey, result);
          results.push(result);
        } else {
          // Échec après tous les retries
          results.push({
            toolName,
            success: false,
            error: lastError?.message || 'Échec après tous les retries',
            executionTime: Date.now() - startTime
          });
        }

      } catch (error) {
        console.error(`❌ Erreur exécution outil ${toolCall.function?.name}:`, error);
        results.push({
          toolName: toolCall.function?.name || 'unknown',
          success: false,
          error: error instanceof Error ? error.message : 'Erreur inconnue',
          executionTime: Date.now() - startTime
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ ${successCount}/${results.length} outils exécutés avec succès`);

    return results;
  }

  /**
   * Obtenir les statistiques d'utilisation des outils
   */
  public getToolUsageStats(): {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    mostUsedTools: string[];
  } {
    // Ici on pourrait implémenter des statistiques détaillées
    return {
      totalExecutions: 0,
      successRate: 0,
      averageExecutionTime: 0,
      mostUsedTools: []
    };
  }

  /**
   * Nettoyer le cache d'exécution
   */
  public clearCache(): void {
    console.log('🗑️ Nettoyage cache ToolManager');
    this.executionCache.clear();
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Récupérer les outils MCP
   */
  private async getMCPTools(config: ClaraAIConfig): Promise<Tool[]> {
    try {
      if (!claraMCPService.isReady()) {
        console.log('⚠️ Service MCP non prêt');
        return [];
      }

      const enabledServers = config.mcp?.enabledServers || [];
      if (enabledServers.length === 0) {
        console.log('⚠️ Aucun serveur MCP activé');
        return [];
      }

      // Obtenir les outils des serveurs activés
      const mcpTools = claraMCPService.getToolsFromEnabledServers(enabledServers);
      
      // Convertir au format Tool
      const tools: Tool[] = mcpTools.map(tool => ({
        id: tool.name,
        name: tool.name,
        description: tool.description || '',
        parameters: Object.entries(tool.inputSchema?.properties || {}).map(([name, prop]: [string, any]) => ({
          name,
          type: prop.type || 'string',
          description: prop.description || '',
          required: tool.inputSchema?.required?.includes(name) || false
        })),
        implementation: 'mcp',
        isEnabled: true
      }));

      return tools;
    } catch (error) {
      console.error('❌ Erreur récupération outils MCP:', error);
      return [];
    }
  }

  /**
   * Valider un appel d'outil
   */
  private validateToolCall(toolCall: any): boolean {
    if (!toolCall.function?.name) {
      console.warn('⚠️ Appel d\'outil sans nom');
      return false;
    }

    if (typeof toolCall.function.arguments !== 'string') {
      console.warn('⚠️ Arguments d\'outil invalides');
      return false;
    }

    return true;
  }

  /**
   * Parser les arguments d'un outil
   */
  private parseToolArguments(argsString: string): any {
    try {
      return JSON.parse(argsString || '{}');
    } catch (error) {
      console.warn('⚠️ Erreur parsing arguments outil:', error);
      return {};
    }
  }

  /**
   * Exécuter un outil avec timeout
   */
  private async executeToolWithTimeout(
    toolName: string,
    args: any,
    timeout: number
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    return new Promise(async (resolve, reject) => {
      // Timer de timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout après ${timeout}ms`));
      }, timeout);

      try {
        let result: any;
        let toolMessage: any;

        // Vérifier si c'est un outil MCP
        if (claraMCPService.isReady() && claraMCPService.hasToolInAnyServer(toolName)) {
          console.log(`🔧 Exécution outil MCP: ${toolName}`);
          
          const mcpResult: ClaraMCPToolResult = await claraMCPService.callTool(toolName, args);
          
          if (mcpResult.success) {
            result = mcpResult.content;
            // Traiter le contenu MCP pour créer un message riche
            toolMessage = this.processMCPResult(mcpResult, toolName);
          } else {
            throw new Error(mcpResult.error || 'Échec outil MCP');
          }
        } else {
          // Outil standard
          console.log(`🔧 Exécution outil standard: ${toolName}`);
          result = await executeTool(toolName, args);
        }

        clearTimeout(timeoutId);
        
        resolve({
          toolName,
          success: true,
          result,
          toolMessage,
          executionTime: Date.now() - startTime
        });
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Traiter le résultat MCP pour créer un message riche
   */
  private processMCPResult(mcpResult: ClaraMCPToolResult, toolName: string): any {
    if (!mcpResult.content || mcpResult.content.length === 0) {
      return null;
    }

    let textContent = '';
    const images: string[] = [];

    for (const contentItem of mcpResult.content) {
      switch (contentItem.type) {
        case 'text':
          if (contentItem.text) {
            textContent += (textContent ? '\n\n' : '') + contentItem.text;
          }
          break;
          
        case 'image':
          if (contentItem.data && contentItem.mimeType) {
            const imageData = contentItem.data.startsWith('data:') 
              ? contentItem.data 
              : `data:${contentItem.mimeType};base64,${contentItem.data}`;
            images.push(imageData);
            textContent += (textContent ? '\n\n' : '') + `📷 Image générée (${contentItem.mimeType})`;
          }
          break;
          
        default:
          // Autres types de contenu
          textContent += (textContent ? '\n\n' : '') + `📊 ${contentItem.type}: ${JSON.stringify(contentItem)}`;
      }
    }

    return {
      role: 'tool',
      content: textContent || `Outil ${toolName} exécuté avec succès`,
      name: toolName,
      images: images.length > 0 ? images : undefined
    };
  }

  /**
   * Générer une clé de cache pour un outil
   */
  private generateCacheKey(toolName: string, args: any): string {
    return `${toolName}:${JSON.stringify(args)}`;
  }

  /**
   * Récupérer un résultat en cache
   */
  private getCachedResult(cacheKey: string): ToolExecutionResult | null {
    const cached = this.executionCache.get(cacheKey);
    if (cached && (Date.now() - cached.executionTime) < this.CACHE_TTL) {
      return cached;
    }
    return null;
  }

  /**
   * Mettre en cache un résultat
   */
  private cacheResult(cacheKey: string, result: ToolExecutionResult): void {
    this.executionCache.set(cacheKey, result);
  }

  /**
   * Délai asynchrone
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton
export const toolManager = new ToolManager();
