/**
 * 📎 Attachment Processor - Traitement des fichiers joints
 * 
 * Responsabilités :
 * - Traitement des différents types de fichiers
 * - Extraction de contenu (texte, images, code)
 * - Validation et sécurité
 * - Optimisation pour l'IA
 * - Gestion des erreurs
 */

import type {
  ClaraFileAttachment,
  ClaraFileProcessingResult
} from '../../../types/clara_assistant_types';

export interface ProcessingOptions {
  maxFileSize?: number;
  allowedTypes?: string[];
  extractText?: boolean;
  optimizeForAI?: boolean;
}

export class AttachmentProcessor {
  private readonly DEFAULT_MAX_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly ALLOWED_TYPES = [
    'text/plain',
    'text/markdown',
    'text/csv',
    'application/json',
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/javascript',
    'text/html',
    'text/css',
    'text/xml'
  ];

  /**
   * Traiter une liste de fichiers joints
   */
  public async processAttachments(
    attachments: ClaraFileAttachment[],
    options: ProcessingOptions = {}
  ): Promise<ClaraFileAttachment[]> {
    const processed: ClaraFileAttachment[] = [];
    const maxSize = options.maxFileSize || this.DEFAULT_MAX_SIZE;
    const allowedTypes = options.allowedTypes || this.ALLOWED_TYPES;

    console.log(`📎 Traitement de ${attachments.length} fichiers joints`);

    for (const attachment of attachments) {
      try {
        // Validation de base
        if (!this.validateAttachment(attachment, maxSize, allowedTypes)) {
          attachment.processed = false;
          attachment.processingResult = {
            success: false,
            error: 'Fichier non valide ou non supporté'
          };
          processed.push(attachment);
          continue;
        }

        // Traitement selon le type
        const result = await this.processAttachmentByType(attachment, options);
        attachment.processed = true;
        attachment.processingResult = result;

        console.log(`✅ Fichier traité: ${attachment.name} (${attachment.type})`);
        processed.push(attachment);

      } catch (error) {
        console.error(`❌ Erreur traitement ${attachment.name}:`, error);
        attachment.processed = false;
        attachment.processingResult = {
          success: false,
          error: error instanceof Error ? error.message : 'Erreur de traitement'
        };
        processed.push(attachment);
      }
    }

    const successCount = processed.filter(a => a.processed).length;
    console.log(`✅ ${successCount}/${processed.length} fichiers traités avec succès`);

    return processed;
  }

  /**
   * Extraire le contenu textuel d'un fichier
   */
  public async extractTextContent(attachment: ClaraFileAttachment): Promise<string> {
    if (!attachment.content) {
      return '';
    }

    try {
      switch (attachment.type) {
        case 'text':
        case 'markdown':
        case 'code':
          return this.extractPlainText(attachment.content);

        case 'json':
          return this.extractJSONContent(attachment.content);

        case 'csv':
          return this.extractCSVContent(attachment.content);

        case 'pdf':
          return this.extractPDFContent(attachment.content);

        case 'image':
          return this.extractImageContent(attachment);

        default:
          return attachment.content.substring(0, 1000); // Fallback
      }
    } catch (error) {
      console.warn(`⚠️ Erreur extraction contenu ${attachment.name}:`, error);
      return '';
    }
  }

  /**
   * Optimiser le contenu pour l'IA
   */
  public optimizeForAI(content: string, type: string): string {
    let optimized = content;

    // Nettoyer les caractères de contrôle
    optimized = optimized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

    // Normaliser les espaces
    optimized = optimized.replace(/\s+/g, ' ');

    // Limiter la longueur selon le type
    const maxLength = this.getMaxLengthForType(type);
    if (optimized.length > maxLength) {
      optimized = optimized.substring(0, maxLength - 100) + '\n\n[...contenu tronqué pour optimisation IA...]';
    }

    // Ajouter des métadonnées contextuelles
    const metadata = this.generateContentMetadata(optimized, type);
    if (metadata) {
      optimized = `${metadata}\n\n${optimized}`;
    }

    return optimized;
  }

  /**
   * Détecter le type de fichier à partir du nom et du contenu
   */
  public detectFileType(filename: string, content?: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();

    // Détection par extension
    const typeMap: Record<string, string> = {
      'txt': 'text',
      'md': 'markdown',
      'json': 'json',
      'csv': 'csv',
      'pdf': 'pdf',
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'webp': 'image',
      'js': 'code',
      'ts': 'code',
      'py': 'code',
      'java': 'code',
      'cpp': 'code',
      'c': 'code',
      'html': 'code',
      'css': 'code',
      'xml': 'code'
    };

    if (extension && typeMap[extension]) {
      return typeMap[extension];
    }

    // Détection par contenu si disponible
    if (content) {
      if (content.startsWith('{') || content.startsWith('[')) {
        return 'json';
      }
      if (content.includes('<!DOCTYPE') || content.includes('<html')) {
        return 'code';
      }
      if (content.includes('function ') || content.includes('const ') || content.includes('import ')) {
        return 'code';
      }
    }

    return 'text'; // Fallback
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Valider un fichier joint
   */
  private validateAttachment(
    attachment: ClaraFileAttachment,
    maxSize: number,
    allowedTypes: string[]
  ): boolean {
    // Vérifier la taille
    if (attachment.size && attachment.size > maxSize) {
      console.warn(`⚠️ Fichier trop volumineux: ${attachment.name} (${attachment.size} bytes)`);
      return false;
    }

    // Vérifier le type MIME si disponible
    if (attachment.mimeType && !allowedTypes.includes(attachment.mimeType)) {
      console.warn(`⚠️ Type MIME non autorisé: ${attachment.mimeType}`);
      return false;
    }

    // Vérifier la présence de contenu
    if (!attachment.content) {
      console.warn(`⚠️ Pas de contenu pour: ${attachment.name}`);
      return false;
    }

    return true;
  }

  /**
   * Traiter un fichier selon son type
   */
  private async processAttachmentByType(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    const type = attachment.type || this.detectFileType(attachment.name, attachment.content);

    switch (type) {
      case 'text':
      case 'markdown':
        return this.processTextFile(attachment, options);

      case 'code':
        return this.processCodeFile(attachment, options);

      case 'json':
        return this.processJSONFile(attachment, options);

      case 'csv':
        return this.processCSVFile(attachment, options);

      case 'image':
        return this.processImageFile(attachment, options);

      case 'pdf':
        return this.processPDFFile(attachment, options);

      default:
        return this.processGenericFile(attachment, options);
    }
  }

  /**
   * Traiter un fichier texte
   */
  private async processTextFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    const content = attachment.content || '';
    const optimizedContent = options.optimizeForAI 
      ? this.optimizeForAI(content, 'text')
      : content;

    return {
      success: true,
      extractedText: optimizedContent,
      metadata: {
        type: 'text',
        wordCount: content.split(/\s+/).length,
        charCount: content.length,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Traiter un fichier de code
   */
  private async processCodeFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    const content = attachment.content || '';
    const language = this.detectCodeLanguage(attachment.name);

    return {
      success: true,
      extractedText: content,
      codeAnalysis: {
        language,
        structure: this.analyzeCodeStructure(content, language),
        metrics: this.calculateCodeMetrics(content)
      },
      metadata: {
        type: 'code',
        language,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Traiter un fichier JSON
   */
  private async processJSONFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    try {
      const parsed = JSON.parse(attachment.content || '{}');
      const summary = this.generateJSONSummary(parsed);

      return {
        success: true,
        extractedText: options.optimizeForAI ? summary : attachment.content || '',
        structuredData: parsed,
        metadata: {
          type: 'json',
          summary,
          processedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'JSON invalide'
      };
    }
  }

  /**
   * Traiter un fichier CSV
   */
  private async processCSVFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    const content = attachment.content || '';
    const lines = content.split('\n').filter(line => line.trim());
    const headers = lines[0]?.split(',') || [];
    const rowCount = lines.length - 1;

    const summary = `Fichier CSV avec ${headers.length} colonnes et ${rowCount} lignes.\nColonnes: ${headers.join(', ')}`;

    return {
      success: true,
      extractedText: options.optimizeForAI ? summary : content,
      structuredData: {
        headers,
        rowCount,
        preview: lines.slice(0, 6) // En-tête + 5 premières lignes
      },
      metadata: {
        type: 'csv',
        headers,
        rowCount,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Traiter un fichier image
   */
  private async processImageFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    // Pour les images, on retourne les métadonnées de base
    // L'analyse d'image nécessiterait des modèles de vision
    return {
      success: true,
      extractedText: `Image: ${attachment.name}`,
      metadata: {
        type: 'image',
        mimeType: attachment.mimeType,
        size: attachment.size,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Traiter un fichier PDF
   */
  private async processPDFFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    // Simulation du traitement PDF
    // En réalité, il faudrait utiliser une bibliothèque comme pdf-parse
    return {
      success: true,
      extractedText: `Contenu PDF: ${attachment.name}`,
      metadata: {
        type: 'pdf',
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Traiter un fichier générique
   */
  private async processGenericFile(
    attachment: ClaraFileAttachment,
    options: ProcessingOptions
  ): Promise<ClaraFileProcessingResult> {
    const content = attachment.content || '';
    const preview = content.substring(0, 500);

    return {
      success: true,
      extractedText: preview,
      metadata: {
        type: 'generic',
        preview,
        processedAt: new Date().toISOString()
      }
    };
  }

  // Méthodes utilitaires simplifiées
  private extractPlainText(content: string): string {
    return content;
  }

  private extractJSONContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return this.generateJSONSummary(parsed);
    } catch {
      return content;
    }
  }

  private extractCSVContent(content: string): string {
    const lines = content.split('\n').slice(0, 10); // Premières lignes
    return lines.join('\n');
  }

  private extractPDFContent(content: string): string {
    return `Contenu PDF (${content.length} caractères)`;
  }

  private extractImageContent(attachment: ClaraFileAttachment): string {
    return `Image: ${attachment.name} (${attachment.mimeType})`;
  }

  private getMaxLengthForType(type: string): number {
    const limits: Record<string, number> = {
      'text': 10000,
      'code': 15000,
      'json': 8000,
      'csv': 5000,
      'image': 1000
    };
    return limits[type] || 5000;
  }

  private generateContentMetadata(content: string, type: string): string {
    return `[Fichier ${type} - ${content.length} caractères]`;
  }

  private detectCodeLanguage(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const langMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'html': 'html',
      'css': 'css'
    };
    return langMap[ext || ''] || 'unknown';
  }

  private analyzeCodeStructure(content: string, language: string): any {
    // Analyse simplifiée
    return {
      functions: [],
      classes: [],
      imports: []
    };
  }

  private calculateCodeMetrics(content: string): any {
    return {
      lines: content.split('\n').length,
      complexity: 0
    };
  }

  private generateJSONSummary(data: any): string {
    if (Array.isArray(data)) {
      return `Tableau JSON avec ${data.length} éléments`;
    } else if (typeof data === 'object') {
      const keys = Object.keys(data);
      return `Objet JSON avec ${keys.length} propriétés: ${keys.slice(0, 5).join(', ')}`;
    }
    return 'Données JSON';
  }
}

// Export singleton
export const attachmentProcessor = new AttachmentProcessor();
