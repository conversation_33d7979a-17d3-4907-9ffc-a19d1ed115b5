/**
 * 🧪 Test du vrai IntelligentCompressor avec serveur central
 */

import { IntelligentCompressor } from './src/services/api/context/IntelligentCompressor';
import type { ClaraMessage } from './src/types/clara_assistant_types';

// Messages de test pour déclencher la compression
const createTestMessages = (): ClaraMessage[] => {
  const baseMessages: ClaraMessage[] = [
    {
      id: '1',
      role: 'user',
      content: 'Bonjour, je voudrais optimiser le Compressor de WeMa IA pour utiliser le serveur central Ollama au lieu de LM Studio.',
      timestamp: new Date(Date.now() - 3600000),
      metadata: {}
    },
    {
      id: '2',
      role: 'assistant',
      content: 'Excellente idée ! Pour migrer le Compressor vers le serveur central Ollama, nous devons modifier plusieurs éléments :\n\n1. **Configuration** : Changer l\'URL de `localhost:1234` vers `localhost:5001/proxy/ollama`\n2. **Modèle** : Utiliser `qwen3-14b-optimized` au lieu de `qwen3-4b`\n3. **Fallback** : Ajouter `qwen3-32b-optimized` comme modèle de secours\n4. **Timeouts** : Adapter les délais pour le serveur central (45s principal, 15s fallback)\n5. **Contexte** : Profiter du contexte étendu de 32K tokens\n\nVoulez-vous que je vous guide dans ces modifications ?',
      timestamp: new Date(Date.now() - 3500000),
      metadata: {}
    },
    {
      id: '3',
      role: 'user',
      content: 'Oui parfait ! Commençons par la configuration. Le serveur central est sur *********:11434 mais on passe par notre proxy backend sur localhost:5001/proxy/ollama. Comment on configure ça exactement ?',
      timestamp: new Date(Date.now() - 3400000),
      metadata: {}
    },
    {
      id: '4',
      role: 'assistant',
      content: 'Parfait ! Voici comment configurer le Compressor pour le serveur central :\n\n```typescript\n// Dans constants.ts\nexport const COMPRESSION_CONFIG = {\n  MODEL: \'qwen3-14b-optimized\',\n  FALLBACK_MODEL: \'qwen3-32b-optimized\',\n  CENTRAL_SERVER: {\n    BASE_URL: \'http://localhost:5001/proxy/ollama\',\n    ENDPOINT: \'/chat\',\n    TIMEOUT: 30000\n  }\n};\n```\n\nEt dans le constructeur du IntelligentCompressor :\n\n```typescript\nconstructor(baseUrl = COMPRESSION_CONFIG.CENTRAL_SERVER.BASE_URL) {\n  this.client = new AssistantAPIClient(baseUrl, { \n    providerId: \'ollama-central\' \n  });\n}\n```\n\nCela permet d\'utiliser votre infrastructure existante !',
      timestamp: new Date(Date.now() - 3300000),
      metadata: {}
    }
  ];

  // Ajouter plus de messages pour atteindre le seuil de compression
  for (let i = 5; i <= 60; i++) {
    baseMessages.push({
      id: i.toString(),
      role: i % 2 === 0 ? 'assistant' : 'user',
      content: `Message ${i}: Discussion technique approfondie sur l'optimisation du Compressor. Ce message contient des détails sur l'architecture serveur central, la configuration des modèles Qwen3 14B et 32B, les timeouts adaptés, la gestion du cache par session, les priorités de préservation intelligentes, et l'intégration avec le proxy backend. L'objectif est de créer suffisamment de contenu pour déclencher la compression automatique et tester le système en conditions réelles. Le Compressor doit analyser ce contenu, détecter les éléments importants comme la configuration technique, les décisions d'architecture, et les solutions de problèmes, puis compresser intelligemment tout en préservant la continuité conversationnelle et les informations critiques pour le développement de WeMa IA.`,
      timestamp: new Date(Date.now() - (3200000 - i * 30000)),
      metadata: {}
    });
  }

  return baseMessages;
};

async function testRealCompressor() {
  console.log('🧪 === TEST DU VRAI INTELLIGENT COMPRESSOR ===\n');

  try {
    // 1. Initialiser le Compressor
    console.log('🚀 1. Initialisation du IntelligentCompressor...');
    const compressor = new IntelligentCompressor();
    
    // Mettre à jour le contexte du modèle
    compressor.updateModelContext('qwen3-14b-optimized', 32000);
    console.log('   ✅ Compressor initialisé avec Qwen3 14B (32K tokens)\n');

    // 2. Créer les messages de test
    console.log('📝 2. Génération des messages de test...');
    const testMessages = createTestMessages();
    const totalChars = testMessages.reduce((sum, msg) => sum + msg.content.length, 0);
    
    console.log(`   📊 Messages générés: ${testMessages.length}`);
    console.log(`   📏 Taille totale: ${totalChars.toLocaleString()} caractères`);
    console.log(`   🎯 Seuil compression: 25,000 caractères`);
    console.log(`   📈 Ratio: ${(totalChars / 25000 * 100).toFixed(1)}%\n`);

    // 3. Analyser le besoin de compression
    console.log('🧠 3. Analyse du besoin de compression...');
    const analysis = compressor.analyzeCompressionNeed(testMessages, 32000);
    
    console.log(`   🎯 Compression nécessaire: ${analysis.shouldCompress ? '✅ OUI' : '❌ NON'}`);
    console.log(`   📊 Taille actuelle: ${analysis.currentSize.toLocaleString()} chars`);
    console.log(`   🎯 Taille cible: ${analysis.targetSize.toLocaleString()} chars`);
    console.log(`   ⚡ Urgence: ${analysis.compressionUrgency.toUpperCase()}`);
    console.log(`   📋 Raison: ${analysis.reason}`);
    console.log(`   🎯 Priorités: ${analysis.preservationPriority.join(', ')}\n`);

    if (analysis.shouldCompress) {
      // 4. Test de compression réelle
      console.log('🔄 4. Test de compression réelle...');
      console.log('   ⚠️  ATTENTION: Ce test va faire un appel réel au serveur central !');
      console.log('   🤖 Modèle: qwen3-14b-optimized via proxy backend');
      console.log('   🌐 Serveur: localhost:5001/proxy/ollama → *********:11434\n');

      const compressionConfig = {
        triggerThreshold: 25000,
        targetContextWindow: 32000,
        compressionModel: 'qwen3-14b-optimized',
        preserveElements: analysis.preservationPriority,
        backgroundProcessing: false
      };

      console.log('   🚀 Lancement de la compression...');
      const startTime = Date.now();

      try {
        const result = await compressor.compressConversation(
          testMessages,
          compressionConfig,
          'test-session'
        );

        const duration = Date.now() - startTime;
        console.log(`   ✅ Compression réussie en ${duration}ms !`);
        console.log(`   📊 Ratio de compression: ${result.metadata.compressionRatio.toFixed(2)}x`);
        console.log(`   📝 Messages originaux: ${result.metadata.originalMessages}`);
        console.log(`   📏 Caractères originaux: ${result.metadata.originalCharacters.toLocaleString()}`);
        console.log(`   📉 Caractères compressés: ${result.metadata.compressedCharacters.toLocaleString()}`);
        console.log(`   🎯 Stratégie: ${result.metadata.preservationStrategy}`);
        
        // Afficher un aperçu du résumé
        if (result.conversationSummary) {
          console.log(`\n   📋 Aperçu du résumé (100 premiers chars):`);
          console.log(`   "${result.conversationSummary.substring(0, 100)}..."`);
        }

        console.log('\n🎉 === TEST DE COMPRESSION RÉUSSI ===');
        console.log('Le Compressor fonctionne parfaitement avec le serveur central !');

      } catch (error) {
        console.error(`   ❌ Erreur de compression: ${error.message}`);
        console.log('\n🔧 Vérifications à faire:');
        console.log('   1. Le backend est-il démarré sur localhost:5001 ?');
        console.log('   2. Le serveur Ollama est-il accessible sur *********:11434 ?');
        console.log('   3. Le modèle qwen3-14b-optimized est-il disponible ?');
      }

    } else {
      console.log('ℹ️  La conversation n\'est pas assez longue pour déclencher la compression.');
      console.log('   Augmentez le nombre de messages ou leur taille pour tester.');
    }

  } catch (error) {
    console.error(`❌ Erreur lors du test: ${error.message}`);
    console.error(error.stack);
  }
}

// Exécuter le test
testRealCompressor().catch(console.error);
