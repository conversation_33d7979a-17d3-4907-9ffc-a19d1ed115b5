/**
 * SERVICE RÉSUMÉ AUTOMATIQUE CONVERSATIONS
 * Génération intelligente de résumés pour conversations longues
 */

import { ClaraMessage } from '../types/clara-types';
import { hierarchicalMemory } from './hierarchicalMemoryService';

export interface ConversationSummary {
  id: string;
  startTime: number;
  endTime: number;
  messageCount: number;
  mainTopics: string[];
  keyDecisions: string[];
  documentsUsed: string[];
  userGoals: string[];
  outcomes: string[];
  importance: number;
  fullSummary: string;
  shortSummary: string;
}

export class ConversationSummaryService {
  private readonly SUMMARY_TRIGGER_LENGTH = 20; // Déclencher résumé après 20 messages
  private readonly MAX_SUMMARY_LENGTH = 500;
  private readonly MAX_SHORT_SUMMARY_LENGTH = 150;

  /**
   * Analyser si une conversation nécessite un résumé
   */
  public shouldSummarize(messages: ClaraMessage[]): boolean {
    return messages.length >= this.SUMMARY_TRIGGER_LENGTH;
  }

  /**
   * Générer un résumé automatique de conversation
   */
  public async generateSummary(messages: ClaraMessage[]): Promise<ConversationSummary> {
    const startTime = messages[0]?.timestamp || Date.now();
    const endTime = messages[messages.length - 1]?.timestamp || Date.now();

    // Analyser le contenu
    const analysis = this.analyzeConversation(messages);

    // Générer le résumé textuel
    const fullSummary = this.generateFullSummary(messages, analysis);
    const shortSummary = this.generateShortSummary(fullSummary);

    return {
      id: `summary_${startTime}_${endTime}`,
      startTime,
      endTime,
      messageCount: messages.length,
      mainTopics: analysis.topics,
      keyDecisions: analysis.decisions,
      documentsUsed: analysis.documents,
      userGoals: analysis.goals,
      outcomes: analysis.outcomes,
      importance: analysis.importance,
      fullSummary,
      shortSummary
    };
  }

  /**
   * Analyser le contenu d'une conversation
   */
  private analyzeConversation(messages: ClaraMessage[]): {
    topics: string[];
    decisions: string[];
    documents: string[];
    goals: string[];
    outcomes: string[];
    importance: number;
  } {
    const topics = new Set<string>();
    const decisions: string[] = [];
    const documents = new Set<string>();
    const goals: string[] = [];
    const outcomes: string[] = [];

    let totalImportance = 0;
    let importanceCount = 0;

    for (const message of messages) {
      // Extraire les sujets
      const messageTopics = this.extractTopics(message.content);
      messageTopics.forEach(topic => topics.add(topic));

      // Extraire les décisions
      const messageDecisions = this.extractDecisions(message.content);
      decisions.push(...messageDecisions);

      // Extraire les documents mentionnés
      const messageDocs = this.extractDocumentReferences(message.content);
      messageDocs.forEach(doc => documents.add(doc));

      // Extraire les objectifs utilisateur
      if (message.role === 'user') {
        const messageGoals = this.extractGoals(message.content);
        goals.push(...messageGoals);
      }

      // Extraire les résultats/solutions
      if (message.role === 'assistant') {
        const messageOutcomes = this.extractOutcomes(message.content);
        outcomes.push(...messageOutcomes);
      }

      // Calculer l'importance
      const messageImportance = this.calculateMessageImportance(message);
      totalImportance += messageImportance;
      importanceCount++;
    }

    return {
      topics: Array.from(topics),
      decisions,
      documents: Array.from(documents),
      goals,
      outcomes,
      importance: importanceCount > 0 ? totalImportance / importanceCount : 5
    };
  }

  /**
   * Générer un résumé complet
   */
  private generateFullSummary(messages: ClaraMessage[], analysis: any): string {
    let summary = '';

    // Introduction
    summary += `Conversation de ${messages.length} messages `;
    summary += `du ${new Date(messages[0]?.timestamp || Date.now()).toLocaleDateString()} `;
    summary += `au ${new Date(messages[messages.length - 1]?.timestamp || Date.now()).toLocaleDateString()}.\n\n`;

    // Sujets principaux
    if (analysis.topics.length > 0) {
      summary += `**Sujets abordés :** ${analysis.topics.join(', ')}\n\n`;
    }

    // Documents utilisés
    if (analysis.documents.length > 0) {
      summary += `**Documents consultés :** ${analysis.documents.join(', ')}\n\n`;
    }

    // Objectifs utilisateur
    if (analysis.goals.length > 0) {
      summary += `**Objectifs utilisateur :**\n`;
      analysis.goals.slice(0, 3).forEach((goal: string, index: number) => {
        summary += `${index + 1}. ${goal}\n`;
      });
      summary += '\n';
    }

    // Décisions prises
    if (analysis.decisions.length > 0) {
      summary += `**Décisions/Actions :**\n`;
      analysis.decisions.slice(0, 3).forEach((decision: string, index: number) => {
        summary += `${index + 1}. ${decision}\n`;
      });
      summary += '\n';
    }

    // Résultats obtenus
    if (analysis.outcomes.length > 0) {
      summary += `**Résultats/Solutions :**\n`;
      analysis.outcomes.slice(0, 3).forEach((outcome: string, index: number) => {
        summary += `${index + 1}. ${outcome}\n`;
      });
    }

    // Limiter la longueur
    if (summary.length > this.MAX_SUMMARY_LENGTH) {
      summary = summary.substring(0, this.MAX_SUMMARY_LENGTH - 3) + '...';
    }

    return summary;
  }

  /**
   * Générer un résumé court
   */
  private generateShortSummary(fullSummary: string): string {
    // Extraire les points clés du résumé complet
    const lines = fullSummary.split('\n').filter(line => line.trim());
    
    let shortSummary = '';
    
    // Prendre la première ligne (introduction)
    if (lines.length > 0) {
      shortSummary += lines[0] + ' ';
    }

    // Ajouter les sujets principaux
    const topicsLine = lines.find(line => line.includes('Sujets abordés'));
    if (topicsLine) {
      shortSummary += topicsLine.replace('**Sujets abordés :**', 'Sujets:') + ' ';
    }

    // Ajouter le nombre de décisions
    const decisionsSection = lines.filter(line => line.match(/^\d+\./));
    if (decisionsSection.length > 0) {
      shortSummary += `${decisionsSection.length} décisions/actions prises.`;
    }

    // Limiter la longueur
    if (shortSummary.length > this.MAX_SHORT_SUMMARY_LENGTH) {
      shortSummary = shortSummary.substring(0, this.MAX_SHORT_SUMMARY_LENGTH - 3) + '...';
    }

    return shortSummary;
  }

  /**
   * Extraire les sujets d'un message
   */
  private extractTopics(content: string): string[] {
    const topics: string[] = [];
    const text = content.toLowerCase();

    // Catégories de sujets
    const topicCategories = {
      'développement': ['code', 'programmation', 'développement', 'api', 'database', 'sql'],
      'business': ['projet', 'client', 'budget', 'planning', 'meeting', 'présentation'],
      'documents': ['pdf', 'document', 'fichier', 'rapport', 'analyse', 'cv'],
      'technique': ['serveur', 'backend', 'frontend', 'deployment', 'configuration'],
      'support': ['problème', 'erreur', 'bug', 'aide', 'assistance', 'dépannage']
    };

    for (const [category, keywords] of Object.entries(topicCategories)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        topics.push(category);
      }
    }

    return topics;
  }

  /**
   * Extraire les décisions d'un message
   */
  private extractDecisions(content: string): string[] {
    const decisions: string[] = [];
    
    const decisionPatterns = [
      /(?:décidé|choisi|opté) (?:de |pour )(.+?)(?:\.|$)/gi,
      /(?:il faut|nous devons|je vais) (.+?)(?:\.|$)/gi,
      /(?:solution|approche|méthode) (?:est|sera) (.+?)(?:\.|$)/gi
    ];

    for (const pattern of decisionPatterns) {
      const matches = [...content.matchAll(pattern)];
      for (const match of matches) {
        if (match[1] && match[1].length < 100) {
          decisions.push(match[1].trim());
        }
      }
    }

    return decisions;
  }

  /**
   * Extraire les références aux documents
   */
  private extractDocumentReferences(content: string): string[] {
    const documents: string[] = [];
    
    // Patterns pour détecter les noms de fichiers
    const filePatterns = [
      /([a-zA-Z0-9_-]+\.(?:pdf|doc|docx|txt|csv|xlsx))/gi,
      /(?:document|fichier|rapport) (?:nommé|appelé) ([a-zA-Z0-9_-]+)/gi
    ];

    for (const pattern of filePatterns) {
      const matches = [...content.matchAll(pattern)];
      for (const match of matches) {
        if (match[1]) {
          documents.push(match[1]);
        }
      }
    }

    return documents;
  }

  /**
   * Extraire les objectifs utilisateur
   */
  private extractGoals(content: string): string[] {
    const goals: string[] = [];
    
    const goalPatterns = [
      /(?:je veux|j'aimerais|je souhaite|peux-tu) (.+?)(?:\?|\.|$)/gi,
      /(?:comment|pourquoi|quand|où) (.+?)(?:\?|$)/gi,
      /(?:aide-moi à|peux-tu m'aider à) (.+?)(?:\?|\.|$)/gi
    ];

    for (const pattern of goalPatterns) {
      const matches = [...content.matchAll(pattern)];
      for (const match of matches) {
        if (match[1] && match[1].length < 100) {
          goals.push(match[1].trim());
        }
      }
    }

    return goals;
  }

  /**
   * Extraire les résultats/solutions
   */
  private extractOutcomes(content: string): string[] {
    const outcomes: string[] = [];
    
    const outcomePatterns = [
      /(?:la solution est|voici comment|vous pouvez) (.+?)(?:\.|$)/gi,
      /(?:résultat|conclusion|réponse) (?:est|:) (.+?)(?:\.|$)/gi,
      /(?:j'ai trouvé|j'ai identifié|j'ai analysé) (.+?)(?:\.|$)/gi
    ];

    for (const pattern of outcomePatterns) {
      const matches = [...content.matchAll(pattern)];
      for (const match of matches) {
        if (match[1] && match[1].length < 100) {
          outcomes.push(match[1].trim());
        }
      }
    }

    return outcomes;
  }

  /**
   * Calculer l'importance d'un message
   */
  private calculateMessageImportance(message: ClaraMessage): number {
    let importance = 5; // Base

    const content = message.content.toLowerCase();

    // Mots-clés d'importance
    const importantKeywords = ['important', 'urgent', 'critique', 'problème', 'erreur'];
    const positiveKeywords = ['solution', 'résolu', 'succès', 'terminé'];
    const questionKeywords = ['comment', 'pourquoi', 'quand', 'où', 'que'];

    for (const keyword of importantKeywords) {
      if (content.includes(keyword)) importance += 2;
    }

    for (const keyword of positiveKeywords) {
      if (content.includes(keyword)) importance += 1;
    }

    for (const keyword of questionKeywords) {
      if (content.includes(keyword)) importance += 1;
    }

    // Longueur du message
    if (message.content.length > 200) importance += 1;
    if (message.content.length > 500) importance += 1;

    return Math.min(importance, 10);
  }

  /**
   * Obtenir un résumé de session en cours
   */
  public getSessionSummary(messages: ClaraMessage[]): string {
    if (messages.length === 0) return 'Aucun message dans cette session.';

    const recentMessages = messages.slice(-10);
    const analysis = this.analyzeConversation(recentMessages);

    let summary = `Session en cours (${messages.length} messages). `;

    if (analysis.topics.length > 0) {
      summary += `Sujets: ${analysis.topics.slice(0, 3).join(', ')}. `;
    }

    if (analysis.documents.length > 0) {
      summary += `Documents: ${analysis.documents.slice(0, 2).join(', ')}. `;
    }

    return summary;
  }
}

// Instance globale
export const conversationSummary = new ConversationSummaryService();
