/**
 * 🎛️ Dashboard Admin Multi-Pôles
 * Interface de supervision et gestion des instances
 */

import React, { useState, useEffect } from 'react';
import { Monitor, Users, Settings, RefreshCw, AlertCircle, CheckCircle, XCircle } from 'lucide-react';

interface PoleStatus {
  name: string;
  port: number;
  updatePort: number;
  users: string[];
  status: 'online' | 'offline' | 'updating';
  lastCheck: string;
  connectedUsers: string[];
  features: string[];
  version: string;
}

interface AdminDashboardProps {
  adminServerUrl?: string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ 
  adminServerUrl = 'http://192.168.1.100:9000' 
}) => {
  const [poles, setPoles] = useState<Record<string, PoleStatus>>({});
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Charger les données des pôles
  const loadPolesData = async () => {
    try {
      const response = await fetch(`${adminServerUrl}/poles`);
      if (response.ok) {
        const data = await response.json();
        setPoles(data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Erreur chargement données pôles:', error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les utilisateurs connectés
  const loadUsersData = async () => {
    try {
      const response = await fetch(`${adminServerUrl}/users`);
      if (response.ok) {
        const usersData = await response.json();
        
        setPoles(prev => {
          const updated = { ...prev };
          Object.keys(updated).forEach(poleId => {
            if (usersData[poleId]) {
              updated[poleId].connectedUsers = usersData[poleId].connected || [];
            }
          });
          return updated;
        });
      }
    } catch (error) {
      console.error('Erreur chargement utilisateurs:', error);
    }
  };

  // Actualisation automatique
  useEffect(() => {
    loadPolesData();
    loadUsersData();

    const interval = setInterval(() => {
      loadPolesData();
      loadUsersData();
    }, 5000); // Toutes les 5 secondes

    return () => clearInterval(interval);
  }, [adminServerUrl]);

  // Redémarrer un pôle
  const restartPole = async (poleId: string) => {
    try {
      const response = await fetch(`${adminServerUrl}/restart/${poleId}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log(`Redémarrage ${poleId} demandé`);
        // Actualiser après 2 secondes
        setTimeout(loadPolesData, 2000);
      }
    } catch (error) {
      console.error(`Erreur redémarrage ${poleId}:`, error);
    }
  };

  // Mettre à jour un pôle
  const updatePole = async (poleId: string) => {
    try {
      const response = await fetch(`${adminServerUrl}/update/${poleId}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log(`Mise à jour ${poleId} démarrée`);
        // Marquer comme en cours de mise à jour
        setPoles(prev => ({
          ...prev,
          [poleId]: { ...prev[poleId], status: 'updating' }
        }));
        
        // Actualiser après 5 secondes
        setTimeout(loadPolesData, 5000);
      }
    } catch (error) {
      console.error(`Erreur mise à jour ${poleId}:`, error);
    }
  };

  // Obtenir l'icône de statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'updating':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'offline':
      default:
        return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  // Obtenir la couleur de la carte selon le statut
  const getCardColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'border-green-200 bg-green-50';
      case 'updating':
        return 'border-blue-200 bg-blue-50';
      case 'offline':
      default:
        return 'border-red-200 bg-red-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Chargement du dashboard...</span>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Monitor className="w-8 h-8 mr-3 text-blue-600" />
              Dashboard Admin WeMa IA
            </h1>
            <p className="text-gray-600 mt-2">
              Supervision et gestion des instances multi-pôles
            </p>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-500">
              Dernière actualisation: {lastUpdate.toLocaleTimeString()}
            </div>
            <button
              onClick={() => {
                loadPolesData();
                loadUsersData();
              }}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Actualiser
            </button>
          </div>
        </div>
      </div>

      {/* Statistiques globales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Monitor className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Total Pôles</p>
              <p className="text-2xl font-bold text-gray-900">{Object.keys(poles).length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">En ligne</p>
              <p className="text-2xl font-bold text-gray-900">
                {Object.values(poles).filter(p => p.status === 'online').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Users className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Utilisateurs</p>
              <p className="text-2xl font-bold text-gray-900">
                {Object.values(poles).reduce((total, pole) => total + (pole.connectedUsers?.length || 0), 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Hors ligne</p>
              <p className="text-2xl font-bold text-gray-900">
                {Object.values(poles).filter(p => p.status === 'offline').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des pôles */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {Object.entries(poles).map(([poleId, pole]) => (
          <div
            key={poleId}
            className={`bg-white rounded-lg shadow border-2 p-6 ${getCardColor(pole.status)}`}
          >
            {/* En-tête de la carte */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {getStatusIcon(pole.status)}
                <h3 className="ml-2 text-lg font-semibold text-gray-900">
                  {pole.name}
                </h3>
              </div>
              <span className="text-sm text-gray-500 capitalize">
                {pole.status}
              </span>
            </div>

            {/* Informations techniques */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Port:</span>
                <span className="font-medium">{pole.port}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Mise à jour:</span>
                <span className="font-medium">{pole.updatePort}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Version:</span>
                <span className="font-medium">{pole.version || '1.0.0'}</span>
              </div>
            </div>

            {/* Utilisateurs */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Utilisateurs:</span>
                <span className="text-sm font-medium">
                  {pole.connectedUsers?.length || 0}/{pole.users.length}
                </span>
              </div>
              
              <div className="space-y-1">
                {pole.users.map(userIp => (
                  <div key={userIp} className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">{userIp}</span>
                    <span className={`px-2 py-1 rounded-full ${
                      pole.connectedUsers?.includes(userIp) 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {pole.connectedUsers?.includes(userIp) ? 'Connecté' : 'Hors ligne'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={() => restartPole(poleId)}
                disabled={pole.status === 'updating'}
                className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Redémarrer
              </button>
              
              <button
                onClick={() => updatePole(poleId)}
                disabled={pole.status === 'updating'}
                className="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <Settings className="w-4 h-4 mr-1" />
                Mettre à jour
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pied de page */}
      <div className="mt-8 text-center text-sm text-gray-500">
        Dashboard Admin WeMa IA - Gestion Multi-Pôles
      </div>
    </div>
  );
};

export default AdminDashboard;
