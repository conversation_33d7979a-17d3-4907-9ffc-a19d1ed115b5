#!/usr/bin/env node
/**
 * 🏢 Gestionnaire Multi-Pôles WeMa IA
 * Gère plusieurs instances par service avec mises à jour ciblées
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

// Configuration des pôles
const POLES_CONFIG = {
  'gestion-patrimoine': {
    name: 'Gestion Patrimoine',
    port: 8000,
    updatePort: 8001,
    users: ['192.168.1.101', '192.168.1.102'], // Postes autorisés
    features: ['rag', 'ocr', 'documents', 'compression'],
    branding: {
      title: 'WeMa IA - Gestion Patrimoine',
      primaryColor: '#2563eb', // Bleu
      logo: 'logo-patrimoine.png'
    }
  },
  'mandats-a': {
    name: 'Mandats A',
    port: 8010,
    updatePort: 8011,
    users: ['192.168.1.103', '192.168.1.104'],
    features: ['rag', 'documents', 'basic-ocr'],
    branding: {
      title: 'WeMa IA - Mandats A',
      primaryColor: '#059669', // Vert
      logo: 'logo-mandats.png'
    }
  },
  'rh': {
    name: 'Ressources Humaines',
    port: 8020,
    updatePort: 8021,
    users: ['*************'],
    features: ['rag', 'documents', 'confidential'],
    branding: {
      title: 'WeMa IA - RH',
      primaryColor: '#dc2626', // Rouge
      logo: 'logo-rh.png'
    }
  }
};

const ADMIN_PORT = 9000; // Dashboard admin

class MultiPoleManager {
  constructor() {
    this.instances = new Map();
    this.adminDashboard = null;
  }

  /**
   * Créer une instance pour un pôle
   */
  async createPoleInstance(poleId) {
    const config = POLES_CONFIG[poleId];
    if (!config) {
      throw new Error(`Pôle inconnu: ${poleId}`);
    }

    console.log(`🏢 Création instance ${config.name}...`);

    const instanceDir = `instances/${poleId}`;
    
    // Créer le dossier d'instance
    if (fs.existsSync(instanceDir)) {
      fs.rmSync(instanceDir, { recursive: true });
    }
    fs.mkdirSync(instanceDir, { recursive: true });

    // 1. Copier le code de base
    console.log('📁 Copie du code de base...');
    this.copyBaseCode(instanceDir);

    // 2. Personnaliser la configuration
    console.log('⚙️ Personnalisation...');
    this.customizeInstance(instanceDir, poleId, config);

    // 3. Build personnalisé
    console.log('🔨 Build personnalisé...');
    await this.buildInstance(instanceDir, poleId, config);

    console.log(`✅ Instance ${config.name} créée sur port ${config.port}`);
  }

  /**
   * Copier le code de base
   */
  copyBaseCode(instanceDir) {
    const filesToCopy = [
      'py_backend',
      'src',
      'public',
      'package.json',
      'vite.config.ts',
      'tsconfig.json'
    ];

    filesToCopy.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(instanceDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  /**
   * Personnaliser une instance
   */
  customizeInstance(instanceDir, poleId, config) {
    // 1. Configuration backend
    const backendConfig = {
      POLE_ID: poleId,
      POLE_NAME: config.name,
      CLARA_HOST: '0.0.0.0',
      CLARA_PORT: config.port,
      UPDATE_PORT: config.updatePort,
      ALLOWED_IPS: config.users.join(','),
      FEATURES: config.features.join(',')
    };

    fs.writeFileSync(
      path.join(instanceDir, 'py_backend', '.env'),
      Object.entries(backendConfig).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 2. Configuration frontend
    const frontendConfig = {
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://*************:${config.port}`,
      REACT_APP_UPDATE_URL: `http://*************:${config.updatePort}`,
      REACT_APP_PRIMARY_COLOR: config.branding.primaryColor,
      REACT_APP_TITLE: config.branding.title
    };

    fs.writeFileSync(
      path.join(instanceDir, '.env'),
      Object.entries(frontendConfig).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 3. Personnaliser le titre et les couleurs
    this.customizeBranding(instanceDir, config);

    // 4. Filtrer les fonctionnalités
    this.filterFeatures(instanceDir, config.features);
  }

  /**
   * Personnaliser le branding
   */
  customizeBranding(instanceDir, config) {
    // Modifier index.html
    const indexPath = path.join(instanceDir, 'public', 'index.html');
    if (fs.existsSync(indexPath)) {
      let indexContent = fs.readFileSync(indexPath, 'utf8');
      indexContent = indexContent.replace(/<title>.*<\/title>/, `<title>${config.branding.title}</title>`);
      fs.writeFileSync(indexPath, indexContent);
    }

    // Créer un fichier de configuration de thème
    const themeConfig = {
      primaryColor: config.branding.primaryColor,
      title: config.branding.title,
      logo: config.branding.logo
    };

    fs.writeFileSync(
      path.join(instanceDir, 'src', 'config', 'pole-theme.json'),
      JSON.stringify(themeConfig, null, 2)
    );
  }

  /**
   * Filtrer les fonctionnalités selon le pôle
   */
  filterFeatures(instanceDir, features) {
    const featureFlags = {
      rag: features.includes('rag'),
      ocr: features.includes('ocr') || features.includes('basic-ocr'),
      advancedOcr: features.includes('ocr'), // OCR avancé seulement si 'ocr'
      documents: features.includes('documents'),
      compression: features.includes('compression'),
      confidential: features.includes('confidential')
    };

    fs.writeFileSync(
      path.join(instanceDir, 'src', 'config', 'features.json'),
      JSON.stringify(featureFlags, null, 2)
    );
  }

  /**
   * Build une instance
   */
  async buildInstance(instanceDir, poleId, config) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(instanceDir);
      
      // Installer les dépendances si nécessaire
      if (!fs.existsSync('node_modules')) {
        console.log('📦 Installation dépendances...');
        execSync('npm install', { stdio: 'inherit' });
      }

      // Build frontend
      console.log('⚛️ Build frontend...');
      execSync('npm run build', { stdio: 'inherit' });

      // Créer script de démarrage
      const startScript = `@echo off
echo 🏢 Démarrage ${config.name}...

REM Backend
cd py_backend
call venv\\Scripts\\activate
start "Backend-${poleId}" python start_server.py --production --host 0.0.0.0 --port ${config.port}

REM Serveur de mise à jour
cd ..
start "Updates-${poleId}" node scripts/update-system.js server --port ${config.updatePort}

echo ✅ ${config.name} démarré sur port ${config.port}
pause`;

      fs.writeFileSync('start-pole.bat', startScript);

    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * Démarrer toutes les instances
   */
  async startAllInstances() {
    console.log('🚀 Démarrage de toutes les instances...');

    for (const [poleId, config] of Object.entries(POLES_CONFIG)) {
      await this.startInstance(poleId);
    }

    // Démarrer le dashboard admin
    await this.startAdminDashboard();
  }

  /**
   * Démarrer une instance spécifique
   */
  async startInstance(poleId) {
    const config = POLES_CONFIG[poleId];
    const instanceDir = `instances/${poleId}`;

    if (!fs.existsSync(instanceDir)) {
      console.log(`⚠️ Instance ${poleId} n'existe pas, création...`);
      await this.createPoleInstance(poleId);
    }

    console.log(`🚀 Démarrage ${config.name}...`);

    // Démarrer backend
    const backendProcess = spawn('python', ['start_server.py', '--production', '--host', '0.0.0.0', '--port', config.port.toString()], {
      cwd: path.join(instanceDir, 'py_backend'),
      stdio: 'inherit'
    });

    // Démarrer serveur de mise à jour
    const updateProcess = spawn('node', ['../scripts/update-system.js', 'server', '--port', config.updatePort.toString()], {
      cwd: instanceDir,
      stdio: 'inherit'
    });

    this.instances.set(poleId, {
      config,
      backendProcess,
      updateProcess
    });

    console.log(`✅ ${config.name} démarré sur port ${config.port}`);
  }

  /**
   * Démarrer le dashboard admin
   */
  async startAdminDashboard() {
    console.log('🎛️ Démarrage dashboard admin...');
    
    // Créer le dashboard admin
    await this.createAdminDashboard();
    
    // Le démarrer
    const adminProcess = spawn('node', ['admin-dashboard.js'], {
      stdio: 'inherit'
    });

    this.adminDashboard = adminProcess;
    console.log(`✅ Dashboard admin démarré sur port ${ADMIN_PORT}`);
  }

  /**
   * Créer le dashboard admin
   */
  async createAdminDashboard() {
    const dashboardCode = this.generateAdminDashboard();
    fs.writeFileSync('admin-dashboard.js', dashboardCode);
  }

  /**
   * Générer le code du dashboard admin
   */
  generateAdminDashboard() {
    return `
const http = require('http');
const fs = require('fs');

const POLES = ${JSON.stringify(POLES_CONFIG, null, 2)};

const server = http.createServer(async (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Content-Type', 'application/json');

  if (req.url === '/poles') {
    // Liste des pôles et leur statut
    const polesStatus = {};
    
    for (const [poleId, config] of Object.entries(POLES)) {
      try {
        const response = await fetch(\`http://localhost:\${config.port}/health\`);
        polesStatus[poleId] = {
          ...config,
          status: response.ok ? 'online' : 'offline',
          lastCheck: new Date().toISOString()
        };
      } catch {
        polesStatus[poleId] = {
          ...config,
          status: 'offline',
          lastCheck: new Date().toISOString()
        };
      }
    }
    
    res.end(JSON.stringify(polesStatus));
    
  } else if (req.url === '/users') {
    // Liste des utilisateurs connectés par pôle
    const users = {};
    
    for (const [poleId, config] of Object.entries(POLES)) {
      users[poleId] = {
        authorized: config.users,
        connected: [] // À implémenter avec WebSocket
      };
    }
    
    res.end(JSON.stringify(users));
    
  } else {
    // Interface web simple
    res.setHeader('Content-Type', 'text/html');
    res.end(\`
<!DOCTYPE html>
<html>
<head>
    <title>WeMa IA - Dashboard Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .pole { border: 1px solid #ddd; margin: 10px; padding: 15px; border-radius: 5px; }
        .online { border-color: #4CAF50; background: #f0fff0; }
        .offline { border-color: #f44336; background: #fff0f0; }
        .users { margin-top: 10px; font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <h1>🎛️ WeMa IA - Dashboard Admin</h1>
    <div id="poles"></div>
    
    <script>
        async function loadPoles() {
            const response = await fetch('/poles');
            const poles = await response.json();
            
            const container = document.getElementById('poles');
            container.innerHTML = '';
            
            for (const [poleId, config] of Object.entries(poles)) {
                const div = document.createElement('div');
                div.className = \`pole \${config.status}\`;
                div.innerHTML = \`
                    <h3>\${config.name} (\${config.status})</h3>
                    <p>Port: \${config.port} | Mise à jour: \${config.updatePort}</p>
                    <div class="users">
                        Utilisateurs autorisés: \${config.users.join(', ')}
                    </div>
                \`;
                container.appendChild(div);
            }
        }
        
        loadPoles();
        setInterval(loadPoles, 5000); // Refresh toutes les 5 secondes
    </script>
</body>
</html>
    \`);
  }
});

server.listen(${ADMIN_PORT}, () => {
  console.log('🎛️ Dashboard admin: http://localhost:${ADMIN_PORT}');
});
`;
  }

  /**
   * Mettre à jour un pôle spécifique
   */
  async updatePole(poleId, updateData) {
    const config = POLES_CONFIG[poleId];
    if (!config) {
      throw new Error(`Pôle inconnu: ${poleId}`);
    }

    console.log(`🔄 Mise à jour ${config.name}...`);

    // Créer package de mise à jour ciblé
    const updatePackage = await this.createTargetedUpdate(poleId, updateData);
    
    // Notifier les clients de ce pôle
    await this.notifyPoleClients(poleId, updatePackage);

    console.log(`✅ Mise à jour ${config.name} distribuée`);
  }

  /**
   * Créer une mise à jour ciblée
   */
  async createTargetedUpdate(poleId, updateData) {
    // Logique de création de mise à jour spécifique au pôle
    return {
      poleId,
      version: updateData.version,
      features: updateData.features,
      downloadUrl: \`http://*************:\${POLES_CONFIG[poleId].updatePort}/download/\${poleId}-v\${updateData.version}.zip\`
    };
  }

  /**
   * Notifier les clients d'un pôle
   */
  async notifyPoleClients(poleId, updatePackage) {
    const config = POLES_CONFIG[poleId];
    
    // Envoyer notification via le serveur de mise à jour du pôle
    console.log(\`📡 Notification envoyée aux clients \${config.name}\`);
  }
}

// CLI
const manager = new MultiPoleManager();
const command = process.argv[2];
const poleId = process.argv[3];

switch (command) {
  case 'create':
    if (!poleId) {
      console.log('Usage: node multi-pole-manager.js create <pole-id>');
      process.exit(1);
    }
    manager.createPoleInstance(poleId);
    break;
    
  case 'start':
    if (poleId) {
      manager.startInstance(poleId);
    } else {
      manager.startAllInstances();
    }
    break;
    
  case 'update':
    if (!poleId) {
      console.log('Usage: node multi-pole-manager.js update <pole-id>');
      process.exit(1);
    }
    manager.updatePole(poleId, { version: '1.0.1', features: [] });
    break;
    
  case 'dashboard':
    manager.startAdminDashboard();
    break;
    
  default:
    console.log(\`
🏢 Gestionnaire Multi-Pôles WeMa IA

Commandes:
  create <pole-id>     Créer une instance pour un pôle
  start [pole-id]      Démarrer une instance (ou toutes)
  update <pole-id>     Mettre à jour un pôle spécifique
  dashboard            Démarrer le dashboard admin

Pôles disponibles: \${Object.keys(POLES_CONFIG).join(', ')}

Exemples:
  node multi-pole-manager.js create gestion-patrimoine
  node multi-pole-manager.js start
  node multi-pole-manager.js update rh
  node multi-pole-manager.js dashboard
\`);
}
`;
