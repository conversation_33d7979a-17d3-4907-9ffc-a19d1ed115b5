/**
 * 🔍 Diagnostic de performance LM Studio
 * 
 * Script pour identifier pourquoi LM Studio prend 4+ secondes à répondre
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

async function testLMStudioPerformance() {
  console.log('🔍 DIAGNOSTIC PERFORMANCE LM STUDIO\n');
  
  // Test 1: Ping simple
  console.log('1️⃣ Test de connectivité...');
  try {
    const startPing = Date.now();
    const pingResponse = await fetch(`${LM_STUDIO_URL}/v1/models`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    const pingTime = Date.now() - startPing;
    
    if (pingResponse.ok) {
      const models = await pingResponse.json();
      console.log(`✅ Connectivité OK: ${pingTime}ms`);
      console.log(`📦 Modèles disponibles: ${models.data?.length || 0}`);
      
      if (models.data?.length > 0) {
        console.log(`🎯 Premier modèle: ${models.data[0].id}`);
      }
    } else {
      console.log(`❌ Erreur connectivité: ${pingResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Erreur ping: ${error.message}`);
    return false;
  }
  
  // Test 2: Message simple ultra-court
  console.log('\n2️⃣ Test message ultra-court...');
  try {
    const startSimple = Date.now();
    const simpleResponse = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 5,
        temperature: 0,
        stream: false
      })
    });
    const simpleTime = Date.now() - startSimple;
    
    if (simpleResponse.ok) {
      const result = await simpleResponse.json();
      console.log(`✅ Message simple: ${simpleTime}ms`);
      console.log(`📝 Réponse: "${result.choices?.[0]?.message?.content || 'Vide'}"`);
      
      if (simpleTime > 3000) {
        console.log(`⚠️ PROBLÈME: ${simpleTime}ms est trop lent pour un message simple !`);
      }
    } else {
      console.log(`❌ Erreur message simple: ${simpleResponse.status}`);
      const errorText = await simpleResponse.text();
      console.log(`📋 Détails erreur: ${errorText}`);
    }
  } catch (error) {
    console.log(`❌ Erreur test simple: ${error.message}`);
  }
  
  // Test 3: Message streaming
  console.log('\n3️⃣ Test streaming...');
  try {
    const startStream = Date.now();
    const streamResponse = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [{ role: 'user', content: 'Count to 3' }],
        max_tokens: 20,
        temperature: 0,
        stream: true
      })
    });
    const firstResponseTime = Date.now() - startStream;
    
    console.log(`⏱️ Premier chunk reçu: ${firstResponseTime}ms`);
    
    if (streamResponse.ok && streamResponse.body) {
      const reader = streamResponse.body.getReader();
      const decoder = new TextDecoder();
      
      let chunkCount = 0;
      let totalContent = '';
      
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        
        chunkCount++;
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ') && !line.includes('[DONE]')) {
            try {
              const parsed = JSON.parse(line.replace('data: ', ''));
              const content = parsed.choices?.[0]?.delta?.content || '';
              if (content) {
                totalContent += content;
              }
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
        
        if (chunkCount >= 5) break; // Limiter pour le test
      }
      
      const totalStreamTime = Date.now() - startStream;
      console.log(`✅ Streaming: ${totalStreamTime}ms total, ${chunkCount} chunks`);
      console.log(`📝 Contenu: "${totalContent}"`);
      
      if (firstResponseTime > 2000) {
        console.log(`⚠️ PROBLÈME: Premier chunk en ${firstResponseTime}ms (trop lent !)`);
      }
    } else {
      console.log(`❌ Erreur streaming: ${streamResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ Erreur test streaming: ${error.message}`);
  }
  
  // Test 4: Charge système
  console.log('\n4️⃣ Test charge système...');
  try {
    const promises = [];
    const startConcurrent = Date.now();
    
    // 3 requêtes simultanées
    for (let i = 0; i < 3; i++) {
      promises.push(
        fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model: 'qwen3-4b',
            messages: [{ role: 'user', content: `Test ${i + 1}` }],
            max_tokens: 5,
            temperature: 0,
            stream: false
          })
        })
      );
    }
    
    const results = await Promise.all(promises);
    const concurrentTime = Date.now() - startConcurrent;
    
    const successCount = results.filter(r => r.ok).length;
    console.log(`✅ Requêtes simultanées: ${successCount}/3 réussies en ${concurrentTime}ms`);
    
    if (concurrentTime > 10000) {
      console.log(`⚠️ PROBLÈME: Charge simultanée très lente (${concurrentTime}ms)`);
    }
  } catch (error) {
    console.log(`❌ Erreur test concurrent: ${error.message}`);
  }
  
  // Diagnostic final
  console.log('\n🎯 DIAGNOSTIC FINAL:');
  console.log('==================');
  console.log('Si les temps sont > 2 secondes:');
  console.log('1. Vérifiez que le modèle qwen3-4b est CHARGÉ dans LM Studio');
  console.log('2. Vérifiez les ressources CPU/GPU disponibles');
  console.log('3. Redémarrez LM Studio si nécessaire');
  console.log('4. Essayez un modèle plus petit pour tester');
  console.log('5. Vérifiez les paramètres de performance LM Studio');
  
  return true;
}

// Exécution du diagnostic
testLMStudioPerformance().catch(console.error);
