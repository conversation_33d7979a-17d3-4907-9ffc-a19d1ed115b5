/**
 * 🔍 Search Progress Inline - Design épuré et moderne
 * Rectangle allongé, centré, couleurs neutres, seule l'étape active visible avec rotation
 */

import React, { useState, useEffect } from 'react';
import { Search, Brain, Globe, Link, Sparkles, Loader2 } from 'lucide-react';
import './SearchProgressInline.css';

interface SearchProgressInlineProps {
  isVisible: boolean;
  query: string;
  onComplete?: () => void;
  realSources?: string[];
  className?: string;
  // 🚀 NOUVEAU: Contrôle externe du progrès
  currentStep?: 'analyzing' | 'searching' | 'extracting' | 'completed';
  searchCompleted?: boolean;
}

interface SearchStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  duration: number;
}

const SearchProgressInline: React.FC<SearchProgressInlineProps> = ({
  isVisible,
  query,
  onComplete,
  realSources = [],
  className = '',
  currentStep = 'analyzing',
  searchCompleted = false
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [hasCompleted, setHasCompleted] = useState(false);
  const [foundSources, setFoundSources] = useState<string[]>([]);
  const [animatingDots, setAnimatingDots] = useState('');

  // Animation des points de chargement
  useEffect(() => {
    if (!isVisible || hasCompleted) return;

    const interval = setInterval(() => {
      setAnimatingDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 600);

    return () => clearInterval(interval);
  }, [isVisible, hasCompleted]);

  // Étapes de recherche intelligente - Reflète les vraies décisions de l'IA
  const searchSteps: SearchStep[] = [
    {
      id: 'analyzing',
      title: 'Décision IA',
      description: 'L\'IA détermine qu\'une recherche est nécessaire',
      icon: <Brain className="w-5 h-5" />,
      duration: 3000
    },
    {
      id: 'searching',
      title: 'Recherche active',
      description: 'Collecte d\'informations en temps réel',
      icon: <Globe className="w-5 h-5" />,
      duration: 15000
    },
    {
      id: 'extracting',
      title: 'Analyse du contenu',
      description: 'Extraction et traitement des données',
      icon: <Link className="w-5 h-5" />,
      duration: 8000
    },
    {
      id: 'completed',
      title: 'Synthèse finale',
      description: 'Formulation de la réponse complète',
      icon: <Sparkles className="w-5 h-5" />,
      duration: 4000
    }
  ];

  // 🚀 NOUVEAU: Animation basée sur les vrais événements de recherche
  useEffect(() => {
    if (!isVisible) {
      setCurrentStepIndex(0);
      setHasCompleted(false);
      setFoundSources([]);
      return;
    }

    // 🎨 Écouter les événements de recherche en temps réel
    const handleSearchEvent = (event: CustomEvent) => {
      const { step, sources, completed } = event.detail;
      console.log(`🎨 SearchProgressInline: Événement reçu:`, { step, sources, completed });

      // Mapper le progrès externe aux étapes internes
      const stepMapping = {
        'analyzing': 0,    // Décision IA
        'searching': 1,    // Recherche active
        'extracting': 2,   // Analyse du contenu
        'completed': 3     // Synthèse finale
      };

      const targetStepIndex = stepMapping[step] || 0;

      // Mettre à jour l'étape
      if (targetStepIndex !== currentStepIndex) {
        setCurrentStepIndex(targetStepIndex);
        console.log(`🎨 Animation: Passage à l'étape ${targetStepIndex} (${step})`);
      }

      // Mettre à jour les sources avec les vraies sources
      if (sources && sources.length > 0) {
        setFoundSources(sources);
        console.log(`🎨 Animation: ${sources.length} sources trouvées:`, sources);
      }

      // Marquer comme terminé
      if (completed && !hasCompleted) {
        setHasCompleted(true);
        console.log('🎨 Animation: Marquage comme terminée');
        setTimeout(() => {
          console.log('🎨 Animation: Appel onComplete');
          onComplete?.();
        }, 1500); // Délai pour voir l'animation finale
      }
    };

    // S'abonner aux événements
    window.addEventListener('search-progress', handleSearchEvent as EventListener);

    // Mapper le progrès externe initial aux étapes internes (fallback)
    const stepMapping = {
      'analyzing': 0,    // Décision IA
      'searching': 1,    // Recherche active
      'extracting': 2,   // Analyse du contenu
      'completed': 3     // Synthèse finale
    };

    const targetStepIndex = stepMapping[currentStep] || 0;
    if (targetStepIndex !== currentStepIndex) {
      setCurrentStepIndex(targetStepIndex);
    }

    // Marquer comme terminé si nécessaire (fallback)
    if (searchCompleted && !hasCompleted) {
      setHasCompleted(true);
      setTimeout(() => onComplete?.(), 1000);
    }

    return () => {
      window.removeEventListener('search-progress', handleSearchEvent as EventListener);
    };
  }, [isVisible, currentStep, searchCompleted, hasCompleted, onComplete]); // ⚡ Retiré currentStepIndex pour éviter la boucle

  // Mise à jour des sources
  useEffect(() => {
    if (realSources && realSources.length > 0) {
      setFoundSources(realSources);
    }
  }, [realSources]);

  if (!isVisible) return null;

  const currentStepData = searchSteps[currentStepIndex];

  return (
    <div className={`
      w-full max-w-3xl mx-auto
      bg-white border border-gray-200 rounded-2xl p-6 mb-4
      shadow-sm hover:shadow-md transition-all duration-300
      ${className}
    `}>
      {/* En-tête minimaliste et centré */}
      <div className="flex items-center justify-center gap-4 mb-6">
        <div className="relative">
          <Search className="w-5 h-5 text-gray-600" />
          {!hasCompleted && (
            <div className="absolute -top-1 -right-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-ping"></div>
            </div>
          )}
        </div>
        <div className="text-center">
          <h3 className="font-medium text-gray-900 text-lg">Recherche Internet</h3>
          <p className="text-sm text-gray-500 mt-1">
            {hasCompleted ? 'Terminée' : 'En cours'}
            {!hasCompleted && <span className="text-gray-600 ml-1">{animatingDots}</span>}
          </p>
        </div>
      </div>

      {/* Rectangle allongé - Seule l'étape active visible */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
        <div className="flex items-center justify-center gap-6">
          {/* Icône avec rotation */}
          <div className="relative">
            <div className={`
              flex items-center justify-center w-14 h-14 rounded-full
              bg-gradient-to-br from-gray-100 to-gray-200
              border-2 border-gray-300
              ${!hasCompleted ? 'animate-spin' : ''}
              transition-all duration-500
            `} style={{ animationDuration: '2s' }}>
              {hasCompleted ? (
                <Sparkles className="w-7 h-7 text-green-600" />
              ) : (
                <div className="relative text-gray-600">
                  {currentStepData.icon}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Loader2 className="w-7 h-7 text-gray-400 animate-spin opacity-30" />
                  </div>
                </div>
              )}
            </div>

            {/* Cercle de progression */}
            {!hasCompleted && (
              <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-gray-400 animate-spin"
                   style={{ animationDuration: '1.5s' }}>
              </div>
            )}
          </div>

          {/* Contenu de l'étape - Centré */}
          <div className="flex-1 text-center min-w-0">
            <h4 className="font-semibold text-gray-900 text-xl mb-2">
              {currentStepData.title}
            </h4>
            <p className="text-gray-600 text-base leading-relaxed">
              {currentStepData.description}
              {!hasCompleted && (
                <span className="text-gray-500 ml-1">{animatingDots}</span>
              )}
            </p>
          </div>

          {/* Barre de progression subtile */}
          {!hasCompleted && (
            <div className="w-1 h-14 bg-gray-200 rounded-full overflow-hidden">
              <div className="w-full bg-gradient-to-t from-gray-400 to-gray-600 rounded-full h-full transform origin-bottom search-progress-bar"
                   style={{
                     animationDelay: `${currentStepIndex * 0.5}s`
                   }}>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sources trouvées - Design épuré */}
      {foundSources.length > 0 && currentStepIndex >= 1 && (
        <div className="mt-6 p-4 bg-gray-50 rounded-xl border border-gray-100">
          <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">
            Sources trouvées ({foundSources.length})
          </h4>
          <div className="flex flex-wrap justify-center gap-2">
            {foundSources.slice(0, 4).map((source, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-2 px-3 py-1.5 bg-white text-gray-700 text-xs rounded-lg border border-gray-200 shadow-sm search-source-animate"
                style={{
                  animationDelay: `${index * 300}ms`,
                  opacity: 0,
                  transform: 'scale(0.8)'
                }}
              >
                <Globe className="w-3 h-3 text-gray-500" />
                {source}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Indicateur de progression global - Centré */}
      <div className="mt-6 flex items-center justify-center gap-2">
        {searchSteps.map((_, index) => (
          <div
            key={index}
            className={`
              h-1 w-8 rounded-full transition-all duration-500
              ${index <= currentStepIndex ? 'bg-gray-400' : 'bg-gray-200'}
              ${index === currentStepIndex ? 'bg-gray-600 w-12' : ''}
            `}
          />
        ))}
      </div>

    </div>
  );
};

export default SearchProgressInline;
