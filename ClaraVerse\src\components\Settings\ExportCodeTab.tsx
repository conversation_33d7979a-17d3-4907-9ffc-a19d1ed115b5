import React from 'react';
import { Zap, ExternalLink } from 'lucide-react';

const ExportCodeTab: React.FC = () => {
  return (
    <div className="glassmorphic rounded-xl p-6">
      <div className="flex items-center gap-3 mb-6">
        <Zap className="w-6 h-6 text-purple-500" />
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Export as Code
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Generate ready-to-use JavaScript code from your flows
          </p>
        </div>
      </div>

      {/* Feature Overview */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-3">
          🚀 Export Your Flows as JavaScript Code
        </h3>
        <p className="text-purple-700 dark:text-purple-300 mb-4">
          Transform your Clara flows into ready-to-use JavaScript modules that can be directly integrated into any application using the Clara Flow SDK.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">✨ What You Get</h4>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
              <li>• Complete JavaScript class</li>
              <li>• Embedded flow definition</li>
              <li>• Custom node implementations</li>
              <li>• Ready-to-use methods</li>
              <li>• TypeScript-friendly</li>
            </ul>
          </div>

          <div className="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🎯 Use Cases</h4>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
              <li>• Embed in web applications</li>
              <li>• Server-side processing</li>
              <li>• Microservices integration</li>
              <li>• Batch processing scripts</li>
              <li>• Custom workflow engines</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Usage Example */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          📝 Generated Code Example
        </h3>
        <div className="bg-gray-900 dark:bg-gray-950 rounded-lg p-4 text-sm font-mono overflow-x-auto">
          <pre className="text-green-400">
            {`// Generated by Clara Agent Studio
import { ClaraFlowRunner } from 'clara-flow-sdk';

export class MyAwesomeFlow {
  constructor(options = {}) {
    this.runner = new ClaraFlowRunner({
      enableLogging: true,
      logLevel: 'info',
      ...options
    });
    
    this.flowData = {
      // Complete flow definition embedded here
      nodes: [...],
      connections: [...],
      customNodes: [...]
    };
    
    this.registerCustomNodes();
  }

  async execute(inputs = {}) {
    return await this.runner.executeFlow(this.flowData, inputs);
  }

  async executeBatch(inputSets, options = {}) {
    // Batch processing with concurrency control
    const results = [];
    for (const inputs of inputSets) {
      results.push(await this.execute(inputs));
    }
    return results;
  }
}

// Export for direct use
export const myAwesomeFlow = new MyAwesomeFlow();
export default MyAwesomeFlow;`}
          </pre>
        </div>
      </div>

      {/* How to Use */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          🛠️ How to Export as Code
        </h3>
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
            <li><strong>1.</strong> Create your flow in Agent Studio</li>
            <li><strong>2.</strong> Click the Export dropdown in the toolbar</li>
            <li><strong>3.</strong> Select "Export as Code" (JavaScript)</li>
            <li><strong>4.</strong> Save the generated .js file</li>
            <li><strong>5.</strong> Install the SDK: <code className="bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded">npm install clara-flow-sdk</code></li>
            <li><strong>6.</strong> Import and use in your application!</li>
          </ol>
        </div>
      </div>

      {/* Integration Examples */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Node.js Server</h4>
          <div className="bg-gray-900 dark:bg-gray-950 rounded-lg p-3 text-xs font-mono overflow-x-auto">
            <pre className="text-yellow-400">
              {`import { myFlow } from './my-flow.js';

app.post('/process', async (req, res) => {
  const result = await myFlow.execute({
    input: req.body.message
  });
  res.json(result);
});`}
            </pre>
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">React Component</h4>
          <div className="bg-gray-900 dark:bg-gray-950 rounded-lg p-3 text-xs font-mono overflow-x-auto">
            <pre className="text-cyan-400">
              {`import { myFlow } from './my-flow.js';

const ProcessButton = () => {
  const handleClick = async () => {
    const result = await myFlow.execute({
      userInput: "Hello World"
    });
    console.log(result);
  };
  
  return <button onClick={handleClick}>
    Process
  </button>;
};`}
            </pre>
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div className="mt-6 bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
        <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
          ✅ Why Export as Code?
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-green-800 dark:text-green-200">
          <div>
            <strong>🚀 Zero Dependencies</strong><br />
            Everything embedded in the generated code
          </div>
          <div>
            <strong>⚡ High Performance</strong><br />
            No JSON parsing or flow loading overhead
          </div>
          <div>
            <strong>🔧 Easy Integration</strong><br />
            Drop into any JavaScript project
          </div>
        </div>
      </div>

      {/* View Integration Examples */}
      <div className="mt-6">
        <button
          onClick={() => window.open('/settings?tab=sdk-demo', '_blank')}
          className="flex items-center gap-2 text-purple-700 dark:text-purple-300 hover:text-purple-900 dark:hover:text-purple-100 transition-colors"
        >
          <ExternalLink className="w-3 h-3" />
          View integration examples
        </button>
      </div>
    </div>
  );
};

export default ExportCodeTab; 