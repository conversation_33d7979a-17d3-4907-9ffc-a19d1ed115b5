/**
 * Mode Rapide RAG - Optimisé pour la vitesse (2-3 secondes)
 * 
 * Caractéristiques :
 * - Pas de recherche vectorielle
 * - Ajout direct des documents au contexte
 * - Pas de gestion de contexte complexe
 * - Traitement minimal et ultra-rapide
 */

import { BaseMode } from './BaseMode';
import {
  RagDocument,
  RagResult,
  RagProcessingResult,
  OnContentChunk
} from '../types/RagTypes';
import { RAG_CONFIG } from '../../../config/constants';

export class FastMode extends BaseMode {
  constructor() {
    // Utiliser la configuration unifiée
    super(RAG_CONFIG.FAST_MODE.THRESHOLD, RAG_CONFIG.FAST_MODE.MAX_CONTEXT);
  }

  async processDocuments(
    documents: RagDocument[],
    userQuery: string,
    onContentChunk?: OnContentChunk
  ): Promise<RagProcessingResult> {
    const startTime = Date.now();
    
    console.log('⚡ MODE RAPIDE: Traitement ultra-simplifié activé');
    
    if (onContentChunk) {
      onContentChunk(`⚡ **Mode Rapide: ${documents.length} documents ajoutés directement**\n\n`);
    }

    // ⚡ TRAITEMENT DIRECT SANS RAG
    // Mode rapide = ajout direct du contenu sans traitement RAG
    const directResults: RagResult[] = documents.map((doc, index) => ({
      documentId: doc.id,
      documentName: doc.name,
      relevanceScore: 1.0, // Score maximal (pas de calcul)
      extractedContent: doc.content, // Contenu direct sans traitement
      metadata: {
        source: 'direct',
        processingMode: 'fast',
        timestamp: new Date().toISOString()
      }
    }));

    // Construction directe du contexte (pas de buildRagContext)
    const ragContext = documents.map(doc =>
      `=== ${doc.name} ===\n${doc.content}\n`
    ).join('\n');
    const totalLength = ragContext.length;

    const processingTime = Date.now() - startTime;

    // Log minimal pour éviter les ralentissements
    console.log('⚡ MODE RAPIDE: Traitement terminé en', processingTime, 'ms');

    return {
      ragContext,
      ragResults: directResults, // Résultats directs sans RAG
      processingStats: {
        mode: 'FAST ⚡ (DIRECT)',
        documentsProcessed: documents.length,
        totalContextLength: totalLength,
        processingTime,
        directDocuments: documents.length, // Tous en mode direct
        ragProcessedDocuments: 0 // Aucun traitement RAG
      }
    };
  }
}
