/**
 * 🧪 TEST D'OPTIMISATION DES LOGS
 * 
 * Script pour tester et valider les optimisations de logs appliquées
 */

import { logger, LogCategory } from './logger';

export class LogOptimizationTest {
  private originalConsole: any;
  private logCounts: { [key: string]: number } = {};

  constructor() {
    this.originalConsole = { ...console };
  }

  /**
   * Démarrer le monitoring des logs
   */
  public startMonitoring(): void {
    this.logCounts = {
      'console.log': 0,
      'console.warn': 0,
      'console.error': 0,
      'logger.debug': 0,
      'logger.info': 0,
      'logger.warn': 0,
      'logger.error': 0,
      'logger.group': 0,
      'logger.performance': 0
    };

    // Intercepter console.log
    console.log = (...args: any[]) => {
      this.logCounts['console.log']++;
      this.originalConsole.log(...args);
    };

    // Intercepter console.warn
    console.warn = (...args: any[]) => {
      this.logCounts['console.warn']++;
      this.originalConsole.warn(...args);
    };

    // Intercepter console.error
    console.error = (...args: any[]) => {
      this.logCounts['console.error']++;
      this.originalConsole.error(...args);
    };

    // Intercepter logger
    const originalLogger = { ...logger };
    
    logger.debug = (category: LogCategory, message: string, data?: any) => {
      this.logCounts['logger.debug']++;
      return originalLogger.debug(category, message, data);
    };

    logger.info = (category: LogCategory, message: string, data?: any) => {
      this.logCounts['logger.info']++;
      return originalLogger.info(category, message, data);
    };

    logger.warn = (category: LogCategory, message: string, data?: any) => {
      this.logCounts['logger.warn']++;
      return originalLogger.warn(category, message, data);
    };

    logger.error = (category: LogCategory, message: string, error?: any) => {
      this.logCounts['logger.error']++;
      return originalLogger.error(category, message, error);
    };

    logger.group = (category: LogCategory, groupId: string, message: string, data?: any) => {
      this.logCounts['logger.group']++;
      return originalLogger.group(category, groupId, message, data);
    };

    logger.performance = (category: LogCategory, operation: string, startTime: number, data?: any) => {
      this.logCounts['logger.performance']++;
      return originalLogger.performance(category, operation, startTime, data);
    };

    logger.info(LogCategory.SYSTEM, 'Log monitoring started');
  }

  /**
   * Arrêter le monitoring et obtenir les résultats
   */
  public stopMonitoring(): LogOptimizationResults {
    // Restaurer console original
    console.log = this.originalConsole.log;
    console.warn = this.originalConsole.warn;
    console.error = this.originalConsole.error;

    const results: LogOptimizationResults = {
      totalConsoleLogs: this.logCounts['console.log'] + this.logCounts['console.warn'] + this.logCounts['console.error'],
      totalLoggerLogs: this.logCounts['logger.debug'] + this.logCounts['logger.info'] + this.logCounts['logger.warn'] + this.logCounts['logger.error'] + this.logCounts['logger.group'] + this.logCounts['logger.performance'],
      breakdown: { ...this.logCounts },
      optimizationRatio: 0,
      recommendations: []
    };

    // Calculer le ratio d'optimisation
    const totalLogs = results.totalConsoleLogs + results.totalLoggerLogs;
    if (totalLogs > 0) {
      results.optimizationRatio = (results.totalLoggerLogs / totalLogs) * 100;
    }

    // Générer des recommandations
    if (results.totalConsoleLogs > results.totalLoggerLogs) {
      results.recommendations.push('⚠️ Plus de console.log que de logger intelligent - continuer l\'optimisation');
    }

    if (results.totalConsoleLogs > 50) {
      results.recommendations.push('🚨 Trop de console.log détectés - optimisation critique nécessaire');
    }

    if (results.optimizationRatio > 70) {
      results.recommendations.push('✅ Bonne utilisation du logger intelligent');
    }

    if (this.logCounts['logger.group'] > 0) {
      results.recommendations.push('✅ Utilisation des logs groupés détectée');
    }

    if (this.logCounts['logger.performance'] > 0) {
      results.recommendations.push('✅ Monitoring de performance actif');
    }

    logger.info(LogCategory.SYSTEM, 'Log monitoring stopped', results);
    return results;
  }

  /**
   * Test automatique des optimisations
   */
  public async runOptimizationTest(): Promise<LogOptimizationResults> {
    logger.info(LogCategory.SYSTEM, 'Démarrage du test d\'optimisation des logs...');
    
    this.startMonitoring();

    // Simuler des activités qui génèrent des logs
    await this.simulateLogActivity();

    const results = this.stopMonitoring();

    // Afficher le rapport
    this.displayReport(results);

    return results;
  }

  /**
   * Simuler des activités qui génèrent des logs
   */
  private async simulateLogActivity(): Promise<void> {
    // Simuler des logs de différents types
    logger.debug(LogCategory.SYSTEM, 'Test debug log');
    logger.info(LogCategory.PROVIDERS, 'Test info log');
    logger.warn(LogCategory.RAG, 'Test warning log');
    logger.group(LogCategory.PERFORMANCE, 'test-group', 'Test grouped log');
    
    const startTime = performance.now();
    await new Promise(resolve => setTimeout(resolve, 10));
    logger.performance(LogCategory.SYSTEM, 'Test operation', startTime);

    // Simuler quelques console.log (pour tester la détection)
    console.log('Test console.log');
    console.warn('Test console.warn');
  }

  /**
   * Afficher le rapport d'optimisation
   */
  private displayReport(results: LogOptimizationResults): void {
    console.log('\n🔍 RAPPORT D\'OPTIMISATION DES LOGS');
    console.log('=====================================');
    console.log(`📊 Total console.log: ${results.totalConsoleLogs}`);
    console.log(`🧠 Total logger intelligent: ${results.totalLoggerLogs}`);
    console.log(`📈 Ratio d'optimisation: ${results.optimizationRatio.toFixed(1)}%`);
    console.log('\n📋 Détail par type:');
    Object.entries(results.breakdown).forEach(([type, count]) => {
      if (count > 0) {
        console.log(`  ${type}: ${count}`);
      }
    });
    
    if (results.recommendations.length > 0) {
      console.log('\n💡 Recommandations:');
      results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
    console.log('=====================================\n');
  }
}

export interface LogOptimizationResults {
  totalConsoleLogs: number;
  totalLoggerLogs: number;
  breakdown: { [key: string]: number };
  optimizationRatio: number;
  recommendations: string[];
}

// Instance singleton
export const logOptimizationTest = new LogOptimizationTest();

// Exposer en développement
if (import.meta.env.DEV) {
  (window as any).logOptimizationTest = logOptimizationTest;
  (window as any).testLogOptimization = () => logOptimizationTest.runOptimizationTest();
}
