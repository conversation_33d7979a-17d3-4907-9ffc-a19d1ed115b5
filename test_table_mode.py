#!/usr/bin/env python3
"""
Test du mode tableau pour les comptes sociaux
"""

import requests
import base64
import json
from PIL import Image, ImageDraw, ImageFont
import io

BASE_URL = "http://127.0.0.1:5000"

def create_financial_table():
    """Crée une image simulant un compte social avec tableaux"""
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 14)
        title_font = ImageFont.truetype("arial.ttf", 18)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # Titre
    draw.text((50, 20), "BILAN COMPTABLE 2024", fill='black', font=title_font)
    draw.text((50, 45), "Société: EXEMPLE SAS", fill='black', font=font)
    
    # Tableau ACTIF
    y_start = 80
    draw.text((50, y_start), "ACTIF", fill='black', font=title_font)
    
    actif_data = [
        ["POSTE", "2024", "2023"],
        ["Immobilisations incorporelles", "125,000", "115,000"],
        ["Immobilisations corporelles", "850,000", "780,000"],
        ["Stocks et en-cours", "320,000", "295,000"],
        ["Créances clients", "180,000", "165,000"],
        ["Disponibilités", "95,000", "85,000"],
        ["TOTAL ACTIF", "1,570,000", "1,440,000"]
    ]
    
    # Dessiner le tableau ACTIF
    table_y = y_start + 30
    col_widths = [200, 100, 100]
    row_height = 25
    
    for row_idx, row in enumerate(actif_data):
        for col_idx, cell in enumerate(row):
            x = 50 + sum(col_widths[:col_idx])
            y = table_y + row_idx * row_height
            
            # Bordure
            draw.rectangle([x, y, x + col_widths[col_idx], y + row_height], 
                         outline='black', width=1)
            
            # Texte
            draw.text((x + 5, y + 5), cell, fill='black', font=font)
    
    # Tableau PASSIF
    passif_y = table_y + len(actif_data) * row_height + 40
    draw.text((50, passif_y - 25), "PASSIF", fill='black', font=title_font)
    
    passif_data = [
        ["POSTE", "2024", "2023"],
        ["Capital social", "500,000", "500,000"],
        ["Réserves", "380,000", "320,000"],
        ["Résultat de l'exercice", "285,000", "265,000"],
        ["Dettes fournisseurs", "245,000", "220,000"],
        ["Dettes fiscales", "160,000", "135,000"],
        ["TOTAL PASSIF", "1,570,000", "1,440,000"]
    ]
    
    # Dessiner le tableau PASSIF
    for row_idx, row in enumerate(passif_data):
        for col_idx, cell in enumerate(row):
            x = 50 + sum(col_widths[:col_idx])
            y = passif_y + row_idx * row_height
            
            # Bordure
            draw.rectangle([x, y, x + col_widths[col_idx], y + row_height], 
                         outline='black', width=1)
            
            # Texte
            draw.text((x + 5, y + 5), cell, fill='black', font=font)
    
    return img

def image_to_base64(image):
    """Convertit une image PIL en base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def test_table_mode():
    """Test du mode tableau"""
    print("🏦 Test Mode Tableau pour Comptes Sociaux...")
    
    try:
        # Créer l'image de test
        test_image = create_financial_table()
        image_base64 = image_to_base64(test_image)
        
        print(f"   📄 Image bilan créée: {test_image.size}")
        
        # Test SANS mode tableau
        print("\n   📊 Test SANS mode tableau:")
        request_normal = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "detect_tables": True,
            "table_mode": False
        }
        
        response_normal = requests.post(f"{BASE_URL}/ocr/process", json=request_normal)
        
        if response_normal.status_code == 200:
            result_normal = response_normal.json()
            tables_normal = len(result_normal.get('tables', []))
            blocks_normal = len(result_normal.get('text_blocks', []))
            print(f"      - Tableaux détectés: {tables_normal}")
            print(f"      - Blocs de texte: {blocks_normal}")
            
            # Aperçu du texte
            text_preview = result_normal.get('formatted_text', '')[:200]
            print(f"      - Aperçu: {text_preview}...")
        
        # Test AVEC mode tableau
        print("\n   📋 Test AVEC mode tableau:")
        request_table = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "detect_tables": True,
            "table_mode": True
        }
        
        response_table = requests.post(f"{BASE_URL}/ocr/process", json=request_table)
        
        if response_table.status_code == 200:
            result_table = response_table.json()
            tables_table = len(result_table.get('tables', []))
            blocks_table = len(result_table.get('text_blocks', []))
            print(f"      - Tableaux détectés: {tables_table}")
            print(f"      - Blocs de texte: {blocks_table}")
            
            # Aperçu du texte formaté
            text_preview = result_table.get('formatted_text', '')[:300]
            print(f"      - Aperçu formaté:")
            for line in text_preview.split('\n')[:10]:
                if line.strip():
                    print(f"        {line}")
            
            # Analyser les tableaux détectés
            tables = result_table.get('tables', [])
            for i, table in enumerate(tables):
                print(f"      - Table {i+1}: {table['rows']}x{table['cols']} (conf: {table['confidence']:.2f})")
            
            return tables_table > 0
        
        return False
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    """Test principal"""
    print("🚀 TEST MODE TABLEAU POUR COMPTES SOCIAUX")
    print("=" * 50)
    
    # Test du mode tableau
    table_success = test_table_mode()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DU TEST")
    print(f"   📋 Mode tableau: {'✅' if table_success else '❌'}")
    
    if table_success:
        print("\n🎉 MODE TABLEAU OPÉRATIONNEL !")
        print("   Parfait pour les comptes sociaux Pappers:")
        print("   - Détection automatique des tableaux")
        print("   - Formatage structuré des données")
        print("   - Préservation de l'alignement")
        print("   - Séparateurs de colonnes")
    else:
        print("\n⚠️ MODE TABLEAU À AMÉLIORER")
        print("   Vérifiez l'installation des dépendances")
    
    print("\n📋 RECOMMANDATIONS POUR PAPPERS:")
    print("   1. Utilisez toujours 'table_mode: true'")
    print("   2. Activez 'detect_tables: true'")
    print("   3. Gardez 'preserve_layout: true'")
    print("   4. Langue: 'eng+fra' pour les documents français")

if __name__ == "__main__":
    main()
