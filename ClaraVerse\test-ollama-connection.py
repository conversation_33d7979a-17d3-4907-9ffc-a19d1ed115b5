#!/usr/bin/env python3
"""
🧪 Test de Connexion Ollama - WeMa IA
Test la connexion au serveur Ollama central 10.0.0.17:11434
"""

import requests
import json
import time
from typing import Dict, Any

def test_ollama_connection() -> Dict[str, Any]:
    """Test la connexion au serveur Ollama central"""
    
    print("🚀 Test de connexion Ollama - WeMa IA")
    print("=" * 50)
    
    # Configuration
    ollama_url = "http://10.0.0.17:11434"
    timeout = 10
    
    results = {
        "server_accessible": False,
        "models_available": False,
        "models_count": 0,
        "models_list": [],
        "response_time": 0,
        "error": None
    }
    
    try:
        print(f"🔍 Testing connection to: {ollama_url}")
        start_time = time.time()
        
        # Test de base - récupérer les modèles
        response = requests.get(
            f"{ollama_url}/api/tags",
            timeout=timeout,
            headers={'Accept': 'application/json'}
        )
        
        response_time = round((time.time() - start_time) * 1000)
        results["response_time"] = response_time
        
        print(f"⏱️ Response time: {response_time}ms")
        print(f"📊 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            results["server_accessible"] = True
            
            # Parser la réponse JSON
            data = response.json()
            models = data.get("models", [])
            
            results["models_available"] = len(models) > 0
            results["models_count"] = len(models)
            results["models_list"] = [model["name"] for model in models]
            
            print(f"✅ Server accessible!")
            print(f"📦 Models available: {len(models)}")
            
            if models:
                print("🤖 Available models:")
                for i, model in enumerate(models[:5]):  # Afficher max 5 modèles
                    size_gb = round(model.get("size", 0) / (1024**3), 1)
                    print(f"   {i+1}. {model['name']} ({size_gb} GB)")
                
                if len(models) > 5:
                    print(f"   ... and {len(models) - 5} more models")
            
            # Test d'inférence simple
            print("\n🧠 Testing inference...")
            test_inference(ollama_url, models[0]["name"] if models else "qwen3:32b-q4_K_M")
            
        else:
            results["error"] = f"HTTP {response.status_code}: {response.text}"
            print(f"❌ Server error: {results['error']}")
            
    except requests.exceptions.Timeout:
        results["error"] = f"Timeout after {timeout}s"
        print(f"⏰ Connection timeout after {timeout}s")
        
    except requests.exceptions.ConnectionError as e:
        results["error"] = f"Connection error: {str(e)}"
        print(f"❌ Connection error: {str(e)}")
        
    except Exception as e:
        results["error"] = f"Unexpected error: {str(e)}"
        print(f"💥 Unexpected error: {str(e)}")
    
    return results

def test_inference(ollama_url: str, model_name: str):
    """Test une inférence simple"""
    try:
        payload = {
            "model": model_name,
            "prompt": "Hello, respond with just 'OK' if you can understand me.",
            "stream": False
        }
        
        print(f"   Using model: {model_name}")
        start_time = time.time()
        
        response = requests.post(
            f"{ollama_url}/api/generate",
            json=payload,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        inference_time = round((time.time() - start_time) * 1000)
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get("response", "").strip()
            print(f"   ✅ Inference successful ({inference_time}ms)")
            print(f"   🤖 Response: {response_text[:50]}...")
        else:
            print(f"   ❌ Inference failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Inference error: {str(e)}")

def main():
    """Fonction principale"""
    results = test_ollama_connection()
    
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DU TEST")
    print("=" * 50)
    
    if results["server_accessible"]:
        if results["models_available"]:
            print("🎉 SUCCÈS COMPLET!")
            print(f"✅ Serveur Ollama accessible ({results['response_time']}ms)")
            print(f"✅ {results['models_count']} modèles disponibles")
            print("🚀 Prêt pour l'inférence IA déléguée!")
        else:
            print("⚠️ SUCCÈS PARTIEL")
            print("✅ Serveur accessible mais aucun modèle disponible")
            print("💡 Installez des modèles avec: ollama pull qwen3:32b-q4_K_M")
    else:
        print("❌ ÉCHEC")
        print(f"❌ Serveur inaccessible: {results['error']}")
        print("💡 Vérifiez que Ollama écoute sur 0.0.0.0:11434")
    
    print("=" * 50)
    
    # Code de sortie
    return 0 if results["server_accessible"] and results["models_available"] else 1

if __name__ == "__main__":
    exit(main())
