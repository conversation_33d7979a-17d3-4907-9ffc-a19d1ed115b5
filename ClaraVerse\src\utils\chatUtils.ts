import { TemporaryDocument } from '../components/assistantLibrary/tempDocs';
import { searchDocuments, SearchResponse, SearchResult } from '../components/assistantLibrary/ragSearch';

interface ExtendedTemporaryDocument extends TemporaryDocument {
  content?: string;
}

interface ExtendedSearchResult extends SearchResult {
  metadata?: {
    source_file?: string;
  };
}

export async function getCombinedContext(
  userQuery: string,
  temporaryDocs: ExtendedTemporaryDocument[],
  ragEnabled: boolean,
  pythonPort: number | null,
  maxContextSize: number = 50000 // Limite par défaut
): Promise<string> {
  let combinedContext = '';
  let currentSize = 0;

  // 1. Local document handling avec limitation
  if (temporaryDocs.length > 0) {
    const localContextParts: string[] = [];

    for (const doc of temporaryDocs) {
      const docContent = `Content from ${doc.name}:\n${doc.content || ''}`;

      if (currentSize + docContent.length <= maxContextSize) {
        localContextParts.push(docContent);
        currentSize += docContent.length;
      } else {
        // Tronquer le dernier document si nécessaire
        const remainingSpace = maxContextSize - currentSize;
        if (remainingSpace > 100) { // Minimum viable
          const truncatedContent = docContent.substring(0, remainingSpace - 3) + '...';
          localContextParts.push(truncatedContent);
          currentSize = maxContextSize;
        }
        break;
      }
    }

    combinedContext = localContextParts.join('\n\n');
  }

  // 2. Knowledge base search avec limitation restante
  if (ragEnabled && pythonPort && currentSize < maxContextSize) {
    try {
      const remainingSpace = maxContextSize - currentSize;
      const results = await searchDocuments(userQuery, pythonPort, [], ragEnabled);

      if (results?.results && results.results.length > 0) {
        const serverContextParts: string[] = [];
        let serverContextSize = 0;

        for (const r of results.results) {
          const source = r.metadata?.source_file
            ? ` (from ${r.metadata.source_file})`
            : '';
          const resultContent = `${r.content}${source}`;

          if (serverContextSize + resultContent.length <= remainingSpace) {
            serverContextParts.push(resultContent);
            serverContextSize += resultContent.length;
          } else {
            // Tronquer si nécessaire
            const spaceLeft = remainingSpace - serverContextSize;
            if (spaceLeft > 100) {
              const truncated = resultContent.substring(0, spaceLeft - 3) + '...';
              serverContextParts.push(truncated);
            }
            break;
          }
        }

        if (serverContextParts.length > 0) {
          const serverContext = serverContextParts.join('\n\n');
          if (combinedContext) {
            combinedContext += '\n\n--- Knowledge Base Content ---\n\n';
          }
          combinedContext += serverContext;
        }
      }
    } catch (error) {
      console.error('Error fetching knowledge base content:', error);
    }
  }

  // 🚨 LOG de la taille finale pour debug
  console.log(`📊 Contexte combiné: ${combinedContext.length} caractères (limite: ${maxContextSize})`);

  return combinedContext;
}