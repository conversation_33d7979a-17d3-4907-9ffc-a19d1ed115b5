/**
 * 🧪 Test simple LM Studio pour debug
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

async function testSimpleCall() {
  console.log('🧪 Test simple LM Studio...');
  
  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-4b', // Modèle plus simple
        messages: [
          {
            role: 'user',
            content: 'Bonjour ! Peux-tu me répondre en JSON avec cette structure : {"message": "ta réponse", "status": "ok"} ?'
          }
        ],
        temperature: 0.1,
        max_tokens: 100,
        stream: false
      })
    });
    
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Erreur body:', errorText);
      return;
    }
    
    const result = await response.json();
    console.log('✅ Réponse reçue:', result);
    
    const aiResponse = result.choices?.[0]?.message?.content || '';
    console.log('📝 Contenu IA:', aiResponse);
    
    // Test parsing JSON
    try {
      const parsed = JSON.parse(aiResponse);
      console.log('✅ JSON valide:', parsed);
    } catch (e) {
      console.log('❌ JSON invalide');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

async function testModels() {
  console.log('📋 Test des modèles disponibles...');
  
  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/models`);
    const models = await response.json();
    
    console.log('Modèles disponibles:');
    models.data?.forEach(model => {
      console.log(`- ${model.id}`);
    });
    
    return models.data;
  } catch (error) {
    console.error('❌ Erreur récupération modèles:', error.message);
    return [];
  }
}

async function testCompressionSimple() {
  console.log('\n🧠 Test compression simple...');
  
  const simplePrompt = `Analyse cette conversation et réponds en JSON :

CONVERSATION:
[USER] Bonjour, j'ai un CV à analyser
[ASSISTANT] Bonjour ! Je peux vous aider à analyser votre CV
[USER] Voici mon CV : Jean Dupont, développeur avec 5 ans d'expérience en React et Node.js
[ASSISTANT] Excellent profil ! Votre expérience en React et Node.js est très recherchée

Réponds avec ce JSON exact :
{
  "summary": "résumé de la conversation",
  "key_points": ["point 1", "point 2"],
  "documents": ["CV Jean Dupont"]
}`;

  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [
          {
            role: 'system',
            content: 'Tu es un assistant qui répond toujours en JSON valide.'
          },
          {
            role: 'user',
            content: simplePrompt
          }
        ],
        temperature: 0.1,
        max_tokens: 500,
        stream: false
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Erreur:', response.status, errorText);
      return;
    }
    
    const result = await response.json();
    const aiResponse = result.choices?.[0]?.message?.content || '';
    
    console.log('📝 Réponse IA:', aiResponse);
    
    try {
      const parsed = JSON.parse(aiResponse);
      console.log('✅ Compression réussie:', parsed);
      
      // Vérifier la qualité
      const hasGoodSummary = parsed.summary?.length > 20;
      const hasKeyPoints = parsed.key_points?.length > 0;
      const hasDocuments = parsed.documents?.length > 0;
      
      console.log('\n📊 Qualité:');
      console.log(`Résumé: ${hasGoodSummary ? '✅' : '❌'}`);
      console.log(`Points clés: ${hasKeyPoints ? '✅' : '❌'}`);
      console.log(`Documents: ${hasDocuments ? '✅' : '❌'}`);
      
      if (hasGoodSummary && hasKeyPoints && hasDocuments) {
        console.log('\n🎉 COMPRESSION FONCTIONNELLE !');
        return true;
      }
      
    } catch (e) {
      console.log('❌ JSON invalide:', e.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur compression:', error.message);
  }
  
  return false;
}

// Test principal
async function runTests() {
  console.log('🧪 TESTS LM STUDIO RÉELS\n');
  
  // 1. Test modèles
  await testModels();
  
  // 2. Test simple
  console.log('\n' + '='.repeat(50));
  await testSimpleCall();
  
  // 3. Test compression
  console.log('\n' + '='.repeat(50));
  const compressionWorks = await testCompressionSimple();
  
  console.log('\n🎯 CONCLUSION:');
  if (compressionWorks) {
    console.log('✅ LM Studio + Qwen fonctionne pour la compression !');
    console.log('✅ Prêt pour intégration dans le système principal');
  } else {
    console.log('❌ Problèmes détectés, ajustements nécessaires');
  }
}

runTests().catch(console.error);
