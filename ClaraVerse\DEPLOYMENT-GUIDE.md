# 🚀 Guide de Déploiement WeMa IA - Mode Client-Serveur

## 🏠 Architecture Réseau Local

```
Serveur Windows (*************)
├── Backend Python (port 8000)
├── LM Studio (port 1234)  
├── Serveur de mise à jour (port 8001)
└── Base de données SQLite

Clients (*************, 102, etc.)
├── App WeMa IA (Electron ou Web)
└── Se connecte au serveur automatiquement
```

## 🔧 Installation Serveur

### 1. Préparer le serveur Windows

```bash
# Cloner le projet
git clone [votre-repo] wema-ia-server
cd wema-ia-server

# Installer les dépendances Python
cd py_backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt

# Tester le backend
python start_server.py --host 0.0.0.0 --port 8000
```

### 2. Configuration réseau

Modifiez `.env` avec l'IP de votre serveur :
```env
# Configuration serveur
CLARA_HOST=0.0.0.0
CLARA_PORT=8000
REACT_APP_BACKEND_URL=http://*************:8000
REACT_APP_LM_STUDIO_URL=http://*************:1234
```

### 3. Démarrage automatique (Windows)

Créez un service Windows ou utilisez le script :
```batch
@echo off
cd /d "C:\wema-ia-server\py_backend"
call venv\Scripts\activate
python start_server.py --production --host 0.0.0.0 --port 8000
```

## 📱 Installation Clients

### Option A : App Electron (Recommandée)

```bash
# Build l'app standalone
npm run build:standalone

# Distribuer le fichier .exe aux clients
# L'app se connecte automatiquement au serveur
```

### Option B : App Web

```bash
# Build pour serveur
npm run build:server

# Servir depuis un serveur web
cd dist
python -m http.server 3000
```

## 🔄 Système de Mise à Jour

### ✅ **Avantages du système :**

- ✅ **Mise à jour centralisée** - Depuis le serveur uniquement
- ✅ **Notification automatique** - Les clients sont prévenus
- ✅ **Mise à jour simple** - Un clic pour télécharger
- ✅ **Pas d'interruption** - Mise à jour en arrière-plan
- ✅ **Rollback facile** - Sauvegarde automatique

### 🔄 **Comment ça marche :**

1. **Serveur** : Vous créez une nouvelle version
2. **Distribution** : Le serveur de mise à jour la distribue
3. **Notification** : Les clients reçoivent une notification
4. **Installation** : Mise à jour en un clic

### 📦 **Créer une mise à jour :**

```bash
# Sur le serveur de développement
npm run update:create

# Démarrer le serveur de mise à jour
npm run update:server

# Ou tout en un
npm run update:build-and-serve
```

### 🔄 **Processus de mise à jour :**

#### **Côté serveur :**
```bash
# 1. Développer les nouvelles fonctionnalités
git pull origin main

# 2. Créer le package de mise à jour
npm run update:create

# 3. Démarrer le serveur de mise à jour
npm run update:server
```

#### **Côté client :**
- ✅ **Automatique** : Notification toutes les 5 minutes
- ✅ **Manuel** : Bouton "Vérifier les mises à jour"
- ✅ **Simple** : Un clic pour télécharger et installer

## 🌐 URLs et Ports

| Service | Port | URL | Description |
|---------|------|-----|-------------|
| Backend | 8000 | http://*************:8000 | API principale |
| LM Studio | 1234 | http://*************:1234 | Modèles IA |
| Mise à jour | 8001 | http://*************:8001 | Serveur de mise à jour |
| Frontend | 3000 | http://*************:3000 | Interface web (optionnel) |

## 🔧 Configuration Firewall Windows

```powershell
# Autoriser les ports nécessaires
netsh advfirewall firewall add rule name="WeMa IA Backend" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="WeMa IA Updates" dir=in action=allow protocol=TCP localport=8001
netsh advfirewall firewall add rule name="LM Studio" dir=in action=allow protocol=TCP localport=1234
```

## 🚀 Démarrage Rapide

### **Pour le serveur :**
```bash
# 1. Copier le projet sur le serveur
# 2. Modifier .env avec l'IP du serveur
# 3. Démarrer :
cd py_backend
python start_server.py --production --host 0.0.0.0

# 4. Démarrer LM Studio manuellement
# 5. Démarrer le serveur de mise à jour :
npm run update:server
```

### **Pour les clients :**
```bash
# 1. Installer l'app Electron
# 2. L'app détecte automatiquement le serveur
# 3. Les mises à jour sont automatiques
```

## 🔍 Dépannage

### **Backend inaccessible :**
- Vérifiez l'IP dans `.env`
- Vérifiez le firewall Windows
- Testez : `curl http://*************:8000/health`

### **LM Studio inaccessible :**
- Démarrez LM Studio avec l'option serveur
- Vérifiez le port 1234
- Testez : `curl http://*************:1234/v1/models`

### **Mises à jour ne fonctionnent pas :**
- Vérifiez le serveur de mise à jour (port 8001)
- Vérifiez les logs dans la console

## 📊 Monitoring

### **Logs serveur :**
```bash
# Backend
tail -f py_backend/clara_backend.log

# Mise à jour
# Logs dans la console du serveur de mise à jour
```

### **Santé du système :**
- Backend : http://*************:8000/health
- LM Studio : http://*************:1234/v1/models
- Mise à jour : http://*************:8001/version

## 🎯 Avantages de cette Architecture

- ✅ **Performance** : IA centralisée sur serveur puissant
- ✅ **Maintenance** : Mise à jour centralisée
- ✅ **Sécurité** : Données centralisées
- ✅ **Simplicité** : Installation client simple
- ✅ **Évolutivité** : Ajout facile de nouveaux clients
- ✅ **Fiabilité** : Système de mise à jour robuste

## 🔄 Workflow de Mise à Jour

1. **Développement** → Nouvelles fonctionnalités
2. **Build** → `npm run update:create`
3. **Distribution** → `npm run update:server`
4. **Notification** → Clients prévenus automatiquement
5. **Installation** → Un clic pour mettre à jour
6. **Redémarrage** → Application redémarre avec nouvelle version

**C'est tout ! 🎉 Le système est conçu pour être simple et efficace.**
