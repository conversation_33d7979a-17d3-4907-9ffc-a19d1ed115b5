/**
 * 🧠 Dashboard de monitoring d'inférence
 * Surveille le serveur d'inférence centralisé
 */

import React, { useState, useEffect } from 'react';
import { Activity, Server, Users, Zap, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface InferenceStats {
  total_requests: number;
  active_requests: number;
  total_tokens: number;
  start_time: string;
  lm_studio_status: string;
  max_concurrent: number;
}

interface HealthStatus {
  status: string;
  lm_studio_status: string;
  lm_studio_url: string;
  active_requests: number;
  total_requests: number;
  max_concurrent: number;
}

const InferenceDashboard: React.FC = () => {
  const [stats, setStats] = useState<InferenceStats | null>(null);
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  const serverUrl = process.env.REACT_APP_INFERENCE_SERVER_URL || 'http://192.168.1.100:1235';

  // Charger les données
  const loadData = async () => {
    try {
      setError('');
      
      // Charger les statistiques
      const statsResponse = await fetch(`${serverUrl}/stats`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }
      
      // Charger le health check
      const healthResponse = await fetch(`${serverUrl}/health`);
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        setHealth(healthData);
      }
      
    } catch (err) {
      setError('Impossible de se connecter au serveur d\'inférence');
      console.error('Erreur chargement données:', err);
    } finally {
      setLoading(false);
    }
  };

  // Actualisation automatique
  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 5000); // Toutes les 5 secondes
    return () => clearInterval(interval);
  }, [serverUrl]);

  // Calculer l'uptime
  const getUptime = () => {
    if (!stats?.start_time) return 'Inconnu';
    
    const startTime = new Date(stats.start_time);
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'offline':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-yellow-600 bg-yellow-100';
    }
  };

  // Obtenir l'icône du statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy':
        return <CheckCircle className="w-5 h-5" />;
      case 'offline':
        return <AlertCircle className="w-5 h-5" />;
      default:
        return <Clock className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du dashboard d'inférence...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-red-800">Erreur de connexion</h3>
            <p className="text-red-600 mt-1">{error}</p>
            <button
              onClick={loadData}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Zap className="w-7 h-7 mr-3 text-blue-600" />
            Serveur d'Inférence
          </h2>
          <p className="text-gray-600 mt-1">Monitoring du serveur d'IA centralisé</p>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-gray-500">
            Dernière actualisation: {new Date().toLocaleTimeString()}
          </div>
          <button
            onClick={loadData}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
          >
            Actualiser
          </button>
        </div>
      </div>

      {/* Statut général */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Statut du serveur</h3>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${getStatusColor(health?.status || 'unknown')}`}>
                {getStatusIcon(health?.status || 'unknown')}
                <span className="ml-2 capitalize">{health?.status || 'Inconnu'}</span>
              </div>
            </div>
            <Server className="w-8 h-8 text-gray-400" />
          </div>
          
          <div className="mt-4 space-y-2 text-sm text-gray-600">
            <div>URL: {serverUrl}</div>
            <div>Uptime: {getUptime()}</div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">LM Studio</h3>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${getStatusColor(health?.lm_studio_status || 'unknown')}`}>
                {getStatusIcon(health?.lm_studio_status || 'unknown')}
                <span className="ml-2 capitalize">{health?.lm_studio_status || 'Inconnu'}</span>
              </div>
            </div>
            <Activity className="w-8 h-8 text-gray-400" />
          </div>
          
          <div className="mt-4 space-y-2 text-sm text-gray-600">
            <div>URL: {health?.lm_studio_url || 'http://localhost:1234'}</div>
            <div>Concurrent max: {health?.max_concurrent || 'N/A'}</div>
          </div>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Requêtes actives</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.active_requests || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Total requêtes</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.total_requests || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Zap className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Total tokens</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.total_tokens ? (stats.total_tokens / 1000).toFixed(1) + 'K' : '0'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Informations détaillées */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Informations détaillées</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Configuration</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div>Serveur d'inférence: {serverUrl}</div>
              <div>LM Studio: {health?.lm_studio_url}</div>
              <div>Requêtes simultanées max: {stats?.max_concurrent}</div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Performance</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div>Démarré le: {stats?.start_time ? new Date(stats.start_time).toLocaleString() : 'Inconnu'}</div>
              <div>Uptime: {getUptime()}</div>
              <div>Charge actuelle: {stats?.active_requests || 0}/{stats?.max_concurrent || 'N/A'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InferenceDashboard;
