#!/usr/bin/env node
/**
 * 🎛️ Build séparé : App Admin vs App Utilisateurs
 * Génère 2 applications distinctes avec interfaces différentes
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const ADMIN_CONFIG = {
  name: 'WeMa IA Admin',
  port: 9000,
  features: ['admin-dashboard', 'multi-pole-management', 'user-monitoring', 'system-control'],
  allowedIPs: ['*************'], // Seulement ton poste
  branding: {
    title: 'WeMa IA - Administration',
    primaryColor: '#1f2937', // Gris foncé admin
    logo: 'logo-admin.png'
  }
};

const USER_CONFIG = {
  name: 'WeMa IA',
  features: ['chat', 'documents', 'rag', 'ocr'],
  branding: {
    title: 'WeMa IA',
    primaryColor: '#3b82f6', // Bleu standard
    logo: 'logo-user.png'
  }
};

class AdminUserBuilder {
  constructor() {
    this.buildDir = 'builds';
  }

  /**
   * Build l'application admin
   */
  async buildAdminApp() {
    console.log('🎛️ Build Application Admin...');

    const adminDir = path.join(this.buildDir, 'admin');
    
    // Nettoyer et créer le dossier
    if (fs.existsSync(adminDir)) {
      fs.rmSync(adminDir, { recursive: true });
    }
    fs.mkdirSync(adminDir, { recursive: true });

    // 1. Copier le code de base
    this.copyBaseCode(adminDir);

    // 2. Configurer pour admin
    this.configureAdminApp(adminDir);

    // 3. Build
    await this.buildApp(adminDir, 'admin');

    console.log('✅ Application Admin créée dans builds/admin/');
  }

  /**
   * Build les applications utilisateurs pour chaque pôle
   */
  async buildUserApps() {
    console.log('👥 Build Applications Utilisateurs...');

    const POLES_CONFIG = {
      'gestion-patrimoine': {
        name: 'Gestion Patrimoine',
        port: 8000,
        users: ['192.168.1.101', '192.168.1.102'],
        features: ['rag', 'ocr', 'documents', 'compression'],
        branding: {
          title: 'WeMa IA - Gestion Patrimoine',
          primaryColor: '#2563eb',
          logo: 'logo-patrimoine.png'
        }
      },
      'mandats-a': {
        name: 'Mandats A',
        port: 8010,
        users: ['192.168.1.103', '192.168.1.104'],
        features: ['rag', 'documents', 'basic-ocr'],
        branding: {
          title: 'WeMa IA - Mandats A',
          primaryColor: '#059669',
          logo: 'logo-mandats.png'
        }
      },
      'rh': {
        name: 'Ressources Humaines',
        port: 8020,
        users: ['192.168.1.105'],
        features: ['rag', 'documents', 'confidential'],
        branding: {
          title: 'WeMa IA - RH',
          primaryColor: '#dc2626',
          logo: 'logo-rh.png'
        }
      }
    };

    for (const [poleId, config] of Object.entries(POLES_CONFIG)) {
      await this.buildUserApp(poleId, config);
    }

    console.log('✅ Applications Utilisateurs créées dans builds/users/');
  }

  /**
   * Build une app utilisateur pour un pôle
   */
  async buildUserApp(poleId, config) {
    console.log(`👥 Build ${config.name}...`);

    const userDir = path.join(this.buildDir, 'users', poleId);
    
    // Nettoyer et créer le dossier
    if (fs.existsSync(userDir)) {
      fs.rmSync(userDir, { recursive: true });
    }
    fs.mkdirSync(userDir, { recursive: true });

    // 1. Copier le code de base
    this.copyBaseCode(userDir);

    // 2. Configurer pour ce pôle
    this.configureUserApp(userDir, poleId, config);

    // 3. Build
    await this.buildApp(userDir, `user-${poleId}`);

    console.log(`✅ App ${config.name} créée`);
  }

  /**
   * Copier le code de base
   */
  copyBaseCode(targetDir) {
    const filesToCopy = [
      'src',
      'public',
      'package.json',
      'vite.config.ts',
      'tsconfig.json',
      'tailwind.config.js'
    ];

    filesToCopy.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(targetDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  /**
   * Configurer l'application admin
   */
  configureAdminApp(adminDir) {
    // 1. Variables d'environnement admin
    const adminEnv = {
      REACT_APP_MODE: 'admin',
      REACT_APP_ADMIN_DASHBOARD: 'true',
      REACT_APP_MULTI_POLE_MANAGEMENT: 'true',
      REACT_APP_USER_MONITORING: 'true',
      REACT_APP_SYSTEM_CONTROL: 'true',
      REACT_APP_BACKEND_URL: 'http://*************:9000',
      REACT_APP_TITLE: ADMIN_CONFIG.branding.title,
      REACT_APP_PRIMARY_COLOR: ADMIN_CONFIG.branding.primaryColor,
      REACT_APP_ALLOWED_IPS: ADMIN_CONFIG.allowedIPs.join(',')
    };

    fs.writeFileSync(
      path.join(adminDir, '.env'),
      Object.entries(adminEnv).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 2. Créer le composant principal admin
    this.createAdminMainComponent(adminDir);

    // 3. Modifier App.tsx pour mode admin
    this.modifyAppForAdmin(adminDir);

    // 4. Créer le backend admin
    this.createAdminBackend(adminDir);
  }

  /**
   * Configurer une application utilisateur
   */
  configureUserApp(userDir, poleId, config) {
    // 1. Variables d'environnement utilisateur
    const userEnv = {
      REACT_APP_MODE: 'user',
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://*************:${config.port}`,
      REACT_APP_UPDATE_URL: `http://*************:${config.port + 1}`,
      REACT_APP_TITLE: config.branding.title,
      REACT_APP_PRIMARY_COLOR: config.branding.primaryColor,
      REACT_APP_ALLOWED_IPS: config.users.join(','),
      REACT_APP_FEATURES: config.features.join(',')
    };

    fs.writeFileSync(
      path.join(userDir, '.env'),
      Object.entries(userEnv).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 2. Filtrer les fonctionnalités
    this.filterUserFeatures(userDir, config.features);

    // 3. Personnaliser le branding
    this.customizeUserBranding(userDir, config.branding);
  }

  /**
   * Créer le composant principal admin
   */
  createAdminMainComponent(adminDir) {
    const adminMainComponent = `
import React from 'react';
import AdminDashboard from './components/AdminDashboard';
import MultiPoleManager from './components/MultiPoleManager';
import UserMonitoring from './components/UserMonitoring';
import SystemControl from './components/SystemControl';

const AdminMain: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState('dashboard');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation admin */}
      <nav className="bg-gray-900 text-white p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">🎛️ WeMa IA - Administration</h1>
          
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={\`px-4 py-2 rounded \${activeTab === 'dashboard' ? 'bg-blue-600' : 'bg-gray-700'}\`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('poles')}
              className={\`px-4 py-2 rounded \${activeTab === 'poles' ? 'bg-blue-600' : 'bg-gray-700'}\`}
            >
              Gestion Pôles
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={\`px-4 py-2 rounded \${activeTab === 'users' ? 'bg-blue-600' : 'bg-gray-700'}\`}
            >
              Utilisateurs
            </button>
            <button
              onClick={() => setActiveTab('system')}
              className={\`px-4 py-2 rounded \${activeTab === 'system' ? 'bg-blue-600' : 'bg-gray-700'}\`}
            >
              Système
            </button>
          </div>
        </div>
      </nav>

      {/* Contenu admin */}
      <div className="p-6">
        {activeTab === 'dashboard' && <AdminDashboard />}
        {activeTab === 'poles' && <MultiPoleManager />}
        {activeTab === 'users' && <UserMonitoring />}
        {activeTab === 'system' && <SystemControl />}
      </div>
    </div>
  );
};

export default AdminMain;
`;

    fs.writeFileSync(
      path.join(adminDir, 'src', 'components', 'AdminMain.tsx'),
      adminMainComponent
    );
  }

  /**
   * Modifier App.tsx pour mode admin
   */
  modifyAppForAdmin(adminDir) {
    const appPath = path.join(adminDir, 'src', 'App.tsx');
    
    const adminApp = `
import React from 'react';
import AdminMain from './components/AdminMain';
import './App.css';

// Vérification IP autorisée
const checkAuthorizedIP = () => {
  const allowedIPs = process.env.REACT_APP_ALLOWED_IPS?.split(',') || [];
  // En production, vérifier l'IP côté serveur
  return true; // Simplifié pour le développement
};

function App() {
  if (!checkAuthorizedIP()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Accès Refusé</h1>
          <p className="text-red-500">Vous n'êtes pas autorisé à accéder à cette interface.</p>
        </div>
      </div>
    );
  }

  return <AdminMain />;
}

export default App;
`;

    fs.writeFileSync(appPath, adminApp);
  }

  /**
   * Créer le backend admin
   */
  createAdminBackend(adminDir) {
    const backendDir = path.join(adminDir, 'admin-backend');
    fs.mkdirSync(backendDir, { recursive: true });

    const adminServer = `
const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Configuration des pôles
const POLES = {
  'gestion-patrimoine': { port: 8000, name: 'Gestion Patrimoine' },
  'mandats-a': { port: 8010, name: 'Mandats A' },
  'rh': { port: 8020, name: 'RH' }
};

// API pour le dashboard admin
app.get('/api/poles', async (req, res) => {
  const polesStatus = {};
  
  for (const [poleId, config] of Object.entries(POLES)) {
    try {
      const response = await fetch(\`http://localhost:\${config.port}/health\`);
      polesStatus[poleId] = {
        ...config,
        status: response.ok ? 'online' : 'offline',
        lastCheck: new Date().toISOString()
      };
    } catch {
      polesStatus[poleId] = {
        ...config,
        status: 'offline',
        lastCheck: new Date().toISOString()
      };
    }
  }
  
  res.json(polesStatus);
});

// Redémarrer un pôle
app.post('/api/restart/:poleId', (req, res) => {
  const { poleId } = req.params;
  console.log(\`Redémarrage demandé pour \${poleId}\`);
  // Logique de redémarrage
  res.json({ success: true });
});

// Mettre à jour un pôle
app.post('/api/update/:poleId', (req, res) => {
  const { poleId } = req.params;
  console.log(\`Mise à jour demandée pour \${poleId}\`);
  // Logique de mise à jour
  res.json({ success: true });
});

const PORT = ${ADMIN_CONFIG.port};
app.listen(PORT, () => {
  console.log(\`🎛️ Serveur Admin démarré sur port \${PORT}\`);
});
`;

    fs.writeFileSync(path.join(backendDir, 'server.js'), adminServer);

    // Package.json pour le backend admin
    const backendPackage = {
      name: 'wema-ia-admin-backend',
      version: '1.0.0',
      scripts: {
        start: 'node server.js'
      },
      dependencies: {
        express: '^4.18.0',
        cors: '^2.8.5'
      }
    };

    fs.writeFileSync(
      path.join(backendDir, 'package.json'),
      JSON.stringify(backendPackage, null, 2)
    );
  }

  /**
   * Filtrer les fonctionnalités utilisateur
   */
  filterUserFeatures(userDir, features) {
    const featureFlags = {
      rag: features.includes('rag'),
      ocr: features.includes('ocr') || features.includes('basic-ocr'),
      advancedOcr: features.includes('ocr'),
      documents: features.includes('documents'),
      compression: features.includes('compression'),
      confidential: features.includes('confidential'),
      adminDashboard: false, // Jamais pour les utilisateurs
      multiPoleManagement: false,
      userMonitoring: false,
      systemControl: false
    };

    fs.writeFileSync(
      path.join(userDir, 'src', 'config', 'features.json'),
      JSON.stringify(featureFlags, null, 2)
    );
  }

  /**
   * Personnaliser le branding utilisateur
   */
  customizeUserBranding(userDir, branding) {
    // Modifier index.html
    const indexPath = path.join(userDir, 'public', 'index.html');
    if (fs.existsSync(indexPath)) {
      let indexContent = fs.readFileSync(indexPath, 'utf8');
      indexContent = indexContent.replace(/<title>.*<\/title>/, `<title>${branding.title}</title>`);
      fs.writeFileSync(indexPath, indexContent);
    }

    // Créer configuration de thème
    const themeConfig = {
      primaryColor: branding.primaryColor,
      title: branding.title,
      logo: branding.logo,
      mode: 'user'
    };

    fs.writeFileSync(
      path.join(userDir, 'src', 'config', 'theme.json'),
      JSON.stringify(themeConfig, null, 2)
    );
  }

  /**
   * Build une application
   */
  async buildApp(appDir, appName) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(appDir);
      
      // Installer les dépendances si nécessaire
      if (!fs.existsSync('node_modules')) {
        console.log(`📦 Installation dépendances ${appName}...`);
        execSync('npm install', { stdio: 'inherit' });
      }

      // Build
      console.log(`🔨 Build ${appName}...`);
      execSync('npm run build', { stdio: 'inherit' });

      console.log(`✅ ${appName} build terminé`);
      
    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * Créer les scripts de démarrage
   */
  createStartupScripts() {
    // Script admin
    const adminScript = `@echo off
echo 🎛️ Démarrage Interface Admin...

cd builds\\admin\\admin-backend
start "Admin-Backend" npm start

cd ..\\..\\..
echo ✅ Interface Admin démarrée sur http://*************:9000
pause`;

    fs.writeFileSync('start-admin.bat', adminScript);

    // Script utilisateurs
    const userScript = `@echo off
echo 👥 Démarrage Applications Utilisateurs...

REM Démarrer chaque pôle
cd builds\\users\\gestion-patrimoine\\py_backend
start "Patrimoine" python start_server.py --production --host 0.0.0.0 --port 8000

cd ..\\..\\mandats-a\\py_backend
start "Mandats" python start_server.py --production --host 0.0.0.0 --port 8010

cd ..\\..\\rh\\py_backend
start "RH" python start_server.py --production --host 0.0.0.0 --port 8020

cd ..\\..\\..\\..
echo ✅ Applications Utilisateurs démarrées
pause`;

    fs.writeFileSync('start-users.bat', userScript);
  }
}

// CLI
const builder = new AdminUserBuilder();
const command = process.argv[2];

switch (command) {
  case 'admin':
    builder.buildAdminApp();
    break;
    
  case 'users':
    builder.buildUserApps();
    break;
    
  case 'all':
    builder.buildAdminApp().then(() => builder.buildUserApps()).then(() => {
      builder.createStartupScripts();
      console.log('🎉 Toutes les applications créées !');
    });
    break;
    
  default:
    console.log(`
🎛️ Build Admin vs Utilisateurs

Commandes:
  admin     Build seulement l'interface admin
  users     Build seulement les apps utilisateurs
  all       Build tout (admin + utilisateurs)

Exemples:
  node scripts/build-admin-user-apps.js admin
  node scripts/build-admin-user-apps.js users
  node scripts/build-admin-user-apps.js all
`);
}
`;
