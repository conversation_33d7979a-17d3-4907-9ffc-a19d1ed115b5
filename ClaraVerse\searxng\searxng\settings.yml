# Configuration SearXNG pour WeMa IA - Version simplifiée
use_default_settings: true

general:
  instance_name: "WeMa IA Search"

search:
  safe_search: 1
  autocomplete: "google"
  default_lang: "fr"
  formats:
    - html
    - json

server:
  secret_key: "wema-ia-searxng-secret-key-2024"
  limiter: false
  image_proxy: false
  method: "GET"
  http_protocol_version: "1.1"

# Configuration CORS pour permettre les requêtes depuis WeMa IA
outgoing:
  request_timeout: 10.0
  useragent_suffix: "WeMa-IA/1.0"

# Désactiver les restrictions pour usage local
enabled_plugins: []
