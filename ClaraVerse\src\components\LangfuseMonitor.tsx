import React, { useState, useEffect } from 'react';
import { Activity, Clock, Database, Zap } from 'lucide-react';

interface PerformanceMetrics {
  lastRequestTime?: number;
  averageResponseTime?: number;
  totalRequests?: number;
  ragEnabled?: boolean;
  langfuseEnabled?: boolean;
}

const LangfuseMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Vérifier si Langfuse est disponible
    checkLangfuseStatus();
  }, []);

  const checkLangfuseStatus = async () => {
    try {
      const response = await fetch('http://localhost:5001/health');
      const data = await response.json();
      setMetrics(prev => ({
        ...prev,
        ragEnabled: data.rag_available,
        langfuseEnabled: true // Langfuse est intégré côté backend
      }));
    } catch (error) {
      console.warn('Backend non disponible:', error);
    }
  };

  const updateMetrics = (responseTime: number) => {
    setMetrics(prev => ({
      ...prev,
      lastRequestTime: responseTime,
      totalRequests: (prev.totalRequests || 0) + 1,
      averageResponseTime: prev.averageResponseTime 
        ? (prev.averageResponseTime + responseTime) / 2 
        : responseTime
    }));
  };

  // Exposer la fonction updateMetrics globalement pour que les autres composants puissent l'utiliser
  useEffect(() => {
    (window as any).updateLangfuseMetrics = updateMetrics;
    return () => {
      delete (window as any).updateLangfuseMetrics;
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 z-50"
        title="Afficher les métriques de performance"
      >
        <Activity size={20} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-80 z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
          <Activity size={20} className="text-blue-500" />
          Performance Monitor
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          ✕
        </button>
      </div>

      <div className="space-y-3">
        {/* Status Langfuse */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
            <Zap size={16} />
            Langfuse Tracing
          </span>
          <span className={`text-sm font-medium ${
            metrics.langfuseEnabled 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          }`}>
            {metrics.langfuseEnabled ? '✅ Actif' : '❌ Inactif'}
          </span>
        </div>

        {/* Status RAG */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
            <Database size={16} />
            RAG Premium
          </span>
          <span className={`text-sm font-medium ${
            metrics.ragEnabled 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          }`}>
            {metrics.ragEnabled ? '✅ Disponible' : '❌ Indisponible'}
          </span>
        </div>

        {/* Dernière requête */}
        {metrics.lastRequestTime && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2">
              <Clock size={16} />
              Dernière requête
            </span>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {metrics.lastRequestTime.toFixed(2)}s
            </span>
          </div>
        )}

        {/* Temps moyen */}
        {metrics.averageResponseTime && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300">
              Temps moyen
            </span>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {metrics.averageResponseTime.toFixed(2)}s
            </span>
          </div>
        )}

        {/* Total requêtes */}
        {metrics.totalRequests && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300">
              Total requêtes
            </span>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              {metrics.totalRequests}
            </span>
          </div>
        )}
      </div>

      {/* Lien vers Langfuse Dashboard */}
      {metrics.langfuseEnabled && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <a
            href="https://cloud.langfuse.com"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-1"
          >
            📊 Ouvrir Langfuse Dashboard
          </a>
        </div>
      )}

      {/* Indicateur de performance */}
      {metrics.lastRequestTime && (
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
            <span>Performance</span>
            <span>
              {metrics.lastRequestTime < 2 ? '🚀 Rapide' : 
               metrics.lastRequestTime < 5 ? '⚡ Normal' : 
               '🐌 Lent'}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                metrics.lastRequestTime < 2 ? 'bg-green-500' :
                metrics.lastRequestTime < 5 ? 'bg-yellow-500' :
                'bg-red-500'
              }`}
              style={{
                width: `${Math.min(100, (metrics.lastRequestTime / 10) * 100)}%`
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default LangfuseMonitor;
