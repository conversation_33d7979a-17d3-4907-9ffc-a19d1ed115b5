/**
 * 💬 CHAT MANAGER
 * Gère les conversations et messages de manière isolée
 */

import { AssistantAPIClient } from '../../utils/AssistantAPIClient';
import type { ChatMessage } from '../../utils/APIClient';
import type { 
  ClaraMessage, 
  <PERSON>FileAttachment, 
  ClaraAIConfig,
  ClaraProvider 
} from '../../types/clara_assistant_types';
import { toolExecutor } from '../tools/ToolExecutor';

export interface ChatContext {
  modelId: string;
  config: ClaraAIConfig;
  systemPrompt?: string;
  conversationHistory?: ClaraMessage[];
  attachments?: ClaraFileAttachment[];
}

export class ChatManager {
  private client: AssistantAPIClient | null = null;
  private currentProvider: ClaraProvider | null = null;

  /**
   * Update the API client for a specific provider
   */
  public updateProvider(provider: <PERSON><PERSON>rovider) {
    this.currentProvider = provider;
    this.client = new AssistantAPIClient(provider.baseUrl || '', {
      apiKey: provider.apiKey || '',
      providerId: provider.id
    });
  }

  /**
   * Send a standard chat message (no tools)
   */
  public async sendStandardMessage(
    message: string,
    context: Chat<PERSON>ontext,
    onContentChunk?: (content: string) => void
  ): Promise<ClaraMessage> {
    if (!this.client) {
      throw new Error('No API client configured. Please select a provider.');
    }

    try {
      // Prepare conversation messages
      const conversationMessages: ChatMessage[] = [];

      // Add system prompt if provided
      if (context.systemPrompt) {
        conversationMessages.push({
          role: 'system',
          content: context.systemPrompt
        });
      }

      // Add conversation history
      if (context.conversationHistory) {
        for (const msg of context.conversationHistory) {
          conversationMessages.push({
            role: msg.role,
            content: msg.content
          });
        }
      }

      // Add current user message
      conversationMessages.push({
        role: 'user',
        content: message
      });

      console.log(`🚀 Sending standard chat message with ${conversationMessages.length} messages`);

      // Send message and handle streaming
      let fullContent = '';
      const response = await this.client.sendMessage(
        context.modelId,
        message,
        {
          temperature: context.config.temperature || 0.7,
          maxTokens: context.config.maxTokens || 1000,
          systemPrompt: context.systemPrompt,
          conversationHistory: context.conversationHistory?.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        },
        (chunk: string) => {
          fullContent += chunk;
          if (onContentChunk) {
            onContentChunk(chunk);
          }
        }
      );

      // Create Clara message
      const claraMessage: ClaraMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: response.content || fullContent,
        timestamp: new Date(),
        model: response.model || context.modelId,
        usage: response.usage,
        isStreaming: false
      };

      return claraMessage;

    } catch (error) {
      console.error('❌ Standard chat error:', error);
      throw new Error(`Chat failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send a message with tool support
   */
  public async sendMessageWithTools(
    message: string,
    context: ChatContext,
    tools: any[],
    onContentChunk?: (content: string) => void
  ): Promise<ClaraMessage> {
    if (!this.client) {
      throw new Error('No API client configured. Please select a provider.');
    }

    try {
      // Prepare conversation messages
      const conversationMessages: ChatMessage[] = [];

      // Add system prompt if provided
      if (context.systemPrompt) {
        conversationMessages.push({
          role: 'system',
          content: context.systemPrompt
        });
      }

      // Add conversation history
      if (context.conversationHistory) {
        for (const msg of context.conversationHistory) {
          conversationMessages.push({
            role: msg.role,
            content: msg.content
          });
        }
      }

      // Add current user message
      conversationMessages.push({
        role: 'user',
        content: message
      });

      console.log(`🚀 Sending message with tools: ${tools.length} tools available`);

      // Send message with tools
      let fullContent = '';
      let toolCalls: any[] = [];

      const response = await this.client.sendMessageWithTools(
        context.modelId,
        conversationMessages,
        tools,
        {
          temperature: context.config.temperature || 0.7,
          maxTokens: context.config.maxTokens || 1000
        },
        (chunk: string) => {
          fullContent += chunk;
          if (onContentChunk) {
            onContentChunk(chunk);
          }
        }
      );

      // Handle tool calls if present
      if (response.message?.tool_calls && response.message.tool_calls.length > 0) {
        console.log(`🔧 Processing ${response.message.tool_calls.length} tool calls...`);
        
        // Execute tool calls
        const toolResults = await toolExecutor.executeToolCalls(response.message.tool_calls);
        
        // Add tool messages to conversation
        for (const toolCall of response.message.tool_calls) {
          const result = toolResults.find(r => r.toolName === toolCall.function?.name);
          
          if (result) {
            conversationMessages.push({
              role: 'tool',
              content: result.success 
                ? JSON.stringify(result.result || { success: true })
                : `Error: ${result.error}`,
              name: toolCall.function?.name || 'unknown_tool',
              tool_call_id: toolCall.id
            });
          }
        }

        // Get final response after tool execution
        const finalResponse = await this.client.sendMessage(
          context.modelId,
          '',
          {
            temperature: context.config.temperature || 0.7,
            maxTokens: context.config.maxTokens || 1000,
            conversationHistory: conversationMessages
          },
          (chunk: string) => {
            if (onContentChunk) {
              onContentChunk(chunk);
            }
          }
        );

        fullContent = finalResponse.content || '';
      }

      // Create Clara message
      const claraMessage: ClaraMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: response.content || fullContent,
        timestamp: new Date(),
        model: response.model || context.modelId,
        usage: response.usage,
        isStreaming: false,
        tool_calls: response.message?.tool_calls
      };

      return claraMessage;

    } catch (error) {
      console.error('❌ Chat with tools error:', error);
      throw new Error(`Chat with tools failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Prepare conversation context
   */
  public prepareConversationContext(
    message: string,
    config: ClaraAIConfig,
    systemPrompt?: string,
    conversationHistory?: ClaraMessage[],
    attachments?: ClaraFileAttachment[]
  ): ChatContext {
    // Determine model ID
    let modelId = config.models.text;
    
    // Extract model name if it includes provider prefix
    if (modelId.includes(':')) {
      const parts = modelId.split(':');
      modelId = parts.slice(1).join(':');
    }

    return {
      modelId,
      config,
      systemPrompt,
      conversationHistory,
      attachments
    };
  }

  /**
   * Truncate conversation history to fit context limits
   */
  public truncateConversationHistory(
    messages: ClaraMessage[],
    maxTokens: number = 32000
  ): ClaraMessage[] {
    const CHARS_PER_TOKEN = 4;
    const maxChars = maxTokens * CHARS_PER_TOKEN;
    
    let totalChars = 0;
    const truncatedMessages: ClaraMessage[] = [];
    
    // Process messages in reverse order (keep most recent)
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageChars = message.content.length;
      
      if (totalChars + messageChars <= maxChars) {
        truncatedMessages.unshift(message);
        totalChars += messageChars;
      } else {
        break;
      }
    }
    
    console.log(`📝 Truncated conversation: ${messages.length} -> ${truncatedMessages.length} messages (${totalChars} chars)`);
    return truncatedMessages;
  }
}

// Export singleton instance
export const chatManager = new ChatManager();
