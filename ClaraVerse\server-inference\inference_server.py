#!/usr/bin/env python3
"""
🧠 Serveur d'inférence centralisé WeMa IA
Gère les requêtes d'inférence pour tous les clients
"""

import os
import json
import logging
import requests
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
INFERENCE_PORT = int(os.getenv('INFERENCE_PORT', '1235'))
LM_STUDIO_URL = os.getenv('LM_STUDIO_URL', 'http://localhost:1234')
MAX_CONCURRENT_REQUESTS = int(os.getenv('MAX_CONCURRENT_REQUESTS', '5'))

# Statistiques
stats = {
    "total_requests": 0,
    "active_requests": 0,
    "total_tokens": 0,
    "start_time": datetime.now().isoformat()
}

# Semaphore pour limiter les requêtes concurrent
request_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

app = FastAPI(title="WeMa IA Inference Server", version="1.0.0")

# CORS pour permettre les requêtes depuis les clients
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    model: str = "auto"
    messages: List[Dict[str, Any]]
    stream: bool = False
    temperature: float = 0.7
    max_tokens: int = 4000
    top_p: float = 0.9
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0

class InferenceStats(BaseModel):
    total_requests: int
    active_requests: int
    total_tokens: int
    start_time: str
    lm_studio_status: str
    max_concurrent: int

def is_lm_studio_available() -> bool:
    """Vérifier si LM Studio est disponible"""
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", timeout=3)
        return response.status_code == 200
    except:
        return False

def get_available_models() -> List[Dict[str, Any]]:
    """Obtenir la liste des modèles disponibles"""
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", timeout=5)
        if response.status_code == 200:
            return response.json().get("data", [])
    except Exception as e:
        logger.error(f"Erreur récupération modèles: {e}")
    return []

@app.get("/health")
async def health_check():
    """Health check du serveur d'inférence"""
    lm_studio_status = "online" if is_lm_studio_available() else "offline"
    
    return {
        "status": "healthy",
        "lm_studio_status": lm_studio_status,
        "lm_studio_url": LM_STUDIO_URL,
        "active_requests": stats["active_requests"],
        "total_requests": stats["total_requests"],
        "max_concurrent": MAX_CONCURRENT_REQUESTS
    }

@app.get("/v1/models")
async def list_models():
    """Proxy pour lister les modèles disponibles"""
    if not is_lm_studio_available():
        raise HTTPException(status_code=503, detail="LM Studio not available")
    
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"Erreur liste modèles: {e}")
        raise HTTPException(status_code=503, detail=f"Error fetching models: {str(e)}")

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatRequest, raw_request: Request):
    """Endpoint principal pour les requêtes de chat"""
    
    # Vérifier LM Studio
    if not is_lm_studio_available():
        raise HTTPException(status_code=503, detail="LM Studio not available")
    
    # Limiter les requêtes concurrentes
    async with request_semaphore:
        stats["active_requests"] += 1
        stats["total_requests"] += 1
        
        try:
            # Obtenir l'IP du client pour les logs
            client_ip = raw_request.client.host if raw_request.client else "unknown"
            logger.info(f"🚀 Requête inférence de {client_ip} - Modèle: {request.model}")
            
            # Préparer la requête pour LM Studio
            lm_studio_payload = {
                "model": request.model,
                "messages": request.messages,
                "stream": request.stream,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "top_p": request.top_p,
                "frequency_penalty": request.frequency_penalty,
                "presence_penalty": request.presence_penalty
            }
            
            # Faire la requête vers LM Studio
            if request.stream:
                return await handle_streaming_request(lm_studio_payload, client_ip)
            else:
                return await handle_non_streaming_request(lm_studio_payload, client_ip)
                
        except Exception as e:
            logger.error(f"❌ Erreur inférence pour {client_ip}: {e}")
            raise HTTPException(status_code=500, detail=f"Inference error: {str(e)}")
        finally:
            stats["active_requests"] -= 1

async def handle_streaming_request(payload: Dict[str, Any], client_ip: str) -> StreamingResponse:
    """Gérer les requêtes en streaming"""
    
    async def stream_generator():
        try:
            response = requests.post(
                f"{LM_STUDIO_URL}/v1/chat/completions",
                json=payload,
                headers={'Content-Type': 'application/json'},
                stream=True,
                timeout=120
            )
            response.raise_for_status()
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # Remove 'data: ' prefix
                        if data_str.strip() == '[DONE]':
                            yield f"data: [DONE]\n\n"
                            break
                        try:
                            # Valider que c'est du JSON valide
                            json.loads(data_str)
                            yield f"data: {data_str}\n\n"
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"❌ Erreur streaming pour {client_ip}: {e}")
            error_data = {
                "error": {
                    "message": f"Streaming error: {str(e)}",
                    "type": "inference_error"
                }
            }
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        stream_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*"
        }
    )

async def handle_non_streaming_request(payload: Dict[str, Any], client_ip: str) -> Dict[str, Any]:
    """Gérer les requêtes non-streaming"""
    
    try:
        response = requests.post(
            f"{LM_STUDIO_URL}/v1/chat/completions",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=120
        )
        response.raise_for_status()
        
        result = response.json()
        
        # Mettre à jour les statistiques
        if "usage" in result and "total_tokens" in result["usage"]:
            stats["total_tokens"] += result["usage"]["total_tokens"]
        
        logger.info(f"✅ Inférence réussie pour {client_ip}")
        return result
        
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Erreur requête LM Studio pour {client_ip}: {e}")
        raise HTTPException(status_code=503, detail=f"LM Studio error: {str(e)}")

@app.get("/stats")
async def get_stats() -> InferenceStats:
    """Obtenir les statistiques du serveur"""
    lm_studio_status = "online" if is_lm_studio_available() else "offline"
    
    return InferenceStats(
        total_requests=stats["total_requests"],
        active_requests=stats["active_requests"],
        total_tokens=stats["total_tokens"],
        start_time=stats["start_time"],
        lm_studio_status=lm_studio_status,
        max_concurrent=MAX_CONCURRENT_REQUESTS
    )

@app.get("/clients")
async def list_connected_clients():
    """Liste des clients connectés (simplifié)"""
    # Dans une vraie implémentation, on trackrait les connexions WebSocket
    return {
        "active_requests": stats["active_requests"],
        "total_requests": stats["total_requests"],
        "note": "Client tracking requires WebSocket implementation"
    }

if __name__ == "__main__":
    import uvicorn
    
    print(f"🧠 Démarrage serveur d'inférence WeMa IA")
    print(f"📡 Port: {INFERENCE_PORT}")
    print(f"🔗 LM Studio: {LM_STUDIO_URL}")
    print(f"👥 Max concurrent: {MAX_CONCURRENT_REQUESTS}")
    
    # Vérifier LM Studio au démarrage
    if is_lm_studio_available():
        models = get_available_models()
        print(f"✅ LM Studio détecté avec {len(models)} modèles")
    else:
        print("⚠️ LM Studio non détecté - Démarrez LM Studio avant d'utiliser le serveur")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=INFERENCE_PORT,
        log_level="info"
    )
