#!/usr/bin/env python3
"""
Test des corrections appliquées
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_corrections():
    print("🔧 Test des Corrections GDPR")
    print("=" * 40)
    
    # Test avec du texte contenant des entités
    test_text = """
    CV - <PERSON>
    Email: <EMAIL>
    Téléphone: +33 6 12 34 56 78
    Adresse: 123 rue de la Paix, 75001 Paris
    Carte de crédit: 4532-1234-5678-9012
    """
    
    print("1. 🛡️ Test GDPR Preview...")
    try:
        preview_data = {
            "text": test_text,
            "language": "en"
        }
        
        response = requests.post(f"{BASE_URL}/gdpr/preview", json=preview_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Preview réussi: {len(result.get('entities', []))} entités détectées")
            
            for entity in result.get('entities', []):
                print(f"      - {entity.get('entity_type', 'UNKNOWN')}: '{entity.get('text', '')}' (confiance: {entity.get('confidence', 0):.2f})")
        else:
            print(f"   ❌ Preview échoué: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur Preview: {e}")
        return False
    
    print("\n2. 🔒 Test GDPR Anonymisation...")
    try:
        anonymize_data = {
            "text": test_text,
            "language": "en",
            "preserve_layout": True
        }
        
        response = requests.post(f"{BASE_URL}/gdpr/anonymize", json=anonymize_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Anonymisation réussie")
            print(f"   📄 Texte original: {test_text.strip()[:100]}...")
            print(f"   🔒 Texte anonymisé: {result.get('anonymized_text', 'N/A')[:100]}...")
            print(f"   🗂️ Mappings: {len(result.get('mappings', {}))}")
            print(f"   🎯 Highlighted ranges: {len(result.get('highlighted_ranges', []))}")
            
            # Vérifier la structure des highlighted_ranges
            highlighted_ranges = result.get('highlighted_ranges', [])
            if highlighted_ranges:
                print("   📊 Structure des highlighted_ranges:")
                for i, range_item in enumerate(highlighted_ranges[:3]):  # Afficher les 3 premiers
                    print(f"      [{i}] Type: {range_item.get('entityType', 'UNKNOWN')}")
                    print(f"          Original: '{range_item.get('originalText', '')}'")
                    print(f"          Replacement: '{range_item.get('replacement', '')}'")
                    print(f"          Position: {range_item.get('start', 0)}-{range_item.get('end', 0)}")
            
            return True
        else:
            print(f"   ❌ Anonymisation échouée: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur Anonymisation: {e}")
        return False

def main():
    success = test_corrections()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 TOUTES LES CORRECTIONS FONCTIONNENT !")
        print("✅ Le système GDPR est maintenant stable")
        print("✅ L'interface peut afficher les résultats sans erreur")
        print("✅ Les highlighted_ranges sont correctement formatés")
        print("\n🚀 Vous pouvez maintenant utiliser l'interface sans problème !")
    else:
        print("❌ Il reste des problèmes à corriger")
    
    print("\n📋 Interface: http://localhost:5174")
    print("🔧 Backend: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
