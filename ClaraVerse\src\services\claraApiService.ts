/**
 * 🔄 Clara Assistant API Service - Proxy vers Architecture Refactorisée
 * 
 * Ce fichier maintient la compatibilité avec l'ancien code tout en 
 * déléguant vers la nouvelle architecture modulaire.
 * 
 * ⚠️ DÉPRÉCIÉ : Utilisez directement les nouveaux services dans /api/
 */

import type { 
  ClaraMessage, 
  ClaraFileAttachment, 
  ClaraProvider, 
  ClaraModel, 
  ClaraAIConfig
} from '../types/clara_assistant_types';

// Import du nouveau service refactorisé
import { claraApiService as newApiService } from './api/ClaraApiService';

/**
 * 🔄 Classe proxy pour maintenir la compatibilité avec l'ancien code
 */
export class ClaraApiService {
  
  constructor() {
    console.log('🔄 ClaraApiService (legacy proxy) → Délégation vers architecture refactorisée');
  }

  // ============================================================================
  // MÉTHODES PRINCIPALES - Délégation vers le nouveau service
  // ============================================================================

  public async sendChatMessage(
    message: string,
    config: ClaraAIConfig,
    attachments?: ClaraFileAttachment[],
    systemPrompt?: string,
    conversationHistory?: ClaraMessage[],
    onContentChunk?: (content: string) => void,
    sessionId?: string
  ): Promise<ClaraMessage> {
    return newApiService.sendChatMessage(
      message,
      config,
      attachments,
      systemPrompt,
      conversationHistory,
      onContentChunk,
      sessionId
    );
  }

  public async getProviders(): Promise<ClaraProvider[]> {
    return newApiService.getProviders();
  }

  public async getModels(providerId?: string): Promise<ClaraModel[]> {
    return newApiService.getModels(providerId);
  }

  public async getCurrentProviderModels(): Promise<ClaraModel[]> {
    return newApiService.getCurrentProviderModels();
  }

  public async getPrimaryProvider(): Promise<ClaraProvider | null> {
    return newApiService.getPrimaryProvider();
  }

  public updateProvider(provider: ClaraProvider): void {
    return newApiService.updateProvider(provider);
  }

  public async processFileAttachments(
    attachments: ClaraFileAttachment[]
  ): Promise<ClaraFileAttachment[]> {
    return newApiService.processFileAttachments(attachments);
  }

  public async preloadModel(config: ClaraAIConfig): Promise<void> {
    return newApiService.preloadModel(config);
  }

  public clearCache(): void {
    return newApiService.clearCache();
  }

  public async checkProviderHealth(provider: ClaraProvider): Promise<boolean> {
    return newApiService.checkProviderHealth(provider);
  }

  /**
   * @deprecated Utilisez checkProviderHealth()
   */
  public async testProvider(provider: ClaraProvider): Promise<boolean> {
    console.warn('⚠️ testProvider() est déprécié. Utilisez checkProviderHealth()');
    return this.checkProviderHealth(provider);
  }

  // ============================================================================
  // MÉTHODES DÉPRÉCIÉES - Compatibilité
  // ============================================================================

  /**
   * @deprecated Utilisez processFileAttachments()
   */
  public async uploadFile(): Promise<any> {
    console.warn('⚠️ uploadFile() est déprécié. Utilisez processFileAttachments()');
    throw new Error('Méthode dépréciée. Utilisez processFileAttachments()');
  }

  /**
   * @deprecated Utilisez les nouveaux services
   */
  public async deleteFile(): Promise<any> {
    console.warn('⚠️ deleteFile() est déprécié');
    throw new Error('Méthode dépréciée');
  }

  /**
   * @deprecated Utilisez le système RAG modulaire
   */
  public async searchDocuments(): Promise<any> {
    console.warn('⚠️ searchDocuments() est déprécié. Utilisez le système RAG modulaire');
    throw new Error('Méthode dépréciée. Utilisez le système RAG modulaire');
  }

  // ============================================================================
  // UTILITAIRES
  // ============================================================================

  public getMigrationInfo(): {
    isLegacyProxy: boolean;
    newServicePath: string;
    recommendedMigration: string;
  } {
    return {
      isLegacyProxy: true,
      newServicePath: './services/api/ClaraApiService',
      recommendedMigration: 'import { claraApiService } from "./services/api"'
    };
  }

  public getServiceStats(): any {
    return {
      ...newApiService.getServiceStats(),
      isLegacyProxy: true,
      proxyVersion: '1.0.0'
    };
  }

  /**
   * 🛑 Arrêter la génération en cours
   */
  public stop(): void {
    console.log('🛑 Stop generation requested via legacy proxy');

    // Déléguer vers le nouveau service
    newApiService.stop();
  }
}

// ============================================================================
// EXPORT POUR COMPATIBILITÉ
// ============================================================================

/**
 * Instance singleton pour compatibilité avec l'ancien code
 */
export const claraApiService = new ClaraApiService();

/**
 * Export par défaut pour compatibilité
 */
export default claraApiService;
