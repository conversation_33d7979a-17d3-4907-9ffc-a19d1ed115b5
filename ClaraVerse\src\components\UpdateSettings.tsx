/**
 * ⚙️ Paramètres de mise à jour
 * Interface pour configurer le comportement des mises à jour
 */

import React, { useState, useEffect } from 'react';
import { Setting<PERSON>, Clock, Zap, Hand, AlertTriangle } from 'lucide-react';
import { autoUpdateService, UpdateConfig } from '../services/AutoUpdateService';

const UpdateSettings: React.FC = () => {
  const [config, setConfig] = useState<UpdateConfig>(autoUpdateService.getConfig());
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setConfig(autoUpdateService.getConfig());
  }, []);

  const handleConfigChange = (key: keyof UpdateConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleMaintenanceWindowChange = (field: 'start' | 'end', value: string) => {
    const newWindow = { ...config.maintenanceWindow, [field]: value };
    handleConfigChange('maintenanceWindow', newWindow);
  };

  const saveConfig = () => {
    autoUpdateService.updateConfig(config);
    setHasChanges(false);
    
    // Notification de sauvegarde
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      z-index: 10000;
    `;
    notification.textContent = '✅ Configuration sauvegardée';
    document.body.appendChild(notification);
    
    setTimeout(() => notification.remove(), 3000);
  };

  const resetConfig = () => {
    const defaultConfig: UpdateConfig = {
      autoUpdate: true,
      updateMode: 'auto',
      maintenanceWindow: { start: '02:00', end: '06:00' },
      forceUpdate: false,
      gracePeriod: 30
    };
    setConfig(defaultConfig);
    setHasChanges(true);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow">
      <div className="flex items-center mb-6">
        <Settings className="w-6 h-6 text-blue-600 mr-3" />
        <h2 className="text-2xl font-bold text-gray-900">Paramètres de mise à jour</h2>
      </div>

      <div className="space-y-6">
        {/* Mode de mise à jour */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Mode de mise à jour
          </label>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="radio"
                name="updateMode"
                value="manual"
                checked={config.updateMode === 'manual'}
                onChange={(e) => handleConfigChange('updateMode', e.target.value)}
                className="mr-3"
              />
              <Hand className="w-5 h-5 text-gray-500 mr-2" />
              <div>
                <div className="font-medium">Manuel</div>
                <div className="text-sm text-gray-500">
                  L'utilisateur décide quand mettre à jour
                </div>
              </div>
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                name="updateMode"
                value="auto"
                checked={config.updateMode === 'auto'}
                onChange={(e) => handleConfigChange('updateMode', e.target.value)}
                className="mr-3"
              />
              <Zap className="w-5 h-5 text-green-500 mr-2" />
              <div>
                <div className="font-medium">Automatique</div>
                <div className="text-sm text-gray-500">
                  Mise à jour immédiate dès qu'elle est disponible
                </div>
              </div>
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                name="updateMode"
                value="scheduled"
                checked={config.updateMode === 'scheduled'}
                onChange={(e) => handleConfigChange('updateMode', e.target.value)}
                className="mr-3"
              />
              <Clock className="w-5 h-5 text-blue-500 mr-2" />
              <div>
                <div className="font-medium">Programmé</div>
                <div className="text-sm text-gray-500">
                  Mise à jour pendant la fenêtre de maintenance
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Fenêtre de maintenance */}
        {config.updateMode === 'scheduled' && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Fenêtre de maintenance
            </label>
            <div className="flex items-center space-x-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Début</label>
                <input
                  type="time"
                  value={config.maintenanceWindow?.start || '02:00'}
                  onChange={(e) => handleMaintenanceWindowChange('start', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Fin</label>
                <input
                  type="time"
                  value={config.maintenanceWindow?.end || '06:00'}
                  onChange={(e) => handleMaintenanceWindowChange('end', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Les mises à jour seront appliquées uniquement pendant cette période
            </p>
          </div>
        )}

        {/* Période de grâce */}
        {config.updateMode !== 'manual' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Période de grâce avant redémarrage
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="300"
                step="15"
                value={config.gracePeriod}
                onChange={(e) => handleConfigChange('gracePeriod', parseInt(e.target.value))}
                className="flex-1"
              />
              <span className="text-sm font-medium text-gray-700 min-w-[80px]">
                {config.gracePeriod} secondes
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Temps laissé à l'utilisateur pour sauvegarder son travail
            </p>
          </div>
        )}

        {/* Options avancées */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Options avancées</h3>
          
          <div className="space-y-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.forceUpdate}
                onChange={(e) => handleConfigChange('forceUpdate', e.target.checked)}
                className="mr-3"
              />
              <AlertTriangle className="w-5 h-5 text-orange-500 mr-2" />
              <div>
                <div className="font-medium">Forcer les mises à jour</div>
                <div className="text-sm text-gray-500">
                  Ignorer les préférences utilisateur pour les mises à jour critiques
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Informations actuelles */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">État actuel</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>Mode: <span className="font-medium">{config.updateMode}</span></div>
            <div>Mises à jour automatiques: <span className="font-medium">{config.autoUpdate ? 'Activées' : 'Désactivées'}</span></div>
            {config.updateMode === 'scheduled' && (
              <div>
                Fenêtre de maintenance: <span className="font-medium">
                  {config.maintenanceWindow?.start} - {config.maintenanceWindow?.end}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Boutons d'action */}
        <div className="flex justify-between pt-6 border-t">
          <button
            onClick={resetConfig}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Réinitialiser
          </button>
          
          <div className="space-x-3">
            <button
              onClick={saveConfig}
              disabled={!hasChanges}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Sauvegarder
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdateSettings;
