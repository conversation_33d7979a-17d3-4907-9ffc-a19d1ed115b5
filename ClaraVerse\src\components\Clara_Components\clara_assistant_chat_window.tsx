/**
 * Clara Assistant Chat Window Component
 * 
 * This component serves as the main chat interface for the <PERSON> assistant.
 * It displays the conversation history, handles message rendering, and manages
 * the chat window state including scrolling, loading states, and empty states.
 * 
 * Features:
 * - Message history display with virtualization for performance
 * - Smooth auto-scrolling like Claude/ChatGPT
 * - Loading states and indicators
 * - Empty state with welcome message
 * - Content chunking for large messages
 * - Message interaction handling
 * - Session management
 */

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { 
  MessageCircle, 
  Sparkles, 
  FileText, 
  Image as ImageIcon, 
  Code,
  Search,
  Bot,
  ArrowDown,
  RefreshCw,
  Loader2,
  Brain,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

// Import types and components
import { 
  ClaraMessage, 
  ClaraChatWindowProps,
  ClaraProcessingState
} from '../../types/clara_assistant_types';
import ClaraMessageBubble from './clara_assistant_message_bubble';
import SearchProgressInline from './SearchProgressInline';
import { useSmoothScroll } from '../../hooks/useSmoothScroll';

// 🚀 OPTIMISATION: Composant memoized pour éviter les re-renders inutiles
const MemoizedMessageBubble = React.memo(ClaraMessageBubble, (prevProps, nextProps) => {
  // Ne re-render que si le contenu, l'état streaming ou les props importantes changent
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.metadata?.isStreaming === nextProps.message.metadata?.isStreaming &&
    prevProps.userName === nextProps.userName &&
    prevProps.isEditable === nextProps.isEditable
  );
});

/**
 * Virtual scrolling configuration
 */
const VIRTUAL_CONFIG = {
  ESTIMATED_MESSAGE_HEIGHT: 150, // Estimated height per message in pixels
  BUFFER_SIZE: 5, // Number of extra messages to render above/below visible area
  CONTAINER_PADDING: 48, // Top/bottom padding in pixels
  SCROLL_DEBOUNCE: 16, // Scroll event debounce in ms (~60fps)
  OVERSCAN: 2 // Additional messages to render for smoother scrolling
};

/**
 * Content chunking configuration for large messages
 */
const CONTENT_CONFIG = {
  CHUNK_SIZE: 2000, // Characters per chunk
  INITIAL_CHUNKS: 2, // Number of chunks to show initially
  EXPAND_THRESHOLD: 5000, // Show "Show More" if content is longer than this
};

/**
 * Smooth auto-scroll configuration (Claude/ChatGPT style)
 */
const SCROLL_CONFIG = {
  STREAMING_INTERVAL: 100, // How often to auto-scroll during streaming (ms)
  SCROLL_DURATION: 300, // Duration of smooth scroll animation (ms)
  EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Smooth easing function
  SCROLL_THRESHOLD: 100, // Pixels from bottom to consider "at bottom"
  STREAMING_DELAY: 50, // Delay before starting auto-scroll during streaming
};

/**
 * Virtual message item interface
 */
interface VirtualMessageItem {
  message: ClaraMessage;
  index: number;
  top: number;
  height: number;
  isVisible: boolean;
}

/**
 * Content chunk interface for large message content
 */
interface ContentChunk {
  id: string;
  content: string;
  isVisible: boolean;
}

/**
 * Chunked Message Content Component
 * Handles large content by breaking it into chunks
 */
const ChunkedMessageContent: React.FC<{
  message: ClaraMessage;
  userName?: string;
  isEditable?: boolean;
  onCopy?: (content: string) => void;
  onRetry?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
}> = ({ message, userName, isEditable, onCopy, onRetry, onEdit }) => {
  const [expandedChunks, setExpandedChunks] = useState<Set<string>>(new Set());
  const [showAll, setShowAll] = useState(false);

  // Check if content needs chunking
  const needsChunking = message.content.length > CONTENT_CONFIG.EXPAND_THRESHOLD;
  
  // Create chunks if needed
  const chunks: ContentChunk[] = useMemo(() => {
    if (!needsChunking) {
      return [{
        id: `${message.id}-full`,
        content: message.content,
        isVisible: true
      }];
    }

    const chunkArray: ContentChunk[] = [];
    const content = message.content;
    
    for (let i = 0; i < content.length; i += CONTENT_CONFIG.CHUNK_SIZE) {
      const chunkContent = content.slice(i, i + CONTENT_CONFIG.CHUNK_SIZE);
      const chunkIndex = Math.floor(i / CONTENT_CONFIG.CHUNK_SIZE);
      
      chunkArray.push({
        id: `${message.id}-chunk-${chunkIndex}`,
        content: chunkContent,
        isVisible: chunkIndex < CONTENT_CONFIG.INITIAL_CHUNKS || showAll
      });
    }
    
    return chunkArray;
  }, [message.content, message.id, needsChunking, showAll]);

  // Handle expand/collapse
  const handleToggleExpand = useCallback(() => {
    setShowAll(!showAll);
  }, [showAll]);

  // For streaming messages, always show all content
  const isStreaming = message.metadata?.isStreaming;
  const chunksToShow = isStreaming || !needsChunking ? chunks : chunks.filter(chunk => chunk.isVisible);

  return (
    <div className="space-y-2">
      {/* Render visible chunks */}
      {chunksToShow.map((chunk, index) => (
        <div key={chunk.id} className={index > 0 ? "pt-2" : ""}>
          <ClaraMessageBubble
            message={{
              ...message,
              content: chunk.content,
              id: chunk.id
            }}
            userName={userName}
            isEditable={isEditable && index === 0} // Only first chunk is editable
            onCopy={onCopy}
            onRetry={index === 0 ? onRetry : undefined} // Only first chunk can retry
            onEdit={index === 0 ? onEdit : undefined} // Only first chunk can edit
          />
        </div>
      ))}
      
      {/* Show More/Less button */}
      {needsChunking && !isStreaming && (
        <div className="flex justify-center mt-3">
          <button
            onClick={handleToggleExpand}
            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                Show More ({Math.ceil((message.content.length - (CONTENT_CONFIG.INITIAL_CHUNKS * CONTENT_CONFIG.CHUNK_SIZE)) / CONTENT_CONFIG.CHUNK_SIZE)} more sections)
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * Virtualized Message List Component
 * Only renders visible messages plus a buffer for performance
 */
const VirtualizedMessageList: React.FC<{
  messages: ClaraMessage[];
  userName?: string;
  containerHeight: number;
  scrollTop: number;
  onMessageAction: (action: string, messageId: string, data?: any) => void;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}> = ({ 
  messages, 
  userName, 
  containerHeight, 
  scrollTop, 
  onMessageAction,
  messagesEndRef 
}) => {
  const [measuredHeights, setMeasuredHeights] = useState<Map<string, number>>(new Map());
  const measurementRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // Measure message heights for more accurate virtualization
  const measureMessage = useCallback((messageId: string, element: HTMLDivElement | null) => {
    if (element) {
      measurementRefs.current.set(messageId, element);
      const height = element.offsetHeight;

      // 🚀 CORRECTION: Éviter la boucle infinie en vérifiant si la hauteur a changé
      setMeasuredHeights(prev => {
        const currentHeight = prev.get(messageId);
        if (currentHeight !== height) {
          const newMap = new Map(prev);
          newMap.set(messageId, height);
          return newMap;
        }
        return prev; // Pas de changement, retourner la même référence
      });
    }
  }, []);

  // Calculate virtual items with actual measured heights when available
  const virtualItems = useMemo((): VirtualMessageItem[] => {
    let currentTop = VIRTUAL_CONFIG.CONTAINER_PADDING;
    
    return messages.map((message, index) => {
      const measuredHeight = measuredHeights.get(message.id);
      // Increase estimated height for long messages
      const contentLength = message.content.length;
      const estimatedHeight = contentLength > CONTENT_CONFIG.EXPAND_THRESHOLD 
        ? Math.min(800, Math.max(VIRTUAL_CONFIG.ESTIMATED_MESSAGE_HEIGHT, contentLength / 10))
        : VIRTUAL_CONFIG.ESTIMATED_MESSAGE_HEIGHT;
      
      const height = measuredHeight || estimatedHeight;
      
      const item: VirtualMessageItem = {
        message,
        index,
        top: currentTop,
        height,
        isVisible: false
      };
      
      currentTop += height + 20; // 20px gap between messages
      return item;
    });
  }, [messages, measuredHeights]);

  // Calculate total height for scrollbar
  const totalHeight = virtualItems.length > 0 
    ? virtualItems[virtualItems.length - 1].top + virtualItems[virtualItems.length - 1].height + VIRTUAL_CONFIG.CONTAINER_PADDING
    : VIRTUAL_CONFIG.CONTAINER_PADDING * 2;

  // Determine which messages are visible
  const visibleItems = useMemo(() => {
    const visibleTop = scrollTop;
    const visibleBottom = scrollTop + containerHeight;
    
    return virtualItems.filter(item => {
      const itemBottom = item.top + item.height;
      return itemBottom >= visibleTop - (VIRTUAL_CONFIG.BUFFER_SIZE * VIRTUAL_CONFIG.ESTIMATED_MESSAGE_HEIGHT) &&
             item.top <= visibleBottom + (VIRTUAL_CONFIG.BUFFER_SIZE * VIRTUAL_CONFIG.ESTIMATED_MESSAGE_HEIGHT);
    });
  }, [virtualItems, scrollTop, containerHeight]);

  // Message action handlers
  const handleCopyMessage = useCallback((content: string) => {
    onMessageAction('copy', '', content);
  }, [onMessageAction]);

  const handleRetryMessage = useCallback((messageId: string) => {
    onMessageAction('retry', messageId);
  }, [onMessageAction]);

  const handleEditMessage = useCallback((messageId: string, newContent: string) => {
    onMessageAction('edit', messageId, newContent);
  }, [onMessageAction]);

  return (
    <div style={{ height: totalHeight, position: 'relative' }}>
      {visibleItems.map(({ message, top, height }) => (
        <div
          key={message.id}
          style={{
            position: 'absolute',
            top: top,
            left: 0,
            right: 0,
            minHeight: height
          }}
          ref={(el) => measureMessage(message.id, el)}
        >
          <div className="mb-5">
            <ClaraMessageBubble
              message={message}
              userName={userName}
              isEditable={message.role === 'user'}
              onCopy={handleCopyMessage}
              onRetry={handleRetryMessage}
              onEdit={handleEditMessage}
            />
          </div>
        </div>
      ))}
      
      {/* Messages end marker */}
      <div 
        ref={messagesEndRef}
        style={{
          position: 'absolute',
          top: totalHeight - VIRTUAL_CONFIG.CONTAINER_PADDING,
          height: 1,
          width: '100%'
        }}
      />
    </div>
  );
};

// 🚀 SYSTÈME D'AUTO-SCROLL SUPPRIMÉ - Scroll 100% natif maintenant

/**
 * Welcome screen component displayed when there are no messages
 */
const WelcomeScreen: React.FC<{
  userName?: string;
  onStartChat?: () => void;
}> = ({ userName, onStartChat }) => {
  const suggestions = [
    {
      icon: FileText,
      title: "Analyser des documents",
      description: "Téléchargez des PDFs, documents ou fichiers texte pour analyse",
      action: "Téléchargez un document et posez-moi des questions dessus"
    },
    {
      icon: Search,
      title: "Recherche & Analyse",
      description: "Posez des questions complexes et obtenez des réponses détaillées",
      action: "Demandez-moi tout ce que vous aimeriez rechercher"
    }
  ];

  return (
    <div className="flex items-center justify-center h-full p-8">
      <div className="max-w-2xl text-center">
        {/* Hero Section */}
        <div className="mb-8">
          <div className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg overflow-hidden">
            <img
              src="/wema-logo-light.png?v=4"
              alt="WeMa IA"
              className="w-full h-full object-cover rounded-full dark:hidden"
            />
            <img
              src="/wema-logo-dark.png?v=4"
              alt="WeMa IA"
              className="w-full h-full object-cover rounded-full hidden dark:block"
            />
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
            Bienvenue{userName ? ` ${userName}` : ''}
          </h1>

          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            Votre assistant intelligent pour l'analyse de documents et bien plus encore.
            Téléchargez vos fichiers et commencez à poser vos questions !
          </p>

          {/* Feature highlights */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium">
              IA Multi-modale
            </span>
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium">
              Analyse de documents
            </span>
            <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium">
              Assistant intelligent
            </span>
          </div>
        </div>

        {/* Suggestions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => onStartChat?.()}
              className="p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all hover:shadow-md group text-left"
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-wema-500 rounded-lg group-hover:scale-110 transition-transform">
                  <suggestion.icon className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                    {suggestion.title}
                  </h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    {suggestion.description}
                  </p>
                  <p className="text-xs text-wema-600 dark:text-wema-400 font-medium">
                    "{suggestion.action}"
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Quick start tips */}
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <p className="mb-2">
            <strong>Astuce :</strong> Vous pouvez glisser-déposer vos fichiers directement dans le chat.
          </p>
          <p>
            WeMa IA détecte automatiquement les types de fichiers et utilise les meilleurs modèles IA pour chaque tâche.
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * Loading screen component displayed when Clara is initializing
 */
const LoadingScreen: React.FC<{
  userName?: string;
}> = ({ userName }) => {
  return (
    <div className="flex items-center justify-center h-full p-8">
      <div className="max-w-md text-center">
        {/* Loading Animation */}
        <div className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse overflow-hidden">
          <img
            src="/wema-logo-light.png"
            alt="WeMa IA"
            className="w-full h-full object-cover rounded-full dark:hidden"
          />
          <img
            src="/wema-logo-dark.png"
            alt="WeMa IA"
            className="w-full h-full object-cover rounded-full hidden dark:block"
          />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
          Initialisation de WeMa IA...
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {userName ? `Bon retour, ${userName} ! ` : ''}
          Configuration de votre assistant IA et chargement de votre historique de chat.
        </p>

        {/* Loading Steps */}
        <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-wema-500 rounded-full animate-bounce"></div>
            <span>Chargement des sessions de chat...</span>
          </div>
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <span>Initialisation des modèles IA...</span>
          </div>
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            <span>Préparation de l'espace de travail...</span>
          </div>
        </div>

        {/* Progress bar */}
        <div className="mt-6 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div className="bg-gradient-to-r from-blue-500 to-wema-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
        </div>
      </div>
    </div>
  );
};

/**
 * Scroll to bottom button component
 */
const ScrollToBottomButton: React.FC<{
  onClick: () => void;
  show: boolean;
}> = ({ onClick, show }) => {
  if (!show) return null;

  return (
    <button
      onClick={onClick}
      className="absolute bottom-6 right-6 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-3 shadow-lg hover:shadow-xl transition-all hover:scale-105 group"
    >
      <ArrowDown className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-wema-600 dark:group-hover:text-wema-400" />
    </button>
  );
};

/**
 * Processing indicator component
 */
const ProcessingIndicator: React.FC<{
  processingState: ClaraProcessingState;
  message?: string;
}> = ({ processingState, message }) => {
  const getIndicatorContent = () => {
    switch (processingState) {
      case 'processing':
        return {
          icon: <Loader2 className="w-5 h-5 animate-spin" />,
          text: message || 'thinking',
          bgColor: 'bg-blue-500'
        };
      case 'success':
        return {
          icon: <Bot className="w-5 h-5" />,
          text: 'Response generated!',
          bgColor: 'bg-green-500'
        };
      case 'error':
        return {
          icon: <Bot className="w-5 h-5" />,
          text: message || 'WeMa IA a rencontré un problème',
          bgColor: 'bg-red-500'
        };
      default:
        return null;
    }
  };

  const content = getIndicatorContent();
  if (!content) return null;

  return (
    <div className="flex justify-center mb-4">
      <div className={`flex items-center gap-2 px-4 py-2 ${content.bgColor} text-white rounded-full text-sm`}>
        {content.icon}
        <span>{content.text}</span>
      </div>
    </div>
  );
};

/**
 * Main Clara Chat Window Component
 */
const ClaraChatWindow: React.FC<ClaraChatWindowProps> = ({
  messages,
  userName,
  isLoading = false,
  isInitializing = false,
  isSearching: isSearchingProp = false,
  scrollToBottom = false,
  onRetryMessage,
  onCopyMessage,
  onEditMessage
}) => {
  // 🔍 DEBUG supprimé - Performance améliorée
  const scrollRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [processingState, setProcessingState] = useState<ClaraProcessingState>('idle');

  // Search state - pour l'animation intégrée
  const [isSearching, setIsSearching] = useState(false);
  const [lastUserMessage, setLastUserMessage] = useState<string>('');
  const [searchStep, setSearchStep] = useState<'analyzing' | 'searching' | 'extracting' | 'completed'>('analyzing');
  const [searchCompleted, setSearchCompleted] = useState(false);

  // Virtual scrolling state
  const [containerHeight, setContainerHeight] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);

  // 🚀 PLUS D'AUTO-SCROLL - Scroll 100% natif

  // Update container dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (scrollRef.current) {
        setContainerHeight(scrollRef.current.clientHeight);
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // 🚀 SCROLL HANDLER NATIF - Juste pour le bouton
  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;

    const element = scrollRef.current;
    const { scrollTop, scrollHeight, clientHeight } = element;
    const nearBottom = scrollHeight - scrollTop - clientHeight < 100;

    // Juste pour afficher/cacher le bouton scroll
    setIsNearBottom(nearBottom);
    setShowScrollButton(!nearBottom && messages.length > 0);
  }, [messages.length]);

  // 🚀 SCROLL LISTENER SIMPLIFIÉ
  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  // 🚀 SCROLL ULTRA-PRÉCIS - SEULEMENT message utilisateur avec contenu
  const prevMessagesLength = useRef(messages.length);
  const lastUserMessageId = useRef<string>('');

  useEffect(() => {
    if (!scrollRef.current) return;

    // Scroll SEULEMENT si un nouveau message utilisateur AVEC CONTENU a été ajouté
    if (messages.length > prevMessagesLength.current) {
      const lastMessage = messages[messages.length - 1];

      // CONDITIONS STRICTES pour scroll :
      // 1. Message utilisateur
      // 2. Avec contenu (pas vide)
      // 3. Nouveau ID (pas déjà traité)
      if (lastMessage?.role === 'user' &&
          lastMessage.content.trim() &&
          lastMessage.id !== lastUserMessageId.current) {

        lastUserMessageId.current = lastMessage.id;
        const scrollElement = scrollRef.current;
        setTimeout(() => {
          scrollElement.scrollTop = scrollElement.scrollHeight;
        }, 100);
      }
    }

    prevMessagesLength.current = messages.length;
  }, [messages.length]);

  // 🚀 AUCUN AUTO-SCROLL AUTOMATIQUE - Interface professionnelle stable
  // L'utilisateur contrôle 100% le scroll

  // Update processing state based on loading and messages
  useEffect(() => {
    if (isLoading) {
      setProcessingState('processing');
    } else {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage?.metadata?.error) {
        setProcessingState('error');
        setTimeout(() => setProcessingState('idle'), 3000);
      } else if (lastMessage && lastMessage.role === 'assistant') {
        setProcessingState('success');
        setTimeout(() => setProcessingState('idle'), 2000);
      } else {
        setProcessingState('idle');
      }
    }
  }, [isLoading, messages]);

  // ⚡ OPTIMISATION : Détecter les recherches SEULEMENT quand nécessaire
  useEffect(() => {
    // Seulement traiter si on commence un nouveau chargement
    if (!isLoading) return;

    // ⚡ PERFORMANCE : Éviter les calculs pendant les animations
    const timeoutId = setTimeout(() => {

    // Capturer le dernier message utilisateur pour l'animation
    const lastUserMsg = messages.filter(m => m.role === 'user').pop();
    if (lastUserMsg && lastUserMsg.content !== lastUserMessage) {
      setLastUserMessage(lastUserMsg.content);
    }

    // 🔍 NOUVELLE LOGIQUE : Vérifier si le mode recherche était vraiment activé
    let isCurrentlySearching = false;

    if (isSearchingProp) {
      // Si explicitement fourni via prop
      isCurrentlySearching = true;
    } else if (lastUserMsg) {
      // Vérifier si le message a été envoyé avec le mode recherche activé
      try {
        // Chercher les métadonnées dans le message
        const displayMetaMatch = lastUserMsg.content.match(/\[DISPLAY_META:(.*?)\]/);
        if (displayMetaMatch) {
          const metadata = JSON.parse(displayMetaMatch[1]);
          isCurrentlySearching = metadata.hasSearchMode === true;
        }
      } catch (error) {
        console.warn('Erreur parsing metadata:', error);
        // Fallback: pas d'animation si on ne peut pas détecter le mode
        isCurrentlySearching = false;
      }
    }

    // ⚡ PROTECTION CONTRE BOUCLE : Ne mettre à jour que si l'état change vraiment
    if (isSearching !== isCurrentlySearching) {
      setIsSearching(isCurrentlySearching);

      if (isCurrentlySearching) {
        console.log('🎨 Animation de recherche activée - Mode recherche détecté');
      } else {
        console.log('🎨 Animation de recherche désactivée');
        // Reset des états de recherche
        setSearchStep('analyzing');
        setSearchCompleted(false);
      }
    }

    // 🎨 DÉTECTER QUAND LE LLM COMMENCE À RÉPONDRE pour masquer l'animation
    // ⚡ PROTECTION : Seulement si on était en train de chercher ET qu'on a une vraie réponse
    if (isCurrentlySearching && !isLoading && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant' && lastMessage.content.length > 50) { // Seuil plus élevé
        console.log('🎨 LLM a commencé à répondre, masquage de l\'animation');

        // 🚀 NOUVEAU : Forcer la fermeture de l'animation si le LLM répond sans tool call
        setTimeout(() => {
          if (isSearching) {
            console.log('🎨 Animation: Fermeture forcée - LLM a répondu sans tool call');
            setIsSearching(false);
            setSearchStep('analyzing');
            setSearchCompleted(false);
          }
        }, 1000); // Petit délai pour voir la réponse arriver
      }
    }

    }, 16); // ⚡ PERFORMANCE : Délai pour éviter les conflits avec les animations CSS

    return () => clearTimeout(timeoutId);
  }, [isLoading, isSearchingProp, messages.length]); // ⚡ OPTIMISÉ : Seulement quand nouveau message ou changement d'état

  // 🎨 Écouter les événements de progression de recherche
  useEffect(() => {
    // ⚡ PROTECTION : Seulement démarrer l'animation si vraiment en recherche
    if (!isSearching) {
      // Reset des états quand pas de recherche
      setSearchStep('analyzing');
      setSearchCompleted(false);
      return;
    }

    console.log('🎨 Animation: Démarrage de la progression de recherche RÉELLE');

    // 🚀 NOUVEAU : Écouter les vrais événements de recherche
    const handleSearchEvent = (event: CustomEvent) => {
      const { step, sources, completed } = event.detail;
      console.log('🎨 Animation: Événement de recherche reçu:', { step, sources, completed });

      if (step) {
        setSearchStep(step);
      }

      if (completed) {
        setSearchCompleted(true);
      }
    };

    // S'abonner aux événements de recherche
    window.addEventListener('search-progress', handleSearchEvent as EventListener);

    // 🚀 NOUVEAU : Écouter l'événement de fermeture forcée
    const handleForceClose = () => {
      console.log('🎨 Animation: Fermeture forcée via événement');
      setIsSearching(false);
      setSearchStep('analyzing');
      setSearchCompleted(false);
    };
    window.addEventListener('search-force-close', handleForceClose);

    return () => {
      window.removeEventListener('search-progress', handleSearchEvent as EventListener);
      window.removeEventListener('search-force-close', handleForceClose);
    };
  }, [isSearching]); // Garder isSearching ici car c'est un useEffect séparé

  // Handle message actions
  const handleMessageAction = useCallback((action: string, messageId: string, data?: any) => {
    switch (action) {
      case 'copy':
        onCopyMessage?.(data);
        break;
      case 'retry':
        onRetryMessage?.(messageId);
        break;
      case 'edit':
        onEditMessage?.(messageId, data);
        break;
    }
  }, [onCopyMessage, onRetryMessage, onEditMessage]);

  // Force scroll to bottom - SIMPLE
  const forceScrollToBottom = useCallback(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, []);

  // 🚀 VIRTUALISATION DÉSACTIVÉE temporairement pour corriger l'affichage
  const shouldUseVirtualization = false; // Désactivé jusqu'à correction

  return (
    <div
      ref={scrollRef}
      className="flex-1 overflow-y-auto p-6 relative"
      style={{
        scrollBehavior: 'smooth',
        overscrollBehavior: 'none',
        scrollSnapType: 'none',
        WebkitOverflowScrolling: 'touch',
        maxHeight: 'calc(100vh - 180px)', // 🚀 LIMITE STRICTE PROFESSIONNELLE
        height: 'calc(100vh - 180px)', // 🚀 HAUTEUR FIXE STRUCTURÉE
        paddingBottom: '80px' // 🚀 ESPACE MINIMAL - LIMITE SCROLL LIBRE ENCORE PLUS HAUTE
      }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Loading screen when Clara is initializing */}
        {isInitializing ? (
          <LoadingScreen userName={userName} />
        ) : /* Welcome screen when no messages */ messages.length === 0 ? (
          <WelcomeScreen userName={userName} />
        ) : isLoading && messages.length === 0 ? (
          // Skeleton loader pour les premiers messages
          <div className="space-y-5 animate-pulse">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex space-x-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : shouldUseVirtualization ? (
          // Use virtualized rendering for large message lists
          <VirtualizedMessageList
            messages={messages}
            userName={userName}
            containerHeight={containerHeight}
            scrollTop={scrollTop}
            onMessageAction={handleMessageAction}
            messagesEndRef={messagesEndRef}
          />
        ) : (
          // Use normal rendering - TOUS LES MESSAGES VISIBLES
          <div className="space-y-5">
            {/* Message list - Optimisé pour 58 messages avec React.memo */}
            {messages.map((message) => (
              <MemoizedMessageBubble
                key={message.id}
                message={message}
                userName={userName}
                isEditable={message.role === 'user'}
                onCopy={(content) => handleMessageAction('copy', '', content)}
                onRetry={(messageId) => handleMessageAction('retry', messageId)}
                onEdit={(messageId, newContent) => handleMessageAction('edit', messageId, newContent)}
              />
            ))}

            {/* Animation de recherche intégrée - Design épuré et centré */}
            {isSearching && (
              <div className="flex justify-center mb-5 w-full">
                <SearchProgressInline
                  isVisible={isSearching}
                  query={lastUserMessage || ''}
                  currentStep={searchStep}
                  searchCompleted={searchCompleted}
                  onComplete={() => {
                    console.log('🎨 Animation de recherche terminée par onComplete');
                    setIsSearching(false);
                    setSearchStep('analyzing');
                    setSearchCompleted(false);
                  }}
                  className="w-full"
                />
              </div>
            )}

            {/* Processing indicator - seulement pour les autres types de traitement */}
            {processingState === 'processing' && !isSearching && (
              <ProcessingIndicator
                processingState={processingState}
                message=""
              />
            )}

            {/* Invisible element to track end of messages */}
            <div ref={messagesEndRef} />

            {/* 🚀 ESPACE POUR LA BARRE DE CHAT FIXE - Minimal pour limite scroll optimale */}
            <div className="h-32" />
          </div>
        )}
        
        {/* Processing indicator for virtualized view */}
        {shouldUseVirtualization && (
          <div style={{ position: 'relative', zIndex: 1 }}>
            <ProcessingIndicator 
              processingState={processingState}
              message={
                processingState === 'processing' 
                  ? '' 
                  : undefined
              }
            />
          </div>
        )}
      </div>

      {/* Scroll to bottom button */}
      <ScrollToBottomButton 
        show={showScrollButton}
        onClick={forceScrollToBottom}
      />
    </div>
  );
};

export default ClaraChatWindow; 