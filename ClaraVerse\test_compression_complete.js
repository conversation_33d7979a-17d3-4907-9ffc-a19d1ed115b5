/**
 * 🧪 Test complet du système de compression IA
 * 
 * Teste toutes les fonctionnalités de compression avec données réelles
 */

// Import du scénario de test
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { testConversation } = require('./test_compression_scenario.js');

// Simulation des classes nécessaires
class MockAssistantAPIClient {
  constructor(baseUrl, options) {
    this.baseUrl = baseUrl;
    this.options = options;
  }

  async sendChat(model, messages, options) {
    // Simulation d'une réponse de Qwen 8B
    const mockResponse = {
      message: {
        content: JSON.stringify({
          conversationSummary: "Conversation détaillée entre <PERSON> et un assistant IA pour préparer un entretien d'embauche. Jean a partagé son CV de développeur Full-Stack senior (8 ans d'expérience) et a bénéficié d'une analyse complète, suivie d'une simulation d'entretien technique avec un CTO de fintech. La conversation couvre l'analyse du CV, les conseils d'amélioration, la préparation aux questions techniques (architecture, performance, sécurité), une simulation d'entretien réaliste, et des conseils sur la négociation salariale et les questions comportementales.",
          
          chronologicalFlow: "1. Demande d'aide pour CV et entretien → 2. Partage du CV Jean Dupont (développeur senior) → 3. Analyse détaillée du CV avec points forts et suggestions → 4. Préparation spécifique pour entretien fintech → 5. Simulation complète d'entretien technique avec CTO → 6. Questions sur architecture, performance, leadership → 7. Transition vers questions RH et motivation → 8. Conseils post-entretien et négociation → 9. Préparation entretien comportemental avec méthode STAR",
          
          keyDecisions: [
            "Analyse du CV : points forts identifiés (expérience progressive, stack moderne, impact quantifié)",
            "Recommandations CV : ajouter certifications, détailler réalisations, inclure soft skills",
            "Stratégie entretien fintech : focus sécurité, performance, conformité réglementaire",
            "Choix architectural : event sourcing avec CQRS pour système de paiement",
            "Solution rate limiting : Redis Cluster avec sliding window algorithm",
            "Gestion d'équipe : processus qualité renforcés, mentoring pour juniors",
            "Négociation salariale : fourchette 75-85K€, package global, timing post-intérêt"
          ],
          
          importantFacts: [
            "Jean Dupont : 8 ans d'expérience, développeur Full-Stack senior",
            "Expérience : TechCorp (2020-présent), StartupTech (2018-2020), WebAgency (2016-2018)",
            "Compétences : React, Node.js, AWS, PostgreSQL, architecture microservices",
            "Réalisations : 500K+ utilisateurs, amélioration 40% performance, équipe 5 développeurs",
            "Entretien cible : Lead Developer fintech, équipe 8 personnes",
            "Congé sabbatique 2019 : 6 mois Asie + formation développement mobile",
            "Prétentions : 75-85K€, disponibilité septembre, préférence hybride"
          ],
          
          criticalMessageIds: ["msg-003", "msg-004", "msg-021", "msg-022", "msg-023"],
          
          documentsContext: {
            documentsUsed: ["CV_Jean_Dupont.pdf"],
            documentsSummary: "CV complet de Jean Dupont, développeur Full-Stack senior avec 8 ans d'expérience. Profil technique solide avec progression claire : WebAgency → StartupTech → TechCorp. Expertise React/Node.js, architecture cloud AWS, leadership équipe. Formation Master Informatique Paris-Saclay. Projets personnels et contributions open source. Langues : français natif, anglais courant.",
            keyExtracts: [
              "Senior Full-Stack Developer chez TechCorp : 500K+ utilisateurs, amélioration 40% performance",
              "Lead Developer chez StartupTech : création plateforme SaaS 0 à 10K utilisateurs",
              "Compétences : React, Vue.js, Node.js, AWS, PostgreSQL, MongoDB, Docker, Kubernetes",
              "Formation : Master Informatique Paris-Saclay, Licence Paris-Sud",
              "Projets : EcoTracker (5K téléchargements), DevTools Extension (2K utilisateurs)"
            ]
          }
        })
      }
    };

    console.log(`🤖 Mock Qwen 8B appelé avec ${messages.length} messages`);
    console.log(`📝 Prompt système : ${messages[0].content.substring(0, 100)}...`);
    console.log(`📊 Conversation à compresser : ${messages[1].content.length} caractères`);
    
    // Simuler un délai de traitement
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return mockResponse;
  }
}

// Mock du module AssistantAPIClient
const mockModule = {
  AssistantAPIClient: MockAssistantAPIClient
};

// Simulation de l'IntelligentCompressor avec mock
class TestIntelligentCompressor {
  constructor() {
    this.client = new MockAssistantAPIClient('http://localhost:1234', { apiKey: '', providerId: 'lmstudio' });
    this.COMPRESSION_MODEL = 'qwen3-8b';
    this.TRIGGER_THRESHOLD = 20000;
    this.compressionCache = new Map();
  }

  analyzeCompressionNeed(messages, modelContextWindow = 100000) {
    const currentSize = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    
    console.log(`🔍 Analyse compression: ${currentSize} chars, seuil: ${this.TRIGGER_THRESHOLD}`);

    if (currentSize < this.TRIGGER_THRESHOLD) {
      return {
        shouldCompress: false,
        reason: `Taille sous le seuil (${currentSize}/${this.TRIGGER_THRESHOLD} chars)`,
        currentSize,
        targetSize: currentSize,
        modelContextWindow,
        compressionUrgency: 'none',
        preservationPriority: []
      };
    }

    const modelLimitChars = modelContextWindow * 4;
    const usageRatio = currentSize / modelLimitChars;
    
    let urgency, targetSize;
    
    if (usageRatio > 0.9) {
      urgency = 'immediate';
      targetSize = Math.floor(modelLimitChars * 0.6);
    } else if (usageRatio > 0.7) {
      urgency = 'background';
      targetSize = Math.floor(modelLimitChars * 0.5);
    } else {
      urgency = 'background';
      targetSize = Math.floor(currentSize * 0.7);
    }

    return {
      shouldCompress: true,
      reason: `Taille: ${currentSize} chars (${(usageRatio * 100).toFixed(1)}% capacité modèle)`,
      currentSize,
      targetSize,
      modelContextWindow,
      compressionUrgency: urgency,
      preservationPriority: ['documents', 'decisions', 'recent_messages', 'chronological_flow']
    };
  }

  async compressConversation(messages, config) {
    console.log(`🧠 Compression IA: ${messages.length} messages → cible ${config.targetContextWindow} tokens`);

    const compressionId = `comp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const compressionPrompt = this.buildCompressionPrompt(messages, config);
      
      const response = await this.client.sendChat(
        this.COMPRESSION_MODEL,
        [
          {
            role: 'system',
            content: this.getCompressionSystemPrompt()
          },
          {
            role: 'user',
            content: compressionPrompt
          }
        ],
        {
          temperature: 0.1,
          max_tokens: config.targetContextWindow
        }
      );

      const compressedContext = this.parseCompressionResponse(
        response.message?.content || '',
        messages,
        config,
        compressionId
      );

      console.log(`✅ Compression terminée: ${messages.length} → ${compressedContext.metadata.compressionRatio.toFixed(2)}x`);

      return compressedContext;

    } catch (error) {
      console.error('❌ Erreur compression IA:', error);
      return this.fallbackCompression(messages, config, compressionId);
    }
  }

  buildCompressionPrompt(messages, config) {
    const conversationText = messages.map((msg, index) => {
      const timestamp = msg.timestamp.toLocaleString('fr-FR');
      const messageId = msg.id;
      return `[MESSAGE ${index + 1}] [${msg.role.toUpperCase()}] [ID: ${messageId}] [${timestamp}]
${msg.content}`;
    }).join('\n\n---\n\n');

    const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    const targetChars = config.targetContextWindow * 4;
    const compressionRatio = targetChars / totalChars;

    return `🧠 MISSION CRITIQUE: COMPRESSION INTELLIGENTE DE CONVERSATION

📊 CONTEXTE:
- Messages à analyser: ${messages.length}
- Taille actuelle: ${totalChars} caractères
- Fenêtre contexte modèle: ${config.targetContextWindow} tokens
- Cible compression: ${targetChars} caractères (ratio: ${compressionRatio.toFixed(2)})
- Éléments prioritaires: ${config.preserveElements.join(', ')}

📋 CONVERSATION COMPLÈTE:
${conversationText}

🎯 RÉPONSE REQUISE (JSON STRICT):
{
  "conversationSummary": "Résumé narratif complet",
  "chronologicalFlow": "Chronologie des événements",
  "keyDecisions": ["Décision 1", "Décision 2"],
  "importantFacts": ["Fait 1", "Fait 2"],
  "criticalMessageIds": ["${messages[messages.length - 5]?.id || ''}", "${messages[messages.length - 1]?.id || ''}"],
  "documentsContext": {
    "documentsUsed": ["nom_document.ext"],
    "documentsSummary": "Résumé des documents",
    "keyExtracts": ["Extrait important"]
  }
}`;
  }

  getCompressionSystemPrompt() {
    return `Tu es un expert en compression intelligente de conversations pour IA. Ta mission est CRITIQUE pour maintenir la continuité parfaite d'une conversation.

🎯 OBJECTIF PRINCIPAL:
Compresser une conversation en préservant ABSOLUMENT TOUT ce qui est important pour qu'un LLM puisse continuer la conversation naturellement.

📋 FORMAT DE SORTIE:
Réponds UNIQUEMENT en JSON valide avec cette structure exacte.

🚨 ATTENTION: La qualité de ta compression détermine si la conversation peut continuer naturellement. Sois PARFAIT.`;
  }

  parseCompressionResponse(response, originalMessages, config, compressionId) {
    try {
      const parsed = JSON.parse(response);
      
      const criticalMessages = originalMessages.filter(msg => 
        parsed.criticalMessageIds?.includes(msg.id)
      );

      const recentMessages = originalMessages.slice(-5);

      const originalChars = originalMessages.reduce((sum, msg) => sum + msg.content.length, 0);
      const compressedChars = response.length;
      const compressionRatio = originalChars / compressedChars;

      return {
        conversationSummary: parsed.conversationSummary || '',
        chronologicalFlow: parsed.chronologicalFlow || '',
        keyDecisions: parsed.keyDecisions || [],
        importantFacts: parsed.importantFacts || [],
        criticalMessages,
        recentMessages,
        documentsContext: {
          documentsUsed: parsed.documentsContext?.documentsUsed || [],
          documentsSummary: parsed.documentsContext?.documentsSummary || '',
          keyExtracts: parsed.documentsContext?.keyExtracts || []
        },
        metadata: {
          originalMessages: originalMessages.length,
          originalCharacters: originalChars,
          compressedCharacters: compressedChars,
          compressionRatio,
          modelContextWindow: config.targetContextWindow,
          compressionTimestamp: new Date(),
          compressionId,
          preservationStrategy: 'ai_intelligent'
        }
      };
    } catch (error) {
      console.warn('⚠️ Erreur parsing réponse compression, utilisation fallback');
      return this.fallbackCompression(originalMessages, config, compressionId);
    }
  }

  fallbackCompression(messages, config, compressionId) {
    const recentMessages = messages.slice(-10);
    const criticalMessages = messages.slice(-5);
    
    const originalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    const compressedChars = recentMessages.reduce((sum, msg) => sum + msg.content.length, 0);
    
    return {
      conversationSummary: `Conversation de ${messages.length} messages (compression de fallback automatique)`,
      chronologicalFlow: 'Compression automatique - fil chronologique non analysé',
      keyDecisions: ['Compression automatique appliquée'],
      importantFacts: ['Système de fallback utilisé'],
      criticalMessages,
      recentMessages,
      documentsContext: {
        documentsUsed: [],
        documentsSummary: 'Aucun document analysé en mode fallback',
        keyExtracts: []
      },
      metadata: {
        originalMessages: messages.length,
        originalCharacters: originalChars,
        compressedCharacters: compressedChars,
        compressionRatio: originalChars / compressedChars,
        modelContextWindow: config.targetContextWindow,
        compressionTimestamp: new Date(),
        compressionId,
        preservationStrategy: 'fallback_automatic'
      }
    };
  }

  reconstructContext(compressed) {
    const reconstructedMessages = [];

    // Messages de résumé
    if (compressed.conversationSummary) {
      reconstructedMessages.push({
        id: `summary-${compressed.metadata.compressionId}`,
        role: 'assistant',
        content: `📋 **Résumé de conversation** (${compressed.metadata.originalMessages} messages compressés):\n\n${compressed.conversationSummary}`,
        timestamp: compressed.metadata.compressionTimestamp,
        metadata: { isCompressed: true, compressionId: compressed.metadata.compressionId }
      });
    }

    if (compressed.chronologicalFlow) {
      reconstructedMessages.push({
        id: `chronology-${compressed.metadata.compressionId}`,
        role: 'assistant',
        content: `🕒 **Fil chronologique:**\n${compressed.chronologicalFlow}`,
        timestamp: compressed.metadata.compressionTimestamp,
        metadata: { isCompressed: true, compressionId: compressed.metadata.compressionId }
      });
    }

    if (compressed.keyDecisions.length > 0) {
      reconstructedMessages.push({
        id: `decisions-${compressed.metadata.compressionId}`,
        role: 'assistant',
        content: `🎯 **Décisions importantes:**\n${compressed.keyDecisions.map(decision => `• ${decision}`).join('\n')}`,
        timestamp: compressed.metadata.compressionTimestamp,
        metadata: { isCompressed: true, compressionId: compressed.metadata.compressionId }
      });
    }

    if (compressed.documentsContext.documentsUsed.length > 0) {
      reconstructedMessages.push({
        id: `documents-${compressed.metadata.compressionId}`,
        role: 'assistant',
        content: `📚 **Documents utilisés:** ${compressed.documentsContext.documentsUsed.join(', ')}\n\n**Résumé:** ${compressed.documentsContext.documentsSummary}`,
        timestamp: compressed.metadata.compressionTimestamp,
        metadata: { isCompressed: true, compressionId: compressed.metadata.compressionId }
      });
    }

    // Messages critiques et récents
    reconstructedMessages.push(...compressed.criticalMessages);
    reconstructedMessages.push(...compressed.recentMessages);

    console.log(`🔧 Contexte reconstruit: ${reconstructedMessages.length} messages (${compressed.metadata.compressionRatio.toFixed(2)}x compression)`);

    return reconstructedMessages;
  }
}

// Test principal
async function testCompressionComplete() {
  console.log('🧪 DÉBUT DU TEST COMPLET DE COMPRESSION IA\n');

  const compressor = new TestIntelligentCompressor();
  
  // 1. Test d'analyse
  console.log('📊 1. TEST D\'ANALYSE DE COMPRESSION');
  const analysis = compressor.analyzeCompressionNeed(testConversation, 100000);
  console.log('Résultat analyse:', analysis);
  console.log('');

  if (!analysis.shouldCompress) {
    console.log('❌ Erreur: La conversation devrait déclencher une compression');
    return;
  }

  // 2. Test de compression
  console.log('🧠 2. TEST DE COMPRESSION IA');
  const config = {
    triggerThreshold: 20000,
    targetContextWindow: 100000,
    compressionModel: 'qwen3-8b',
    preserveElements: analysis.preservationPriority,
    backgroundProcessing: false
  };

  const compressed = await compressor.compressConversation(testConversation, config);
  console.log('Métadonnées compression:', compressed.metadata);
  console.log('');

  // 3. Test de reconstruction
  console.log('🔧 3. TEST DE RECONSTRUCTION');
  const reconstructed = compressor.reconstructContext(compressed);
  console.log(`Messages reconstruits: ${reconstructed.length}`);
  console.log('');

  // 4. Analyse des résultats
  console.log('📈 4. ANALYSE DES RÉSULTATS');
  console.log(`Original: ${compressed.metadata.originalMessages} messages, ${compressed.metadata.originalCharacters} chars`);
  console.log(`Compressé: ${reconstructed.length} messages, ${compressed.metadata.compressedCharacters} chars`);
  console.log(`Ratio compression: ${compressed.metadata.compressionRatio.toFixed(2)}x`);
  console.log(`Stratégie: ${compressed.metadata.preservationStrategy}`);
  console.log('');

  // 5. Vérification qualité
  console.log('✅ 5. VÉRIFICATION QUALITÉ');
  const hasDocuments = compressed.documentsContext.documentsUsed.length > 0;
  const hasDecisions = compressed.keyDecisions.length > 0;
  const hasSummary = compressed.conversationSummary.length > 100;
  const hasFlow = compressed.chronologicalFlow.length > 50;

  console.log(`Documents préservés: ${hasDocuments ? '✅' : '❌'}`);
  console.log(`Décisions extraites: ${hasDecisions ? '✅' : '❌'}`);
  console.log(`Résumé substantiel: ${hasSummary ? '✅' : '❌'}`);
  console.log(`Fil chronologique: ${hasFlow ? '✅' : '❌'}`);

  const qualityScore = [hasDocuments, hasDecisions, hasSummary, hasFlow].filter(Boolean).length;
  console.log(`Score qualité: ${qualityScore}/4`);

  console.log('\n🎉 TEST COMPLET TERMINÉ !');
  
  return {
    analysis,
    compressed,
    reconstructed,
    qualityScore
  };
}

// Exécution du test
if (import.meta.url === `file://${process.argv[1]}`) {
  testCompressionComplete().catch(console.error);
}

export { testCompressionComplete, TestIntelligentCompressor };
