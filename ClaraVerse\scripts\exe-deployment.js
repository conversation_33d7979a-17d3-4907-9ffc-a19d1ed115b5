#!/usr/bin/env node
/**
 * 🎯 Déploiement EXE Parfait
 * Génère des EXE séparés qui marchent du premier coup
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuration finale
const EXE_CONFIG = {
  server: {
    ip: '*************',
    poles: {
      'patrimoine': { port: 8000, name: 'Gestion Patrimoine' },
      'mandats': { port: 8010, name: 'Mandats A' },
      'rh': { port: 8020, name: 'Ressources Humaines' }
    }
  }
};

class ExeDeployment {
  constructor() {
    this.buildDir = 'exe-builds';
  }

  /**
   * 🎛️ Créer EXE Admin (pour toi)
   */
  async createAdminExe() {
    console.log('🎛️ Création EXE Admin...');
    
    const adminDir = path.join(this.buildDir, 'admin');
    this.ensureDir(adminDir);
    
    // 1. <PERSON><PERSON>r le code
    this.copyBaseCode(adminDir);
    
    // 2. Configuration admin avec auto-connexion
    this.createAdminConfig(adminDir);
    
    // 3. Modifier App.tsx pour admin
    this.createAdminApp(adminDir);
    
    // 4. Build Electron
    await this.buildElectronApp(adminDir, 'admin', 'WeMa IA Admin');
    
    console.log('✅ EXE Admin créé dans exe-builds/admin/dist/');
  }

  /**
   * 🏢 Créer EXE Serveur (backend complet)
   */
  async createServerExe() {
    console.log('🏢 Création EXE Serveur...');
    
    const serverDir = path.join(this.buildDir, 'server');
    this.ensureDir(serverDir);
    
    // 1. Copier tout le code
    this.copyBaseCode(serverDir);
    
    // 2. Configuration serveur
    this.createServerConfig(serverDir);
    
    // 3. Créer app serveur multi-pôles
    this.createServerApp(serverDir);
    
    // 4. Build Electron avec backend intégré
    await this.buildElectronApp(serverDir, 'server', 'WeMa IA Serveur');
    
    console.log('✅ EXE Serveur créé dans exe-builds/server/dist/');
  }

  /**
   * 👥 Créer EXE Utilisateurs (par pôle)
   */
  async createUserExes() {
    console.log('👥 Création EXE Utilisateurs...');
    
    for (const [poleId, config] of Object.entries(EXE_CONFIG.server.poles)) {
      await this.createUserExe(poleId, config);
    }
    
    console.log('✅ EXE Utilisateurs créés');
  }

  /**
   * Créer un EXE utilisateur pour un pôle
   */
  async createUserExe(poleId, config) {
    console.log(`👥 Création EXE ${config.name}...`);
    
    const userDir = path.join(this.buildDir, 'users', poleId);
    this.ensureDir(userDir);
    
    // 1. Copier le frontend seulement
    this.copyFrontendCode(userDir);
    
    // 2. Configuration utilisateur avec auto-connexion
    this.createUserConfig(userDir, poleId, config);
    
    // 3. Modifier App.tsx pour utilisateur
    this.createUserApp(userDir, poleId, config);
    
    // 4. Build Electron
    await this.buildElectronApp(userDir, `user-${poleId}`, `WeMa IA - ${config.name}`);
    
    console.log(`✅ EXE ${config.name} créé dans exe-builds/users/${poleId}/dist/`);
  }

  /**
   * Configuration admin avec auto-connexion
   */
  createAdminConfig(adminDir) {
    // .env pour admin
    const adminEnv = {
      REACT_APP_MODE: 'admin',
      REACT_APP_AUTO_CONNECT: 'true',
      REACT_APP_SERVER_IP: EXE_CONFIG.server.ip,
      REACT_APP_ADMIN_PORT: '9000'
    };
    
    this.writeEnvFile(adminDir, adminEnv);
    
    // Configuration Electron pour admin
    this.createElectronConfig(adminDir, {
      appId: 'com.wema-ia.admin',
      productName: 'WeMa IA Admin',
      icon: 'admin-icon.ico',
      width: 1400,
      height: 900
    });
  }

  /**
   * Configuration serveur
   */
  createServerConfig(serverDir) {
    // .env pour serveur
    const serverEnv = {
      REACT_APP_MODE: 'server',
      REACT_APP_AUTO_START_BACKENDS: 'true',
      REACT_APP_SERVER_IP: EXE_CONFIG.server.ip,
      REACT_APP_POLES: JSON.stringify(EXE_CONFIG.server.poles)
    };
    
    this.writeEnvFile(serverDir, serverEnv);
    
    // Configuration backend
    const backendEnv = {
      CLARA_HOST: '0.0.0.0',
      AUTO_START_ALL_POLES: 'true',
      POLES_CONFIG: JSON.stringify(EXE_CONFIG.server.poles)
    };
    
    this.writeEnvFile(path.join(serverDir, 'py_backend'), backendEnv);
    
    // Configuration Electron pour serveur
    this.createElectronConfig(serverDir, {
      appId: 'com.wema-ia.server',
      productName: 'WeMa IA Serveur',
      icon: 'server-icon.ico',
      width: 1200,
      height: 800
    });
  }

  /**
   * Configuration utilisateur avec auto-connexion
   */
  createUserConfig(userDir, poleId, config) {
    // Générer un ID unique pour cette machine
    const machineId = crypto.randomBytes(8).toString('hex');
    
    // .env pour utilisateur
    const userEnv = {
      REACT_APP_MODE: 'user',
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://${EXE_CONFIG.server.ip}:${config.port}`,
      REACT_APP_AUTO_CONNECT: 'true',
      REACT_APP_MACHINE_ID: machineId
    };
    
    this.writeEnvFile(userDir, userEnv);
    
    // Configuration Electron pour utilisateur
    this.createElectronConfig(userDir, {
      appId: `com.wema-ia.${poleId}`,
      productName: `WeMa IA - ${config.name}`,
      icon: `${poleId}-icon.ico`,
      width: 1200,
      height: 800
    });
  }

  /**
   * Créer App.tsx pour admin
   */
  createAdminApp(adminDir) {
    const adminAppCode = `
import React, { useEffect, useState } from 'react';
import AdminDashboard from './components/AdminDashboard';
import './App.css';

function App() {
  const [connected, setConnected] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Auto-connexion admin
    const connectAdmin = async () => {
      try {
        // Vérifier si le serveur admin est accessible
        const response = await fetch('http://${EXE_CONFIG.server.ip}:9000/health');
        if (response.ok) {
          setConnected(true);
        } else {
          // Démarrer le serveur admin automatiquement
          if (window.electronAPI?.startAdminServer) {
            await window.electronAPI.startAdminServer();
            setConnected(true);
          }
        }
      } catch (error) {
        console.log('Démarrage serveur admin...');
        if (window.electronAPI?.startAdminServer) {
          await window.electronAPI.startAdminServer();
          setConnected(true);
        }
      } finally {
        setLoading(false);
      }
    };

    connectAdmin();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Démarrage interface admin...</p>
        </div>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Erreur de connexion</h1>
          <p className="text-red-500">Impossible de démarrer le serveur admin</p>
        </div>
      </div>
    );
  }

  return <AdminDashboard />;
}

export default App;
`;

    fs.writeFileSync(path.join(adminDir, 'src', 'App.tsx'), adminAppCode);
  }

  /**
   * Créer App.tsx pour serveur
   */
  createServerApp(serverDir) {
    const serverAppCode = `
import React, { useEffect, useState } from 'react';
import ServerDashboard from './components/ServerDashboard';
import './App.css';

function App() {
  const [status, setStatus] = useState({
    patrimoine: 'starting',
    mandats: 'starting',
    rh: 'starting'
  });

  useEffect(() => {
    // Auto-démarrage de tous les backends
    const startAllBackends = async () => {
      if (window.electronAPI?.startAllBackends) {
        await window.electronAPI.startAllBackends();
        
        // Vérifier le statut de chaque service
        const checkStatus = async () => {
          const newStatus = {};
          
          for (const [poleId, config] of Object.entries(${JSON.stringify(EXE_CONFIG.server.poles)})) {
            try {
              const response = await fetch(\`http://localhost:\${config.port}/health\`);
              newStatus[poleId] = response.ok ? 'online' : 'offline';
            } catch {
              newStatus[poleId] = 'offline';
            }
          }
          
          setStatus(newStatus);
        };
        
        // Vérifier toutes les 5 secondes
        setInterval(checkStatus, 5000);
        checkStatus();
      }
    };

    startAllBackends();
  }, []);

  return <ServerDashboard status={status} />;
}

export default App;
`;

    fs.writeFileSync(path.join(serverDir, 'src', 'App.tsx'), serverAppCode);
  }

  /**
   * Créer App.tsx pour utilisateur
   */
  createUserApp(userDir, poleId, config) {
    const userAppCode = `
import React, { useEffect, useState } from 'react';
import ClaraAssistant from './components/ClaraAssistant';
import './App.css';

function App() {
  const [connected, setConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Auto-connexion utilisateur
    const connectUser = async () => {
      try {
        // Générer un ID utilisateur automatique
        const machineId = process.env.REACT_APP_MACHINE_ID;
        const userId = \`\${poleId}-\${machineId.substring(0, 4)}\`;
        
        // Vérifier la connexion au serveur
        const response = await fetch('${`http://${EXE_CONFIG.server.ip}:${config.port}`}/health');
        
        if (response.ok) {
          // Enregistrer l'utilisateur automatiquement
          localStorage.setItem('wema_user_id', userId);
          localStorage.setItem('wema_pole_id', '${poleId}');
          localStorage.setItem('wema_auto_connected', 'true');
          
          setConnected(true);
        } else {
          setError('Serveur ${config.name} inaccessible');
        }
      } catch (error) {
        setError(\`Impossible de se connecter au serveur ${config.name}\`);
      } finally {
        setLoading(false);
      }
    };

    connectUser();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Connexion à ${config.name}...</p>
        </div>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Connexion impossible</h1>
          <p className="text-red-500">{error}</p>
          <p className="text-sm text-gray-500 mt-4">
            Vérifiez que le serveur WeMa IA est démarré sur ${EXE_CONFIG.server.ip}
          </p>
        </div>
      </div>
    );
  }

  return <ClaraAssistant />;
}

export default App;
`;

    fs.writeFileSync(path.join(userDir, 'src', 'App.tsx'), userAppCode);
  }

  /**
   * Créer configuration Electron
   */
  createElectronConfig(appDir, config) {
    const electronConfig = {
      main: "public/electron.js",
      homepage: "./",
      build: {
        appId: config.appId,
        productName: config.productName,
        directories: {
          output: "dist"
        },
        files: [
          "build/**/*",
          "public/electron.js",
          "py_backend/**/*"
        ],
        win: {
          icon: config.icon,
          target: "nsis"
        },
        nsis: {
          oneClick: false,
          allowToChangeInstallationDirectory: true
        }
      }
    };

    // Modifier package.json
    const packagePath = path.join(appDir, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    Object.assign(packageJson, electronConfig);
    
    // Ajouter scripts Electron
    packageJson.scripts = {
      ...packageJson.scripts,
      "electron": "electron .",
      "electron-dev": "ELECTRON_IS_DEV=true electron .",
      "build-electron": "npm run build && electron-builder"
    };

    // Ajouter dépendances Electron
    packageJson.devDependencies = {
      ...packageJson.devDependencies,
      "electron": "^latest",
      "electron-builder": "^latest"
    };

    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));

    // Créer electron.js
    this.createElectronMain(appDir, config);
  }

  /**
   * Créer le fichier electron.js principal
   */
  createElectronMain(appDir, config) {
    const electronMainCode = `
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcesses = [];

function createWindow() {
  mainWindow = new BrowserWindow({
    width: ${config.width},
    height: ${config.height},
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '${config.icon}'),
    title: '${config.productName}'
  });

  const isDev = process.env.ELECTRON_IS_DEV === 'true';
  
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
  }
}

// Démarrer serveur admin
ipcMain.handle('start-admin-server', async () => {
  try {
    const adminProcess = spawn('node', ['admin-backend/server.js'], {
      cwd: path.join(__dirname, '../'),
      stdio: 'pipe'
    });
    
    backendProcesses.push(adminProcess);
    return true;
  } catch (error) {
    console.error('Erreur démarrage admin:', error);
    return false;
  }
});

// Démarrer tous les backends
ipcMain.handle('start-all-backends', async () => {
  try {
    const poles = ${JSON.stringify(EXE_CONFIG.server.poles)};
    
    for (const [poleId, poleConfig] of Object.entries(poles)) {
      const backendProcess = spawn('python', ['main.py'], {
        cwd: path.join(__dirname, '../py_backend'),
        env: {
          ...process.env,
          CLARA_PORT: poleConfig.port,
          POLE_ID: poleId
        },
        stdio: 'pipe'
      });
      
      backendProcesses.push(backendProcess);
    }
    
    return true;
  } catch (error) {
    console.error('Erreur démarrage backends:', error);
    return false;
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Arrêter tous les processus backend
  backendProcesses.forEach(process => {
    if (process && !process.killed) {
      process.kill();
    }
  });
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
`;

    fs.writeFileSync(path.join(appDir, 'public', 'electron.js'), electronMainCode);

    // Créer preload.js
    const preloadCode = `
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  startAdminServer: () => ipcRenderer.invoke('start-admin-server'),
  startAllBackends: () => ipcRenderer.invoke('start-all-backends')
});
`;

    fs.writeFileSync(path.join(appDir, 'public', 'preload.js'), preloadCode);
  }

  /**
   * Build application Electron
   */
  async buildElectronApp(appDir, appName, productName) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(appDir);
      
      // Installer dépendances
      if (!fs.existsSync('node_modules')) {
        console.log(`📦 Installation dépendances ${appName}...`);
        execSync('npm install', { stdio: 'inherit' });
      }

      // Build React
      console.log(`⚛️ Build React ${appName}...`);
      execSync('npm run build', { stdio: 'inherit' });

      // Build Electron
      console.log(`🔨 Build Electron ${appName}...`);
      execSync('npm run build-electron', { stdio: 'inherit' });
      
    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * Utilitaires
   */
  ensureDir(dir) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true });
    }
    fs.mkdirSync(dir, { recursive: true });
  }

  copyBaseCode(targetDir) {
    const items = ['src', 'public', 'py_backend', 'package.json', 'vite.config.ts', 'tsconfig.json'];
    
    items.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(targetDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  copyFrontendCode(targetDir) {
    const items = ['src', 'public', 'package.json', 'vite.config.ts', 'tsconfig.json'];
    
    items.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(targetDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  writeEnvFile(dir, envVars) {
    const envContent = Object.entries(envVars)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(path.join(dir, '.env'), envContent);
  }

  /**
   * Créer guide d'installation
   */
  createInstallationGuide() {
    const guide = `
🎯 GUIDE D'INSTALLATION WEMA IA

📁 FICHIERS GÉNÉRÉS:
├── exe-builds/admin/dist/ → WeMa IA Admin.exe (pour toi)
├── exe-builds/server/dist/ → WeMa IA Serveur.exe (pour le serveur)
└── exe-builds/users/
    ├── patrimoine/dist/ → WeMa IA Patrimoine.exe
    ├── mandats/dist/ → WeMa IA Mandats.exe
    └── rh/dist/ → WeMa IA RH.exe

🚀 INSTALLATION:

1. SERVEUR (*************):
   - Installer "WeMa IA Serveur.exe"
   - Lancer l'application
   - Tous les backends démarrent automatiquement

2. TON POSTE:
   - Installer "WeMa IA Admin.exe"
   - Lancer l'application
   - Interface admin accessible automatiquement

3. UTILISATEURS:
   - Patrimoine: Installer "WeMa IA Patrimoine.exe"
   - Mandats: Installer "WeMa IA Mandats.exe"
   - RH: Installer "WeMa IA RH.exe"
   - Connexion automatique au serveur

✅ AVANTAGES:
- Pas de token à saisir
- Connexion automatique
- Démarrage en 1 clic
- Tout fonctionne du premier coup
- Mises à jour automatiques

🔧 DÉPANNAGE:
- Si connexion impossible: Vérifier que le serveur est démarré
- Si erreur: Redémarrer l'application
- Support: Voir les logs dans l'application
`;

    fs.writeFileSync('INSTALLATION-GUIDE.txt', guide);
  }
}

// CLI
const deployer = new ExeDeployment();
const command = process.argv[2];

switch (command) {
  case 'admin':
    deployer.createAdminExe();
    break;
    
  case 'server':
    deployer.createServerExe();
    break;
    
  case 'users':
    deployer.createUserExes();
    break;
    
  case 'all':
    deployer.createAdminExe()
      .then(() => deployer.createServerExe())
      .then(() => deployer.createUserExes())
      .then(() => {
        deployer.createInstallationGuide();
        console.log('🎉 TOUS LES EXE CRÉÉS !');
        console.log('📁 Voir exe-builds/ pour tous les EXE');
        console.log('📋 Voir INSTALLATION-GUIDE.txt');
      });
    break;
    
  default:
    console.log(`
🎯 Générateur EXE WeMa IA

Commandes:
  admin      Créer WeMa IA Admin.exe
  server     Créer WeMa IA Serveur.exe  
  users      Créer tous les EXE utilisateurs
  all        Créer tous les EXE

Usage:
  node scripts/exe-deployment.js all
`);
}
`;
