/**
 * 🔄 Service de mise à jour automatique
 * Gère les mises à jour automatiques sans intervention utilisateur
 */

import { SERVER_CONFIG } from '../config/server.config';

export interface UpdateConfig {
  autoUpdate: boolean;
  updateMode: 'manual' | 'auto' | 'scheduled';
  maintenanceWindow?: {
    start: string; // HH:MM
    end: string;   // HH:MM
  };
  forceUpdate: boolean;
  gracePeriod: number; // secondes avant redémarrage
}

export interface UpdateInfo {
  version: string;
  poleId: string;
  downloadUrl: string;
  releaseNotes: string;
  critical: boolean;
  scheduledFor?: string;
}

class AutoUpdateService {
  private updateConfig: UpdateConfig;
  private checkInterval: number = 2 * 60 * 1000; // 2 minutes
  private intervalId?: NodeJS.Timeout;
  private updateInProgress = false;

  constructor() {
    // Configuration par défaut
    this.updateConfig = {
      autoUpdate: true,
      updateMode: 'auto', // 'manual', 'auto', 'scheduled'
      maintenanceWindow: {
        start: '02:00', // 2h du matin
        end: '06:00'    // 6h du matin
      },
      forceUpdate: false,
      gracePeriod: 30 // 30 secondes
    };

    this.loadConfig();
  }

  /**
   * Charger la configuration depuis localStorage
   */
  private loadConfig(): void {
    try {
      const savedConfig = localStorage.getItem('wema_update_config');
      if (savedConfig) {
        this.updateConfig = { ...this.updateConfig, ...JSON.parse(savedConfig) };
      }
    } catch (error) {
      console.warn('Erreur chargement config mise à jour:', error);
    }
  }

  /**
   * Sauvegarder la configuration
   */
  private saveConfig(): void {
    try {
      localStorage.setItem('wema_update_config', JSON.stringify(this.updateConfig));
    } catch (error) {
      console.warn('Erreur sauvegarde config mise à jour:', error);
    }
  }

  /**
   * Configurer le mode de mise à jour
   */
  public setUpdateMode(mode: 'manual' | 'auto' | 'scheduled'): void {
    this.updateConfig.updateMode = mode;
    this.updateConfig.autoUpdate = mode !== 'manual';
    this.saveConfig();
    
    console.log(`🔄 Mode mise à jour changé: ${mode}`);
    
    if (mode === 'manual') {
      this.stopAutoCheck();
    } else {
      this.startAutoCheck();
    }
  }

  /**
   * Vérifier si on est dans la fenêtre de maintenance
   */
  private isInMaintenanceWindow(): boolean {
    if (!this.updateConfig.maintenanceWindow) return true;
    
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { start, end } = this.updateConfig.maintenanceWindow;
    
    // Gérer le cas où la fenêtre traverse minuit
    if (start > end) {
      return currentTime >= start || currentTime <= end;
    } else {
      return currentTime >= start && currentTime <= end;
    }
  }

  /**
   * Vérifier les mises à jour disponibles
   */
  public async checkForUpdates(): Promise<UpdateInfo | null> {
    try {
      const poleId = process.env.REACT_APP_POLE_ID || 'unknown';
      const updateUrl = process.env.REACT_APP_UPDATE_URL || SERVER_CONFIG.BACKEND_URL.replace(':8000', ':8001');
      
      const response = await fetch(`${updateUrl}/check-update?poleId=${poleId}`, {
        method: 'GET',
        timeout: 10000
      });

      if (!response.ok) {
        throw new Error(`Erreur serveur: ${response.status}`);
      }

      const updateInfo: UpdateInfo = await response.json();
      
      if (updateInfo.version) {
        console.log('🔄 Mise à jour disponible:', updateInfo);
        return updateInfo;
      }
      
      return null;
    } catch (error) {
      console.warn('Erreur vérification mise à jour:', error);
      return null;
    }
  }

  /**
   * Télécharger et appliquer une mise à jour automatiquement
   */
  public async applyUpdateAutomatically(updateInfo: UpdateInfo): Promise<boolean> {
    if (this.updateInProgress) {
      console.log('⚠️ Mise à jour déjà en cours');
      return false;
    }

    this.updateInProgress = true;

    try {
      console.log('🔄 Début mise à jour automatique...');
      
      // 1. Afficher notification de début
      this.showUpdateNotification('Mise à jour en cours...', 'info');
      
      // 2. Sauvegarder l'état actuel
      this.saveCurrentState();
      
      // 3. Télécharger la mise à jour
      const success = await this.downloadUpdate(updateInfo.downloadUrl);
      
      if (!success) {
        throw new Error('Échec téléchargement');
      }
      
      // 4. Période de grâce avant redémarrage
      if (this.updateConfig.gracePeriod > 0) {
        await this.showGracePeriodCountdown(this.updateConfig.gracePeriod);
      }
      
      // 5. Appliquer la mise à jour et redémarrer
      await this.applyUpdateAndRestart(updateInfo);
      
      return true;
      
    } catch (error) {
      console.error('❌ Erreur mise à jour automatique:', error);
      this.showUpdateNotification('Erreur lors de la mise à jour', 'error');
      return false;
    } finally {
      this.updateInProgress = false;
    }
  }

  /**
   * Télécharger une mise à jour
   */
  private async downloadUpdate(downloadUrl: string): Promise<boolean> {
    try {
      // En mode Electron, utiliser l'API native
      if (window.electronAPI?.downloadUpdate) {
        return await window.electronAPI.downloadUpdate(downloadUrl);
      }
      
      // En mode web, préparer le rechargement
      console.log('🌐 Mode web: préparation rechargement...');
      return true;
      
    } catch (error) {
      console.error('❌ Erreur téléchargement:', error);
      return false;
    }
  }

  /**
   * Sauvegarder l'état actuel avant mise à jour
   */
  private saveCurrentState(): void {
    try {
      const currentState = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userToken: localStorage.getItem('wema_user_token'),
        sessionData: localStorage.getItem('wema_session_data')
      };
      
      localStorage.setItem('wema_pre_update_state', JSON.stringify(currentState));
      console.log('💾 État sauvegardé avant mise à jour');
    } catch (error) {
      console.warn('Erreur sauvegarde état:', error);
    }
  }

  /**
   * Afficher le compte à rebours de la période de grâce
   */
  private async showGracePeriodCountdown(seconds: number): Promise<void> {
    return new Promise((resolve) => {
      let remaining = seconds;
      
      const countdownElement = document.createElement('div');
      countdownElement.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;
      
      const updateCountdown = () => {
        countdownElement.innerHTML = `
          <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
          <h3 style="margin: 10px 0;">Mise à jour en cours</h3>
          <p style="margin: 10px 0;">L'application va redémarrer dans:</p>
          <div style="font-size: 48px; font-weight: bold; color: #3b82f6;">${remaining}</div>
          <p style="margin: 10px 0; font-size: 14px; opacity: 0.8;">
            Sauvegardez votre travail si nécessaire
          </p>
        `;
        
        if (remaining <= 0) {
          document.body.removeChild(countdownElement);
          resolve();
        } else {
          remaining--;
          setTimeout(updateCountdown, 1000);
        }
      };
      
      document.body.appendChild(countdownElement);
      updateCountdown();
    });
  }

  /**
   * Appliquer la mise à jour et redémarrer
   */
  private async applyUpdateAndRestart(updateInfo: UpdateInfo): Promise<void> {
    try {
      // Marquer la mise à jour comme appliquée
      localStorage.setItem('wema_last_update', JSON.stringify({
        version: updateInfo.version,
        timestamp: new Date().toISOString(),
        poleId: updateInfo.poleId
      }));
      
      // En mode Electron, redémarrer l'app
      if (window.electronAPI?.restartApp) {
        window.electronAPI.restartApp();
        return;
      }
      
      // En mode web, recharger la page
      console.log('🔄 Rechargement de l\'application...');
      window.location.reload();
      
    } catch (error) {
      console.error('❌ Erreur redémarrage:', error);
      throw error;
    }
  }

  /**
   * Afficher une notification de mise à jour
   */
  private showUpdateNotification(message: string, type: 'info' | 'success' | 'error'): void {
    const colors = {
      info: '#3b82f6',
      success: '#10b981',
      error: '#ef4444'
    };
    
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type]};
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 300px;
    `;
    
    notification.innerHTML = `
      <div style="display: flex; align-items: center;">
        <span style="margin-right: 10px;">🔄</span>
        <span>${message}</span>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-suppression après 5 secondes (sauf pour les erreurs)
    if (type !== 'error') {
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 5000);
    }
  }

  /**
   * Démarrer la vérification automatique
   */
  public startAutoCheck(): void {
    if (this.updateConfig.updateMode === 'manual') {
      console.log('🔄 Mode manuel - vérification automatique désactivée');
      return;
    }

    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    this.intervalId = setInterval(async () => {
      // Vérifier seulement si pas en cours de mise à jour
      if (this.updateInProgress) return;
      
      // En mode programmé, vérifier la fenêtre de maintenance
      if (this.updateConfig.updateMode === 'scheduled' && !this.isInMaintenanceWindow()) {
        return;
      }
      
      const updateInfo = await this.checkForUpdates();
      
      if (updateInfo) {
        // Mise à jour critique ou forcée = automatique immédiate
        if (updateInfo.critical || this.updateConfig.forceUpdate) {
          await this.applyUpdateAutomatically(updateInfo);
        }
        // Sinon, selon le mode configuré
        else if (this.updateConfig.updateMode === 'auto') {
          await this.applyUpdateAutomatically(updateInfo);
        }
        // Mode programmé = attendre la fenêtre de maintenance
        else if (this.updateConfig.updateMode === 'scheduled') {
          console.log('🕐 Mise à jour programmée pour la fenêtre de maintenance');
        }
      }
    }, this.checkInterval);
    
    console.log(`🔄 Vérification automatique démarrée (mode: ${this.updateConfig.updateMode})`);
  }

  /**
   * Arrêter la vérification automatique
   */
  public stopAutoCheck(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
    console.log('🔄 Vérification automatique arrêtée');
  }

  /**
   * Obtenir la configuration actuelle
   */
  public getConfig(): UpdateConfig {
    return { ...this.updateConfig };
  }

  /**
   * Mettre à jour la configuration
   */
  public updateConfig(newConfig: Partial<UpdateConfig>): void {
    this.updateConfig = { ...this.updateConfig, ...newConfig };
    this.saveConfig();
    
    // Redémarrer la vérification avec la nouvelle config
    if (this.updateConfig.autoUpdate) {
      this.startAutoCheck();
    } else {
      this.stopAutoCheck();
    }
  }
}

// Export singleton
export const autoUpdateService = new AutoUpdateService();
