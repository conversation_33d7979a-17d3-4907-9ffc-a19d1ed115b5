/**
 * 🧪 Test LM Studio avec prompt corrigé
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

// Fonction pour nettoyer la réponse
function cleanAIResponse(response) {
  // Supprimer les balises <think> et </think>
  let cleaned = response.replace(/<think>[\s\S]*?<\/think>/g, '');
  
  // Supprimer les espaces en début/fin
  cleaned = cleaned.trim();
  
  // Chercher le JSON dans la réponse
  const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    return jsonMatch[0];
  }
  
  return cleaned;
}

async function testCompressionFixed() {
  console.log('🧠 Test compression avec prompt corrigé...');
  
  const systemPrompt = `Tu es un expert en compression de conversations. 

RÈGLES ABSOLUES:
- Réponds UNIQUEMENT en JSON valide
- Pas de balises <think> ou autres
- Pas de texte avant ou après le JSON
- Structure exacte requise

Format de réponse:
{
  "summary": "résumé de la conversation",
  "key_points": ["point 1", "point 2"],
  "documents": ["document 1"]
}`;

  const userPrompt = `Analyse cette conversation et réponds en JSON strict :

CONVERSATION:
[USER] Bonjour, j'ai un CV à analyser
[ASSISTANT] Bonjour ! Je peux vous aider à analyser votre CV
[USER] Voici mon CV : Jean Dupont, développeur avec 5 ans d'expérience en React et Node.js
[ASSISTANT] Excellent profil ! Votre expérience en React et Node.js est très recherchée

RÉPONSE REQUISE (JSON uniquement):`;

  try {
    console.log('📤 Envoi à LM Studio...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        temperature: 0.0, // Très déterministe
        max_tokens: 300,
        stream: false,
        stop: ["\n\n", "```"] // Arrêter sur certains patterns
      })
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Erreur:', response.status, errorText);
      return false;
    }
    
    const result = await response.json();
    const rawResponse = result.choices?.[0]?.message?.content || '';
    
    console.log(`⏱️ Temps de réponse: ${duration}ms`);
    console.log('📝 Réponse brute:', rawResponse);
    
    // Nettoyer la réponse
    const cleanedResponse = cleanAIResponse(rawResponse);
    console.log('🧹 Réponse nettoyée:', cleanedResponse);
    
    try {
      const parsed = JSON.parse(cleanedResponse);
      console.log('✅ JSON valide:', parsed);
      
      // Vérifier la qualité
      const hasGoodSummary = parsed.summary?.length > 20;
      const hasKeyPoints = parsed.key_points?.length > 0;
      const hasDocuments = parsed.documents?.length > 0;
      
      console.log('\n📊 Qualité de la compression:');
      console.log(`Résumé substantiel: ${hasGoodSummary ? '✅' : '❌'} (${parsed.summary?.length || 0} chars)`);
      console.log(`Points clés extraits: ${hasKeyPoints ? '✅' : '❌'} (${parsed.key_points?.length || 0} items)`);
      console.log(`Documents identifiés: ${hasDocuments ? '✅' : '❌'} (${parsed.documents?.length || 0} items)`);
      
      const qualityScore = [hasGoodSummary, hasKeyPoints, hasDocuments].filter(Boolean).length;
      console.log(`Score qualité: ${qualityScore}/3`);
      
      if (qualityScore >= 2) {
        console.log('\n🎉 COMPRESSION FONCTIONNELLE !');
        return { success: true, duration, parsed, qualityScore };
      } else {
        console.log('\n⚠️ Qualité insuffisante');
        return { success: false, reason: 'Qualité insuffisante' };
      }
      
    } catch (parseError) {
      console.log('❌ Erreur parsing JSON:', parseError.message);
      console.log('📋 Contenu à parser:', cleanedResponse);
      return { success: false, reason: 'JSON invalide' };
    }
    
  } catch (error) {
    console.error('❌ Erreur appel LM Studio:', error.message);
    return { success: false, reason: error.message };
  }
}

async function testLongConversation() {
  console.log('\n🧠 Test avec conversation plus longue...');
  
  const longConversation = `CONVERSATION LONGUE:
[USER] Salut ! J'ai besoin d'aide pour analyser mon CV et préparer un entretien d'embauche.
[ASSISTANT] Bonjour ! Je serais ravi de vous aider avec votre CV et la préparation d'entretien.
[USER] Voici mon CV : Jean Dupont, développeur Full-Stack senior avec 8 ans d'expérience. J'ai travaillé chez TechCorp sur des applications React/Node.js pour 500K+ utilisateurs. J'ai aussi dirigé une équipe de 5 développeurs et amélioré les performances de 40%.
[ASSISTANT] Excellent profil ! Votre expérience de leadership et vos réalisations quantifiées sont très impressionnantes. Pour l'entretien, préparez-vous à détailler vos projets d'architecture et votre gestion d'équipe.
[USER] J'ai un entretien chez une fintech demain. Quelles questions techniques puis-je attendre ?
[ASSISTANT] Pour une fintech, attendez-vous à des questions sur la sécurité, la performance, et la conformité réglementaire. Préparez des exemples sur l'optimisation d'APIs et la gestion de données sensibles.
[USER] Merci ! Peux-tu simuler un entretien technique ?
[ASSISTANT] Bien sûr ! Voici une question typique : "Comment optimiseriez-vous une API qui traite 5 millions de transactions par jour ?"`;

  const systemPrompt = `Tu es un expert en compression de conversations. Réponds UNIQUEMENT en JSON valide, sans balises <think> ni texte supplémentaire.

Structure requise:
{
  "conversationSummary": "résumé narratif complet",
  "chronologicalFlow": "chronologie des événements",
  "keyDecisions": ["décision 1", "décision 2"],
  "importantFacts": ["fait 1", "fait 2"],
  "documentsContext": {
    "documentsUsed": ["document 1"],
    "documentsSummary": "résumé des documents"
  }
}`;

  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Analyse cette conversation et compresse-la en JSON:\n\n${longConversation}`
          }
        ],
        temperature: 0.0,
        max_tokens: 800,
        stream: false
      })
    });
    
    if (!response.ok) {
      console.log('❌ Erreur conversation longue');
      return false;
    }
    
    const result = await response.json();
    const rawResponse = result.choices?.[0]?.message?.content || '';
    const cleanedResponse = cleanAIResponse(rawResponse);
    
    console.log('📝 Réponse conversation longue:', cleanedResponse.substring(0, 500) + '...');
    
    try {
      const parsed = JSON.parse(cleanedResponse);
      console.log('✅ Compression conversation longue réussie !');
      
      // Vérifier la structure complète
      const hasFullStructure = 
        parsed.conversationSummary &&
        parsed.chronologicalFlow &&
        parsed.keyDecisions &&
        parsed.importantFacts &&
        parsed.documentsContext;
      
      console.log(`Structure complète: ${hasFullStructure ? '✅' : '❌'}`);
      
      if (hasFullStructure) {
        console.log('🎉 COMPRESSION AVANCÉE FONCTIONNELLE !');
        return true;
      }
      
    } catch (e) {
      console.log('❌ JSON invalide pour conversation longue');
    }
    
  } catch (error) {
    console.log('❌ Erreur test conversation longue:', error.message);
  }
  
  return false;
}

// Test principal
async function runFixedTests() {
  console.log('🧪 TESTS LM STUDIO CORRIGÉS\n');
  
  // Test simple
  const simpleResult = await testCompressionFixed();
  
  if (simpleResult.success) {
    console.log('\n✅ Test simple réussi ! Passage au test avancé...');
    
    // Test conversation longue
    const longResult = await testLongConversation();
    
    console.log('\n🎯 RÉSULTATS FINAUX:');
    console.log('==================');
    
    if (longResult) {
      console.log('🎉 SYSTÈME PARFAITEMENT FONCTIONNEL !');
      console.log(`⏱️ Performance: ${simpleResult.duration}ms pour compression simple`);
      console.log(`🎯 Qualité: ${simpleResult.qualityScore}/3 sur test simple`);
      console.log('✅ Structure complète validée sur conversation longue');
      console.log('\n🚀 PRÊT POUR INTÉGRATION DANS L\'APPLICATION !');
    } else {
      console.log('⚠️ Test simple OK, mais problèmes sur conversation longue');
      console.log('🔧 Ajustements nécessaires pour les cas complexes');
    }
    
  } else {
    console.log('\n❌ Test simple échoué');
    console.log(`Raison: ${simpleResult.reason}`);
    console.log('🔧 Prompt ou configuration à ajuster');
  }
}

runFixedTests().catch(console.error);
