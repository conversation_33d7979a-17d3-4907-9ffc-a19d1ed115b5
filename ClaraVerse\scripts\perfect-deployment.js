#!/usr/bin/env node
/**
 * 🎯 Déploiement Parfait WeMa IA
 * Solution simple et qui marche à 100%
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuration finale et simple
const DEPLOYMENT_CONFIG = {
  server: {
    ip: '*************',
    adminPort: 9000,
    poles: {
      'patrimoine': { port: 8000, name: 'Gestion Patrimoine' },
      'mandats': { port: 8010, name: 'Mandats A' },
      'rh': { port: 8020, name: 'Ressources Humaines' }
    }
  },
  
  // Tokens d'accès (générés automatiquement)
  tokens: {
    admin: 'admin-master-2024',
    patrimoine: ['pat-user-01', 'pat-user-02'],
    mandats: ['mand-user-01', 'mand-user-02'],
    rh: ['rh-user-01']
  }
};

class PerfectDeployment {
  constructor() {
    this.buildDir = 'perfect-build';
  }

  /**
   * 🎯 ÉTAPE 1: Créer l'application ADMIN (pour toi uniquement)
   */
  async createAdminApp() {
    console.log('🎛️ Création Application Admin...');
    
    const adminDir = path.join(this.buildDir, 'admin');
    this.ensureDir(adminDir);
    
    // 1. Copier le code de base
    this.copyBaseCode(adminDir);
    
    // 2. Configuration admin
    const adminEnv = {
      REACT_APP_MODE: 'admin',
      REACT_APP_ADMIN_TOKEN: DEPLOYMENT_CONFIG.tokens.admin,
      REACT_APP_SERVER_IP: DEPLOYMENT_CONFIG.server.ip
    };
    
    this.writeEnvFile(adminDir, adminEnv);
    
    // 3. Créer le backend admin
    this.createAdminBackend(adminDir);
    
    // 4. Build
    await this.buildApp(adminDir, 'admin');
    
    console.log('✅ Application Admin créée');
  }

  /**
   * 🏢 ÉTAPE 2: Créer les applications SERVEUR (backend + frontend par pôle)
   */
  async createServerApps() {
    console.log('🏢 Création Applications Serveur...');
    
    for (const [poleId, config] of Object.entries(DEPLOYMENT_CONFIG.server.poles)) {
      await this.createServerApp(poleId, config);
    }
    
    console.log('✅ Applications Serveur créées');
  }

  /**
   * Créer une app serveur complète (backend + frontend)
   */
  async createServerApp(poleId, config) {
    console.log(`🏢 Création ${config.name}...`);
    
    const serverDir = path.join(this.buildDir, 'server', poleId);
    this.ensureDir(serverDir);
    
    // 1. Copier TOUT le code (backend + frontend)
    this.copyBaseCode(serverDir);
    
    // 2. Configuration pour ce pôle
    const serverEnv = {
      REACT_APP_MODE: 'user',
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://${DEPLOYMENT_CONFIG.server.ip}:${config.port}`,
      REACT_APP_TOKENS: DEPLOYMENT_CONFIG.tokens[poleId].join(',')
    };
    
    this.writeEnvFile(serverDir, serverEnv);
    
    // 3. Configuration backend
    const backendEnv = {
      POLE_ID: poleId,
      POLE_NAME: config.name,
      CLARA_HOST: '0.0.0.0',
      CLARA_PORT: config.port,
      AUTHORIZED_TOKENS: DEPLOYMENT_CONFIG.tokens[poleId].join(',')
    };
    
    this.writeEnvFile(path.join(serverDir, 'py_backend'), backendEnv);
    
    // 4. Build frontend
    await this.buildApp(serverDir, `server-${poleId}`);
    
    // 5. Créer script de démarrage serveur
    this.createServerStartScript(serverDir, poleId, config);
    
    console.log(`✅ ${config.name} créé`);
  }

  /**
   * 👥 ÉTAPE 3: Créer les applications CLIENTS (frontend seulement)
   */
  async createClientApps() {
    console.log('👥 Création Applications Clients...');
    
    for (const [poleId, config] of Object.entries(DEPLOYMENT_CONFIG.server.poles)) {
      await this.createClientApp(poleId, config);
    }
    
    console.log('✅ Applications Clients créées');
  }

  /**
   * Créer une app client (frontend seulement)
   */
  async createClientApp(poleId, config) {
    console.log(`👥 Création Client ${config.name}...`);
    
    const clientDir = path.join(this.buildDir, 'clients', poleId);
    this.ensureDir(clientDir);
    
    // 1. Copier seulement le frontend
    this.copyFrontendOnly(clientDir);
    
    // 2. Configuration client (pointe vers le serveur)
    const clientEnv = {
      REACT_APP_MODE: 'user',
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://${DEPLOYMENT_CONFIG.server.ip}:${config.port}`,
      REACT_APP_TOKENS: DEPLOYMENT_CONFIG.tokens[poleId].join(',')
    };
    
    this.writeEnvFile(clientDir, clientEnv);
    
    // 3. Build client
    await this.buildApp(clientDir, `client-${poleId}`);
    
    // 4. Créer app Electron ou script de démarrage
    this.createClientStartScript(clientDir, poleId, config);
    
    console.log(`✅ Client ${config.name} créé`);
  }

  /**
   * Utilitaires
   */
  ensureDir(dir) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true });
    }
    fs.mkdirSync(dir, { recursive: true });
  }

  copyBaseCode(targetDir) {
    const items = ['src', 'public', 'py_backend', 'package.json', 'vite.config.ts', 'tsconfig.json'];
    
    items.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(targetDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  copyFrontendOnly(targetDir) {
    const items = ['src', 'public', 'package.json', 'vite.config.ts', 'tsconfig.json'];
    
    items.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(targetDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  writeEnvFile(dir, envVars) {
    const envContent = Object.entries(envVars)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(path.join(dir, '.env'), envContent);
  }

  async buildApp(appDir, appName) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(appDir);
      
      if (!fs.existsSync('node_modules')) {
        console.log(`📦 Installation dépendances ${appName}...`);
        execSync('npm install', { stdio: 'inherit' });
      }

      console.log(`🔨 Build ${appName}...`);
      execSync('npm run build', { stdio: 'inherit' });
      
    } finally {
      process.chdir(originalDir);
    }
  }

  createAdminBackend(adminDir) {
    const backendDir = path.join(adminDir, 'admin-backend');
    fs.mkdirSync(backendDir, { recursive: true });

    const serverCode = `
const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

const POLES = ${JSON.stringify(DEPLOYMENT_CONFIG.server.poles, null, 2)};
const ADMIN_TOKEN = '${DEPLOYMENT_CONFIG.tokens.admin}';

// Middleware auth admin
const requireAdmin = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token !== ADMIN_TOKEN) {
    return res.status(401).json({ error: 'Token admin invalide' });
  }
  next();
};

// API Dashboard
app.get('/api/poles', requireAdmin, async (req, res) => {
  const status = {};
  
  for (const [poleId, config] of Object.entries(POLES)) {
    try {
      const response = await fetch(\`http://localhost:\${config.port}/health\`);
      status[poleId] = {
        ...config,
        status: response.ok ? 'online' : 'offline'
      };
    } catch {
      status[poleId] = { ...config, status: 'offline' };
    }
  }
  
  res.json(status);
});

// Contrôles
app.post('/api/restart/:poleId', requireAdmin, (req, res) => {
  console.log(\`Redémarrage \${req.params.poleId}\`);
  res.json({ success: true });
});

app.post('/api/update/:poleId', requireAdmin, (req, res) => {
  console.log(\`Mise à jour \${req.params.poleId}\`);
  res.json({ success: true });
});

app.listen(${DEPLOYMENT_CONFIG.server.adminPort}, () => {
  console.log('🎛️ Admin Dashboard: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.adminPort}');
});
`;

    fs.writeFileSync(path.join(backendDir, 'server.js'), serverCode);
    
    const packageJson = {
      name: 'wema-admin-backend',
      version: '1.0.0',
      scripts: { start: 'node server.js' },
      dependencies: { express: '^4.18.0', cors: '^2.8.5' }
    };
    
    fs.writeFileSync(path.join(backendDir, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  createServerStartScript(serverDir, poleId, config) {
    const script = `@echo off
echo 🏢 Démarrage ${config.name} (Serveur Complet)

REM Backend Python
cd py_backend
call venv\\Scripts\\activate
start "Backend-${poleId}" python main.py

REM Frontend (servir les fichiers statiques)
cd ..\\dist
start "Frontend-${poleId}" python -m http.server ${config.port + 100}

echo ✅ ${config.name} démarré
echo 🌐 Backend: http://${DEPLOYMENT_CONFIG.server.ip}:${config.port}
echo 🌐 Frontend: http://${DEPLOYMENT_CONFIG.server.ip}:${config.port + 100}
pause`;

    fs.writeFileSync(path.join(serverDir, 'start-server.bat'), script);
  }

  createClientStartScript(clientDir, poleId, config) {
    const script = `@echo off
echo 👥 Démarrage Client ${config.name}

REM Servir l'application client
cd dist
start "Client-${poleId}" python -m http.server 3000

echo ✅ Client ${config.name} démarré
echo 🌐 Application: http://localhost:3000
echo 🔗 Se connecte au serveur: http://${DEPLOYMENT_CONFIG.server.ip}:${config.port}
pause`;

    fs.writeFileSync(path.join(clientDir, 'start-client.bat'), script);
  }

  /**
   * Créer les scripts de démarrage globaux
   */
  createGlobalScripts() {
    // Script admin
    const adminScript = `@echo off
echo 🎛️ Démarrage Interface Admin

cd perfect-build\\admin\\admin-backend
npm install
start "Admin" npm start

echo ✅ Interface Admin: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.adminPort}
echo 🔑 Token Admin: ${DEPLOYMENT_CONFIG.tokens.admin}
pause`;

    fs.writeFileSync('start-admin.bat', adminScript);

    // Script serveur complet
    const serverScript = `@echo off
echo 🏢 Démarrage Serveur Complet (tous les pôles)

cd perfect-build\\server

start "Patrimoine" cmd /c "cd patrimoine && start-server.bat"
start "Mandats" cmd /c "cd mandats && start-server.bat"
start "RH" cmd /c "cd rh && start-server.bat"

echo ✅ Tous les services serveur démarrés
pause`;

    fs.writeFileSync('start-server.bat', serverScript);

    // Afficher les tokens
    this.createTokensFile();
  }

  createTokensFile() {
    const tokensInfo = `
🔑 TOKENS D'ACCÈS WEMA IA

👑 ADMINISTRATEUR:
   Token: ${DEPLOYMENT_CONFIG.tokens.admin}
   URL: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.adminPort}

🏢 GESTION PATRIMOINE:
   Tokens: ${DEPLOYMENT_CONFIG.tokens.patrimoine.join(', ')}
   URL: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.poles.patrimoine.port}

🏢 MANDATS A:
   Tokens: ${DEPLOYMENT_CONFIG.tokens.mandats.join(', ')}
   URL: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.poles.mandats.port}

🏢 RESSOURCES HUMAINES:
   Token: ${DEPLOYMENT_CONFIG.tokens.rh.join(', ')}
   URL: http://${DEPLOYMENT_CONFIG.server.ip}:${DEPLOYMENT_CONFIG.server.poles.rh.port}

📋 INSTRUCTIONS:
1. Installer le serveur complet sur ${DEPLOYMENT_CONFIG.server.ip}
2. Distribuer les apps clients aux utilisateurs
3. Chaque utilisateur se connecte avec son token
4. Admin utilise l'interface admin avec le token admin
`;

    fs.writeFileSync('TOKENS.txt', tokensInfo);
  }
}

// CLI
const deployer = new PerfectDeployment();
const command = process.argv[2];

switch (command) {
  case 'admin':
    deployer.createAdminApp().then(() => {
      console.log('✅ Application Admin créée dans perfect-build/admin/');
    });
    break;
    
  case 'server':
    deployer.createServerApps().then(() => {
      console.log('✅ Applications Serveur créées dans perfect-build/server/');
    });
    break;
    
  case 'clients':
    deployer.createClientApps().then(() => {
      console.log('✅ Applications Clients créées dans perfect-build/clients/');
    });
    break;
    
  case 'all':
    deployer.createAdminApp()
      .then(() => deployer.createServerApps())
      .then(() => deployer.createClientApps())
      .then(() => {
        deployer.createGlobalScripts();
        console.log('🎉 DÉPLOIEMENT PARFAIT TERMINÉ !');
        console.log('📁 Tout est dans perfect-build/');
        console.log('🔑 Voir TOKENS.txt pour les accès');
      });
    break;
    
  default:
    console.log(`
🎯 Déploiement Parfait WeMa IA

Commandes:
  admin      Créer l'interface admin (pour toi)
  server     Créer les apps serveur (backend + frontend)
  clients    Créer les apps clients (frontend seulement)
  all        Créer tout le déploiement

Usage:
  node scripts/perfect-deployment.js all
`);
}
`;
