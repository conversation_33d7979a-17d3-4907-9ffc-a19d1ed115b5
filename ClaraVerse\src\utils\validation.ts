/**
 * 🔍 VALIDATION SYSTÈME - WeMa IA
 * 
 * Script de validation pour vérifier que tous les composants fonctionnent correctement
 * après les optimisations de la Phase 2
 */

import { logger, LogCategory } from './logger';
import { CONFIG } from '../config/constants';

export interface ValidationResult {
  component: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

export class SystemValidator {
  private results: ValidationResult[] = [];

  /**
   * Valider l'ensemble du système
   */
  public async validateSystem(): Promise<ValidationResult[]> {
    this.results = [];
    
    logger.info(LogCategory.SYSTEM, 'Démarrage validation système complète...');
    
    // 1. Validation de la configuration
    this.validateConfiguration();
    
    // 2. Validation du logger
    this.validateLogger();
    
    // 3. Validation des services
    await this.validateServices();
    
    // 4. Validation des stores
    this.validateStores();
    
    // 5. Validation des APIs
    await this.validateAPIs();
    
    // 6. Résumé final
    this.logValidationSummary();
    
    return this.results;
  }

  /**
   * Valider la configuration unifiée
   */
  private validateConfiguration(): void {
    try {
      // Vérifier que toutes les configs sont définies
      const configs = [
        'COMPRESSION',
        'RAG', 
        'CONTEXT',
        'PERFORMANCE',
        'DOCUMENT',
        'UI',
        'DEBUG'
      ];
      
      for (const configName of configs) {
        if (!CONFIG[configName as keyof typeof CONFIG]) {
          this.addResult('error', 'Configuration', `Config ${configName} manquante`);
          return;
        }
      }
      
      // Vérifier la cohérence des seuils
      const compressionThreshold = CONFIG.COMPRESSION.TRIGGER_THRESHOLD;
      const ragFastThreshold = CONFIG.RAG.FAST_MODE.THRESHOLD;
      const ragPerformantThreshold = CONFIG.RAG.PERFORMANT_MODE.THRESHOLD;
      
      if (compressionThreshold !== 25000) {
        this.addResult('warning', 'Configuration', `Seuil compression non standard: ${compressionThreshold}`);
      }
      
      if (ragFastThreshold !== 50000 || ragPerformantThreshold !== 20000) {
        this.addResult('warning', 'Configuration', `Seuils RAG non standards: Fast=${ragFastThreshold}, Performant=${ragPerformantThreshold}`);
      }
      
      this.addResult('success', 'Configuration', 'Configuration unifiée validée');
      
    } catch (error) {
      this.addResult('error', 'Configuration', `Erreur validation config: ${error}`);
    }
  }

  /**
   * Valider le système de logs
   */
  private validateLogger(): void {
    try {
      // Tester tous les niveaux de log
      logger.error(LogCategory.SYSTEM, 'Test log error (validation)');
      logger.warn(LogCategory.SYSTEM, 'Test log warn (validation)');
      logger.info(LogCategory.SYSTEM, 'Test log info (validation)');
      logger.debug(LogCategory.SYSTEM, 'Test log debug (validation)');
      
      // Tester les logs groupés
      logger.group(LogCategory.SYSTEM, 'validation-test', 'Test log groupé');
      logger.group(LogCategory.SYSTEM, 'validation-test', 'Test log groupé (2)');
      
      // Tester les logs de performance
      const startTime = performance.now();
      setTimeout(() => {
        logger.performance(LogCategory.SYSTEM, 'Test performance', startTime);
      }, 10);
      
      this.addResult('success', 'Logger', 'Système de logs fonctionnel');
      
    } catch (error) {
      this.addResult('error', 'Logger', `Erreur validation logger: ${error}`);
    }
  }

  /**
   * Valider les services principaux
   */
  private async validateServices(): Promise<void> {
    try {
      // Vérifier que les services peuvent être importés
      const { RagManager } = await import('../services/rag/RagManager');
      const { intelligentCompressor } = await import('../services/api/context/IntelligentCompressor');
      
      // Tester RagManager
      const ragManager = new RagManager();
      if (ragManager) {
        this.addResult('success', 'RagManager', 'Service RAG initialisé');
      }
      
      // Tester IntelligentCompressor
      if (intelligentCompressor) {
        this.addResult('success', 'IntelligentCompressor', 'Service compression disponible');
      }
      
    } catch (error) {
      this.addResult('error', 'Services', `Erreur validation services: ${error}`);
    }
  }

  /**
   * Valider les stores
   */
  private validateStores(): void {
    try {
      // Vérifier DocumentStore
      import('../stores/documentStore').then(({ useDocumentStore }) => {
        const store = useDocumentStore.getState();
        
        if (store && typeof store.addDocument === 'function' && typeof store.removeDocument === 'function') {
          this.addResult('success', 'DocumentStore', 'Store documents fonctionnel');
        } else {
          this.addResult('error', 'DocumentStore', 'Méthodes store manquantes');
        }
      }).catch(error => {
        this.addResult('error', 'DocumentStore', `Erreur import store: ${error}`);
      });
      
    } catch (error) {
      this.addResult('error', 'Stores', `Erreur validation stores: ${error}`);
    }
  }

  /**
   * Valider les APIs
   */
  private async validateAPIs(): Promise<void> {
    try {
      // Vérifier ClaraApiService
      const { claraApiService } = await import('../services/api/ClaraApiService');
      
      if (claraApiService && typeof claraApiService.stop === 'function') {
        this.addResult('success', 'ClaraApiService', 'API principale fonctionnelle');
      } else {
        this.addResult('error', 'ClaraApiService', 'Méthode stop() manquante');
      }
      
      // Vérifier ChatManager
      const { ChatManager } = await import('../services/api/chat/ChatManager');
      const chatManager = new ChatManager();
      
      if (chatManager && typeof chatManager.stopGeneration === 'function') {
        this.addResult('success', 'ChatManager', 'ChatManager fonctionnel');
      } else {
        this.addResult('error', 'ChatManager', 'Méthode stopGeneration() manquante');
      }
      
    } catch (error) {
      this.addResult('error', 'APIs', `Erreur validation APIs: ${error}`);
    }
  }

  /**
   * Ajouter un résultat de validation
   */
  private addResult(status: 'success' | 'warning' | 'error', component: string, message: string, details?: any): void {
    this.results.push({ component, status, message, details });
    
    switch (status) {
      case 'success':
        logger.info(LogCategory.SYSTEM, `✅ ${component}: ${message}`, details);
        break;
      case 'warning':
        logger.warn(LogCategory.SYSTEM, `⚠️ ${component}: ${message}`, details);
        break;
      case 'error':
        logger.error(LogCategory.SYSTEM, `❌ ${component}: ${message}`, details);
        break;
    }
  }

  /**
   * Logger le résumé de validation
   */
  private logValidationSummary(): void {
    const summary = {
      total: this.results.length,
      success: this.results.filter(r => r.status === 'success').length,
      warnings: this.results.filter(r => r.status === 'warning').length,
      errors: this.results.filter(r => r.status === 'error').length
    };
    
    logger.info(LogCategory.SYSTEM, 'Résumé validation système', summary);
    
    if (summary.errors > 0) {
      logger.error(LogCategory.SYSTEM, `🚨 ${summary.errors} erreurs détectées !`);
    } else if (summary.warnings > 0) {
      logger.warn(LogCategory.SYSTEM, `⚠️ ${summary.warnings} avertissements`);
    } else {
      logger.info(LogCategory.SYSTEM, '🎉 Validation système réussie !');
    }
  }

  /**
   * Obtenir les statistiques de validation
   */
  public getValidationStats(): { success: number; warnings: number; errors: number; total: number } {
    return {
      success: this.results.filter(r => r.status === 'success').length,
      warnings: this.results.filter(r => r.status === 'warning').length,
      errors: this.results.filter(r => r.status === 'error').length,
      total: this.results.length
    };
  }
}

// Instance singleton
export const systemValidator = new SystemValidator();

// Fonction de convenance pour validation rapide
export async function validateSystem(): Promise<ValidationResult[]> {
  return systemValidator.validateSystem();
}

// Exposer en développement
if (import.meta.env.DEV) {
  (window as any).validateSystem = validateSystem;
  (window as any).systemValidator = systemValidator;
}
