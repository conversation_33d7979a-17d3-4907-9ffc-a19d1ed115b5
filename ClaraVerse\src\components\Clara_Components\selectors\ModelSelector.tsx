/**
 * 🤖 MODEL SELECTOR
 * Composant pour sélectionner les modèles IA
 */

import React, { useState } from 'react';
import { Image as ImageIcon, Bot, Zap, ChevronDown } from 'lucide-react';
import type { ClaraModel } from '../../../types/clara_assistant_types';

interface ModelSelectorProps {
  models: ClaraModel[];
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  modelType?: 'text' | 'vision' | 'code';
  currentProvider?: string;
  isLoading?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({ 
  models, 
  selectedModel, 
  onModelChange, 
  modelType = 'text', 
  currentProvider, 
  isLoading 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Filter models by provider first, then by type and capability
  const filteredModels = models.filter(model => {
    // First filter by current provider
    if (currentProvider && model.provider !== currentProvider) {
      return false;
    }

    // Then filter by capability
    if (modelType === 'vision') return model.supportsVision;
    if (modelType === 'code') return model.supportsCode;
    return model.type === 'text' || model.type === 'multimodal';
  });

  const selectedModelObj = filteredModels.find(m => m.id === selectedModel);

  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'vision': return ImageIcon;
      case 'code': return Zap;
      default: return Bot;
    }
  };

  const getModelTypeColor = (model: ClaraModel) => {
    if (model.type === 'vision' || model.supportsVision) return 'text-purple-500';
    if (model.type === 'code' || model.supportsCode) return 'text-blue-500';
    if (model.supportsTools) return 'text-green-500';
    return 'text-gray-500';
  };

  // Helper function to truncate model names intelligently
  const truncateModelName = (name: string, maxLength: number = 25) => {
    if (name.length <= maxLength) return name;
    
    // Try to keep important parts of the model name
    // Remove common prefixes/suffixes that are less important
    let truncated = name
      .replace(/^(mannix\/|huggingface\/|microsoft\/|meta-llama\/|google\/)/i, '') // Remove common prefixes
      .replace(/(-instruct|-chat|-base|-v\d+)$/i, ''); // Remove common suffixes
    
    if (truncated.length <= maxLength) return truncated;
    
    // If still too long, truncate from the end and add ellipsis
    return truncated.substring(0, maxLength - 3) + '...';
  };

  const formatModelSize = (size?: number): string => {
    if (!size) return '';
    const gb = size / (1024 * 1024 * 1024);
    if (gb >= 1) return `${gb.toFixed(1)}GB`;
    const mb = size / (1024 * 1024);
    return `${mb.toFixed(0)}MB`;
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading || filteredModels.length === 0}
        className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm bg-white/50 dark:bg-gray-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors border border-gray-300 dark:border-gray-600 w-full max-w-[220px] min-w-[180px]"
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {selectedModelObj ? (
            <>
              {React.createElement(getModelTypeIcon(modelType), {
                className: `w-4 h-4 flex-shrink-0 ${getModelTypeColor(selectedModelObj)}`
              })}
              <span className="text-gray-700 dark:text-gray-300 truncate text-left" title={selectedModelObj.name}>
                {truncateModelName(selectedModelObj.name)}
              </span>
            </>
          ) : (
            <>
              <Bot className="w-4 h-4 flex-shrink-0 text-gray-400" />
              <span className="text-gray-500 dark:text-gray-400 truncate">
                {filteredModels.length === 0 ? 'No models' : 'Select model'}
              </span>
            </>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 flex-shrink-0 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full mt-2 left-0 w-full min-w-[280px] max-w-[400px] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {filteredModels.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No {modelType} models available for this provider
            </div>
          ) : (
            filteredModels.map((model) => {
              const IconComponent = getModelTypeIcon(modelType);
              return (
                <button
                  key={model.id}
                  onClick={() => {
                    onModelChange(model.id);
                    setIsOpen(false);
                  }}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    model.id === selectedModel ? 'bg-sakura-50 dark:bg-sakura-900/20' : ''
                  }`}
                  title={model.name}
                >
                  <IconComponent className={`w-4 h-4 flex-shrink-0 ${getModelTypeColor(model)}`} />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {truncateModelName(model.name, 30)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-2">
                      <span>{model.type}</span>
                      {model.size && (
                        <>
                          <span>•</span>
                          <span>{formatModelSize(model.size)}</span>
                        </>
                      )}
                      {model.supportsTools && (
                        <>
                          <span>•</span>
                          <span className="text-green-500">Tools</span>
                        </>
                      )}
                    </div>
                  </div>
                </button>
              );
            })
          )}
        </div>
      )}
    </div>
  );
};
