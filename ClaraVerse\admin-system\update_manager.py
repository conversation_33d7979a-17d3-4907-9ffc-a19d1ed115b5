#!/usr/bin/env python3
"""
🚀 Gestionnaire de mises à jour WeMa IA
Système de déploiement sélectif par pôle/PC
"""

import os
import json
import shutil
import hashlib
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import requests

@dataclass
class FeatureConfig:
    """Configuration d'une fonctionnalité"""
    name: str
    enabled: bool
    description: str
    files: List[str]  # Fichiers concernés par cette fonctionnalité
    dependencies: List[str] = None  # Autres fonctionnalités requises

@dataclass
class UpdatePackage:
    """Package de mise à jour"""
    version: str
    target_poles: List[str]  # ['patrimoine', 'mandats', 'rh', 'admin']
    target_pcs: List[str] = None  # IPs spécifiques ou None pour tous
    features: Dict[str, bool]  # Fonctionnalités activées/désactivées
    description: str = ""
    created_at: str = ""
    files_hash: str = ""

class UpdateManager:
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.builds_dir = self.base_path / "builds"
        self.updates_dir = self.base_path / "updates"
        self.configs_dir = self.base_path / "configs"
        
        # Créer les dossiers
        for dir_path in [self.builds_dir, self.updates_dir, self.configs_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # Configuration des fonctionnalités disponibles
        self.available_features = {
            "rag_system": FeatureConfig(
                name="Système RAG",
                enabled=True,
                description="Recherche dans les documents avec IA",
                files=["src/services/rag/", "py_backend/rag_service_unified.py"]
            ),
            "ocr_processing": FeatureConfig(
                name="Traitement OCR",
                enabled=True,
                description="Reconnaissance de texte dans les documents",
                files=["py_backend/ocr_processor_premium.py", "py_backend/ocr_service.py"]
            ),
            "document_manager": FeatureConfig(
                name="Gestionnaire de documents",
                enabled=True,
                description="Interface de gestion des documents",
                files=["src/components/DocumentManager/"]
            ),
            "advanced_settings": FeatureConfig(
                name="Paramètres avancés",
                enabled=True,
                description="Configuration avancée de l'IA",
                files=["src/components/Clara_Components/AdvancedOptions.tsx"]
            ),
            "context_viewer": FeatureConfig(
                name="Visualiseur de contexte",
                enabled=False,  # Désactivé par défaut pour les utilisateurs
                description="Outil de debug pour développeurs",
                files=["src/components/Clara_Components/ContextViewer.tsx"]
            ),
            "langfuse_monitoring": FeatureConfig(
                name="Monitoring Langfuse",
                enabled=False,  # Seulement pour admin
                description="Métriques de performance IA",
                files=["src/components/LangfuseMonitor.tsx", "py_backend/langfuse_service.py"]
            ),
            "inference_dashboard": FeatureConfig(
                name="Dashboard d'inférence",
                enabled=False,  # Seulement pour admin
                description="Monitoring du serveur d'inférence",
                files=["src/components/InferenceDashboard.tsx"]
            ),
            "update_system": FeatureConfig(
                name="Système de mise à jour",
                enabled=False,  # Seulement pour admin
                description="Gestion des mises à jour",
                files=["admin-system/", "src/components/UpdateManager.tsx"]
            )
        }
        
        # Configuration par pôle
        self.pole_configs = {
            "patrimoine": {
                "features": {
                    "rag_system": True,
                    "ocr_processing": True,
                    "document_manager": True,
                    "advanced_settings": True,
                    "context_viewer": False,
                    "langfuse_monitoring": False,
                    "inference_dashboard": False,
                    "update_system": False
                }
            },
            "mandats": {
                "features": {
                    "rag_system": True,
                    "ocr_processing": True,
                    "document_manager": True,
                    "advanced_settings": True,
                    "context_viewer": False,
                    "langfuse_monitoring": False,
                    "inference_dashboard": False,
                    "update_system": False
                }
            },
            "rh": {
                "features": {
                    "rag_system": True,
                    "ocr_processing": True,
                    "document_manager": True,
                    "advanced_settings": False,  # RH n'a pas besoin des paramètres avancés
                    "context_viewer": False,
                    "langfuse_monitoring": False,
                    "inference_dashboard": False,
                    "update_system": False
                }
            },
            "admin": {
                "features": {
                    "rag_system": True,
                    "ocr_processing": True,
                    "document_manager": True,
                    "advanced_settings": True,
                    "context_viewer": True,
                    "langfuse_monitoring": True,
                    "inference_dashboard": True,
                    "update_system": True
                }
            }
        }
    
    def create_custom_build(self, 
                           pole: str, 
                           custom_features: Dict[str, bool] = None,
                           version: str = None) -> str:
        """Créer un build personnalisé pour un pôle"""
        
        if version is None:
            version = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print(f"🔨 Création build personnalisé pour {pole} v{version}")
        
        # Obtenir la configuration de base du pôle
        base_config = self.pole_configs.get(pole, self.pole_configs["patrimoine"])
        features = base_config["features"].copy()
        
        # Appliquer les personnalisations
        if custom_features:
            features.update(custom_features)
        
        # Créer le dossier de build
        build_dir = self.builds_dir / f"{pole}_v{version}"
        build_dir.mkdir(exist_ok=True)
        
        # Copier les fichiers de base
        self._copy_base_files(build_dir)
        
        # Appliquer les fonctionnalités
        self._apply_features(build_dir, features)
        
        # Créer la configuration
        config = {
            "pole": pole,
            "version": version,
            "features": features,
            "created_at": datetime.now().isoformat(),
            "build_path": str(build_dir)
        }
        
        # Sauvegarder la configuration
        config_file = self.configs_dir / f"{pole}_v{version}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Build créé: {build_dir}")
        return str(build_dir)
    
    def _copy_base_files(self, build_dir: Path):
        """Copier les fichiers de base de l'application"""
        base_files = [
            "src/",
            "py_backend/",
            "public/",
            "package.json",
            "vite.config.ts",
            "tsconfig.json",
            ".env.inference"
        ]
        
        for file_path in base_files:
            src = self.base_path / file_path
            dst = build_dir / file_path
            
            if src.exists():
                if src.is_dir():
                    shutil.copytree(src, dst, dirs_exist_ok=True)
                else:
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src, dst)
    
    def _apply_features(self, build_dir: Path, features: Dict[str, bool]):
        """Appliquer les fonctionnalités au build"""
        
        for feature_name, enabled in features.items():
            feature_config = self.available_features.get(feature_name)
            if not feature_config:
                continue
            
            print(f"  {'✅' if enabled else '❌'} {feature_config.name}")
            
            if not enabled:
                # Supprimer les fichiers de la fonctionnalité
                for file_path in feature_config.files:
                    target_path = build_dir / file_path
                    if target_path.exists():
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()
                        print(f"    Supprimé: {file_path}")
        
        # Créer le fichier de configuration des fonctionnalités
        features_config = {
            "enabled_features": {k: v for k, v in features.items() if v},
            "disabled_features": {k: v for k, v in features.items() if not v}
        }
        
        config_file = build_dir / "src" / "config" / "features.json"
        config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(features_config, f, indent=2)
    
    def create_update_package(self, 
                             poles: List[str],
                             features: Dict[str, bool] = None,
                             target_pcs: List[str] = None,
                             description: str = "") -> str:
        """Créer un package de mise à jour"""
        
        version = datetime.now().strftime("%Y%m%d_%H%M%S")
        package_name = f"update_{version}"
        
        print(f"📦 Création package de mise à jour: {package_name}")
        print(f"🎯 Cibles: {', '.join(poles)}")
        
        # Créer les builds pour chaque pôle
        builds = {}
        for pole in poles:
            build_path = self.create_custom_build(pole, features, version)
            builds[pole] = build_path
        
        # Créer le package de mise à jour
        package_dir = self.updates_dir / package_name
        package_dir.mkdir(exist_ok=True)
        
        # Créer les archives pour chaque pôle
        for pole, build_path in builds.items():
            archive_path = package_dir / f"{pole}.zip"
            self._create_archive(build_path, archive_path)
            print(f"  📁 Archive créée: {pole}.zip")
        
        # Créer le manifeste de mise à jour
        manifest = UpdatePackage(
            version=version,
            target_poles=poles,
            target_pcs=target_pcs,
            features=features or {},
            description=description,
            created_at=datetime.now().isoformat(),
            files_hash=self._calculate_package_hash(package_dir)
        )
        
        manifest_file = package_dir / "manifest.json"
        with open(manifest_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(manifest), f, indent=2, ensure_ascii=False)
        
        print(f"✅ Package créé: {package_dir}")
        return str(package_dir)
    
    def _create_archive(self, source_dir: str, archive_path: Path):
        """Créer une archive ZIP"""
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            source_path = Path(source_dir)
            for file_path in source_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(source_path)
                    zipf.write(file_path, arcname)
    
    def _calculate_package_hash(self, package_dir: Path) -> str:
        """Calculer le hash du package"""
        hasher = hashlib.sha256()
        for file_path in sorted(package_dir.rglob('*')):
            if file_path.is_file() and file_path.suffix != '.json':
                with open(file_path, 'rb') as f:
                    hasher.update(f.read())
        return hasher.hexdigest()[:16]
    
    def deploy_update(self, package_path: str, target_ips: List[str] = None):
        """Déployer une mise à jour vers les clients"""
        package_dir = Path(package_path)
        manifest_file = package_dir / "manifest.json"
        
        if not manifest_file.exists():
            raise ValueError("Manifeste de mise à jour introuvable")
        
        with open(manifest_file, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        print(f"🚀 Déploiement mise à jour v{manifest['version']}")
        
        # Si pas d'IPs spécifiées, utiliser la configuration par défaut
        if not target_ips:
            target_ips = self._get_default_ips_for_poles(manifest['target_poles'])
        
        # Déployer vers chaque client
        for ip in target_ips:
            try:
                self._deploy_to_client(ip, package_dir, manifest)
                print(f"  ✅ Déployé vers {ip}")
            except Exception as e:
                print(f"  ❌ Échec déploiement vers {ip}: {e}")
    
    def _get_default_ips_for_poles(self, poles: List[str]) -> List[str]:
        """Obtenir les IPs par défaut pour les pôles"""
        # Configuration par défaut (à adapter selon votre réseau)
        default_ips = {
            "patrimoine": ["*************", "*************"],
            "mandats": ["*************", "*************"],
            "rh": ["*************"],
            "admin": ["*************"]  # Ton PC
        }
        
        ips = []
        for pole in poles:
            ips.extend(default_ips.get(pole, []))
        
        return ips
    
    def _deploy_to_client(self, ip: str, package_dir: Path, manifest: Dict):
        """Déployer vers un client spécifique"""
        # Ici tu peux implémenter différentes méthodes :
        # 1. HTTP POST vers un endpoint de mise à jour sur le client
        # 2. Copie réseau (SMB/CIFS)
        # 3. SSH/SCP
        # 4. Service de mise à jour dédié
        
        # Exemple avec HTTP POST
        try:
            update_url = f"http://{ip}:8000/admin/update"
            
            # Envoyer le manifeste
            response = requests.post(
                f"{update_url}/manifest",
                json=manifest,
                timeout=10
            )
            
            if response.status_code == 200:
                # Envoyer les fichiers
                for pole in manifest['target_poles']:
                    archive_path = package_dir / f"{pole}.zip"
                    if archive_path.exists():
                        with open(archive_path, 'rb') as f:
                            files = {'update': f}
                            requests.post(
                                f"{update_url}/files",
                                files=files,
                                timeout=60
                            )
            
        except requests.exceptions.RequestException:
            # Fallback vers copie réseau ou autre méthode
            print(f"    HTTP non disponible pour {ip}, utilisation méthode alternative")
            # Implémenter copie réseau ici

# CLI pour utilisation
if __name__ == "__main__":
    import sys
    
    manager = UpdateManager()
    
    if len(sys.argv) < 2:
        print("""
🚀 Gestionnaire de mises à jour WeMa IA

Commandes:
  build <pole> [features]     Créer un build personnalisé
  package <poles> [features]  Créer un package de mise à jour
  deploy <package_path>       Déployer une mise à jour
  
Exemples:
  python update_manager.py build patrimoine
  python update_manager.py package patrimoine,mandats
  python update_manager.py deploy updates/update_20241219_143022
""")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "build":
        pole = sys.argv[2]
        build_path = manager.create_custom_build(pole)
        print(f"Build créé: {build_path}")
        
    elif command == "package":
        poles = sys.argv[2].split(',')
        package_path = manager.create_update_package(poles)
        print(f"Package créé: {package_path}")
        
    elif command == "deploy":
        package_path = sys.argv[2]
        manager.deploy_update(package_path)
        print("Déploiement terminé")
        
    else:
        print(f"Commande inconnue: {command}")
