// Script de nettoyage des providers pour mode serveur uniquement
// Exécuter avec: node reset-providers.js

console.log('🧹 Nettoyage des providers pour mode serveur central...');

// Nettoyer localStorage
if (typeof window !== 'undefined') {
  // Dans le navigateur
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.includes('providers') || key.includes('clara-db')) {
      console.log(`🗑️ Suppression: ${key}`);
      localStorage.removeItem(key);
    }
  });
} else {
  // En Node.js - nettoyer les fichiers de base de données
  const fs = require('fs');
  const path = require('path');
  const os = require('os');
  
  const claraDir = path.join(os.homedir(), '.clara');
  const dbFile = path.join(claraDir, 'clara.db');
  
  if (fs.existsSync(dbFile)) {
    console.log(`🗑️ Suppression base de données: ${dbFile}`);
    fs.unlinkSync(dbFile);
  }
}

console.log('✅ Nettoyage terminé. Redémarrez l\'application.');
