/**
 * Generated by Clara Agent Studio
 * Flow: NewWorkFLow
 * Description: AI workflow generated from Clara Agent Studio
 * Generated at: 2025-06-01T21:27:00.071Z
 */

import { <PERSON><PERSON>lowRunner } from 'clara-flow-sdk';

export class NewWorkFLowFlow {
  constructor(options = {}) {
    this.runner = new ClaraFlowRunner({
      enableLogging: true,
      logLevel: 'info',
      ...options
    });
    
    this.flowData = {
  "format": "clara-sdk",
  "version": "1.0.0",
  "flow": {
    "id": "1748805990314-ctpxbceja",
    "name": "NewWorkFLow",
    "nodes": [
      {
        "id": "1748807631470-0ye422j78",
        "type": "structured-llm",
        "name": "Structured LLM",
        "position": {
          "x": 989.5995557837314,
          "y": -383.5766271517435
        },
        "data": {
          "label": "Structured LLM",
          "inputs": [
            {
              "id": "prompt",
              "name": "Prompt",
              "type": "input",
              "dataType": "string",
              "required": true,
              "description": "The prompt describing what to generate"
            },
            {
              "id": "jsonExample",
              "name": "JSON Example",
              "type": "input",
              "dataType": "string",
              "required": true,
              "description": "Example JSON structure that defines the output format"
            },
            {
              "id": "context",
              "name": "Context",
              "type": "input",
              "dataType": "string",
              "required": false,
              "description": "Additional context for generation"
            }
          ],
          "outputs": [
            {
              "id": "jsonOutput",
              "name": "JSON Output",
              "type": "output",
              "dataType": "object",
              "description": "Generated JSON object matching the example structure"
            },
            {
              "id": "rawResponse",
              "name": "Raw Response",
              "type": "output",
              "dataType": "string",
              "description": "Raw JSON string response"
            },
            {
              "id": "usage",
              "name": "Usage Stats",
              "type": "output",
              "dataType": "object",
              "description": "Token usage and cost information"
            }
          ],
          "apiKey": "dsad",
          "apiBaseUrl": "http://************/ollama/v1",
          "model": "gemma3:4b",
          "temperature": 0.7,
          "maxTokens": 1000
        },
        "inputs": [
          {
            "id": "prompt",
            "name": "Prompt",
            "type": "input",
            "dataType": "string",
            "required": true,
            "description": "The prompt describing what to generate"
          },
          {
            "id": "jsonExample",
            "name": "JSON Example",
            "type": "input",
            "dataType": "string",
            "required": true,
            "description": "Example JSON structure that defines the output format"
          },
          {
            "id": "context",
            "name": "Context",
            "type": "input",
            "dataType": "string",
            "required": false,
            "description": "Additional context for generation"
          }
        ],
        "outputs": [
          {
            "id": "jsonOutput",
            "name": "JSON Output",
            "type": "output",
            "dataType": "object",
            "description": "Generated JSON object matching the example structure"
          },
          {
            "id": "rawResponse",
            "name": "Raw Response",
            "type": "output",
            "dataType": "string",
            "description": "Raw JSON string response"
          },
          {
            "id": "usage",
            "name": "Usage Stats",
            "type": "output",
            "dataType": "object",
            "description": "Token usage and cost information"
          }
        ],
        "metadata": {
          "tags": [
            "ai",
            "llm",
            "structured",
            "json",
            "openai"
          ],
          "documentation": "Uses OpenAI structured output feature to generate JSON that matches a provided example format."
        }
      },
      {
        "id": "1748807956551-zyefozler",
        "type": "input",
        "name": "Input",
        "position": {
          "x": -289.97444195061735,
          "y": -432.70609072066827
        },
        "data": {
          "label": "Input",
          "inputs": [],
          "outputs": [
            {
              "id": "output",
              "name": "Value",
              "type": "output",
              "dataType": "any",
              "description": "Input value"
            }
          ],
          "value": "hi your product is so bad"
        },
        "inputs": [],
        "outputs": [
          {
            "id": "output",
            "name": "Value",
            "type": "output",
            "dataType": "any",
            "description": "Input value"
          }
        ],
        "metadata": {
          "tags": [
            "input",
            "basic",
            "source"
          ],
          "documentation": "Provides input values to start or feed the workflow. Supports text, numbers, and JSON objects."
        }
      },
      {
        "id": "1748807959670-fmfuga7rz",
        "type": "input",
        "name": "Input",
        "position": {
          "x": 115.72244529273303,
          "y": -497.85801425660725
        },
        "data": {
          "label": "Input",
          "inputs": [],
          "outputs": [
            {
              "id": "output",
              "name": "Value",
              "type": "output",
              "dataType": "any",
              "description": "Input value"
            }
          ],
          "inputType": "json",
          "value": "{\n   \"sentence\":\"sentence from the user\",\n   \"sentiment\":\"good, bad, very bad\",\n\"reason\": \"reason for the choice\"\n}"
        },
        "inputs": [],
        "outputs": [
          {
            "id": "output",
            "name": "Value",
            "type": "output",
            "dataType": "any",
            "description": "Input value"
          }
        ],
        "metadata": {
          "tags": [
            "input",
            "basic",
            "source"
          ],
          "documentation": "Provides input values to start or feed the workflow. Supports text, numbers, and JSON objects."
        }
      },
      {
        "id": "1748807963207-4dru4tgb4",
        "type": "input",
        "name": "Input",
        "position": {
          "x": 524.2309676053981,
          "y": -438.54546643168146
        },
        "data": {
          "label": "Input",
          "inputs": [],
          "outputs": [
            {
              "id": "output",
              "name": "Value",
              "type": "output",
              "dataType": "any",
              "description": "Input value"
            }
          ],
          "value": "you always try to predict if the sentence provided is good or bad "
        },
        "inputs": [],
        "outputs": [
          {
            "id": "output",
            "name": "Value",
            "type": "output",
            "dataType": "any",
            "description": "Input value"
          }
        ],
        "metadata": {
          "tags": [
            "input",
            "basic",
            "source"
          ],
          "documentation": "Provides input values to start or feed the workflow. Supports text, numbers, and JSON objects."
        }
      },
      {
        "id": "1748808364816-4vuck76ef",
        "type": "output",
        "name": "Output",
        "position": {
          "x": 1925.1455858981271,
          "y": 263.9619180830108
        },
        "data": {},
        "inputs": [
          {
            "id": "input",
            "name": "Value",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Value to output"
          }
        ],
        "outputs": [],
        "metadata": {
          "tags": [
            "output",
            "basic",
            "sink"
          ],
          "documentation": "Displays the final result with various formatting options."
        }
      },
      {
        "id": "1748808540616-6wcnsz008",
        "type": "output",
        "name": "Output",
        "position": {
          "x": 1980.8747120363678,
          "y": -638.7917794037674
        },
        "data": {},
        "inputs": [
          {
            "id": "input",
            "name": "Value",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Value to output"
          }
        ],
        "outputs": [],
        "metadata": {
          "tags": [
            "output",
            "basic",
            "sink"
          ],
          "documentation": "Displays the final result with various formatting options."
        }
      },
      {
        "id": "1748808548395-krp3xp58q",
        "type": "output",
        "name": "Output",
        "position": {
          "x": 1921.796436536617,
          "y": 1185.4621528767475
        },
        "data": {},
        "inputs": [
          {
            "id": "input",
            "name": "Value",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Value to output"
          }
        ],
        "outputs": [],
        "metadata": {
          "tags": [
            "output",
            "basic",
            "sink"
          ],
          "documentation": "Displays the final result with various formatting options."
        }
      },
      {
        "id": "1748808711347-sm3i7r94r",
        "type": "json-parse",
        "name": "JSON Parser",
        "position": {
          "x": 1977.042272941837,
          "y": -1117.3375918503061
        },
        "data": {
          "label": "JSON Parser",
          "inputs": [
            {
              "id": "input",
              "name": "JSON Data",
              "type": "input",
              "dataType": "string",
              "required": true,
              "description": "JSON string to parse"
            }
          ],
          "outputs": [
            {
              "id": "output",
              "name": "Parsed Data",
              "type": "output",
              "dataType": "any",
              "description": "Parsed JSON object or extracted field value"
            }
          ],
          "extractField": "sentiment",
          "failOnError": true
        },
        "inputs": [
          {
            "id": "input",
            "name": "JSON Data",
            "type": "input",
            "dataType": "string",
            "required": true,
            "description": "JSON string to parse"
          }
        ],
        "outputs": [
          {
            "id": "output",
            "name": "Parsed Data",
            "type": "output",
            "dataType": "any",
            "description": "Parsed JSON object or extracted field value"
          }
        ],
        "metadata": {
          "tags": [
            "json",
            "parser",
            "data"
          ],
          "documentation": "Parses JSON strings and optionally extracts specific fields using dot notation."
        }
      },
      {
        "id": "1748808825556-1qb5xfupm",
        "type": "if-else",
        "name": "If/Else",
        "position": {
          "x": 2640.494565648249,
          "y": -1127.2232650418387
        },
        "data": {
          "label": "If/Else",
          "inputs": [
            {
              "id": "input",
              "name": "Input Variable",
              "type": "input",
              "dataType": "any",
              "required": true,
              "description": "Input value to evaluate"
            }
          ],
          "outputs": [
            {
              "id": "true",
              "name": "True",
              "type": "output",
              "dataType": "any",
              "description": "Output when condition is true"
            },
            {
              "id": "false",
              "name": "False",
              "type": "output",
              "dataType": "any",
              "description": "Output when condition is false"
            }
          ],
          "expression": "input == \"very bad\""
        },
        "inputs": [
          {
            "id": "input",
            "name": "Input Variable",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Input value to evaluate"
          }
        ],
        "outputs": [
          {
            "id": "true",
            "name": "True",
            "type": "output",
            "dataType": "any",
            "description": "Output when condition is true"
          },
          {
            "id": "false",
            "name": "False",
            "type": "output",
            "dataType": "any",
            "description": "Output when condition is false"
          }
        ],
        "metadata": {
          "tags": [
            "logic",
            "conditional",
            "control-flow"
          ],
          "documentation": "Evaluates JavaScript expressions and routes data based on the result."
        }
      },
      {
        "id": "1748808865172-idjjd6uaa",
        "type": "output",
        "name": "Output",
        "position": {
          "x": 3205.4038017510766,
          "y": -1666.1964167198191
        },
        "data": {},
        "inputs": [
          {
            "id": "input",
            "name": "Value",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Value to output"
          }
        ],
        "outputs": [],
        "metadata": {
          "tags": [
            "output",
            "basic",
            "sink"
          ],
          "documentation": "Displays the final result with various formatting options."
        }
      },
      {
        "id": "1748808870811-sir28onhg",
        "type": "output",
        "name": "Output",
        "position": {
          "x": 3212.5095783058287,
          "y": -798.5810993845337
        },
        "data": {},
        "inputs": [
          {
            "id": "input",
            "name": "Value",
            "type": "input",
            "dataType": "any",
            "required": true,
            "description": "Value to output"
          }
        ],
        "outputs": [],
        "metadata": {
          "tags": [
            "output",
            "basic",
            "sink"
          ],
          "documentation": "Displays the final result with various formatting options."
        }
      }
    ],
    "connections": [
      {
        "id": "1748807968475-w0ipzimz3",
        "sourceNodeId": "1748807956551-zyefozler",
        "sourcePortId": "output",
        "targetNodeId": "1748807631470-0ye422j78",
        "targetPortId": "prompt"
      },
      {
        "id": "1748807996784-6gsrnhyf4",
        "sourceNodeId": "1748807959670-fmfuga7rz",
        "sourcePortId": "output",
        "targetNodeId": "1748807631470-0ye422j78",
        "targetPortId": "jsonExample"
      },
      {
        "id": "1748808001718-er9baatly",
        "sourceNodeId": "1748807963207-4dru4tgb4",
        "sourcePortId": "output",
        "targetNodeId": "1748807631470-0ye422j78",
        "targetPortId": "context"
      },
      {
        "id": "1748808386562-rogrif8z5",
        "sourceNodeId": "1748807631470-0ye422j78",
        "sourcePortId": "rawResponse",
        "targetNodeId": "1748808364816-4vuck76ef",
        "targetPortId": "input"
      },
      {
        "id": "1748808544090-n3rhj9mcz",
        "sourceNodeId": "1748807631470-0ye422j78",
        "sourcePortId": "jsonOutput",
        "targetNodeId": "1748808540616-6wcnsz008",
        "targetPortId": "input"
      },
      {
        "id": "1748808610903-sjaos2t0a",
        "sourceNodeId": "1748807631470-0ye422j78",
        "sourcePortId": "usage",
        "targetNodeId": "1748808548395-krp3xp58q",
        "targetPortId": "input"
      },
      {
        "id": "1748808790544-2hp3bc7he",
        "sourceNodeId": "1748807631470-0ye422j78",
        "sourcePortId": "rawResponse",
        "targetNodeId": "1748808711347-sm3i7r94r",
        "targetPortId": "input"
      },
      {
        "id": "1748808828820-bvuoubcun",
        "sourceNodeId": "1748808711347-sm3i7r94r",
        "sourcePortId": "output",
        "targetNodeId": "1748808825556-1qb5xfupm",
        "targetPortId": "input"
      },
      {
        "id": "1748808869207-9jkix8q7b",
        "sourceNodeId": "1748808825556-1qb5xfupm",
        "sourcePortId": "true",
        "targetNodeId": "1748808865172-idjjd6uaa",
        "targetPortId": "input"
      },
      {
        "id": "1748808873686-ax01ex75w",
        "sourceNodeId": "1748808825556-1qb5xfupm",
        "sourcePortId": "false",
        "targetNodeId": "1748808870811-sir28onhg",
        "targetPortId": "input"
      }
    ],
    "customNodes": []
  },
  "metadata": {
    "createdAt": "2025-06-01T21:27:00.071Z",
    "exportedAt": "2025-06-01T21:27:00.071Z",
    "exportedFrom": "Clara Agent Studio",
    "hasCustomNodes": false
  }
};
    
    this.registerCustomNodes();
  }

  registerCustomNodes() {
    // Register all custom nodes used in this flow
    if (this.flowData.flow.customNodes) {
      this.flowData.flow.customNodes.forEach(node => {
        this.runner.registerCustomNode(node);
      });
    }
  }

  async execute(inputs = {}) {
    return await this.runner.executeFlow(this.flowData, inputs);
  }

  async executeBatch(inputSets, options = {}) {
    const { concurrency = 3, onProgress } = options;
    const results = [];
    
    for (let i = 0; i < inputSets.length; i += concurrency) {
      const batch = inputSets.slice(i, i + concurrency);
      const batchPromises = batch.map(inputs => this.execute(inputs));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      if (onProgress) {
        onProgress({
          completed: Math.min(i + concurrency, inputSets.length),
          total: inputSets.length,
          results: results
        });
      }
    }
    
    return results;
  }

  async executeWithCallback(inputs = {}, onNodeComplete = null) {
    return await this.runner.executeFlow(this.flowData, inputs, {
      onNodeComplete: onNodeComplete
    });
  }

  getFlowInfo() {
    return {
      name: this.flowData.flow.name,
      description: this.flowData.flow.description,
      nodeCount: this.flowData.flow.nodes.length,
      connectionCount: this.flowData.flow.connections.length,
      customNodeCount: this.flowData.flow.customNodes?.length || 0,
      hasCustomNodes: this.flowData.metadata.hasCustomNodes
    };
  }

  validate() {
    return this.runner.validateFlow(this.flowData);
  }
}

// Export for direct use
export const newworkflowFlow = new NewWorkFLowFlow();
export default NewWorkFLowFlow;

// Usage Examples:
/*
// Basic usage
import { NewWorkFLowFlow } from './newworkflow_flow.js';

const flow = new NewWorkFLowFlow();
const result = await flow.execute({ input: "Hello World" });
console.log(result);

// Batch processing
const inputSets = [
  { input: "Hello" },
  { input: "World" },
  { input: "Clara" }
];

const results = await flow.executeBatch(inputSets, {
  concurrency: 2,
  onProgress: (progress) => {
    console.log(`Progress: ${progress.completed}/${progress.total}`);
  }
});

// With node completion callbacks
const result = await flow.executeWithCallback(
  { input: "Hello" },
  (nodeId, nodeName, result) => {
    console.log(`Node ${nodeName} completed with result:`, result);
  }
);
*/