/**
 * 🚨 Message d'erreur serveur WeMa IA
 * Affiché quand le serveur d'inférence est indisponible
 */

import React from 'react';
import { AlertTriangle, Server, Phone } from 'lucide-react';

interface ServerErrorMessageProps {
  onRetry: () => void;
}

const ServerErrorMessage: React.FC<ServerErrorMessageProps> = ({ onRetry }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-red-50 p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        {/* Icône d'erreur */}
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>

        {/* Titre */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Serveur WeMa IA indisponible
        </h1>

        {/* Message principal */}
        <p className="text-gray-600 mb-6">
          Impossible de se connecter au serveur d'intelligence artificielle. 
          L'application ne peut pas fonctionner sans cette connexion.
        </p>

        {/* Informations techniques */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
          <h3 className="font-medium text-gray-900 mb-2 flex items-center">
            <Server className="w-4 h-4 mr-2" />
            Informations techniques
          </h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div>Serveur : *************:1235</div>
            <div>Statut : Hors ligne</div>
            <div>Code : 503 - Service indisponible</div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <button
            onClick={onRetry}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Réessayer la connexion
          </button>

          <div className="text-sm text-gray-500">
            <p className="mb-2">Si le problème persiste :</p>
            <div className="flex items-center justify-center text-blue-600">
              <Phone className="w-4 h-4 mr-1" />
              Contactez l'administrateur système
            </div>
          </div>
        </div>

        {/* Note */}
        <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
          <strong>Note :</strong> Vos documents et données restent sécurisés sur votre PC. 
          Seule la fonction d'intelligence artificielle est temporairement indisponible.
        </div>
      </div>
    </div>
  );
};

export default ServerErrorMessage;
