#!/usr/bin/env python3
"""
Test simple pour vérifier le traitement PDF
"""

import requests
import base64
import json

BASE_URL = "http://127.0.0.1:5000"

def test_pdf_processing():
    print("🧪 Test de Traitement PDF Simple")
    print("=" * 40)
    
    # Créer un PDF simple en base64 (simulé avec du texte)
    test_content = """C<PERSON> - <PERSON>ail: <EMAIL>
Téléphone: +33 6 12 34 56 78
Adresse: 123 rue de la Paix, 75001 Paris

Expérience:
- Développeur Senior (2020-2024)
- Analyste Junior (2018-2020)

Compétences:
- Python, JavaScript
- Gestion de projet
- Communication
"""
    
    # Convertir en base64
    test_base64 = base64.b64encode(test_content.encode('utf-8')).decode('utf-8')
    
    print(f"📄 Contenu de test créé: {len(test_content)} caractères")
    print(f"🔢 Base64 généré: {len(test_base64)} caractères")
    
    # Test OCR
    print("\n1. 👁️ Test OCR...")
    try:
        ocr_data = {
            "file_base64": test_base64,
            "file_type": "txt",  # Utilisons txt pour simplifier
            "preserve_layout": True,
            "language": "eng+fra",
            "dpi": 300
        }
        
        response = requests.post(f"{BASE_URL}/ocr/process", json=ocr_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ OCR réussi!")
            print(f"   📄 Texte extrait: {result.get('text', 'N/A')[:100]}...")
            print(f"   📝 Texte formaté: {result.get('formatted_text', 'N/A')[:100]}...")
            print(f"   📊 Blocs de texte: {len(result.get('text_blocks', []))}")
            
            # Test GDPR avec le texte extrait
            print("\n2. 🛡️ Test GDPR...")
            text_to_anonymize = result.get('formatted_text') or result.get('text', '')
            
            if text_to_anonymize:
                gdpr_data = {
                    "text": text_to_anonymize,
                    "language": "en",
                    "preserve_layout": True
                }
                
                gdpr_response = requests.post(f"{BASE_URL}/gdpr/anonymize", json=gdpr_data)
                print(f"   Status: {gdpr_response.status_code}")
                
                if gdpr_response.status_code == 200:
                    gdpr_result = gdpr_response.json()
                    print("   ✅ GDPR réussi!")
                    print(f"   🔒 Texte anonymisé: {gdpr_result.get('anonymized_text', 'N/A')[:100]}...")
                    print(f"   🗂️ Mappings: {len(gdpr_result.get('mappings', {}))}")
                else:
                    print(f"   ❌ GDPR échoué: {gdpr_response.text}")
            else:
                print("   ⚠️ Pas de texte à anonymiser")
                
        else:
            print(f"   ❌ OCR échoué: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Test terminé")

if __name__ == "__main__":
    test_pdf_processing()
