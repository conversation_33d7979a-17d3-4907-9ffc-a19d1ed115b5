@echo off
echo 🧠 Serveur d'inférence WeMa IA
echo ================================

REM Vérifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouvé
    echo 📦 Installez Python 3.8+ depuis python.org
    pause
    exit /b 1
)

REM Installer les dépendances si nécessaire
echo 📦 Vérification des dépendances...
pip install fastapi uvicorn requests pydantic

REM Démarrer le serveur
echo 🚀 Démarrage du serveur d'inférence...
python start_inference_server.py

pause
