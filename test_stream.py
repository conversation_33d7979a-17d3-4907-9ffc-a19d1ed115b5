#!/usr/bin/env python3
"""
Test script pour vérifier le streaming du backend
"""
import requests
import json

def test_streaming():
    url = "http://localhost:5001/proxy/chat"
    
    payload = {
        "model": "qwen/qwen3-4b",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "stream": True,
        "temperature": 0.7,
        "_provider": "lmstudio"
    }
    
    print("🚀 Testing streaming...")
    
    try:
        response = requests.post(
            url, 
            json=payload, 
            stream=True,
            timeout=30
        )
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("🌊 Reading stream...")
            chunk_count = 0
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    chunk_count += 1
                    print(f"📦 Chunk {chunk_count}: {len(chunk)} bytes")
                    try:
                        # Essayer de décoder le chunk
                        chunk_text = chunk.decode('utf-8')
                        print(f"📝 Content: {chunk_text[:100]}...")
                    except:
                        print(f"📝 Binary content: {chunk[:50]}...")
            
            print(f"✅ Stream completed - {chunk_count} chunks received")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_streaming()
