# 🔍 Installation SearXNG pour WeMa IA

Guide d'installation de SearXNG pour activer la recherche internet dans WeMa IA.

## Qu'est-ce que SearXNG ?

SearXNG est un métamoteur de recherche open-source qui :
- Agrège les résultats de plus de 70 moteurs de recherche
- Respecte votre vie privée (pas de tracking, pas de publicités)
- Fonctionne entièrement en local
- Offre une API REST pour l'intégration

## Installation rapide avec Docker

### Prérequis
- Docker Desktop installé sur Windows
- 2 Go d'espace disque libre
- Connexion internet

### 1. Télécharger SearXNG

```bash
# Créer un dossier pour SearXNG
mkdir C:\searxng
cd C:\searxng

# Télécharger la configuration
curl -o docker-compose.yml https://raw.githubusercontent.com/searxng/searxng/master/docker-compose.yml
```

### 2. Configuration minimale

Créer un fichier `settings.yml` :

```yaml
# Configuration SearXNG pour WeMa IA
use_default_settings: true

server:
  port: 8888
  bind_address: "0.0.0.0"
  secret_key: "changez-cette-clé-secrète-unique"
  
search:
  safe_search: 1
  autocomplete: "google"
  default_lang: "fr"
  formats:
    - html
    - json
    - csv
    - rss

engines:
  - name: google
    disabled: false
  - name: bing
    disabled: false
  - name: duckduckgo
    disabled: false
  - name: wikipedia
    disabled: false
  - name: stackoverflow
    disabled: false
```

### 3. Lancer SearXNG

```bash
# Démarrer SearXNG
docker-compose up -d

# Vérifier que ça fonctionne
curl "http://localhost:8888/search?q=test&format=json"
```

## Installation alternative (WSL)

Si vous préférez WSL :

```bash
# Dans WSL Ubuntu
git clone https://github.com/searxng/searxng.git
cd searxng
make install
make run
```

## Configuration pour WeMa IA

### 1. Dans WeMa IA
1. Aller dans **Paramètres** → **Services**
2. Activer **Recherche Internet - SearXNG**
3. URL: `http://localhost:8888`
4. Tester la connexion

### 2. Catégories recommandées
- ✅ **Général** : Recherche web standard
- ✅ **Actualités** : Articles de presse
- ✅ **Science** : Publications académiques
- ✅ **Informatique** : Ressources techniques

### 3. Paramètres optimaux
- **Langue** : Français
- **Filtrage** : Modéré
- **Résultats max** : 10
- **Timeout** : 10 secondes

## Utilisation dans WeMa IA

Une fois configuré, WeMa IA peut :

### Recherche automatique
```
Utilisateur: "Quelles sont les dernières actualités sur l'IA ?"
WeMa IA: [Recherche automatique] → Résultats récents
```

### Recherche explicite
```
Utilisateur: "Recherche sur internet : prix Bitcoin aujourd'hui"
WeMa IA: [Utilise SearXNG] → Cours actuel du Bitcoin
```

### Recherche contextuelle
```
Utilisateur: "Comment installer Docker ?"
WeMa IA: [Détecte le contexte technique] → Recherche sur StackOverflow, GitHub
```

## Dépannage

### SearXNG ne démarre pas
```bash
# Vérifier les logs
docker-compose logs searxng

# Redémarrer
docker-compose restart
```

### Erreur de connexion dans WeMa IA
1. Vérifier que SearXNG fonctionne : `http://localhost:8888`
2. Tester l'API : `http://localhost:8888/search?q=test&format=json`
3. Vérifier les paramètres de firewall Windows

### Résultats de mauvaise qualité
1. Ajuster les moteurs dans `settings.yml`
2. Modifier les catégories par défaut
3. Changer la langue de recherche

## Sécurité et confidentialité

### Avantages SearXNG
- ✅ Aucun tracking des utilisateurs
- ✅ Pas de collecte de données
- ✅ Recherches anonymisées
- ✅ Contrôle total des sources

### Bonnes pratiques
- Changer la clé secrète par défaut
- Limiter l'accès réseau si nécessaire
- Mettre à jour régulièrement
- Surveiller les logs

## Moteurs de recherche supportés

### Généraux
- Google, Bing, DuckDuckGo, Yandex
- Brave Search, Mojeek, Searx instances

### Spécialisés
- **Actualités** : Google News, Bing News, Reddit
- **Science** : Google Scholar, ArXiv, CrossRef
- **Technique** : StackOverflow, GitHub, GitLab
- **Images** : Google Images, Bing Images, Flickr
- **Vidéos** : YouTube, Vimeo, Dailymotion

## Performance

### Optimisations
- Cache des résultats activé
- Timeout adaptatif par moteur
- Parallélisation des requêtes
- Compression des réponses

### Monitoring
```bash
# Statistiques d'utilisation
curl "http://localhost:8888/stats"

# Santé des moteurs
curl "http://localhost:8888/engines"
```

## Mise à jour

```bash
# Mettre à jour SearXNG
docker-compose pull
docker-compose up -d
```

## Support

- **Documentation officielle** : https://docs.searxng.org/
- **GitHub** : https://github.com/searxng/searxng
- **Instances publiques** : https://searx.space/
- **Support WeMa IA** : Paramètres → Aide

---

**Note** : SearXNG est un projet open-source maintenu par la communauté. L'installation locale garantit la meilleure performance et confidentialité pour WeMa IA.
