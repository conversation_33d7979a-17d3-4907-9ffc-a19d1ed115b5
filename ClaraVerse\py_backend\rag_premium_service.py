# -*- coding: utf-8 -*-
"""
RAG PREMIUM SERVICE
LightRAG + Qdrant + BGE-M3 + OCR intégré + Langfuse Observability
"""

import os
import json
import asyncio
import time
import uuid
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# 🚀 Import Langfuse pour le monitoring des performances
from langfuse_service import langfuse_service, trace_rag_operation
from langfuse.decorators import observe, langfuse_context

# Configuration du logging
logger = logging.getLogger(__name__)

# Imports pour LightRAG
try:
    from lightrag import LightRAG, QueryParam
    from lightrag.llm import gpt_4o_mini_complete, gpt_4o_complete
    from lightrag.utils import EmbeddingFunc
    LIGHTRAG_AVAILABLE = True
except ImportError:
    LIGHTRAG_AVAILABLE = False
    print("WARNING: LightRAG non disponible")

# Imports pour Qdrant
try:
    from qdrant_client import QdrantClient, models
    from qdrant_client.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    print("WARNING: Qdrant non disponible")

# Imports pour embeddings
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("WARNING: SentenceTransformers non disponible")

class PremiumRAGService:
    """Service RAG premium avec LightRAG + Qdrant + BGE-M3"""
    
    def __init__(self, config_path: str = "./rag_config.json"):
        self.config = self._load_config(config_path)
        self.lightrag = None
        self.qdrant_client = None
        self.embedding_model = None
        self.initialized = False
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Charger la configuration"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise FileNotFoundError(f"Configuration non trouvée: {config_path}")
    
    async def initialize(self):
        """Initialiser tous les composants"""
        print("INFO: Initialisation RAG Premium...")
        
        # 1. Initialiser le modèle d'embedding
        await self._init_embeddings()
        
        # 2. Initialiser LightRAG
        await self._init_lightrag()
        
        # 3. Initialiser Qdrant
        await self._init_qdrant()
        
        self.initialized = True
        print("SUCCESS: RAG Premium initialise avec succes")
    
    async def _init_embeddings(self):
        """Initialiser le modèle d'embedding BGE-M3"""
        if not EMBEDDINGS_AVAILABLE:
            raise ImportError("SentenceTransformers non disponible")
        
        model_name = self.config["rag_system"]["embedding_model"]
        print(f"INFO: Chargement modele embedding: {model_name}")

        self.embedding_model = SentenceTransformer(model_name)
        print("SUCCESS: Modele embedding charge")
    
    async def _init_lightrag(self):
        """Initialiser LightRAG"""
        if not LIGHTRAG_AVAILABLE:
            print("WARNING: LightRAG non disponible, utilisation de Qdrant seul")
            return

        storage_path = self.config["lightrag"]["storage_path"]
        os.makedirs(storage_path, exist_ok=True)

        # Fonction LLM simple pour test
        async def simple_llm_func(prompt, **kwargs):
            return "Réponse générée par le système RAG premium."

        # Configuration LightRAG
        self.lightrag = LightRAG(
            working_dir=storage_path,
            llm_model_func=simple_llm_func,
            embedding_func=EmbeddingFunc(
                embedding_dim=1024,
                max_token_size=8192,
                func=self._embed_text
            ),
            chunk_token_size=self.config["rag_system"]["chunk_size"],
            chunk_overlap_token_size=self.config["rag_system"]["chunk_overlap"]
        )

        # Initialiser les storages
        await self.lightrag.initialize_storages()
        print("SUCCESS: LightRAG initialise")
    
    async def _init_qdrant(self):
        """Initialiser Qdrant"""
        if not QDRANT_AVAILABLE:
            print("WARNING: Qdrant non disponible")
            return
        
        config = self.config["qdrant"]
        
        # Client Qdrant (local)
        self.qdrant_client = QdrantClient(path=config["storage_path"])
        
        # Créer la collection si elle n'existe pas
        collection_name = config["collection_name"]
        try:
            self.qdrant_client.get_collection(collection_name)
            print(f"SUCCESS: Collection Qdrant existante: {collection_name}")
        except:
            self.qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=config["vector_size"],
                    distance=Distance.COSINE
                )
            )
            print(f"SUCCESS: Collection Qdrant creee: {collection_name}")
    
    async def _embed_text(self, texts) -> List[float]:
        """Générer l'embedding d'un texte (async) - CORRIGÉ"""
        if self.embedding_model is None:
            raise RuntimeError("Modèle embedding non initialisé")

        # Toujours retourner un seul embedding (pas une liste de listes)
        if isinstance(texts, list):
            # Si c'est une liste, prendre le premier élément
            text = texts[0] if texts else ""
        else:
            text = texts

        # Générer l'embedding pour un seul texte
        embedding = self.embedding_model.encode([text])
        return embedding[0].tolist()  # Retourner un seul vecteur
    
    # ✅ MÉTHODE add_document SUPPRIMÉE - UTILISATION DE LA VERSION CORRIGÉE PLUS BAS
    
    @trace_rag_operation("Premium RAG Search")
    async def search(self, query: str, selected_documents: List[str] = None,
                    use_lightrag: bool = True, use_vector_store: bool = True,
                    limit: int = 5, use_cache: bool = True, fast_mode: bool = False) -> Dict[str, Any]:
        """Rechercher dans le RAG avec paramètres compatibles + Langfuse tracing"""
        start_time = time.time()  # 🚀 Mesurer le temps de traitement

        # 🚀 Mise à jour du contexte Langfuse avec les paramètres
        try:
            langfuse_context.update_current_observation(
                input={
                    "query": query[:200],
                    "fast_mode": fast_mode,
                    "use_lightrag": use_lightrag,
                    "limit": limit
                },
                metadata={
                    "selected_documents_count": len(selected_documents) if selected_documents else 0,
                    "use_cache": use_cache,
                    "use_vector_store": use_vector_store
                }
            )
        except Exception as e:
            logger.warning(f"⚠️ Langfuse context update failed: {e}")

        if not self.initialized:
            await self.initialize()

        vector_results = []
        lightrag_results = ""

        # 🚀 MODE RAPIDE: Désactiver LightRAG et limiter les résultats
        if fast_mode:
            use_lightrag = False
            limit = min(limit, 5)  # Maximum 5 résultats en mode rapide
            print(f"🚀 Mode rapide activé: LightRAG désactivé, limite={limit}")

        # 1. Essayer LightRAG d'abord (sauf en mode rapide)
        if use_lightrag and self.lightrag and not fast_mode:
            try:
                lightrag_results = await self.lightrag.aquery(
                    query,
                    param=QueryParam(mode="hybrid")
                )
                print(f"✅ LightRAG: {len(lightrag_results)} chars")
            except Exception as e:
                print(f"⚠️  Erreur recherche LightRAG: {e}")

        # 2. Recherche vectorielle Qdrant
        if use_vector_store and self.qdrant_client:
            try:
                query_embedding = await self._embed_text(query)
                qdrant_results = self.qdrant_client.search(
                    collection_name=self.config["qdrant"]["collection_name"],
                    query_vector=query_embedding,
                    limit=limit
                )

                for result in qdrant_results:
                    vector_results.append({
                        "content": result.payload.get("text", ""),
                        "metadata": result.payload,
                        "score": result.score
                    })
            except Exception as e:
                print(f"⚠️  Erreur recherche Qdrant: {e}")

        # 🚀 MÉTRIQUES DE PERFORMANCE
        processing_time = time.time() - start_time

        # Format de retour compatible avec l'API
        result = {
            "lightrag_results": lightrag_results,
            "vector_results": vector_results,
            "combined_response": lightrag_results or "\n\n".join([r["content"] for r in vector_results]),
            "sources": list(set([r["metadata"].get("source_file", "unknown") for r in vector_results])),
            "query": query,
            "processing_time": round(processing_time, 3),
            "timestamp": int(time.time() * 1000),
            "from_cache": False,  # TODO: Implémenter le cache si nécessaire
            "fast_mode": fast_mode,
            "total_results": len(vector_results)
        }

        # 🚀 Mise à jour finale du contexte Langfuse avec les résultats
        try:
            langfuse_context.update_current_observation(
                output={
                    "total_results": len(vector_results),
                    "processing_time": processing_time,
                    "sources_count": len(result["sources"]),
                    "response_preview": result["combined_response"][:200]
                },
                metadata={
                    "performance": {
                        "processing_time_seconds": processing_time,
                        "fast_mode_used": fast_mode,
                        "lightrag_used": use_lightrag and bool(lightrag_results),
                        "vector_search_used": use_vector_store
                    }
                }
            )
        except Exception as e:
            logger.warning(f"⚠️ Langfuse final update failed: {e}")

        return result

    async def health_check(self) -> Dict[str, Any]:
        """Vérifier la santé du service RAG"""
        return {
            "status": "healthy" if self.initialized else "unhealthy",
            "lightrag_available": self.lightrag is not None,
            "qdrant_available": self.qdrant_client is not None,
            "embedding_model_loaded": self.embedding_model is not None,
            "initialized": self.initialized
        }

    async def get_metrics(self) -> Dict[str, Any]:
        """Obtenir les métriques du service RAG"""
        return {
            "total_documents": 0,  # TODO: Implémenter le comptage
            "last_search_time": "N/A",
            "service_status": "healthy" if self.initialized else "unhealthy",
            "lightrag_enabled": self.lightrag is not None,
            "qdrant_enabled": self.qdrant_client is not None
        }

    def list_documents(self) -> List[Dict[str, Any]]:
        """Lister les documents (synchrone pour compatibilité)"""
        # TODO: Implémenter la liste des documents depuis Qdrant
        return []

    async def add_document(self, content: str, metadata: dict) -> bool:
        """Ajouter un document au RAG Premium"""
        if not self.initialized:
            await self.initialize()

        try:
            # 1. Ajouter à LightRAG si disponible
            if self.lightrag:
                try:
                    await self.lightrag.ainsert(content)
                    print(f"✅ Document ajouté à LightRAG")
                except Exception as e:
                    print(f"⚠️  Erreur ajout LightRAG: {e}")

            # 2. Ajouter à Qdrant
            if self.qdrant_client:
                try:
                    # Découper le contenu en chunks
                    chunks = self._split_text(content)
                    points = []

                    for i, chunk in enumerate(chunks):
                        embedding = await self._embed_text(chunk)

                        # 🔧 CORRECTION: Générer un UUID valide pour Qdrant
                        point_id = str(uuid.uuid4())

                        point = PointStruct(
                            id=point_id,
                            vector=embedding,  # 🔧 CORRECTION: embedding est maintenant un seul vecteur
                            payload={
                                "text": chunk,
                                "source_file": metadata.get("source_file", "unknown"),
                                "chunk_index": i,
                                "total_chunks": len(chunks),
                                "original_filename": metadata.get("source_file", "unknown"),
                                **metadata
                            }
                        )
                        points.append(point)

                    # Insérer tous les points
                    self.qdrant_client.upsert(
                        collection_name=self.config["qdrant"]["collection_name"],
                        points=points
                    )

                    print(f"✅ Document ajouté à Qdrant: {len(chunks)} chunks")
                    return True

                except Exception as e:
                    print(f"⚠️  Erreur ajout Qdrant: {e}")
                    return False

            return False

        except Exception as e:
            print(f"⚠️  Erreur ajout document RAG Premium: {e}")
            return False

    def _split_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Découper le texte en chunks"""
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size

            # Essayer de couper à un espace pour éviter de couper les mots
            if end < len(text):
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            start = end - overlap
            if start >= len(text):
                break

        return chunks

    async def delete_document(self, filename: str) -> bool:
        """Supprimer un document du RAG"""
        if not self.qdrant_client:
            return False

        try:
            # Supprimer de Qdrant par filename
            self.qdrant_client.delete(
                collection_name=self.config["qdrant"]["collection_name"],
                points_selector=models.FilterSelector(
                    filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="source_file",
                                match=models.MatchValue(value=filename)
                            )
                        ]
                    )
                )
            )
            print(f"🗑️ Document {filename} supprimé du RAG Premium")
            return True
        except Exception as e:
            print(f"⚠️  Erreur suppression RAG Premium: {e}")
            return False

# Instance globale
premium_rag = None

async def get_premium_rag() -> PremiumRAGService:
    """Obtenir l'instance du RAG premium"""
    global premium_rag
    if premium_rag is None:
        premium_rag = PremiumRAGService()
        await premium_rag.initialize()
    return premium_rag
