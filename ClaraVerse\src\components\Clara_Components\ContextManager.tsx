import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Trash2, BarChart3 } from 'lucide-react';

interface ContextStats {
  totalMessages: number;
  totalCharacters: number;
  estimatedTokens: number;
  maxTokens: number;
  utilizationPercentage: number;
  compressionRatio?: number;
  lastCompression?: Date;
  // 🚀 NOUVELLES MÉTRIQUES POUR RAG
  ragDocuments: number;
  ragCharacters: number;
  ragTokens: number;
  systemPromptTokens: number;
  totalContextTokens: number;
}

interface ContextManagerProps {
  conversationHistory: any[];
  onCompress?: () => void;
  onViewContext?: () => void;
  onClearContext?: () => void;
  className?: string;
  // 🚀 NOUVELLES PROPS POUR RAG
  ragContext?: string;
  systemPrompt?: string;
  selectedDocuments?: any[];
  currentModelContextLimit?: number;
}

const ContextManager: React.FC<ContextManagerProps> = ({
  conversationHistory = [],
  onCompress,
  onViewContext,
  onClearContext,
  className = '',
  // 🚀 NOUVELLES PROPS POUR RAG
  ragContext = '',
  systemPrompt = '',
  selectedDocuments = [], // Fallback si pas de store
  currentModelContextLimit = 100000
}) => {
  // 🚀 DOCUMENTS RÉELS: Extraire depuis les messages RAG en base (pas depuis la sélection UI)
  const actualDocuments = React.useMemo(() => {
    // Extraire les documents depuis les messages RAG dans l'historique
    const ragMessages = conversationHistory.filter(msg =>
      msg.role === 'system' && msg.metadata?.type === 'rag'
    );

    const documents = ragMessages.map(msg => ({
      id: msg.metadata?.documentId || msg.id,
      filename: msg.metadata?.documentName || 'Document RAG',
      content: msg.content || '',
      fileType: msg.metadata?.documentType || 'unknown'
    }));

    console.log(`🔍 Context Manager: ${ragMessages.length} messages RAG → ${documents.length} documents en base`);
    console.log(`🔍 DEBUG Context Manager RAG messages:`, ragMessages.map(msg => ({
      id: msg.id,
      role: msg.role,
      type: msg.metadata?.type,
      docName: msg.metadata?.documentName,
      contentLength: msg.content?.length
    })));
    return documents;
  }, [conversationHistory]);
  const [stats, setStats] = useState<ContextStats>({
    totalMessages: 0,
    totalCharacters: 0,
    estimatedTokens: 0,
    maxTokens: 100000, // Défaut pour modèles 32B
    utilizationPercentage: 0,
    // 🚀 NOUVELLES MÉTRIQUES
    ragDocuments: 0,
    ragCharacters: 0,
    ragTokens: 0,
    systemPromptTokens: 0,
    totalContextTokens: 0
  });
  
  const [showDetails, setShowDetails] = useState(false);
  const [compressionMode, setCompressionMode] = useState<'auto' | 'manual'>('auto');

  // 🚀 CALCUL COMPLET DES STATISTIQUES DU CONTEXTE (Messages + RAG + System Prompt)
  useEffect(() => {
    // 🎯 COMPTEUR DE TOKENS AMÉLIORÉ - Plus précis que chars/4
    const estimateTokens = (text: string): number => {
      if (!text) return 0;

      // Méthode plus précise : compter les mots + caractères spéciaux
      const words = text.split(/\s+/).filter(word => word.length > 0);
      const specialChars = (text.match(/[^\w\s]/g) || []).length;

      // Estimation : 1 mot ≈ 1.3 tokens, caractères spéciaux ≈ 0.5 tokens
      return Math.ceil(words.length * 1.3 + specialChars * 0.5);
    };

    // 1. Calculer les tokens des messages de conversation (IDENTIQUE PerfectCompressor - SANS RAG)
    const conversationMessages = conversationHistory.filter(msg =>
      !(msg.role === 'system' && msg.metadata?.type === 'rag')
    );
    const messageChars = conversationMessages.reduce((sum, msg) => sum + (msg.content?.length || 0), 0);
    const messageTokens = estimateTokens(conversationMessages.map(msg => msg.content || '').join(' '));

    // 2. Calculer les tokens du contexte RAG (IDENTIQUE PerfectCompressor)
    const ragChars = ragContext?.length || 0;

    // Extraire les messages RAG depuis l'historique (IDENTIQUE PerfectCompressor)
    const ragMessagesInHistory = conversationHistory.filter(msg =>
      msg.role === 'system' && msg.metadata?.type === 'rag'
    );
    const ragTokensFromHistory = estimateTokens(ragMessagesInHistory.map(msg => msg.content || '').join(' '));
    const ragTokensFromContext = ragContext ? estimateTokens(ragContext) : 0;

    const documentsChars = ragMessagesInHistory.reduce((total, msg) => {
      return total + (msg.content?.length || 0);
    }, 0);
    const totalRagChars = ragChars + documentsChars;
    const ragTokens = ragTokensFromHistory + ragTokensFromContext;

    // 3. Calculer les tokens du system prompt
    const systemChars = systemPrompt?.length || 0;
    const systemTokens = estimateTokens(systemPrompt || '');

    // 4. Total du contexte réel envoyé au LLM
    const totalContextChars = messageChars + totalRagChars + systemChars;
    const totalContextTokens = messageTokens + ragTokens + systemTokens;

    // 🔍 DEBUG : Log détaillé du calcul Context Manager (IDENTIQUE PerfectCompressor)
    console.log(`🔍 DEBUG Context Manager calcul IDENTIQUE PerfectCompressor:
      - Messages conversation: ${messageTokens} tokens (${messageChars} chars) [${conversationMessages.length} messages sans RAG]
      - RAG: ${ragTokens} tokens (${totalRagChars} chars) [ragContext: ${ragChars}, historique: ${documentsChars}, ${ragMessagesInHistory.length} messages RAG]
      - System: ${systemTokens} tokens (${systemChars} chars)
      - TOTAL: ${totalContextTokens} tokens (${totalContextChars} chars)`);

    // 5. Utilisation par rapport à la limite du modèle
    const maxTokens = currentModelContextLimit || 100000;
    const utilization = (totalContextTokens / maxTokens) * 100;

    setStats({
      totalMessages: conversationHistory.length,
      totalCharacters: messageChars,
      estimatedTokens: messageTokens,
      maxTokens,
      utilizationPercentage: Math.min(utilization, 100),
      // 🚀 NOUVELLES MÉTRIQUES RAG
      ragDocuments: actualDocuments.length,
      ragCharacters: totalRagChars,
      ragTokens,
      systemPromptTokens: systemTokens,
      totalContextTokens
    });
  }, [conversationHistory, ragContext, systemPrompt, actualDocuments, currentModelContextLimit]); // 🚀 TEMPS RÉEL: Utiliser actualDocuments

  // Déterminer la couleur de la barre selon l'utilisation
  const getProgressColor = () => {
    if (stats.utilizationPercentage < 50) return 'bg-green-500';
    if (stats.utilizationPercentage < 75) return 'bg-yellow-500';
    if (stats.utilizationPercentage < 90) return 'bg-orange-500';
    return 'bg-red-500';
  };

  // Déterminer le statut du contexte
  const getContextStatus = () => {
    if (stats.utilizationPercentage < 50) return { text: 'Optimal', color: 'text-green-600' };
    if (stats.utilizationPercentage < 75) return { text: 'Modéré', color: 'text-yellow-600' };
    if (stats.utilizationPercentage < 90) return { text: 'Élevé', color: 'text-orange-600' };
    return { text: 'Critique', color: 'text-red-600' };
  };

  const status = getContextStatus();

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 ${className}`}>
      {/* En-tête avec titre et actions */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Brain className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Contexte
          </span>
          <span className={`text-xs font-medium ${status.color}`}>
            {status.text}
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Voir les détails"
          >
            <Eye className="w-4 h-4 text-gray-500" />
          </button>
          
          <button
            onClick={onViewContext}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Voir le contexte complet"
          >
            <BarChart3 className="w-4 h-4 text-gray-500" />
          </button>
          
          <button
            onClick={onCompress}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Compresser maintenant"
          >
            <Zap className="w-4 h-4 text-blue-500" />
          </button>
        </div>
      </div>

      {/* Barre de progression du contexte */}
      <div className="mb-2">
        <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
          <span>{stats.totalContextTokens.toLocaleString()} tokens total</span>
          <span>{stats.utilizationPercentage.toFixed(1)}%</span>
        </div>
        
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
            style={{ width: `${Math.min(stats.utilizationPercentage, 100)}%` }}
          />
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
        <span>{stats.totalMessages} messages</span>
        <span>{stats.ragDocuments} docs</span>
        <span>{(stats.totalContextTokens / 1000).toFixed(1)}K tokens</span>
        <span className="flex items-center gap-1">
          <Settings className="w-3 h-3" />
          {compressionMode === 'auto' ? 'Auto' : 'Manuel'}
        </span>
      </div>

      {/* Détails étendus */}
      {showDetails && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          {/* 🚀 RÉPARTITION COMPLÈTE DU CONTEXTE */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              Répartition du contexte
            </h4>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">💬 Messages ({stats.totalMessages}):</span>
                <span className="font-medium">{stats.estimatedTokens.toLocaleString()} tokens</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">📄 Documents RAG ({stats.ragDocuments}):</span>
                <span className="font-medium">{stats.ragTokens.toLocaleString()} tokens</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-500">⚙️ System Prompt:</span>
                <span className="font-medium">{stats.systemPromptTokens.toLocaleString()} tokens</span>
              </div>
              <div className="flex justify-between text-xs border-t border-gray-200 dark:border-gray-600 pt-1">
                <span className="text-gray-700 dark:text-gray-300 font-medium">🎯 Total contexte:</span>
                <span className="font-bold text-blue-600">{stats.totalContextTokens.toLocaleString()} tokens</span>
              </div>
            </div>
          </div>

          {/* Métriques techniques */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">Limite modèle:</span>
              <span className="ml-1 font-medium">{stats.maxTokens.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-gray-500">Utilisation:</span>
              <span className="ml-1 font-medium">{stats.utilizationPercentage.toFixed(1)}%</span>
            </div>
          </div>

          {/* Options de compression */}
          <div className="mt-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <label className="text-xs text-gray-600 dark:text-gray-400">Mode:</label>
              <select
                value={compressionMode}
                onChange={(e) => setCompressionMode(e.target.value as 'auto' | 'manual')}
                className="text-xs bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-2 py-1"
              >
                <option value="auto">Automatique</option>
                <option value="manual">Manuel</option>
              </select>
            </div>
            
            <button
              onClick={onClearContext}
              className="flex items-center gap-1 text-xs text-red-600 hover:text-red-700 transition-colors"
              title="Vider le contexte"
            >
              <Trash2 className="w-3 h-3" />
              Vider
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContextManager;
