{
    "model": "qwen3:30b-a3b",
    "messages": [
        {
            "role": "user",
            "content": "You are an expert react-vite-tailwind developer. Based on the following app requirements, create a comprehensive file structure for a production-ready application.\n\nAPP REQUIREMENTS:\n- Name: snake game\n- Type: other\n- Description: snake game with dark mode\n- Pages: sanke\n- Features: none\n- Styling: tailwind\n- Additional: \n- Framework: react-vite-tailwind\n\nPlease respond with a JSON array of file structures. Each entry should have:\n- path: relative file path (e.g., \"src/components/Header.tsx\")\n- type: \"file\" or \"directory\"\n- description: what this file/folder contains\n- priority: 1-5 (1=critical, 5=optional)\n\nInclude ALL necessary files for a functional app:\n- Component files for each page\n- Routing setup\n- Styling files\n- Type definitions\n- Configuration files\n- Utils/helpers\n- Assets folders\n\nMake sure the structure follows react-vite-tailwind best practices and includes modern patterns.\n\nRESPOND ONLY WITH VALID JSON - NO EXPLANATIONS:"
        }
    ],
    "stream": false,
    "temperature": 0.3,
    "max_tokens": 4000
}


result 

{
    "id": "chatcmpl-219",
    "object": "chat.completion",
    "created": **********,
    "model": "qwen3:30b-a3b",
    "system_fingerprint": "fp_ollama",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "<think>\nOkay, the user wants a file structure for a React-Vite-Tailwind Snake Game with dark mode. Let me start by recalling the standard structure for a Vite React app. Typically, there's a src directory with components, pages, styles, etc. Since it's a single-page app, maybe the pages are just one component, but the user mentioned \"pages: snake\", so maybe a single page.\n\nFirst, the main files: index.html and main.tsx in the public and src directories. Then, the app structure. The user specified dark mode, so I need to handle that. Maybe a context or a state management for theme. But since it's a simple game, perhaps a simple toggle in the component.\n\nFor the Snake game, the main component would be SnakeGame.tsx. I'll need to organize components like the Snake, Food, GameBoard. Also, a GameOver component. Maybe a GameControls for start/restart.\n\nStyling: Tailwind is used, so a global CSS file, maybe a styles.css in src, and then component-specific styles. But Tailwind is typically used via CDN or PostCSS, so maybe the user just needs the Tailwind config. Wait, the user said \"styling: tailwind\", so the structure should include Tailwind's configuration. So a tailwind.config.js and postcss.config.js in the root.\n\nRouting: Since it's a single page, maybe no routing needed. But the user mentioned \"routing setup\" in the required files. Hmm, maybe the user expects a basic routing setup even if there's only one page. So using React Router? But the app is a single page, so maybe it's not necessary. But the user's instruction says to include routing setup. So perhaps a basic setup with a single route.\n\nType definitions: Maybe a types.ts file for any custom types, like Position or GameStatus.\n\nUtils: Maybe a helper function for generating random positions, or game logic helpers.\n\nAssets: The game might need images for the snake or food, but since it's a simple game, maybe just CSS styles. So an assets folder with images, but the user didn't mention any, so maybe it's optional.\n\nNow, structuring the JSON. Each entry needs path, type, description, priority. Critical files are the main ones like index.html, main.tsx, App.tsx, tailwind config, etc. Then components, pages, styles, types, utils.\n\nWait, the user said \"pages: snake\", so maybe a pages directory with SnakePage.tsx. But in a single-page app, maybe the page is just the main component. So perhaps the Snake component is in src/pages/Snake.tsx.\n\nAlso, dark mode: need to handle the theme. Maybe a ThemeProvider component, or a state in the App component. So a context for theme, or a simple state variable.\n\nIncluding all necessary files: components, pages, routing, styling, types, config, utils, assets.\n\nLet me list out the files:\n\n- public/index.html\n- src/main.tsx\n- src/App.tsx\n- src/index.css (or tailwind.css)\n- src/tailwind.config.js\n- src/postcss.config.js\n- src/components/SnakeGame.tsx\n- src/components/GameOver.tsx\n- src/components/GameBoard.tsx\n- src/components/Snake.tsx\n- src/components/Food.tsx\n- src/pages/SnakePage.tsx\n- src/router/index.tsx (for routing)\n- src/types.ts\n- src/utils/helpers.ts\n- src/assets/ (maybe empty or with images)\n- src/styles/global.css (if needed)\n\nBut the user said \"styling: tailwind\", so maybe the global styles are handled via Tailwind. So the index.css might just import Tailwind's CSS.\n\nAlso, the priority: critical files are the main ones like App, main.tsx, routing, tailwind config. Optional could be assets, utils, etc.\n\nNow, structuring the JSON array with each entry. Make sure all required files are included. Check for any missing parts. For example, the routing setup: if using React Router, then the router/index.tsx would set up the routes. But since there's only one page, maybe it's a simple setup.\n\nAlso, the dark mode: maybe a toggle button in the component, and the theme is managed via a state. So the App component would have a theme state, and apply the dark class to the body or a container.\n\nI think that's covered. Now, compiling all into the JSON structure as per the user's request.\n</think>\n\n[\n  {\n    \"path\": \"public/index.html\",\n    \"type\": \"file\",\n    \"description\": \"HTML entry point for the application\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/main.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Vite entry point and React root component\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/App.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Main application component with routing and theme context\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/tailwind.config.js\",\n    \"type\": \"file\",\n    \"description\": \"Tailwind CSS configuration file\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/postcss.config.js\",\n    \"type\": \"file\",\n    \"description\": \"PostCSS configuration for Tailwind\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/index.css\",\n    \"type\": \"file\",\n    \"description\": \"Global CSS styles for the application\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/components/SnakeGame.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Core game component for the snake game\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/components/GameBoard.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Game board component with grid layout\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/components/Snake.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Snake segment and movement logic\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/components/Food.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Food item component for the snake to eat\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/components/GameOver.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Game over screen component\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/pages/SnakePage.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Main game page component\",\n    \"priority\": 1\n  },\n  {\n    \"path\": \"src/router/index.tsx\",\n    \"type\": \"file\",\n    \"description\": \"Routing configuration for the application\",\n    \"priority\": 2\n  },\n  {\n    \"path\": \"src/types.ts\",\n    \"type\": \"file\",\n    \"description\": \"Type definitions for game components\",\n    \"priority\": 3\n  },\n  {\n    \"path\": \"src/utils/helpers.ts\",\n    \"type\": \"file\",\n    \"description\": \"Helper functions for game logic\",\n    \"priority\": 3\n  },\n  {\n    \"path\": \"src/assets\",\n    \"type\": \"directory\",\n    \"description\": \"Static assets like images or icons\",\n    \"priority\": 4\n  },\n  {\n    \"path\": \"src/styles\",\n    \"type\": \"directory\",\n    \"description\": \"Custom CSS modules or utility classes\",\n    \"priority\": 4\n  }\n]"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 234,
        "completion_tokens": 1651,
        "total_tokens": 1885
    }
}