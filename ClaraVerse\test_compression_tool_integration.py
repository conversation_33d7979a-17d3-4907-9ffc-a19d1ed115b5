#!/usr/bin/env python3
"""
🧪 Test d'intégration du tool de compression
Vérifie que le tool fonctionne de bout en bout
"""

import asyncio
import json
import requests
import time

# Configuration
BACKEND_URL = "http://localhost:5001"
TEST_CONTEXT = """
Voici une longue conversation de test pour vérifier le fonctionnement du tool de compression.

Utilisateur: <PERSON><PERSON><PERSON>, comment allez-vous aujourd'hui ?
Assistant: <PERSON><PERSON><PERSON> ! Je vais très bien, merci de demander. Comment puis-je vous aider aujourd'hui ? Je suis là pour répondre à toutes vos questions et vous assister dans vos tâches.

Utilisateur: Pouvez-vous m'expliquer ce qu'est l'intelligence artificielle ?
Assistant: Bien sûr ! L'intelligence artificielle (IA) est un domaine de l'informatique qui vise à créer des systèmes capables de réaliser des tâches qui nécessitent normalement l'intelligence humaine. Cela inclut l'apprentissage automatique, le raisonnement logique, la perception visuelle et auditive, la compréhension du langage naturel, et la prise de décision autonome.

L'IA peut être divisée en plusieurs catégories :
1. IA faible (ou étroite) : spécialisée dans une tâche spécifique
2. IA forte (ou générale) : capable de comprendre et d'apprendre n'importe quelle tâche intellectuelle
3. Super-intelligence : dépassant les capacités humaines dans tous les domaines

Les applications actuelles incluent les assistants virtuels, la reconnaissance vocale, la vision par ordinateur, les voitures autonomes, et bien plus encore.

Utilisateur: C'est très intéressant ! Pouvez-vous me parler des différents types d'apprentissage automatique ?
Assistant: Absolument ! L'apprentissage automatique se divise en plusieurs catégories principales :

1. **Apprentissage supervisé** : L'algorithme apprend à partir d'exemples étiquetés. On lui montre des données d'entrée avec les bonnes réponses, et il apprend à faire des prédictions sur de nouvelles données.

2. **Apprentissage non supervisé** : L'algorithme trouve des patterns dans des données non étiquetées. Il découvre des structures cachées sans qu'on lui dise quoi chercher.

3. **Apprentissage par renforcement** : L'algorithme apprend en interagissant avec un environnement, recevant des récompenses ou des punitions selon ses actions.

4. **Apprentissage semi-supervisé** : Combine des données étiquetées et non étiquetées pour améliorer les performances.

5. **Apprentissage par transfert** : Utilise les connaissances acquises sur une tâche pour en résoudre une autre similaire.

Chaque type a ses avantages selon le problème à résoudre et les données disponibles.

Utilisateur: Merci pour ces explications détaillées ! Avez-vous des recommandations pour quelqu'un qui veut commencer à apprendre l'IA ?
Assistant: Excellente question ! Voici mes recommandations pour débuter en IA :

**Fondamentaux mathématiques :**
- Statistiques et probabilités
- Algèbre linéaire
- Calcul différentiel

**Langages de programmation :**
- Python (le plus populaire en IA)
- R pour les statistiques
- SQL pour les bases de données

**Ressources d'apprentissage :**
- Cours en ligne : Coursera, edX, Udacity
- Livres : "Hands-On Machine Learning" de Aurélien Géron
- Plateformes pratiques : Kaggle, Google Colab

**Étapes recommandées :**
1. Commencer par les concepts de base
2. Pratiquer avec des projets simples
3. Participer à des compétitions Kaggle
4. Rejoindre des communautés (Reddit, Discord)
5. Construire un portfolio de projets

L'important est de pratiquer régulièrement et de ne pas avoir peur de faire des erreurs !
"""

def test_compression_endpoint():
    """Test de l'endpoint de compression"""
    print("🧪 Test de l'endpoint /api/compress-context")
    
    try:
        # Préparer la requête
        payload = {
            "context": TEST_CONTEXT,
            "compression_ratio": 0.5,
            "preserve_recent_messages": 3
        }
        
        print(f"📤 Envoi contexte: {len(TEST_CONTEXT)} caractères")
        
        # Envoyer la requête
        start_time = time.time()
        response = requests.post(
            f"{BACKEND_URL}/api/compress-context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        end_time = time.time()
        
        print(f"⏱️ Temps de réponse: {(end_time - start_time):.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                print("✅ Compression réussie!")
                print(f"📏 Original: {result.get('original_length')} caractères")
                print(f"📏 Compressé: {result.get('compressed_length')} caractères")
                print(f"📊 Ratio: {result.get('compression_ratio', 0):.2f}")
                print(f"⏱️ Temps compression: {result.get('compression_time')}ms")
                
                # Afficher un aperçu du contenu compressé
                compressed_content = result.get('compressed_context', '')
                preview = compressed_content[:200] + "..." if len(compressed_content) > 200 else compressed_content
                print(f"👀 Aperçu compressé:\n{preview}")
                
                return True
            else:
                print(f"❌ Compression échouée: {result.get('error')}")
                return False
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test: {e}")
        return False

def test_compression_status():
    """Test de l'endpoint de statut"""
    print("\n🧪 Test de l'endpoint /api/compression/status")
    
    try:
        response = requests.get(f"{BACKEND_URL}/api/compression/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Statut récupéré!")
            print(f"🔗 LM Studio URL: {result.get('lmstudio_url')}")
            print(f"🤖 Modèle: {result.get('compression_model')}")
            print(f"📊 Seuil: {result.get('compression_threshold')} tokens")
            return True
        else:
            print(f"❌ Erreur statut: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test statut: {e}")
        return False

def test_compression_test_endpoint():
    """Test de l'endpoint de test"""
    print("\n🧪 Test de l'endpoint /api/compression/test")
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/compression/test", timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test compression réussi!")
            print(f"📏 Original: {result.get('original_length')} caractères")
            print(f"📏 Compressé: {result.get('compressed_length')} caractères")
            print(f"⏱️ Temps: {result.get('compression_time')}ms")
            
            preview = result.get('compressed_preview', '')
            print(f"👀 Aperçu: {preview}")
            return True
        else:
            print(f"❌ Erreur test: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test endpoint: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test d'intégration du tool de compression WeMa IA")
    print("=" * 60)
    
    # Tests séquentiels
    tests = [
        ("Statut compression", test_compression_status),
        ("Test compression", test_compression_test_endpoint),
        ("Compression complète", test_compression_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        success = test_func()
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name} : SUCCÈS")
        else:
            print(f"❌ {test_name} : ÉCHEC")
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 Résultat global: {success_count}/{len(results)} tests réussis")
    
    if success_count == len(results):
        print("🎉 Tous les tests sont passés ! Le tool de compression est opérationnel.")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")

if __name__ == "__main__":
    main()
