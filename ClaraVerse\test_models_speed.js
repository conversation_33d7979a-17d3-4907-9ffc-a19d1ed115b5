/**
 * 🚀 Test de vitesse des modèles LM Studio
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

async function testAllModels() {
  console.log('🚀 TEST VITESSE TOUS LES MODÈLES\n');
  
  // Récupérer la liste des modèles
  try {
    const modelsResponse = await fetch(`${LM_STUDIO_URL}/v1/models`);
    const modelsData = await modelsResponse.json();
    const models = modelsData.data || [];
    
    console.log(`📦 ${models.length} modèles trouvés\n`);
    
    for (const model of models) {
      console.log(`🧪 Test modèle: ${model.id}`);
      
      try {
        const startTime = Date.now();
        const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model: model.id,
            messages: [{ role: 'user', content: 'Hi' }],
            max_tokens: 5,
            temperature: 0,
            stream: false
          })
        });
        
        const responseTime = Date.now() - startTime;
        
        if (response.ok) {
          const result = await response.json();
          const content = result.choices?.[0]?.message?.content || 'Vide';
          
          console.log(`  ✅ ${responseTime}ms - "${content.substring(0, 20)}..."`);
          
          if (responseTime < 2000) {
            console.log(`  🚀 RAPIDE ! Recommandé pour mode rapide`);
          } else if (responseTime < 5000) {
            console.log(`  ⚡ Acceptable`);
          } else {
            console.log(`  🐌 LENT - Éviter`);
          }
        } else {
          console.log(`  ❌ Erreur: ${response.status}`);
        }
      } catch (error) {
        console.log(`  ❌ Erreur: ${error.message}`);
      }
      
      console.log(''); // Ligne vide
    }
    
    console.log('🎯 RECOMMANDATIONS:');
    console.log('- Utilisez le modèle le plus rapide pour le mode rapide');
    console.log('- Si tous sont lents, redémarrez LM Studio');
    console.log('- Vérifiez les ressources système (CPU/GPU)');
    
  } catch (error) {
    console.error('❌ Erreur récupération modèles:', error.message);
  }
}

testAllModels().catch(console.error);
