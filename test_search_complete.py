#!/usr/bin/env python3
"""
Test complet de la recherche internet - Vérification que tout fonctionne parfaitement
"""

import requests
import json
import time

def test_search_with_scraping():
    """Test avec scraping de contenu réel"""
    
    print("🔍 Test complet de la recherche avec scraping...")
    
    search_data = {
        "query": "nouveautés ChatGPT 2024",
        "search_type": "general", 
        "max_results": 3
    }
    
    try:
        print(f"📡 Recherche: {search_data['query']}")
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:5001/search/internet",
            json=search_data,
            timeout=30
        )
        search_time = time.time() - start_time
        
        print(f"📊 Statut: {response.status_code}")
        print(f"⏱️ Temps: {search_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérifier la structure de la réponse
            print(f"✅ Recherche réussie!")
            print(f"🔧 Structure de la réponse:")
            print(f"  - success: {data.get('success', False)}")
            print(f"  - result présent: {'result' in data}")
            print(f"  - metadata présent: {'metadata' in data}")
            
            if 'metadata' in data:
                metadata = data['metadata']
                print(f"📈 Métadonnées détaillées:")
                print(f"  - Sources: {len(metadata.get('sources', []))}")
                print(f"  - Contenu scrapé: {len(metadata.get('scraped_content', []))}")
                print(f"  - Résultats: {metadata.get('results_count', 0)}")
                
                # Afficher les sources
                sources = metadata.get('sources', [])
                if sources:
                    print(f"🔗 Sources trouvées:")
                    for i, source in enumerate(sources, 1):
                        print(f"  {i}. {source}")
                
                # Afficher le contenu scrapé
                scraped = metadata.get('scraped_content', [])
                if scraped:
                    print(f"📄 Contenu scrapé:")
                    for i, content in enumerate(scraped, 1):
                        print(f"  {i}. {content.get('title', 'Sans titre')}")
                        print(f"     URL: {content.get('url', '')}")
                        print(f"     Mots: {content.get('word_count', 0)}")
                        print(f"     Temps: {content.get('scrape_time', 0):.2f}s")
                        if content.get('content'):
                            preview = content['content'][:150] + "..." if len(content['content']) > 150 else content['content']
                            print(f"     Aperçu: {preview}")
                        print()
                else:
                    print("⚠️ Aucun contenu scrapé")
            
            # Afficher le résultat final
            if 'result' in data:
                result = data['result']
                print(f"📝 Résultat final:")
                print(f"  Longueur: {len(result)} caractères")
                if len(result) > 300:
                    print(f"  Aperçu: {result[:300]}...")
                else:
                    print(f"  Contenu: {result}")
            
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_technical_search():
    """Test de recherche technique"""
    
    print("\n🔧 Test de recherche technique...")
    
    search_data = {
        "query": "Python FastAPI streaming",
        "max_results": 2
    }
    
    try:
        response = requests.post(
            "http://localhost:5001/search/technical",
            json=search_data,
            timeout=20
        )
        
        print(f"📊 Statut: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Recherche technique réussie!")
            
            if 'result' in data:
                result_length = len(data['result'])
                print(f"📝 Résultat: {result_length} caractères")
                
                if result_length > 0:
                    print("✅ Contenu technique trouvé")
                else:
                    print("⚠️ Pas de contenu technique")
            
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_searxng_direct():
    """Test direct de SearXNG"""
    
    print("\n🔍 Test direct SearXNG...")
    
    try:
        response = requests.get(
            "http://localhost:8888/search",
            params={
                'q': 'intelligence artificielle',
                'format': 'json',
                'categories': 'general'
            },
            timeout=10
        )
        
        print(f"📊 Statut SearXNG: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"✅ SearXNG fonctionne: {len(results)} résultats")
            
            if results:
                print("🔗 Premiers résultats:")
                for i, result in enumerate(results[:3], 1):
                    print(f"  {i}. {result.get('title', 'Sans titre')}")
                    print(f"     {result.get('url', '')}")
            
            return True
        else:
            print(f"❌ Erreur SearXNG: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur SearXNG: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Tests complets de la recherche internet WeMa IA")
    print("=" * 60)
    
    # Test 1: SearXNG direct
    searxng_ok = test_searxng_direct()
    
    # Test 2: Recherche avec scraping
    search_ok = test_search_with_scraping()
    
    # Test 3: Recherche technique
    tech_ok = test_technical_search()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS:")
    print(f"✅ SearXNG direct: {'OK' if searxng_ok else 'ERREUR'}")
    print(f"✅ Recherche + scraping: {'OK' if search_ok else 'ERREUR'}")
    print(f"✅ Recherche technique: {'OK' if tech_ok else 'ERREUR'}")
    
    if all([searxng_ok, search_ok, tech_ok]):
        print("\n🎉 TOUS LES TESTS RÉUSSIS - Recherche internet parfaite!")
    else:
        print("\n⚠️ Certains tests ont échoué - Vérification nécessaire")
