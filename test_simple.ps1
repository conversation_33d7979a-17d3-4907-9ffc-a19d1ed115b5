# Test Simple du Système GDPR
Write-Host "Test du Système GDPR" -ForegroundColor Green

# Test Health
Write-Host "1. Test Health..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://127.0.0.1:5000/health" -Method Get
    Write-Host "OK Backend: $($health.status) - Port: $($health.port)" -ForegroundColor Green
} catch {
    Write-Host "ERREUR Health: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test GDPR Preview
Write-Host "2. Test GDPR Preview..." -ForegroundColor Yellow
try {
    $body = @{
        text = "<PERSON>, email: <EMAIL>, tel: **********"
        language = "en"
    } | ConvertTo-Json

    $preview = Invoke-RestMethod -Uri "http://127.0.0.1:5000/gdpr/preview" -Method Post -Body $body -ContentType "application/json"
    Write-Host "OK GDPR: $($preview.entities.Count) entites detectees" -ForegroundColor Green
    
    foreach ($entity in $preview.entities) {
        Write-Host "  - $($entity.entity_type): $($entity.text)" -ForegroundColor White
    }
} catch {
    Write-Host "ERREUR GDPR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Collections
Write-Host "3. Test Collections..." -ForegroundColor Yellow
try {
    $collections = Invoke-RestMethod -Uri "http://127.0.0.1:5000/collections" -Method Get
    Write-Host "OK Collections: $($collections.collections.Count) collections" -ForegroundColor Green
} catch {
    Write-Host "ERREUR Collections: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "SYSTEME GDPR FONCTIONNEL !" -ForegroundColor Green
Write-Host "Frontend: http://localhost:5174" -ForegroundColor Cyan
Write-Host "Backend: http://127.0.0.1:5000" -ForegroundColor Cyan
