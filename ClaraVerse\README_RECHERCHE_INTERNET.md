# 🔍 Recherche Internet - WeMa IA

## 🚀 Démarrage Rapide

### **1. <PERSON><PERSON>marrer SearXNG**
```bash
# Double-cliquez sur le fichier ou exécutez :
start-searxng.bat
```

### **2. Configurer dans WeMa IA**
1. Ouvrir WeMa IA : http://localhost:5174
2. Aller dans **Paramètres** → **Services**
3. Activer **Recherche Internet - SearXNG**
4. URL : `http://localhost:8888`
5. Cliquer sur **Tester** ✅

### **3. Utiliser la Recherche**

#### **Automatique** 🤖
```
Vous: "Quelles sont les actualités du jour ?"
WeMa IA: [Recherche automatiquement] → Résultats récents
```

#### **Manuel** 🔍
- Tapez votre question
- Cliquez sur le bouton 🔍
- Les résultats s'ajoutent à votre message

#### **Suggestion** 💡
- WeMa IA détecte automatiquement les questions
- Affiche une suggestion de recherche
- Cliquez sur "Rechercher" pour l'utiliser

## ✨ Fonctionnalités

### **Types de Recherche**
- 🌐 **Générale** : Questions, définitions, informations
- 📰 **Actualités** : News récentes, événements
- 🔬 **Scientifique** : Publications, études académiques  
- 💻 **Technique** : Code, programmation, IT

### **Détection Intelligente**
WeMa IA détecte automatiquement :
- Questions avec "quoi", "comment", "pourquoi"
- Demandes d'actualités avec "récent", "aujourd'hui"
- Questions techniques avec "code", "programmation"
- Recherches explicites avec "recherche", "trouve"

### **Intégration au Contexte**
- ✅ Résultats ajoutés automatiquement au contexte
- ✅ Sources vérifiées et horodatées
- ✅ Formatage optimisé pour l'IA
- ✅ Préservation lors de la compression

## 🛠️ Gestion

### **Démarrer SearXNG**
```bash
start-searxng.bat
```

### **Arrêter SearXNG**
```bash
stop-searxng.bat
```

### **Vérifier le Statut**
```bash
docker ps
# Doit afficher : wema-searxng
```

### **Voir les Logs**
```bash
cd searxng
docker logs wema-searxng
```

## 🔧 Configuration Avancée

### **Paramètres SearXNG**
Fichier : `searxng/searxng/settings.yml`

```yaml
search:
  safe_search: 1        # 0=off, 1=modéré, 2=strict
  default_lang: "fr"    # Langue par défaut
  
server:
  secret_key: "votre-clé-secrète"
```

### **Paramètres WeMa IA**
- **Catégories** : Général, Actualités, Science, IT
- **Langue** : Français par défaut
- **Résultats max** : 5-10 recommandé
- **Timeout** : 10 secondes

## 🚨 Dépannage

### **SearXNG ne démarre pas**
```bash
# Vérifier Docker
docker --version

# Redémarrer
cd searxng
docker-compose restart
```

### **Erreur 500 dans WeMa IA**
1. Vérifier que SearXNG fonctionne : http://localhost:8888
2. Tester l'API : http://localhost:8888/search?q=test&format=json
3. Redémarrer SearXNG si nécessaire

### **Pas de résultats**
- Vérifier la connexion internet
- Essayer des mots-clés différents
- Changer le type de recherche

### **Recherche lente**
- Réduire le nombre de résultats max
- Ajuster le timeout
- Vérifier la charge réseau

## 📊 Performance

### **Métriques Typiques**
- **Temps de recherche** : 1-3 secondes
- **Résultats** : 5-10 par recherche  
- **Sources** : Google, Bing, DuckDuckGo, Wikipedia
- **Précision** : 85-95% selon le type

### **Optimisations**
- Cache automatique des résultats
- Parallélisation des requêtes
- Compression intelligente du contexte
- Filtrage de la qualité

## 🎯 Exemples d'Usage

### **Questions Générales**
```
"Qu'est-ce que l'intelligence artificielle ?"
"Comment fonctionne la blockchain ?"
"Définition de machine learning"
```

### **Actualités**
```
"Actualités IA cette semaine"
"Dernières nouvelles tech"
"Que s'est-il passé aujourd'hui ?"
```

### **Technique**
```
"Comment utiliser React hooks ?"
"Erreur Python import module"
"Meilleurs frameworks JavaScript 2024"
```

### **Scientifique**
```
"Études récentes sur le climat"
"Publications IA en médecine"
"Recherche quantique 2024"
```

## 🔗 Liens Utiles

- **Interface SearXNG** : http://localhost:8888
- **API Test** : http://localhost:8888/search?q=test&format=json
- **WeMa IA** : http://localhost:5174
- **Documentation SearXNG** : https://docs.searxng.org/

---

**🎉 La recherche internet est maintenant intégrée à WeMa IA !**

Profitez de réponses enrichies avec des informations récentes et vérifiées. 🚀
