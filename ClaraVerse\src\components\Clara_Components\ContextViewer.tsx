import React, { useState, useEffect } from 'react';
import { X, Copy, Download, Zap, <PERSON>, Clock, User, Bot, FileText, Settings } from 'lucide-react';

interface ContextViewerProps {
  isOpen: boolean;
  onClose: () => void;
  conversationHistory: any[];
  onCompress?: () => void;
  // 🚀 NOUVELLES PROPS POUR CONTEXTE COMPLET
  ragContext?: string;
  systemPrompt?: string;
  selectedDocuments?: any[];
}

interface ContextMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'rag' | 'prompt';
  content: string;
  timestamp: Date;
  tokens: number;
  isCompressed?: boolean;
  type?: 'message' | 'rag' | 'system';
}

const ContextViewer: React.FC<ContextViewerProps> = ({
  isOpen,
  onClose,
  conversationHistory = [],
  onCompress,
  // 🚀 NOUVELLES PROPS
  ragContext = '',
  systemPrompt = '',
  selectedDocuments = []
}) => {
  const [contextMessages, setContextMessages] = useState<ContextMessage[]>([]);
  const [totalTokens, setTotalTokens] = useState(0);
  const [copied, setCopied] = useState(false);

  // 🚀 PRÉPARER LE CONTEXTE COMPLET (Messages + RAG + System Prompt)
  useEffect(() => {
    const allMessages: ContextMessage[] = [];

    // 1. Ajouter le system prompt s'il existe
    if (systemPrompt && systemPrompt.trim()) {
      allMessages.push({
        id: 'system-prompt',
        role: 'prompt',
        content: systemPrompt,
        timestamp: new Date(),
        tokens: Math.ceil(systemPrompt.length / 4),
        type: 'system'
      });
    }

    // 2. Ajouter le contexte RAG s'il existe
    if (ragContext && ragContext.trim()) {
      allMessages.push({
        id: 'rag-context',
        role: 'rag',
        content: ragContext,
        timestamp: new Date(),
        tokens: Math.ceil(ragContext.length / 4),
        type: 'rag'
      });
    }

    // 3. Ajouter les messages de conversation
    if (conversationHistory && conversationHistory.length > 0) {
      const conversationMessages: ContextMessage[] = conversationHistory.map(msg => {
        // 🚀 DÉTECTION: Identifier les messages RAG
        const isRagMessage = msg.metadata?.isRagDocument || msg.metadata?.type === 'rag' ||
                            (msg.role === 'system' && msg.content?.includes('📄 Document RAG:'));

        return {
          id: msg.id || `msg-${Date.now()}-${Math.random()}`,
          role: isRagMessage ? 'rag' : msg.role, // 🚀 CORRECTION: Utiliser 'rag' comme rôle pour les documents
          content: msg.content || '',
          timestamp: new Date(msg.timestamp || Date.now()),
          tokens: Math.ceil((msg.content?.length || 0) / 4),
          isCompressed: msg.metadata?.isCompressed || false,
          type: isRagMessage ? 'rag' : 'message' // 🚀 CORRECTION: Type 'rag' pour les documents
        };
      });
      allMessages.push(...conversationMessages);
    }

    const total = allMessages.reduce((sum, msg) => sum + msg.tokens, 0);

    setContextMessages(allMessages);
    setTotalTokens(total);
  }, [conversationHistory, ragContext, systemPrompt]);

  // Copier le contexte complet
  const copyContext = async () => {
    const contextText = contextMessages.map(msg => 
      `[${msg.role.toUpperCase()}] ${msg.content}`
    ).join('\n\n---\n\n');

    try {
      await navigator.clipboard.writeText(contextText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Erreur copie:', error);
    }
  };

  // Télécharger le contexte
  const downloadContext = () => {
    const contextText = contextMessages.map(msg => 
      `[${msg.role.toUpperCase()}] [${msg.timestamp.toLocaleString()}] [${msg.tokens} tokens]\n${msg.content}`
    ).join('\n\n' + '='.repeat(80) + '\n\n');

    const blob = new Blob([contextText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `context-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 🚀 OBTENIR L'ICÔNE SELON LE RÔLE (avec nouveaux types)
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'user': return <User className="w-4 h-4 text-blue-500" />;
      case 'assistant': return <Bot className="w-4 h-4 text-green-500" />;
      case 'system': return <Brain className="w-4 h-4 text-purple-500" />;
      case 'rag': return <FileText className="w-4 h-4 text-orange-500" />;
      case 'prompt': return <Settings className="w-4 h-4 text-indigo-500" />;
      default: return <Brain className="w-4 h-4 text-gray-500" />;
    }
  };

  // 🚀 OBTENIR LA COULEUR SELON LE RÔLE (avec nouveaux types)
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'user': return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'assistant': return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'system': return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      case 'rag': return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800';
      case 'prompt': return 'bg-indigo-50 dark:bg-indigo-900/20 border-indigo-200 dark:border-indigo-800';
      default: return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
    }
  };

  // 🚀 OBTENIR LE NOM DU RÔLE (avec nouveaux types)
  const getRoleName = (role: string) => {
    switch (role) {
      case 'user': return 'Utilisateur';
      case 'assistant': return 'Assistant';
      case 'system': return 'Système';
      case 'rag': return 'Documents RAG';
      case 'prompt': return 'System Prompt';
      default: return role;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* En-tête */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Brain className="w-5 h-5 text-blue-500" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Contexte de Conversation
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {contextMessages.length} messages • {totalTokens.toLocaleString()} tokens
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={copyContext}
              className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
            >
              <Copy className="w-4 h-4" />
              {copied ? 'Copié!' : 'Copier'}
            </button>
            
            <button
              onClick={downloadContext}
              className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
            >
              <Download className="w-4 h-4" />
              Télécharger
            </button>
            
            {onCompress && (
              <button
                onClick={onCompress}
                className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
              >
                <Zap className="w-4 h-4" />
                Compresser
              </button>
            )}
            
            <button
              onClick={onClose}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Contenu scrollable */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {contextMessages.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Brain className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>Aucun message dans le contexte</p>
            </div>
          ) : (
            contextMessages.map((message, index) => (
              <div
                key={message.id}
                className={`border rounded-lg p-4 ${getRoleColor(message.role)}`}
              >
                {/* En-tête du message */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getRoleIcon(message.role)}
                    <span className="font-medium text-sm capitalize">
                      {message.role}
                    </span>
                    {message.isCompressed && (
                      <span className="px-2 py-0.5 text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded">
                        Compressé
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                    <span>{message.tokens} tokens</span>
                    <span>#{index + 1}</span>
                  </div>
                </div>
                
                {/* Contenu du message */}
                <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {message.content.length > 500 ? (
                    <details>
                      <summary className="cursor-pointer text-blue-600 dark:text-blue-400 hover:underline">
                        {message.content.substring(0, 500)}... (Voir plus)
                      </summary>
                      <div className="mt-2">
                        {message.content}
                      </div>
                    </details>
                  ) : (
                    message.content
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pied de page avec statistiques */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>Total: {contextMessages.length} messages</span>
              <span>Tokens: {totalTokens.toLocaleString()}</span>
              <span>Caractères: {contextMessages.reduce((sum, msg) => sum + msg.content.length, 0).toLocaleString()}</span>
            </div>
            
            <div className="text-xs">
              Utilisation contexte: {((totalTokens / 100000) * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextViewer;
