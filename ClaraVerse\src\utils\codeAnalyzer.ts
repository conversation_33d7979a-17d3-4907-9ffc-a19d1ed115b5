/**
 * 🔍 ANALYSEUR DE CODE - DÉTECTION AUTOMATIQUE DES PROBLÈMES
 * 
 * Analyse le code pour identifier :
 * - Méthodes mortes (non utilisées)
 * - Imports inutiles
 * - Code dupliqué
 * - Variables non utilisées
 * - Incohérences architecturales
 */

export interface CodeIssue {
  type: 'unused_import' | 'unused_function' | 'unused_variable' | 'duplicate_code' | 'dead_code' | 'inconsistency';
  severity: 'error' | 'warning' | 'info';
  line: number;
  column?: number;
  message: string;
  suggestion?: string;
  codeSnippet?: string;
}

export interface CodeAnalysisResult {
  file: string;
  issues: CodeIssue[];
  metrics: {
    totalLines: number;
    codeLines: number;
    commentLines: number;
    blankLines: number;
    functions: number;
    variables: number;
    imports: number;
  };
  suggestions: string[];
}

export class CodeAnalyzer {
  private fileContent: string = '';
  private lines: string[] = [];

  /**
   * Analyser un fichier de code
   */
  public async analyzeFile(filePath: string, content: string): Promise<CodeAnalysisResult> {
    this.fileContent = content;
    this.lines = content.split('\n');

    const issues: CodeIssue[] = [];
    const metrics = this.calculateMetrics();

    // 1. Analyser les imports
    issues.push(...this.analyzeImports());

    // 2. Analyser les fonctions
    issues.push(...this.analyzeFunctions());

    // 3. Analyser les variables
    issues.push(...this.analyzeVariables());

    // 4. Analyser le code dupliqué
    issues.push(...this.analyzeDuplicateCode());

    // 5. Analyser les incohérences
    issues.push(...this.analyzeInconsistencies());

    // 6. Générer des suggestions
    const suggestions = this.generateSuggestions(issues, metrics);

    return {
      file: filePath,
      issues,
      metrics,
      suggestions
    };
  }

  /**
   * Calculer les métriques du code
   */
  private calculateMetrics() {
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;
    let functions = 0;
    let variables = 0;
    let imports = 0;

    for (const line of this.lines) {
      const trimmed = line.trim();
      
      if (!trimmed) {
        blankLines++;
      } else if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*')) {
        commentLines++;
      } else {
        codeLines++;
        
        // Compter les fonctions
        if (trimmed.includes('function ') || trimmed.includes('const ') && trimmed.includes('=>') || 
            trimmed.includes('useCallback') || trimmed.includes('useMemo')) {
          functions++;
        }
        
        // Compter les variables
        if (trimmed.startsWith('const ') || trimmed.startsWith('let ') || trimmed.startsWith('var ') ||
            trimmed.includes('useState') || trimmed.includes('useRef')) {
          variables++;
        }
        
        // Compter les imports
        if (trimmed.startsWith('import ')) {
          imports++;
        }
      }
    }

    return {
      totalLines: this.lines.length,
      codeLines,
      commentLines,
      blankLines,
      functions,
      variables,
      imports
    };
  }

  /**
   * Analyser les imports inutilisés
   */
  private analyzeImports(): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"]/g;
    
    for (let i = 0; i < this.lines.length; i++) {
      const line = this.lines[i];
      let match;
      
      while ((match = importRegex.exec(line)) !== null) {
        const [fullMatch, namedImports, namespaceImport, defaultImport] = match;
        
        if (namedImports) {
          // Analyser les imports nommés
          const imports = namedImports.split(',').map(imp => imp.trim());
          for (const imp of imports) {
            if (!this.isUsedInCode(imp)) {
              issues.push({
                type: 'unused_import',
                severity: 'warning',
                line: i + 1,
                message: `Import '${imp}' is not used`,
                suggestion: `Remove unused import '${imp}'`,
                codeSnippet: line.trim()
              });
            }
          }
        }
        
        if (defaultImport && !this.isUsedInCode(defaultImport)) {
          issues.push({
            type: 'unused_import',
            severity: 'warning',
            line: i + 1,
            message: `Import '${defaultImport}' is not used`,
            suggestion: `Remove unused import '${defaultImport}'`,
            codeSnippet: line.trim()
          });
        }
      }
    }
    
    return issues;
  }

  /**
   * Analyser les fonctions non utilisées
   */
  private analyzeFunctions(): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const functionRegex = /(?:const|function)\s+(\w+)/g;
    
    for (let i = 0; i < this.lines.length; i++) {
      const line = this.lines[i];
      let match;
      
      while ((match = functionRegex.exec(line)) !== null) {
        const functionName = match[1];
        
        // Ignorer les fonctions React standard
        if (['useState', 'useEffect', 'useCallback', 'useMemo', 'useRef'].includes(functionName)) {
          continue;
        }
        
        if (!this.isUsedInCode(functionName, i)) {
          issues.push({
            type: 'unused_function',
            severity: 'warning',
            line: i + 1,
            message: `Function '${functionName}' is not used`,
            suggestion: `Remove unused function '${functionName}' or export it if needed`,
            codeSnippet: line.trim()
          });
        }
      }
    }
    
    return issues;
  }

  /**
   * Analyser les variables non utilisées
   */
  private analyzeVariables(): CodeIssue[] {
    const issues: CodeIssue[] = [];
    // Implémentation simplifiée - peut être étendue
    return issues;
  }

  /**
   * Analyser le code dupliqué
   */
  private analyzeDuplicateCode(): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const codeBlocks = new Map<string, number[]>();
    
    // Analyser les blocs de 3+ lignes similaires
    for (let i = 0; i < this.lines.length - 2; i++) {
      const block = this.lines.slice(i, i + 3).join('\n').trim();
      if (block.length > 50) { // Ignorer les petits blocs
        if (codeBlocks.has(block)) {
          codeBlocks.get(block)!.push(i);
        } else {
          codeBlocks.set(block, [i]);
        }
      }
    }
    
    // Identifier les duplications
    for (const [block, lines] of codeBlocks) {
      if (lines.length > 1) {
        for (const lineNum of lines) {
          issues.push({
            type: 'duplicate_code',
            severity: 'info',
            line: lineNum + 1,
            message: `Duplicate code block detected`,
            suggestion: `Consider extracting this code into a reusable function`,
            codeSnippet: block.substring(0, 100) + '...'
          });
        }
      }
    }
    
    return issues;
  }

  /**
   * Analyser les incohérences architecturales
   */
  private analyzeInconsistencies(): CodeIssue[] {
    const issues: CodeIssue[] = [];
    
    // Vérifier les console.log restants
    for (let i = 0; i < this.lines.length; i++) {
      const line = this.lines[i];
      if (line.includes('console.log') || line.includes('console.warn') || line.includes('console.error')) {
        issues.push({
          type: 'inconsistency',
          severity: 'warning',
          line: i + 1,
          message: 'Console statement found - should use logger instead',
          suggestion: 'Replace with logger.debug(), logger.warn(), or logger.error()',
          codeSnippet: line.trim()
        });
      }
    }
    
    return issues;
  }

  /**
   * Vérifier si un identifiant est utilisé dans le code
   */
  private isUsedInCode(identifier: string, excludeLine?: number): boolean {
    for (let i = 0; i < this.lines.length; i++) {
      if (excludeLine !== undefined && i === excludeLine) continue;
      
      const line = this.lines[i];
      // Recherche simple - peut être améliorée avec une analyse AST
      if (line.includes(identifier) && !line.trim().startsWith('import ')) {
        return true;
      }
    }
    return false;
  }

  /**
   * Générer des suggestions d'optimisation
   */
  private generateSuggestions(issues: CodeIssue[], metrics: any): string[] {
    const suggestions: string[] = [];
    
    const unusedImports = issues.filter(i => i.type === 'unused_import').length;
    const unusedFunctions = issues.filter(i => i.type === 'unused_function').length;
    const duplicateCode = issues.filter(i => i.type === 'duplicate_code').length;
    const inconsistencies = issues.filter(i => i.type === 'inconsistency').length;
    
    if (unusedImports > 0) {
      suggestions.push(`🧹 Remove ${unusedImports} unused imports to reduce bundle size`);
    }
    
    if (unusedFunctions > 0) {
      suggestions.push(`🗑️ Remove ${unusedFunctions} unused functions to clean up code`);
    }
    
    if (duplicateCode > 0) {
      suggestions.push(`♻️ Refactor ${duplicateCode} duplicate code blocks into reusable functions`);
    }
    
    if (inconsistencies > 0) {
      suggestions.push(`🔧 Fix ${inconsistencies} architectural inconsistencies`);
    }
    
    if (metrics.functions > 50) {
      suggestions.push(`📦 Consider splitting this large file (${metrics.functions} functions) into smaller modules`);
    }
    
    if (metrics.totalLines > 2000) {
      suggestions.push(`📏 File is very large (${metrics.totalLines} lines) - consider refactoring`);
    }
    
    return suggestions;
  }
}

// Instance singleton
export const codeAnalyzer = new CodeAnalyzer();

// Exposer en développement
if (import.meta.env.DEV) {
  (window as any).codeAnalyzer = codeAnalyzer;
}
