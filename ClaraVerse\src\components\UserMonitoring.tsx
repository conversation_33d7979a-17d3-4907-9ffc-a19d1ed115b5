/**
 * 👥 Monitoring des utilisateurs WeMa IA
 * Vue d'ensemble des utilisateurs enregistrés et connectés
 */

import React, { useState, useEffect } from 'react';
import { Users, MapPin, Clock, User, Building, Activity, RefreshCw } from 'lucide-react';

interface RegisteredUser {
  firstName: string;
  lastName: string;
  pole: string;
  pcName: string;
  ipAddress: string;
  registeredAt: string;
  lastSeen?: string;
  status: 'online' | 'offline' | 'unknown';
  version?: string;
}

interface PoleStats {
  name: string;
  userCount: number;
  onlineCount: number;
  lastActivity?: string;
}

const UserMonitoring: React.FC = () => {
  const [users, setUsers] = useState<RegisteredUser[]>([]);
  const [poleStats, setPoleStats] = useState<Record<string, PoleStats>>({});
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Charger les données
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Charger les utilisateurs enregistrés
      const usersResponse = await fetch('/api/admin/users');
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setUsers(usersData);
        
        // Calculer les stats par pôle
        const stats: Record<string, PoleStats> = {};
        usersData.forEach((user: RegisteredUser) => {
          if (!stats[user.pole]) {
            stats[user.pole] = {
              name: getPoleDisplayName(user.pole),
              userCount: 0,
              onlineCount: 0
            };
          }
          stats[user.pole].userCount++;
          if (user.status === 'online') {
            stats[user.pole].onlineCount++;
          }
        });
        setPoleStats(stats);
      }
      
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Erreur chargement données utilisateurs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Actualisation automatique
  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 30000); // Toutes les 30 secondes
    return () => clearInterval(interval);
  }, []);

  const getPoleDisplayName = (poleId: string): string => {
    const poleNames: Record<string, string> = {
      'patrimoine': 'Gestion Patrimoine',
      'ma': 'M&A',
      'rh': 'Ressources Humaines',
      'direction': 'Direction',
      'comptabilite': 'Comptabilité',
      'autre': 'Autre'
    };
    return poleNames[poleId] || poleId;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <div className="w-2 h-2 bg-green-500 rounded-full"></div>;
      case 'offline':
        return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
      default:
        return <div className="w-2 h-2 bg-gray-400 rounded-full"></div>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('fr-FR');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des utilisateurs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Users className="w-7 h-7 mr-3 text-blue-600" />
            Monitoring Utilisateurs
          </h2>
          <p className="text-gray-600 mt-1">Vue d'ensemble des utilisateurs WeMa IA</p>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-gray-500">
            Dernière actualisation: {lastUpdate.toLocaleTimeString()}
          </div>
          <button
            onClick={loadData}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Stats par pôle */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(poleStats).map(([poleId, stats]) => (
          <div key={poleId} className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-medium text-gray-900">{stats.name}</h3>
                <p className="text-sm text-gray-600">
                  {stats.onlineCount}/{stats.userCount} en ligne
                </p>
              </div>
            </div>
            
            {/* Barre de progression */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Activité</span>
                <span>{Math.round((stats.onlineCount / stats.userCount) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(stats.onlineCount / stats.userCount) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Statistiques globales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Total utilisateurs</p>
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">En ligne</p>
              <p className="text-2xl font-bold text-gray-900">
                {users.filter(u => u.status === 'online').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Building className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Pôles actifs</p>
              <p className="text-2xl font-bold text-gray-900">{Object.keys(poleStats).length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Nouveaux (24h)</p>
              <p className="text-2xl font-bold text-gray-900">
                {users.filter(u => {
                  const registeredDate = new Date(u.registeredAt);
                  const yesterday = new Date();
                  yesterday.setDate(yesterday.getDate() - 1);
                  return registeredDate > yesterday;
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des utilisateurs */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Utilisateurs Enregistrés ({users.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisateur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pôle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  PC / IP
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Enregistré le
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="p-2 bg-gray-100 rounded-full">
                        <User className="w-4 h-4 text-gray-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {getPoleDisplayName(user.pole)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                        {user.pcName}
                      </div>
                      <div className="text-xs text-gray-500">{user.ipAddress}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(user.status)}
                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                        {user.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {formatDate(user.registeredAt)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserMonitoring;
