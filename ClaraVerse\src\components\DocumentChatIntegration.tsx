/**
 * Document Chat Integration Component
 * Allows users to select and chat with documents from within the main chat interface
 */

import React, { useState, useEffect } from 'react';
import {
  FileText,
  Database,
  Search,
  MessageSquare,
  Shield,
  X,
  ChevronDown,
  ChevronRight,
  Loader2,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

import { gdprApiService } from '../services/gdprApiService';
import { DocumentInfo, Collection } from '../types/gdpr-types';


interface DocumentChatIntegrationProps {
  onDocumentSelect: (document: DocumentInfo) => void;
  selectedDocuments: DocumentInfo[];
  onRemoveDocument: (documentId: string | number) => void;
  isVisible: boolean;
  onToggle: () => void;
}

const DocumentChatIntegration: React.FC<DocumentChatIntegrationProps> = ({
  onDocumentSelect,
  selectedDocuments,
  onRemoveDocument,
  isVisible,
  onToggle
}) => {
  // ============================================================================
  // State Management
  // ============================================================================

  const [collections, setCollections] = useState<Collection[]>([]);
  const [documents, setDocuments] = useState<DocumentInfo[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedCollections, setExpandedCollections] = useState<Set<string>>(new Set());



  // ============================================================================
  // Data Loading
  // ============================================================================

  const loadCollections = async () => {
    try {
      const response = await gdprApiService.getCollections();
      if (response.success && response.data) {
        setCollections(response.data.collections || []);
      }
    } catch (error) {
      console.error('Error loading collections:', error);
      setError('Failed to load collections');
    }
  };

  const loadRAGDocuments = async () => {
    console.log('📄 Loading RAG documents...');
    setIsLoading(true);
    setError(null);

    try {
      // 🔄 UTILISER LE MÊME ENDPOINT QUE LE DOCUMENT MANAGER POUR LA COHÉRENCE
      const response = await fetch('http://localhost:8000/documents', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`Failed to load documents: ${response.status}`);
      }

      const data = await response.json();

      const documents = data.documents || [];

      // Convertir en format DocumentInfo compatible
      const formattedDocuments: DocumentInfo[] = documents.map((doc: any) => ({
        id: doc.id,
        filename: doc.filename,
        fileType: doc.file_type || 'document',
        content: doc.content || '',
        collection_name: doc.collection_name || 'default_collection',
        collectionName: doc.collection_name || 'default_collection',
        chunk_count: doc.chunk_count || 0,
        chunkCount: doc.chunk_count || 0,
        created_at: doc.created_at || new Date().toISOString(),
        metadata: {
          ...doc.metadata,
          source: 'document_manager',
          file_type: doc.file_type
        }
      }));

      console.log('📄 RAG Documents loaded successfully:', formattedDocuments.length, 'documents');
      setDocuments(formattedDocuments);

      // 🔄 SYNCHRONISATION : Mettre à jour le store aussi
      try {
        const { useDocumentStore } = await import('../stores/documentStore');
        const setAvailableDocuments = useDocumentStore.getState().setAvailableDocuments;

        // Formater pour le store (format simplifié)
        const storeDocuments = formattedDocuments.map(doc => ({
          id: doc.id,
          filename: doc.filename,
          fileType: doc.fileType,
          collectionName: doc.collectionName,
          chunkCount: doc.chunkCount,
          isAnonymized: doc.metadata?.isAnonymized || false,
          metadata: doc.metadata
        }));

        setAvailableDocuments(storeDocuments);
        console.log('🔄 Store synchronisé depuis RAG Chat');
      } catch (storeError) {
        console.warn('⚠️ Échec synchronisation store depuis RAG Chat:', storeError);
      }

    } catch (error) {
      console.error('📄 Error loading RAG documents:', error);
      setError('Failed to load RAG documents');
    } finally {
      setIsLoading(false);
    }
  };

  // ============================================================================
  // Effects
  // ============================================================================

  useEffect(() => {
    if (isVisible && documents.length === 0) {
      console.log('📄 DocumentChatIntegration became visible, loading RAG documents...');
      loadCollections();
      loadRAGDocuments();
    }
  }, [isVisible]);

  useEffect(() => {
    // Pour le RAG, on charge toujours tous les documents
    // La collection sera utilisée pour le filtrage côté client
    loadRAGDocuments();
  }, [selectedCollection]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleDocumentClick = (document: DocumentInfo) => {
    console.log('📄 Document clicked:', document.filename, document.id);

    const isAlreadySelected = selectedDocuments.some(d => d.id === document.id);
    console.log('📄 Is already selected:', isAlreadySelected);

    if (!isAlreadySelected) {
      console.log('📄 Calling onDocumentSelect...');
      try {
        onDocumentSelect(document);
        console.log('✅ Document successfully added to RAG selection');
      } catch (error) {
        console.error('❌ Error in onDocumentSelect:', error);
      }
    } else {
      console.log('📄 Document already selected - removing it');
      try {
        onRemoveDocument(document.id);
        console.log('✅ Document removed from selection');
      } catch (error) {
        console.error('❌ Error removing document:', error);
      }
    }
  };

  const toggleCollection = (collectionName: string) => {
    const newExpanded = new Set(expandedCollections);
    if (newExpanded.has(collectionName)) {
      newExpanded.delete(collectionName);
    } else {
      newExpanded.add(collectionName);
    }
    setExpandedCollections(newExpanded);
  };

  // ============================================================================
  // Filtering
  // ============================================================================

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchQuery === '' ||
      (doc.filename || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.fileType || '').toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCollection = selectedCollection === '' ||
      doc.collectionName === selectedCollection;

    return matchesSearch && matchesCollection;
  });

  const documentsByCollection = filteredDocuments.reduce((acc, doc) => {
    const collectionName = doc.collectionName || 'undefined';
    if (!acc[collectionName]) {
      acc[collectionName] = [];
    }
    acc[collectionName].push(doc);
    return acc;
  }, {} as Record<string, DocumentInfo[]>);

  // ============================================================================
  // Render Methods
  // ============================================================================

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
      >
        <Database className="w-4 h-4" />
        <span className="text-sm">Add Documents</span>
      </button>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <Database className="w-5 h-5 text-blue-500" />
          <h3 className="font-semibold text-gray-900 dark:text-white">
            RAG Documents
          </h3>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => {
              loadCollections();
              loadRAGDocuments();
            }}
            disabled={isLoading}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
            title="Refresh RAG documents"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={onToggle}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Selected Documents */}
      {selectedDocuments.length > 0 && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Selected Documents ({selectedDocuments.length})
          </h4>
          <div className="space-y-1">
            {selectedDocuments.map((doc) => (
              <div
                key={doc.id}
                className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded-lg"
              >
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm text-green-700 dark:text-green-300">
                    {doc.filename || 'Unknown Document'}
                  </span>
                  {doc.isAnonymized && (
                    <Shield className="w-3 h-3 text-green-500" />
                  )}
                </div>
                <button
                  onClick={() => {
                    console.log('🗑️ X button clicked for document:', doc.filename, 'ID:', doc.id, 'type:', typeof doc.id);
                    onRemoveDocument(doc.id);
                  }}
                  className="p-1 hover:bg-green-100 dark:hover:bg-green-800/30 rounded transition-colors"
                  title="Remove document from selection"
                >
                  <X className="w-3 h-3 text-green-600 dark:text-green-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={selectedCollection}
            onChange={(e) => setSelectedCollection(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Collections</option>
            {collections.map((collection) => (
              <option key={collection.name} value={collection.name}>
                {collection.name} ({collection.documentCount})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Document List */}
      <div className="max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
          </div>
        ) : error ? (
          <div className="p-4 text-center">
            <AlertCircle className="w-6 h-6 text-red-500 mx-auto mb-2" />
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        ) : Object.keys(documentsByCollection).length === 0 ? (
          <div className="p-8 text-center">
            <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 dark:text-gray-400 text-sm">No documents found</p>
          </div>
        ) : (
          <div className="p-2">
            {Object.entries(documentsByCollection).map(([collectionName, docs]) => (
              <div key={collectionName} className="mb-2">
                <button
                  onClick={() => toggleCollection(collectionName)}
                  className="w-full flex items-center gap-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  {expandedCollections.has(collectionName) ? (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  )}
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {collectionName} ({docs.length})
                  </span>
                </button>
                
                {expandedCollections.has(collectionName) && (
                  <div className="ml-6 space-y-1">
                    {docs.map((doc) => {
                      const isSelected = selectedDocuments.some(d => d.id === doc.id);
                      return (
                        <button
                          key={doc.id}
                          data-doc-id={doc.id}
                          onClick={() => handleDocumentClick(doc)}
                          className={`group w-full flex items-center gap-2 p-2 rounded-md transition-all duration-200 text-left cursor-pointer ${
                            isSelected
                              ? 'bg-wema-50 dark:bg-wema-900/20 text-wema-700 dark:text-wema-300 border border-wema-300 dark:border-wema-600 hover:bg-wema-100 dark:hover:bg-wema-900/30'
                              : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 hover:border-wema-200 dark:hover:border-wema-700 border border-transparent'
                          }`}
                        >
                          <div className={`p-1.5 rounded ${isSelected ? 'bg-wema-200 dark:bg-wema-700' : 'bg-gray-100 dark:bg-gray-700'} transition-colors`}>
                            <FileText className="w-3 h-3 flex-shrink-0" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium truncate">
                              {doc.filename || 'Unknown Document'}
                            </div>
                            <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                              <span className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">
                                {(doc.fileType || 'UNKNOWN').toUpperCase()}
                              </span>
                              <span>{doc.chunkCount || 0} chunks</span>
                              {doc.isAnonymized && (
                                <span className="flex items-center gap-1 text-green-600 dark:text-green-400">
                                  <Shield className="w-3 h-3" />
                                </span>
                              )}
                            </div>
                          </div>
                          {isSelected ? (
                            <CheckCircle className="w-4 h-4 text-wema-500 flex-shrink-0" />
                          ) : (
                            <div className="w-4 h-4 border border-gray-300 dark:border-gray-600 rounded-full flex-shrink-0 group-hover:border-wema-400 transition-colors"></div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>


    </div>
  );
};

export default DocumentChatIntegration;
