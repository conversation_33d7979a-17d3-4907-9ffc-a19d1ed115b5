/**
 * 🧪 Test avec Gemma au lieu de Qwen
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

// Fonction pour nettoyer la réponse
function cleanAIResponse(response) {
  // Supprimer les balises <think> et </think>
  let cleaned = response.replace(/<think>[\s\S]*?<\/think>/g, '');
  
  // Supprimer les markdown
  cleaned = cleaned.replace(/```json\s*/g, '');
  cleaned = cleaned.replace(/```\s*/g, '');
  
  // Supprimer les espaces en début/fin
  cleaned = cleaned.trim();
  
  // Chercher le JSON dans la réponse
  const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    return jsonMatch[0];
  }
  
  return cleaned;
}

async function testWithGemma() {
  console.log('🧪 Test compression avec Gemma...');
  
  const systemPrompt = `You are a conversation compression expert. You must respond ONLY with valid JSON, no other text.

Required JSON structure:
{
  "summary": "conversation summary",
  "key_points": ["point 1", "point 2"],
  "documents": ["document 1"]
}

Rules:
- No markdown formatting
- No explanations
- Only pure JSON output`;

  const userPrompt = `Analyze this conversation and respond with JSON only:

CONVERSATION:
[USER] Hello, I have a CV to analyze
[ASSISTANT] Hello! I can help you analyze your CV
[USER] Here is my CV: Jean Dupont, developer with 5 years experience in React and Node.js
[ASSISTANT] Excellent profile! Your React and Node.js experience is highly sought after

JSON response:`;

  try {
    console.log('📤 Testing with Gemma...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'google/gemma-3-4b', // Utiliser Gemma
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        temperature: 0.1,
        max_tokens: 200,
        stream: false
      })
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Erreur Gemma:', response.status, errorText);
      return false;
    }
    
    const result = await response.json();
    const rawResponse = result.choices?.[0]?.message?.content || '';
    
    console.log(`⏱️ Temps de réponse Gemma: ${duration}ms`);
    console.log('📝 Réponse brute Gemma:', rawResponse);
    
    // Nettoyer la réponse
    const cleanedResponse = cleanAIResponse(rawResponse);
    console.log('🧹 Réponse nettoyée:', cleanedResponse);
    
    try {
      const parsed = JSON.parse(cleanedResponse);
      console.log('✅ JSON valide avec Gemma:', parsed);
      
      // Vérifier la qualité
      const hasGoodSummary = parsed.summary?.length > 10;
      const hasKeyPoints = parsed.key_points?.length > 0;
      const hasDocuments = parsed.documents?.length > 0;
      
      console.log('\n📊 Qualité Gemma:');
      console.log(`Résumé: ${hasGoodSummary ? '✅' : '❌'}`);
      console.log(`Points clés: ${hasKeyPoints ? '✅' : '❌'}`);
      console.log(`Documents: ${hasDocuments ? '✅' : '❌'}`);
      
      const qualityScore = [hasGoodSummary, hasKeyPoints, hasDocuments].filter(Boolean).length;
      console.log(`Score qualité: ${qualityScore}/3`);
      
      if (qualityScore >= 2) {
        console.log('\n🎉 GEMMA FONCTIONNE POUR LA COMPRESSION !');
        return { success: true, duration, parsed, qualityScore, model: 'gemma-3-4b' };
      }
      
    } catch (parseError) {
      console.log('❌ JSON invalide avec Gemma:', parseError.message);
    }
    
  } catch (error) {
    console.error('❌ Erreur test Gemma:', error.message);
  }
  
  return false;
}

async function testQwenWithStrictPrompt() {
  console.log('\n🧪 Test Qwen avec prompt ultra-strict...');
  
  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-4b',
        messages: [
          {
            role: 'system',
            content: 'Respond only with valid JSON. No thinking, no explanations, no markdown. Pure JSON only.'
          },
          {
            role: 'user',
            content: 'Create JSON: {"test": "hello", "number": 42}'
          }
        ],
        temperature: 0.0,
        max_tokens: 50,
        stream: false,
        stop: ["<think>", "\n\n"]
      })
    });
    
    if (!response.ok) {
      console.log('❌ Erreur Qwen strict');
      return false;
    }
    
    const result = await response.json();
    const rawResponse = result.choices?.[0]?.message?.content || '';
    
    console.log('📝 Qwen strict response:', rawResponse);
    
    const cleaned = cleanAIResponse(rawResponse);
    
    try {
      const parsed = JSON.parse(cleaned);
      console.log('✅ Qwen peut faire du JSON avec prompt strict !');
      return true;
    } catch (e) {
      console.log('❌ Qwen échoue même avec prompt strict');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Erreur test Qwen strict:', error.message);
    return false;
  }
}

async function testCompressionPerformance() {
  console.log('\n🧠 Test performance compression réelle...');
  
  const longConversation = `CONVERSATION:
[USER] Bonjour ! J'ai besoin d'aide pour analyser mon CV et préparer un entretien d'embauche.
[ASSISTANT] Bonjour ! Je serais ravi de vous aider avec votre CV et la préparation d'entretien. Pouvez-vous partager votre CV ?
[USER] Voici mon CV : Jean Dupont, développeur Full-Stack senior avec 8 ans d'expérience. J'ai travaillé chez TechCorp sur des applications React/Node.js pour 500K+ utilisateurs. J'ai dirigé une équipe de 5 développeurs et amélioré les performances de 40%. Mes compétences incluent React, TypeScript, Node.js, PostgreSQL, AWS, Docker, Kubernetes.
[ASSISTANT] Excellent profil ! Votre expérience de leadership et vos réalisations quantifiées sont très impressionnantes. Points forts : progression claire, stack moderne, impact mesurable. Suggestions : ajouter certifications AWS, détailler soft skills, inclure métriques business.
[USER] J'ai un entretien chez une fintech demain. Quelles questions techniques puis-je attendre ?
[ASSISTANT] Pour une fintech, préparez-vous à des questions sur : sécurité des données financières, performance haute charge, conformité réglementaire, architecture distribuée, gestion d'erreurs critiques.
[USER] Peux-tu simuler un entretien technique ?
[ASSISTANT] Bien sûr ! Question : "Comment optimiseriez-vous une API qui traite 5 millions de transactions par jour avec des exigences de latence < 100ms ?"`;

  const systemPrompt = `You are a conversation compression expert. Respond ONLY with valid JSON.

Structure:
{
  "conversationSummary": "complete narrative summary",
  "keyDecisions": ["decision 1", "decision 2"],
  "importantFacts": ["fact 1", "fact 2"],
  "documentsContext": {
    "documentsUsed": ["CV Jean Dupont"],
    "documentsSummary": "summary of documents"
  }
}

No markdown, no explanations, pure JSON only.`;

  try {
    console.log('📤 Testing compression performance...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'google/gemma-3-4b',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Compress this conversation to JSON:\n\n${longConversation}`
          }
        ],
        temperature: 0.1,
        max_tokens: 600,
        stream: false
      })
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      console.log('❌ Erreur compression performance');
      return false;
    }
    
    const result = await response.json();
    const rawResponse = result.choices?.[0]?.message?.content || '';
    const cleanedResponse = cleanAIResponse(rawResponse);
    
    console.log(`⏱️ Temps compression: ${duration}ms`);
    console.log('📝 Compression result:', cleanedResponse.substring(0, 300) + '...');
    
    try {
      const parsed = JSON.parse(cleanedResponse);
      console.log('✅ Compression performance réussie !');
      
      // Calculer les métriques
      const originalChars = longConversation.length;
      const compressedChars = cleanedResponse.length;
      const compressionRatio = originalChars / compressedChars;
      
      console.log('\n📊 MÉTRIQUES RÉELLES:');
      console.log(`Original: ${originalChars} caractères`);
      console.log(`Compressé: ${compressedChars} caractères`);
      console.log(`Ratio: ${compressionRatio.toFixed(2)}x`);
      console.log(`Temps: ${duration}ms`);
      console.log(`Efficacité: ${((1 - compressedChars/originalChars) * 100).toFixed(1)}%`);
      
      // Vérifier la qualité
      const hasGoodSummary = parsed.conversationSummary?.length > 50;
      const hasDecisions = parsed.keyDecisions?.length > 0;
      const hasDocuments = parsed.documentsContext?.documentsUsed?.length > 0;
      
      console.log('\n📋 QUALITÉ:');
      console.log(`Résumé substantiel: ${hasGoodSummary ? '✅' : '❌'}`);
      console.log(`Décisions extraites: ${hasDecisions ? '✅' : '❌'}`);
      console.log(`Documents identifiés: ${hasDocuments ? '✅' : '❌'}`);
      
      const qualityScore = [hasGoodSummary, hasDecisions, hasDocuments].filter(Boolean).length;
      
      if (qualityScore >= 2 && duration < 10000) {
        console.log('\n🎉 COMPRESSION RÉELLE VALIDÉE !');
        return {
          success: true,
          duration,
          compressionRatio,
          qualityScore,
          model: 'gemma-3-4b'
        };
      }
      
    } catch (e) {
      console.log('❌ JSON invalide en compression performance');
    }
    
  } catch (error) {
    console.log('❌ Erreur test performance:', error.message);
  }
  
  return false;
}

// Test principal
async function runRealTests() {
  console.log('🧪 TESTS RÉELS LM STUDIO - MODÈLES MULTIPLES\n');
  
  // Test Qwen strict
  const qwenWorks = await testQwenWithStrictPrompt();
  
  // Test Gemma
  const gemmaResult = await testWithGemma();
  
  if (gemmaResult.success) {
    console.log('\n✅ Gemma fonctionne ! Test de performance...');
    
    // Test performance
    const perfResult = await testCompressionPerformance();
    
    console.log('\n🎯 RÉSULTATS FINAUX:');
    console.log('==================');
    
    if (perfResult.success) {
      console.log('🎉 SYSTÈME COMPRESSION IA VALIDÉ !');
      console.log(`🤖 Modèle optimal: ${perfResult.model}`);
      console.log(`⏱️ Performance: ${perfResult.duration}ms`);
      console.log(`📊 Compression: ${perfResult.compressionRatio.toFixed(2)}x`);
      console.log(`🎯 Qualité: ${perfResult.qualityScore}/3`);
      console.log('\n✅ PRÊT POUR INTÉGRATION PRODUCTION !');
      
      // Recommandations
      console.log('\n💡 RECOMMANDATIONS:');
      console.log('- Utiliser Gemma 3-4B pour la compression');
      console.log('- Temps de réponse acceptable (< 10s)');
      console.log('- Qualité de compression validée');
      console.log('- Intégration en arrière-plan recommandée');
      
    } else {
      console.log('⚠️ Gemma fonctionne mais performance à optimiser');
    }
    
  } else {
    console.log('\n❌ Problèmes avec tous les modèles testés');
    console.log('🔧 Ajustements nécessaires ou changement de modèle');
    
    if (qwenWorks) {
      console.log('💡 Qwen peut fonctionner avec prompt très strict');
    }
  }
}

runRealTests().catch(console.error);
