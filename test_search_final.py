#!/usr/bin/env python3
"""
Test final de la recherche internet avec scraping
"""

import requests
import json
import time

def test_search_with_real_scraping():
    """Test avec scraping réel"""
    
    print("🔍 Test final - Recherche avec scraping réel...")
    
    search_data = {
        "query": "Python FastAPI tutorial",
        "search_type": "technical",
        "max_results": 2
    }
    
    try:
        print(f"📡 Recherche: {search_data['query']}")
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:5001/search/internet",
            json=search_data,
            timeout=45
        )
        search_time = time.time() - start_time
        
        print(f"📊 Statut: {response.status_code}")
        print(f"⏱️ Temps: {search_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Recherche réussie!")
            
            if 'metadata' in data:
                metadata = data['metadata']
                sources = metadata.get('sources', [])
                scraped = metadata.get('scraped_content', [])
                
                print(f"📈 Résultats:")
                print(f"  - Sources: {len(sources)}")
                print(f"  - Contenu scrapé: {len(scraped)}")
                
                if scraped:
                    print(f"📄 Contenu scrapé détaillé:")
                    for i, content in enumerate(scraped, 1):
                        print(f"  {i}. {content.get('title', 'Sans titre')}")
                        print(f"     URL: {content.get('url', '')}")
                        print(f"     Mots: {content.get('word_count', 0)}")
                        print(f"     Temps: {content.get('scrape_time', 0):.2f}s")
                        if content.get('content'):
                            preview = content['content'][:100] + "..." if len(content['content']) > 100 else content['content']
                            print(f"     Contenu: {preview}")
                        print()
                else:
                    print("⚠️ Pas de contenu scrapé")
            
            # Afficher le résultat final
            if 'result' in data:
                result = data['result']
                print(f"📝 Résultat final ({len(result)} caractères):")
                if len(result) > 500:
                    print(f"{result[:500]}...")
                else:
                    print(result)
            
            return len(scraped) > 0 if 'metadata' in data else False
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test final de la recherche internet avec scraping")
    print("=" * 60)
    
    success = test_search_with_real_scraping()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCÈS - Recherche avec scraping fonctionne parfaitement!")
    else:
        print("⚠️ Le scraping ne fonctionne pas encore - mais la recherche de base marche")
