/**
 * 🧪 TEST RÉEL avec LM Studio et Qwen 8B
 * 
 * Test complet du système de compression avec vraie IA
 */

import fetch from 'node-fetch';

// Configuration LM Studio
const LM_STUDIO_URL = 'http://localhost:1234';
const MODEL_NAME = 'qwen3-8b'; // Ou le nom exact du modèle chargé

// Messages de test (conversation longue)
const testMessages = [
  {
    id: "msg-001",
    role: "user",
    content: "Salut ! J'ai besoin d'aide pour analyser mon CV et préparer un entretien d'embauche.",
    timestamp: new Date("2024-06-16T08:00:00Z")
  },
  {
    id: "msg-002", 
    role: "assistant",
    content: "Bonjour ! Je serais ravi de vous aider avec votre CV et la préparation d'entretien. Pouvez-vous partager votre CV pour que je puisse l'analyser ?",
    timestamp: new Date("2024-06-16T08:00:30Z")
  },
  {
    id: "msg-003",
    role: "user", 
    content: `Voici mon CV, peux-tu l'analyser stp et me donner des conseils ?

JEAN DUPONT - Développeur Full-Stack Senior
📧 <EMAIL> | 📱 +33 6 12 34 56 78

PROFIL PROFESSIONNEL
Développeur Full-Stack passionné avec 8 ans d'expérience dans le développement d'applications web modernes. Expert en JavaScript, React, Node.js et architectures cloud. Reconnu pour ma capacité à livrer des solutions techniques innovantes et à diriger des équipes de développement.

EXPÉRIENCE PROFESSIONNELLE

Senior Full-Stack Developer | TechCorp Solutions | 2020 - Présent
• Développement et maintenance d'applications React/Node.js pour 500K+ utilisateurs
• Architecture et implémentation de microservices sur AWS (Lambda, ECS, RDS)
• Encadrement d'une équipe de 5 développeurs juniors
• Amélioration des performances applicatives de 40% via optimisation du code
• Mise en place de pipelines CI/CD avec Jenkins et Docker
• Technologies: React, TypeScript, Node.js, PostgreSQL, Redis, AWS

Lead Developer | StartupTech | 2018 - 2020
• Création from scratch d'une plateforme SaaS B2B (0 à 10K utilisateurs)
• Développement d'APIs REST et GraphQL haute performance
• Implémentation d'architecture event-driven avec RabbitMQ
• Gestion de base de données MongoDB et optimisation des requêtes
• Technologies: Vue.js, Express.js, MongoDB, Docker, Kubernetes

Full-Stack Developer | WebAgency Pro | 2016 - 2018
• Développement de sites e-commerce sur Shopify et WooCommerce
• Intégration de systèmes de paiement (Stripe, PayPal, Adyen)
• Optimisation SEO et performance web (Core Web Vitals)
• Collaboration avec équipes design et marketing
• Technologies: PHP, Laravel, MySQL, jQuery, SASS

FORMATION
Master en Informatique | Université Paris-Saclay | 2014 - 2016
Licence en Informatique | Université Paris-Sud | 2011 - 2014

COMPÉTENCES TECHNIQUES
Frontend: React, Vue.js, Angular, TypeScript, HTML5, CSS3, SASS
Backend: Node.js, Express, PHP, Laravel, Python, Django
Bases de données: PostgreSQL, MongoDB, MySQL, Redis
Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, GitLab CI
Outils: Git, Jira, Figma, Postman, VS Code

PROJETS PERSONNELS
• EcoTracker - App mobile React Native pour suivi empreinte carbone (5K téléchargements)
• DevTools Extension - Extension Chrome pour développeurs (2K utilisateurs actifs)
• Open Source - Contributeur actif sur plusieurs projets GitHub (500+ stars)

LANGUES
Français: Natif | Anglais: Courant (TOEIC 950) | Espagnol: Intermédiaire`,
    timestamp: new Date("2024-06-16T08:01:00Z")
  }
];

// Ajouter plus de messages pour dépasser 20K caractères
for (let i = 4; i <= 30; i++) {
  testMessages.push({
    id: `msg-${i.toString().padStart(3, '0')}`,
    role: i % 2 === 0 ? 'assistant' : 'user',
    content: `Message ${i} - Analyse détaillée du CV de Jean Dupont. Ce développeur Full-Stack senior présente un profil très solide avec 8 années d'expérience progressive. Son parcours montre une évolution claire depuis WebAgency Pro vers des postes de plus en plus techniques et managériaux chez StartupTech puis TechCorp. Les compétences techniques sont modernes et recherchées : React, Node.js, AWS, architecture microservices. L'expérience de leadership avec encadrement d'équipe de 5 développeurs est un atout majeur. Les réalisations sont bien quantifiées (500K+ utilisateurs, amélioration 40% performance). La formation Master en Informatique de Paris-Saclay est solide. Les projets personnels montrent la passion et l'engagement (EcoTracker, DevTools Extension). Points d'amélioration suggérés : ajouter des certifications AWS, détailler davantage les soft skills, inclure des métriques business. Pour l'entretien fintech, se préparer sur la sécurité, la conformité réglementaire, et les architectures haute performance.`,
    timestamp: new Date(`2024-06-16T08:${(i + 10).toString().padStart(2, '0')}:00Z`)
  });
}

// Fonctions de test
async function testLMStudioConnection() {
  console.log('🔗 Test de connexion LM Studio...');
  
  try {
    const response = await fetch(`${LM_STUDIO_URL}/v1/models`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const models = await response.json();
    console.log('✅ Connexion LM Studio réussie');
    console.log('📋 Modèles disponibles:', models.data?.map(m => m.id) || 'Aucun');
    
    return models.data || [];
  } catch (error) {
    console.error('❌ Erreur connexion LM Studio:', error.message);
    console.log('💡 Vérifiez que LM Studio est démarré sur localhost:1234');
    return null;
  }
}

async function testCompressionWithRealAI(messages) {
  console.log('\n🧠 Test compression avec vraie IA...');
  
  const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
  console.log(`📊 Conversation: ${messages.length} messages, ${totalChars} caractères`);
  
  // Construire le prompt de compression
  const conversationText = messages.map((msg, index) => {
    const timestamp = msg.timestamp.toLocaleString('fr-FR');
    return `[MESSAGE ${index + 1}] [${msg.role.toUpperCase()}] [${timestamp}]
${msg.content}`;
  }).join('\n\n---\n\n');

  const compressionPrompt = `🧠 MISSION CRITIQUE: COMPRESSION INTELLIGENTE DE CONVERSATION

📊 CONTEXTE:
- Messages à analyser: ${messages.length}
- Taille actuelle: ${totalChars} caractères
- Fenêtre contexte modèle: 100000 tokens
- Cible compression: 28000 caractères (ratio: 0.7)
- Éléments prioritaires: documents, decisions, recent_messages, chronological_flow

🎯 MISSION:
Tu dois analyser cette conversation et créer une compression PARFAITE qui permet à un LLM de continuer la conversation naturellement, sans perdre AUCUNE information critique.

📋 CONVERSATION COMPLÈTE:
${conversationText}

⚠️ INSTRUCTIONS CRITIQUES:
1. ANALYSER le fil chronologique complet
2. IDENTIFIER toutes les décisions et conclusions
3. PRÉSERVER les références aux documents/fichiers
4. MAINTENIR le contexte relationnel
5. GARDER les informations factuelles importantes
6. RESPECTER la personnalité et le ton
7. ASSURER la continuité narrative

🎯 RÉPONSE REQUISE (JSON STRICT):
{
  "conversationSummary": "Résumé narratif complet maintenant le fil et le contexte",
  "chronologicalFlow": "Chronologie des événements et échanges importants",
  "keyDecisions": ["Décision/conclusion 1", "Décision/conclusion 2"],
  "importantFacts": ["Fait important 1", "Fait important 2"],
  "criticalMessageIds": ["${messages[messages.length - 5]?.id || ''}", "${messages[messages.length - 1]?.id || ''}"],
  "documentsContext": {
    "documentsUsed": ["nom_document.ext"],
    "documentsSummary": "Résumé du contenu des documents utilisés",
    "keyExtracts": ["Extrait important du document"]
  }
}

🚨 QUALITÉ REQUISE: PARFAITE - La conversation doit pouvoir continuer naturellement après compression.`;

  const systemPrompt = `Tu es un expert en compression intelligente de conversations pour IA. Ta mission est CRITIQUE pour maintenir la continuité parfaite d'une conversation.

🎯 OBJECTIF PRINCIPAL:
Compresser une conversation en préservant ABSOLUMENT TOUT ce qui est important pour qu'un LLM puisse continuer la conversation naturellement.

🧠 ANALYSE REQUISE:
1. Identifier le FIL CHRONOLOGIQUE de la conversation
2. Extraire les DÉCISIONS et CONCLUSIONS importantes
3. Préserver les RÉFÉRENCES aux documents/fichiers
4. Maintenir le CONTEXTE RELATIONNEL (qui a dit quoi)
5. Garder les INFORMATIONS FACTUELLES critiques

⚠️ RÈGLES ABSOLUES:
- JAMAIS perdre d'information critique
- TOUJOURS maintenir la chronologie
- PRÉSERVER les nuances et le ton
- GARDER les références exactes
- MAINTENIR la cohérence narrative

📋 FORMAT DE SORTIE:
Réponds UNIQUEMENT en JSON valide avec cette structure exacte.

🚨 ATTENTION: La qualité de ta compression détermine si la conversation peut continuer naturellement. Sois PARFAIT.`;

  try {
    console.log('📤 Envoi à LM Studio...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: MODEL_NAME,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user', 
            content: compressionPrompt
          }
        ],
        temperature: 0.1,
        max_tokens: 4000,
        stream: false
      })
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    const aiResponse = result.choices?.[0]?.message?.content || '';
    
    console.log(`⏱️ Temps de réponse: ${duration}ms`);
    console.log(`📝 Longueur réponse: ${aiResponse.length} caractères`);
    console.log('📋 Réponse IA (premiers 500 chars):');
    console.log(aiResponse.substring(0, 500) + '...');
    
    // Tenter de parser le JSON
    try {
      const parsed = JSON.parse(aiResponse);
      console.log('\n✅ JSON valide reçu !');
      console.log('📊 Structure:', Object.keys(parsed));
      
      // Calculer les métriques
      const originalChars = totalChars;
      const compressedChars = aiResponse.length;
      const compressionRatio = originalChars / compressedChars;
      
      console.log(`\n📈 MÉTRIQUES DE COMPRESSION:`);
      console.log(`Original: ${originalChars} caractères`);
      console.log(`Compressé: ${compressedChars} caractères`);
      console.log(`Ratio: ${compressionRatio.toFixed(2)}x`);
      console.log(`Efficacité: ${((1 - compressedChars/originalChars) * 100).toFixed(1)}%`);
      
      return {
        success: true,
        duration,
        originalChars,
        compressedChars,
        compressionRatio,
        parsed,
        rawResponse: aiResponse
      };
      
    } catch (parseError) {
      console.log('\n❌ Erreur parsing JSON:', parseError.message);
      console.log('📋 Réponse brute:', aiResponse);
      
      return {
        success: false,
        duration,
        error: 'JSON invalide',
        rawResponse: aiResponse
      };
    }
    
  } catch (error) {
    console.error('❌ Erreur appel LM Studio:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test principal
async function runRealTest() {
  console.log('🧪 TEST RÉEL DE COMPRESSION IA AVEC LM STUDIO');
  console.log('==============================================\n');
  
  // 1. Test connexion
  const models = await testLMStudioConnection();
  if (!models) {
    console.log('\n❌ Impossible de continuer sans LM Studio');
    return;
  }
  
  // 2. Vérifier si le modèle est disponible
  const availableModel = models.find(m => m.id.includes('qwen') || m.id.includes('8b'));
  if (availableModel) {
    console.log(`✅ Modèle trouvé: ${availableModel.id}`);
  } else {
    console.log('⚠️ Aucun modèle Qwen 8B trouvé, utilisation du premier disponible');
    if (models.length > 0) {
      console.log(`📋 Utilisation de: ${models[0].id}`);
    }
  }
  
  // 3. Test compression
  const result = await testCompressionWithRealAI(testMessages);
  
  // 4. Analyse des résultats
  console.log('\n🎯 RÉSULTATS FINAUX:');
  console.log('==================');
  
  if (result.success) {
    console.log('✅ Test réussi !');
    console.log(`⏱️ Performance: ${result.duration}ms`);
    console.log(`📊 Compression: ${result.compressionRatio.toFixed(2)}x`);
    console.log(`🎯 Qualité: JSON valide avec structure complète`);
    
    // Vérifier la qualité du contenu
    const quality = result.parsed;
    const hasGoodSummary = quality.conversationSummary?.length > 100;
    const hasDecisions = quality.keyDecisions?.length > 0;
    const hasDocuments = quality.documentsContext?.documentsUsed?.length > 0;
    const hasFlow = quality.chronologicalFlow?.length > 50;
    
    console.log('\n📋 QUALITÉ DU CONTENU:');
    console.log(`Résumé substantiel: ${hasGoodSummary ? '✅' : '❌'}`);
    console.log(`Décisions extraites: ${hasDecisions ? '✅' : '❌'}`);
    console.log(`Documents identifiés: ${hasDocuments ? '✅' : '❌'}`);
    console.log(`Fil chronologique: ${hasFlow ? '✅' : '❌'}`);
    
    const qualityScore = [hasGoodSummary, hasDecisions, hasDocuments, hasFlow].filter(Boolean).length;
    console.log(`Score qualité: ${qualityScore}/4`);
    
    if (qualityScore >= 3) {
      console.log('\n🎉 SYSTÈME VALIDÉ ! Prêt pour production.');
    } else {
      console.log('\n⚠️ Qualité à améliorer. Ajustement du prompt nécessaire.');
    }
    
  } else {
    console.log('❌ Test échoué');
    console.log(`Erreur: ${result.error}`);
    
    if (result.rawResponse) {
      console.log('\n📋 Réponse brute pour debug:');
      console.log(result.rawResponse.substring(0, 1000));
    }
  }
}

// Exécution
runRealTest().catch(console.error);
