#!/usr/bin/env python3
"""
🚀 Script de démarrage du serveur d'inférence WeMa IA
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path

def check_lm_studio():
    """Vérifier si LM Studio est en cours d'exécution"""
    try:
        response = requests.get("http://localhost:1234/v1/models", timeout=3)
        return response.status_code == 200
    except:
        return False

def wait_for_lm_studio(max_wait=60):
    """Attendre que LM Studio soit disponible"""
    print("🔍 Vérification de LM Studio...")
    
    for i in range(max_wait):
        if check_lm_studio():
            print("✅ LM Studio détecté et fonctionnel")
            return True
        
        if i == 0:
            print("⚠️ LM Studio non détecté")
            print("📋 Actions requises :")
            print("   1. Démarrez LM Studio")
            print("   2. Chargez un modèle (ex: Qwen 1.7B)")
            print("   3. Activez le serveur local (port 1234)")
            print()
            print("⏳ Attente de LM Studio...")
        
        time.sleep(1)
        if i % 10 == 9:  # Afficher un point toutes les 10 secondes
            print(f"   Attente... ({i+1}s)")
    
    return False

def start_inference_server():
    """Démarrer le serveur d'inférence"""
    print("🧠 Démarrage du serveur d'inférence WeMa IA")
    print("=" * 50)
    
    # Vérifier Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ requis")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Vérifier les dépendances
    required_packages = ['fastapi', 'uvicorn', 'requests', 'pydantic']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Packages manquants: {', '.join(missing_packages)}")
        print("📦 Installez avec: pip install fastapi uvicorn requests pydantic")
        return False
    
    print("✅ Toutes les dépendances sont installées")
    
    # Attendre LM Studio
    if not wait_for_lm_studio():
        print("❌ LM Studio non disponible après 60 secondes")
        print("🔧 Démarrez LM Studio manuellement et relancez ce script")
        return False
    
    # Obtenir les modèles disponibles
    try:
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        models = response.json().get("data", [])
        print(f"📋 {len(models)} modèles disponibles dans LM Studio")
        for model in models[:3]:  # Afficher les 3 premiers
            print(f"   - {model.get('id', 'Unknown')}")
        if len(models) > 3:
            print(f"   ... et {len(models) - 3} autres")
    except:
        print("⚠️ Impossible de lister les modèles")
    
    print()
    print("🚀 Démarrage du serveur d'inférence...")
    print("📡 Port: 1235")
    print("🔗 LM Studio: http://localhost:1234")
    print("🌐 Serveur: http://0.0.0.0:1235")
    print()
    print("🛑 Appuyez sur Ctrl+C pour arrêter")
    print("=" * 50)
    
    # Démarrer le serveur
    try:
        # Changer vers le répertoire du script
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        # Lancer uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "inference_server:app",
            "--host", "0.0.0.0",
            "--port", "1235",
            "--log-level", "info",
            "--reload"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur d'inférence")
        return True
    except Exception as e:
        print(f"❌ Erreur démarrage serveur: {e}")
        return False

if __name__ == "__main__":
    success = start_inference_server()
    if not success:
        input("Appuyez sur Entrée pour fermer...")
        sys.exit(1)
