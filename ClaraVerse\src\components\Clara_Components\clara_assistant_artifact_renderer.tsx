/**
 * Clara Assistant Artifact Renderer
 * 
 * This component handles rendering of various artifact types generated by <PERSON>,
 * including code blocks, charts, tables, HTML, markdown, and more.
 * 
 * Features:
 * - Syntax highlighting for code
 * - Interactive charts and tables
 * - HTML preview with sandboxing
 * - Copy and download functionality
 * - Theme-aware rendering
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Copy,
  Check,
  Download,
  Eye,
  EyeOff,
  Code,
  BarChart3,
  FileText,
  Table,
  Globe,
  FileCode,
  Maximize2,
  Minimize2,
  Play,
  Square,
  Image as ImageIcon
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import mermaid from 'mermaid';

// Import types
import { 
  ClaraArtifact, 
  ClaraArtifactRendererProps,
  ClaraArtifactType 
} from '../../types/clara_assistant_types';
import { copyToClipboard } from '../../utils/clipboard';

/**
 * Simple syntax highlighter for code blocks
 * This is a basic implementation - can be enhanced with proper syntax highlighting libraries
 */
const CodeHighlighter: React.FC<{ 
  code: string; 
  language: string; 
  isDark: boolean;
}> = ({ code, language, isDark }) => {
  const getLanguageColor = (lang: string) => {
    const colors = {
      javascript: 'text-yellow-600 dark:text-yellow-400',
      typescript: 'text-blue-600 dark:text-blue-400',
      python: 'text-green-600 dark:text-green-400',
      java: 'text-red-600 dark:text-red-400',
      cpp: 'text-purple-600 dark:text-purple-400',
      html: 'text-orange-600 dark:text-orange-400',
      css: 'text-pink-600 dark:text-pink-400',
      json: 'text-gray-600 dark:text-gray-400',
    };
    return colors[lang as keyof typeof colors] || 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className="relative">
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <span className={`text-sm font-medium ${getLanguageColor(language)}`}>
          {language.toUpperCase()}
        </span>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {code.split('\n').length} lines
        </span>
      </div>
      <pre className="p-4 overflow-x-auto text-sm bg-white dark:bg-gray-900">
        <code className="text-gray-800 dark:text-gray-200 font-mono">
          {code}
        </code>
      </pre>
    </div>
  );
};

/**
 * Table renderer for structured data
 */
const TableRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [tableData, setTableData] = useState<any[] | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed) && parsed.length > 0) {
        setTableData(parsed);
        setError(null);
      } else {
        setError('Invalid table data format');
      }
    } catch (e) {
      setError('Failed to parse table data');
    }
  }, [content]);

  if (error) {
    return (
      <div className="p-4 text-center text-red-600 dark:text-red-400">
        {error}
      </div>
    );
  }

  if (!tableData) {
    return (
      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
        Loading table data...
      </div>
    );
  }

  const headers = Object.keys(tableData[0] || {});

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            {headers.map((header) => (
              <th
                key={header}
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {tableData.map((row, idx) => (
            <tr key={idx} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              {headers.map((header) => (
                <td
                  key={header}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                >
                  {String(row[header] || '')}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

/**
 * HTML preview with safe rendering
 */
const HtmlRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' : ''}`}>
      {isFullscreen && (
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={() => setIsFullscreen(false)}
            className="p-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Minimize2 className="w-4 h-4" />
          </button>
        </div>
      )}
      
      <div className={`w-full ${isFullscreen ? 'h-full' : 'h-96'} border border-gray-200 dark:border-gray-700`}>
        <iframe
          srcDoc={content}
          className="w-full h-full"
          sandbox="allow-scripts"
          title="HTML Preview"
        />
      </div>
      
      {!isFullscreen && (
        <button
          onClick={() => setIsFullscreen(true)}
          className="absolute top-2 right-2 p-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <Maximize2 className="w-3 h-3 text-gray-600 dark:text-gray-400" />
        </button>
      )}
    </div>
  );
};

/**
 * Markdown renderer (basic implementation)
 */
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  return (
    <div className="prose prose-sm dark:prose-invert max-w-none p-4">
      <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
        {content}
      </pre>
    </div>
  );
};

/**
 * 📊 Mermaid Diagram Renderer - ACTIVÉ !
 */
const MermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [diagramHeight, setDiagramHeight] = useState<number | null>(null);

  useEffect(() => {
    const renderMermaid = async () => {
      if (!mermaidRef.current || !content.trim()) return;

      try {
        setIsLoading(true);
        setError(null);

        // Initialiser Mermaid avec configuration sombre/claire
        mermaid.initialize({
          startOnLoad: false,
          theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, sans-serif'
        });

        // Générer un ID unique pour ce diagramme
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Valider et rendre le diagramme
        const isValid = await mermaid.parse(content);
        if (isValid) {
          const { svg } = await mermaid.render(id, content);
          mermaidRef.current.innerHTML = svg;

          // 🚀 STABILITÉ: Capturer la hauteur réelle du diagramme
          setTimeout(() => {
            if (mermaidRef.current) {
              const svgElement = mermaidRef.current.querySelector('svg');
              if (svgElement) {
                const height = svgElement.getBoundingClientRect().height;
                setDiagramHeight(height);
              }
            }
          }, 100);
        } else {
          throw new Error('Invalid Mermaid syntax');
        }
      } catch (err) {
        console.error('Mermaid rendering error:', err);
        setError(err instanceof Error ? err.message : 'Failed to render diagram');
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = '';
        }
      } finally {
        setIsLoading(false);
      }
    };

    renderMermaid();
  }, [content]);

  if (error) {
    return (
      <div className="p-8 text-center bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
        <BarChart3 className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <p className="text-red-600 dark:text-red-400 mb-2">Erreur de rendu du diagramme</p>
        <p className="text-xs text-red-500 mb-4">{error}</p>
        <details className="text-left">
          <summary className="text-sm text-red-600 dark:text-red-400 cursor-pointer">
            Voir le code source
          </summary>
          <pre className="mt-2 text-xs text-red-500 bg-white dark:bg-gray-900 p-2 rounded border font-mono">
            {content}
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
      {/* 🚀 STABLE: Container adaptatif pour éviter les sauts */}
      <div className="relative w-full overflow-x-auto">
        {isLoading && (
          <div className="flex items-center justify-center py-16 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-wema-500"></div>
              <span className="text-gray-600 dark:text-gray-400">Génération du diagramme...</span>
            </div>
          </div>
        )}
        <div
          ref={mermaidRef}
          className={`mermaid-diagram ${isLoading ? 'hidden' : 'block'} transition-all duration-500 ease-out text-center w-full`}
          style={{
            transform: isLoading ? 'translateY(10px)' : 'translateY(0)',
            opacity: isLoading ? 0 : 1
          }}
        />
      </div>
    </div>
  );
};

/**
 * Chart placeholder (for future chart library integration)
 */
const ChartRenderer: React.FC<{ content: string; artifact: ClaraArtifact }> = ({ content, artifact }) => {
  return (
    <div className="p-8 text-center bg-gray-50 dark:bg-gray-800">
      <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <p className="text-gray-600 dark:text-gray-400 mb-2">Chart Rendering</p>
      <p className="text-xs text-gray-500 mb-4">
        Integration with Chart.js, D3, or similar library would go here
      </p>
      <details className="text-left">
        <summary className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
          View Chart Data
        </summary>
        <pre className="mt-2 text-xs text-gray-500 bg-white dark:bg-gray-900 p-2 rounded border">
          {content}
        </pre>
      </details>
    </div>
  );
};

/**
 * Get appropriate icon for artifact type
 */
const getArtifactIcon = (type: ClaraArtifactType) => {
  const icons = {
    code: Code,
    chart: BarChart3,
    table: Table,
    html: Globe,
    markdown: FileText,
    csv: Table,
    json: FileCode,
    mermaid: BarChart3,
    diagram: BarChart3,
    report: FileText,
  };
  
  return icons[type] || FileText;
};

/**
 * Main Clara Artifact Renderer Component
 */
const ClaraArtifactRenderer: React.FC<ClaraArtifactRendererProps> = ({
  artifact,
  isExpanded = false,
  onToggleExpanded,
  onCopy,
  onDownload
}) => {
  const [copied, setCopied] = useState(false);
  const [internalExpanded, setInternalExpanded] = useState(isExpanded);
  const [isDark, setIsDark] = useState(false);

  // Detect theme (basic implementation - can be enhanced with proper theme context)
  useEffect(() => {
    const checkTheme = () => {
      setIsDark(
        document.documentElement.classList.contains('dark') ||
        window.matchMedia('(prefers-color-scheme: dark)').matches
      );
    };
    
    checkTheme();
    window.addEventListener('storage', checkTheme);
    return () => window.removeEventListener('storage', checkTheme);
  }, []);

  const IconComponent = getArtifactIcon(artifact.type);
  const expanded = onToggleExpanded ? isExpanded : internalExpanded;

  const handleToggleExpanded = () => {
    if (onToggleExpanded) {
      onToggleExpanded(artifact.id);
    } else {
      setInternalExpanded(!internalExpanded);
    }
  };

  const handleCopy = async () => {
    const success = await copyToClipboard(artifact.content);
    if (success) {
      setCopied(true);
      onCopy?.(artifact.content);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleDownload = () => {
    try {
      const blob = new Blob([artifact.content], { 
        type: getContentType(artifact.type) 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${artifact.title}.${getFileExtension(artifact.type)}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      onDownload?.(artifact);
    } catch (error) {
      console.error('Failed to download artifact:', error);
    }
  };

  const getContentType = (type: ClaraArtifactType): string => {
    const types = {
      code: 'text/plain',
      html: 'text/html',
      markdown: 'text/markdown',
      csv: 'text/csv',
      json: 'application/json',
      table: 'application/json',
      chart: 'application/json',
      mermaid: 'text/plain',
      diagram: 'text/plain',
      report: 'text/plain',
    };
    return types[type] || 'text/plain';
  };

  const getFileExtension = (type: ClaraArtifactType): string => {
    const extensions = {
      code: artifact.language || 'txt',
      html: 'html',
      markdown: 'md',
      csv: 'csv',
      json: 'json',
      table: 'json',
      chart: 'json',
      mermaid: 'mmd',
      diagram: 'txt',
      report: 'txt',
    };
    return extensions[type] || 'txt';
  };

  const renderArtifactContent = () => {
    switch (artifact.type) {
      case 'code':
        return (
          <CodeHighlighter 
            code={artifact.content} 
            language={artifact.language || 'text'} 
            isDark={isDark}
          />
        );
      
      case 'table':
      case 'csv':
        return <TableRenderer content={artifact.content} />;
      
      case 'html':
        return <HtmlRenderer content={artifact.content} />;
      
      case 'markdown':
        return <MarkdownRenderer content={artifact.content} />;
      
      case 'chart':
        return <ChartRenderer content={artifact.content} artifact={artifact} />;
      
      case 'json':
        return (
          <CodeHighlighter 
            code={JSON.stringify(JSON.parse(artifact.content), null, 2)} 
            language="json" 
            isDark={isDark}
          />
        );
      
      case 'mermaid':
      case 'diagram':
        return <MermaidRenderer content={artifact.content} />;
      
      default:
        return (
          <pre className="p-4 text-sm whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 overflow-x-auto">
            {artifact.content}
          </pre>
        );
    }
  };

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <IconComponent className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {artifact.title}
          </span>
          {artifact.language && (
            <span className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded">
              {artifact.language}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {/* Toggle expanded button */}
          <button
            onClick={handleToggleExpanded}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
            title={expanded ? "Collapse" : "Expand"}
          >
            {expanded ? (
              <EyeOff className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            ) : (
              <Eye className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            )}
          </button>
          
          {/* Copy button */}
          <button
            onClick={handleCopy}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
            title="Copy content"
          >
            {copied ? (
              <Check className="w-4 h-4 text-green-500" />
            ) : (
              <Copy className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            )}
          </button>
          
          {/* Download button */}
          <button
            onClick={handleDownload}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
            title="Download artifact"
          >
            <Download className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </button>
        </div>
      </div>
      
      {/* Content */}
      {expanded && (
        <div className="max-h-96 overflow-auto">
          {renderArtifactContent()}
        </div>
      )}
      
      {/* Metadata footer */}
      {expanded && artifact.metadata && (
        <div className="px-3 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-4">
              {artifact.createdAt && (
                <span>Created: {artifact.createdAt.toLocaleString()}</span>
              )}
              {artifact.dependencies && artifact.dependencies.length > 0 && (
                <span>Dependencies: {artifact.dependencies.join(', ')}</span>
              )}
            </div>
            {artifact.isExecutable && (
              <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded">
                Executable
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ClaraArtifactRenderer; 