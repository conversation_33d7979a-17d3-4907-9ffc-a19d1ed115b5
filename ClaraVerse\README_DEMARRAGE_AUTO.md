# 🚀 DÉMARRAGE AUTOMATIQUE WEMA IA

## ✨ Fonctionnalités automatiques

WeMa IA démarre maintenant **automatiquement** tous les services nécessaires :

- 🐳 **Docker Desktop** (si installé)
- 🔍 **SearXNG** (recherche internet)
- 🌐 **Backend Python** (API)
- 💻 **Frontend React** (interface)

## 🎯 Démarrage en un clic

### **Windows**
```bash
# Double-cliquez sur le fichier ou exécutez :
start-wema-ia.bat
```

### **Linux/Mac**
```bash
./start-wema-ia.sh
```

### **<PERSON> (comme avant)**
```bash
# Terminal 1 - Backend
cd py_backend
python main.py

# Terminal 2 - Frontend  
cd ..
npm run dev
```

## 🔧 Ce qui se passe automatiquement

### **1. Démarrage du Backend**
- ✅ Vérification de Docker
- ✅ Démarrage automatique de Docker Desktop (si nécessaire)
- ✅ Démarrage de SearXNG
- ✅ Configuration de la recherche internet
- ✅ Initialisation de tous les services

### **2. Démarrage du Frontend**
- ✅ Compilation automatique
- ✅ Connexion au backend
- ✅ Interface prête à l'utilisation

## 📊 Surveillance du démarrage

### **Endpoint de statut**
```
GET http://localhost:5001/startup/status
```

**Réponse exemple :**
```json
{
  "success": true,
  "services": {
    "docker": {
      "status": "running",
      "message": "Docker démarré automatiquement"
    },
    "searxng": {
      "status": "running", 
      "message": "SearXNG démarré automatiquement"
    },
    "search": {
      "status": "operational",
      "message": "Recherche internet disponible"
    }
  }
}
```

## 🌐 Interfaces disponibles

| Service | URL | Description |
|---------|-----|-------------|
| **WeMa IA** | http://localhost:5173 | Interface principale |
| **Backend API** | http://localhost:5001 | API REST |
| **SearXNG** | http://localhost:8888 | Moteur de recherche |
| **Statut** | http://localhost:5001/startup/status | Statut des services |

## 🔍 Recherche Internet

### **Automatique**
- ✅ Docker se lance automatiquement
- ✅ SearXNG se configure automatiquement  
- ✅ Recherche internet opérationnelle immédiatement

### **Fallback intelligent**
- ⚠️ Si Docker n'est pas disponible → Mode fallback
- ⚠️ Si SearXNG échoue → Résultats d'exemple
- ✅ L'application fonctionne toujours

## 🛠️ Dépannage

### **Docker ne démarre pas**
```bash
# Vérifier l'installation
docker --version

# Démarrer manuellement
# Windows: Ouvrir Docker Desktop
# Linux: sudo systemctl start docker
```

### **SearXNG ne répond pas**
```bash
# Vérifier le conteneur
docker ps | grep searxng

# Redémarrer SearXNG
cd searxng
docker-compose restart
```

### **Backend ne démarre pas**
```bash
# Vérifier les dépendances
cd py_backend
pip install -r requirements.txt

# Démarrer en mode debug
python main.py
```

## 📈 Logs de démarrage

Le backend affiche des logs détaillés :

```
🚀 Starting WeMa IA Backend on 0.0.0.0:5001
🔧 Démarrage automatique de Docker et SearXNG...
🐳 Vérification de Docker...
✅ Docker est déjà en cours d'exécution
🔍 Vérification de SearXNG...
✅ SearXNG démarré avec succès
🔍 Vérification du service de recherche...
✅ Service de recherche internet opérationnel
🎉 WeMa IA prêt à l'utilisation !
```

## 🎯 Avantages

### **Pour l'utilisateur**
- 🚀 **Un seul clic** pour tout démarrer
- ⚡ **Démarrage rapide** et automatique
- 🔧 **Configuration automatique** des services
- 📊 **Monitoring** en temps réel

### **Pour le développement**
- 🛠️ **Environnement cohérent** 
- 🔄 **Reproductibilité** garantie
- 🐛 **Debugging** facilité
- 📦 **Déploiement** simplifié

## 🚀 Prochaines étapes

1. **Double-cliquez** sur `start-wema-ia.bat` (Windows)
2. **Attendez** le démarrage automatique (30-60 secondes)
3. **Ouvrez** http://localhost:5173
4. **Profitez** de WeMa IA avec recherche internet ! 🎉

---

**🎉 WeMa IA est maintenant entièrement automatisé !**
