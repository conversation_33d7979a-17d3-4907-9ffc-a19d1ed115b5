<div align="center">
  <img src="/public/logo.png" alt="WeMa IA Logo" width="90" height="90" />
  <h1>WeMa IA</h1>
  <p><strong>Your Fully Local AI Superstack — Not Just Another Chat UI.</strong></p>
  <p>WeMa IA isn't a wrapper. It's a full-blown AI assistant, workflow engine, agent builder, and image lab — all running 100% offline on your machine.</p>

  <p>
    💬 LLM Chat • 🧠 Agents • 🔄 Automation • 🎨 Image Gen • 🧱 Widgets • 🧩 App Builder • 🛡️ GDPR Processor — <br/>
    Powered by open-source, privacy-first, zero-cloud tech.
  </p>

  <br/>
  <a href="https://claraverse.netlify.app/"><img src="https://img.shields.io/badge/Clara-1.2.61-FFD700.svg" alt="Clara Version Badge"></a>
  <br/><br/>
  <a href="https://clara.badboysm890.in/" target="_blank"><strong>🌐 Try Clara Online</strong></a> • 
  <a href="https://github.com/badboysm890/ClaraVerse/releases" target="_blank"><strong>⬇️ Download Desktop App</strong></a>
  <br/><br/>
  <a href="https://www.producthunt.com/posts/clara-433c5291-7639-4271-b246-8df30cbc449f" target="_blank">
    <img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=942765&theme=light" alt="Clara on Product Hunt" width="250" height="54" />
  </a>
</div>

---

> 🧠 Clara is what happens when you give Ollama, N8N, and OpenInterpreter a shared brain — and run them offline with a beautiful UI.

> If OpenWebUI, LibreChat, and crew feel limited — Clara *feels limitless.*

---

## 🌟 Help Clara Grow

If you like what I'm building:

- ⭐ [Star the repo](https://github.com/badboysm890/ClaraVerse)
- 🐦 [Tweet about Clara](https://twitter.com/intent/tweet?text=Clara%20is%20a%20fully%20offline%20AI%20superstack%20with%20LLMs%2C%20automations%2C%20agents%2C%20image%20generation%20%2B%20more%20%E2%80%94%20all%20open-source.%20Get%20it%20here%20%F0%9F%91%87&url=https%3A%2F%2Fgithub.com%2Fbadboysm890%2FClaraVerse)
- 💬 Post workflows, bugs, memes, or PRs — I respond to all

---

## 🔐 Privacy-First by Default

* 💻 **Runs 100% Locally** — Every model, every workflow
* 🧠 **No Cloud. No API Keys. No Phoning Home**
* 🛠️ **Open Source & Self-Hosted** — You own the stack

---

---

## 🤜 Clara vs The Rest

| Feature                      | Clara         | OpenWebUI   | LibreChat  |
|------------------------------|---------------|-------------|------------|
| 💻 Fully Local Stack         | ✅ Yes         | ✅ Yes      | ✅ Yes     |
| 🧩 Visual App Builder        | ✅ Built-in    | ❌          | ❌         |
| 🔁 Native N8N Automation     | ✅ Integrated  | ❌          | ❌         |
| 🧠 OpenInterpreter Support   | ✅ Yes         | ❌          | ❌         |
| 📂 File Manager & Widgets    | ✅ Fully Local | ❌          | ✅ Basic   |
| 🧱 Agent Templates & Builder | ✅ Yes         | ❌          | ❌         |
| 🖼️ Image Generation w/ SD    | ✅ ComfyUI     | ✅ Basic    | ❌         |
| 🛡️ GDPR Document Processor   | ✅ OCR + RAG   | ❌          | ❌         |

---

## 🚀 Feature Showcase

### 🧱 Widgets for Days

Modular, dynamic, and persistent. Build your own dashboard.

<p align="center"><img src="/public/screenshots/desktop_widgets.png" width="800"/></p>

---

### 💬 Talk to LLMs Like a Boss

Ollama + Vision models. Local. Context-aware. No cloud bills.

<p align="center"><img src="/public/screenshots/assistant-screenshot.png" width="800"/></p>

---

### 🧑‍💻 Clara ≠ Chatbot. It's Your Local Dev Intern

Write code, summarize docs, train models — via OpenInterpreter.

<p align="center"><img src="/public/screenshots/clara_interpreter.png" width="800"/></p>

---

### 🔄 Built-in N8N Automation

Drag-and-drop automation directly inside Clara. No tabs. No hacks.

<p align="center"><img src="/public/screenshots/n8n_ScreenShot.png" width="800"/></p>

---

### 🧩 1000+ Workflow Templates

Prebuilt automations with full source visibility.

<p align="center"><img src="/public/screenshots/Workflows.png" width="800"/></p>

---

### ⚙️ Agent Builder w/ Templates

Deploy agents that actually *do* things. Logic + memory + purpose.

<p align="center"><img src="/public/screenshots/Appstore.png" width="800"/></p>

---

### 🏗️ Visual App Builder

Wire up APIs, prompts, tools, and logic into fully working apps.

<p align="center"><img src="/public/screenshots/app-builder-screenshot.png" width="800"/></p>

---

### 🎨 Image Generation w/ ComfyUI

Offline Stable Diffusion. Stylized, tuned, and customizable.

<p align="center"><img src="/public/screenshots/image-gen-screenshot.png" width="800"/></p>

---

### 🖼️ Image Gallery Management

Organize your generations with tags, thumbnails, and metadata.

<p align="center"><img src="/public/screenshots/gallery-screenshot.png" width="800"/></p>

---

### 🛡️ GDPR Document Processor

Process documents with OCR and GDPR-compliant anonymization, then automatically integrate them into your RAG pipeline.

**Features:**
- 📄 **Multi-format OCR**: PDF, images, DOCX, TXT with layout preservation
- 🔒 **GDPR Anonymization**: Automatic detection of sensitive data (names, emails, phones, etc.)
- 🎨 **Visual Preview**: Real-time highlighting of anonymized entities
- ✏️ **Manual Editing**: Fine-tune results before processing
- 🔄 **RAG Integration**: Automatically add processed documents to your knowledge base

Perfect for processing sensitive documents while maintaining privacy compliance.


## 🧪 Install & Run (Docker)

> ✅ **Only Prerequisite**: [Docker Desktop](https://www.docker.com/products/docker-desktop)

---

## 💻 Download Desktop App

* 🪟 [Windows (.exe)](https://github.com/badboysm890/ClaraVerse/releases)
* 🍎 [macOS (.dmg)](https://github.com/badboysm890/ClaraVerse/releases)
* 🐧 [Linux (.AppImage)](https://github.com/badboysm890/ClaraVerse/releases)

---

## 🍏 macOS Unsigned App Fix

> Getting the "App is damaged" warning?

```bash
# Just right-click the app and hit "Open"  
# Then go to System Preferences > Security and allow it manually
```

Totally safe. It's unsigned, not unsafe.

---

## 👨‍💻 Dev Setup

```bash
git clone https://github.com/badboysm890/ClaraVerse.git
cd ClaraVerse
npm install
npm run dev           # For web
npm run electron:dev  # For desktop
```

### ⚒️ Build

```bash
npm run build              # Web
npm run electron:build     # Desktop
```

**Note**: During build/CI processes, Docker-related popups are automatically suppressed. Set `SKIP_DOCKER_SETUP=true` to completely skip Docker initialization if needed.

---

## 📈 GitHub Star Growth

[![Star History](https://api.star-history.com/svg?repos=badboysm890/ClaraVerse&type=Date)](https://www.star-history.com/#badboysm890/ClaraVerse&Date)

---

## 💬 Support / Feedback / Memes

📧 **[<EMAIL>](mailto:<EMAIL>)**  
🐙 [Raise issues or PRs on GitHub](https://github.com/badboysm890/ClaraVerse)  
🔥 Got memes? Post them. I'll probably feature them.

---

## 🧠 Want to Launch Your Own AI SaaS?

Clara gives you the full local AI stack — no vendor lock-in, no API hell, no GPU bills.

**Clara's your rocket. Light it up. 🚀**

## Features

- 🧠 **AI-Powered Agent Building**: Create intelligent agents with visual flow-based programming
- 🤖 **Custom Node Creation**: Build your own nodes with custom logic and interfaces
  - **Auto Mode**: AI-powered node generation using structured output - just describe what you want and let AI create the complete implementation
  - **Manual Mode**: Step-by-step node creation with full control over inputs, outputs, properties, and code
- 🔧 **Drag & Drop Interface**: Intuitive visual programming with nodes and connections
- ⚡ **Real-time Execution**: See your agents run and debug in real-time
- 🎨 **Customizable Themes**: Beautiful light and dark themes
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
