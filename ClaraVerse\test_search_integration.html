<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Recherche Internet - WeMa IA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f1f8e9; }
        .error { border-color: #f44336; background-color: #ffebee; }
        .loading { border-color: #2196F3; background-color: #e3f2fd; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Recherche Internet - WeMa IA</h1>
        
        <div class="test-section" id="health-test">
            <h3>1. Test de Santé du Service</h3>
            <button onclick="testHealth()">Tester la Santé</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section" id="search-test">
            <h3>2. Test de Recherche Générale</h3>
            <input type="text" id="search-query" placeholder="Entrez votre recherche..." value="intelligence artificielle" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="testSearch()">Rechercher</button>
            <div id="search-result" class="result"></div>
        </div>

        <div class="test-section" id="news-test">
            <h3>3. Test de Recherche d'Actualités</h3>
            <input type="text" id="news-query" placeholder="Sujet d'actualité..." value="actualités technologie" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="testNews()">Rechercher Actualités</button>
            <div id="news-result" class="result"></div>
        </div>

        <div class="test-section" id="tech-test">
            <h3>4. Test de Recherche Technique</h3>
            <input type="text" id="tech-query" placeholder="Question technique..." value="React hooks tutorial" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="testTechnical()">Rechercher Technique</button>
            <div id="tech-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Résumé des Tests</h3>
            <div id="summary">Cliquez sur les boutons pour tester l'intégration.</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5003';
        let testResults = {
            health: null,
            search: null,
            news: null,
            technical: null
        };

        async function testHealth() {
            const section = document.getElementById('health-test');
            const result = document.getElementById('health-result');
            
            section.className = 'test-section loading';
            result.textContent = 'Test en cours...';
            
            try {
                const response = await fetch(`${API_BASE}/search/health`);
                const data = await response.json();
                
                result.textContent = JSON.stringify(data, null, 2);
                testResults.health = response.ok;
                section.className = response.ok ? 'test-section success' : 'test-section error';
            } catch (error) {
                result.textContent = `Erreur: ${error.message}`;
                testResults.health = false;
                section.className = 'test-section error';
            }
            
            updateSummary();
        }

        async function testSearch() {
            const section = document.getElementById('search-test');
            const result = document.getElementById('search-result');
            const query = document.getElementById('search-query').value;
            
            section.className = 'test-section loading';
            result.textContent = 'Recherche en cours...';
            
            try {
                const response = await fetch(`${API_BASE}/search/internet`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: 'general',
                        max_results: 3
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.textContent = data.result;
                    testResults.search = true;
                    section.className = 'test-section success';
                } else {
                    result.textContent = `Erreur: ${data.error}`;
                    testResults.search = false;
                    section.className = 'test-section error';
                }
            } catch (error) {
                result.textContent = `Erreur: ${error.message}`;
                testResults.search = false;
                section.className = 'test-section error';
            }
            
            updateSummary();
        }

        async function testNews() {
            const section = document.getElementById('news-test');
            const result = document.getElementById('news-result');
            const query = document.getElementById('news-query').value;
            
            section.className = 'test-section loading';
            result.textContent = 'Recherche d\'actualités en cours...';
            
            try {
                const response = await fetch(`${API_BASE}/search/news`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        time_range: 'month',
                        max_results: 3
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.textContent = data.result;
                    testResults.news = true;
                    section.className = 'test-section success';
                } else {
                    result.textContent = `Erreur: ${data.error}`;
                    testResults.news = false;
                    section.className = 'test-section error';
                }
            } catch (error) {
                result.textContent = `Erreur: ${error.message}`;
                testResults.news = false;
                section.className = 'test-section error';
            }
            
            updateSummary();
        }

        async function testTechnical() {
            const section = document.getElementById('tech-test');
            const result = document.getElementById('tech-result');
            const query = document.getElementById('tech-query').value;
            
            section.className = 'test-section loading';
            result.textContent = 'Recherche technique en cours...';
            
            try {
                const response = await fetch(`${API_BASE}/search/technical`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        max_results: 3
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.textContent = data.result;
                    testResults.technical = true;
                    section.className = 'test-section success';
                } else {
                    result.textContent = `Erreur: ${data.error}`;
                    testResults.technical = false;
                    section.className = 'test-section error';
                }
            } catch (error) {
                result.textContent = `Erreur: ${error.message}`;
                testResults.technical = false;
                section.className = 'test-section error';
            }
            
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const total = Object.values(testResults).filter(r => r !== null).length;
            const passed = Object.values(testResults).filter(r => r === true).length;
            const failed = Object.values(testResults).filter(r => r === false).length;
            
            summary.innerHTML = `
                <strong>Tests effectués:</strong> ${total}/4<br>
                <strong style="color: green;">Réussis:</strong> ${passed}<br>
                <strong style="color: red;">Échoués:</strong> ${failed}<br>
                ${total === 4 ? (failed === 0 ? '🎉 Tous les tests sont passés !' : '⚠️ Certains tests ont échoué') : '⏳ Tests en cours...'}
            `;
        }

        // Test automatique au chargement
        window.onload = function() {
            setTimeout(testHealth, 1000);
        };
    </script>
</body>
</html>
