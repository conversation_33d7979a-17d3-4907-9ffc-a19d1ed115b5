#!/usr/bin/env python3
"""
CONFIGURATION DU RAG PREMIUM
LightRAG + Qdrant + BGE-M3 + OCR intégré
"""

import os
import json
import asyncio
from pathlib import Path

def create_rag_config():
    """Créer la configuration pour le RAG premium"""
    print("🔧 Création configuration RAG premium...")
    
    config = {
        "rag_system": {
            "primary": "lightrag",
            "fallback": "qdrant",
            "embedding_model": "BAAI/bge-m3",
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "max_tokens": 4096
        },
        "lightrag": {
            "storage_path": "./lightrag_storage",
            "graph_enabled": True,
            "entity_extraction": True,
            "relationship_extraction": True,
            "community_detection": True
        },
        "qdrant": {
            "host": "localhost",
            "port": 6333,
            "collection_name": "wema_documents",
            "vector_size": 1024,
            "distance": "Cosine",
            "storage_path": "./qdrant_storage"
        },
        "ocr": {
            "enabled": True,
            "language": "fra",
            "preserve_layout": True,
            "table_detection": True,
            "confidence_threshold": 0.7
        },
        "performance": {
            "batch_size": 32,
            "max_concurrent": 4,
            "cache_enabled": True,
            "preload_embeddings": True
        }
    }
    
    config_path = "./rag_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Configuration créée: {config_path}")
    return config

def check_dependencies():
    """Vérifier les dépendances nécessaires"""
    print("\n🔍 Vérification des dépendances...")
    
    required_packages = [
        "lightrag",
        "qdrant-client", 
        "sentence-transformers",
        "transformers",
        "torch",
        "numpy",
        "pandas"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MANQUANT")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Packages manquants: {missing_packages}")
        print("💡 Installez avec: pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n✅ Toutes les dépendances sont présentes")
        return True

def create_rag_service():
    """Créer le service RAG premium unifié"""
    print("\n🏗️  Création du service RAG premium...")
    
    service_code = '''"""
RAG PREMIUM SERVICE
LightRAG + Qdrant + BGE-M3 + OCR intégré
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

# Imports pour LightRAG
try:
    from lightrag import LightRAG, QueryParam
    from lightrag.llm import gpt_4o_mini_complete, gpt_4o_complete
    from lightrag.utils import EmbeddingFunc
    LIGHTRAG_AVAILABLE = True
except ImportError:
    LIGHTRAG_AVAILABLE = False
    print("⚠️  LightRAG non disponible")

# Imports pour Qdrant
try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    print("⚠️  Qdrant non disponible")

# Imports pour embeddings
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("⚠️  SentenceTransformers non disponible")

class PremiumRAGService:
    """Service RAG premium avec LightRAG + Qdrant + BGE-M3"""
    
    def __init__(self, config_path: str = "./rag_config.json"):
        self.config = self._load_config(config_path)
        self.lightrag = None
        self.qdrant_client = None
        self.embedding_model = None
        self.initialized = False
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Charger la configuration"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise FileNotFoundError(f"Configuration non trouvée: {config_path}")
    
    async def initialize(self):
        """Initialiser tous les composants"""
        print("🚀 Initialisation RAG Premium...")
        
        # 1. Initialiser le modèle d'embedding
        await self._init_embeddings()
        
        # 2. Initialiser LightRAG
        await self._init_lightrag()
        
        # 3. Initialiser Qdrant
        await self._init_qdrant()
        
        self.initialized = True
        print("✅ RAG Premium initialisé avec succès")
    
    async def _init_embeddings(self):
        """Initialiser le modèle d'embedding BGE-M3"""
        if not EMBEDDINGS_AVAILABLE:
            raise ImportError("SentenceTransformers non disponible")
        
        model_name = self.config["rag_system"]["embedding_model"]
        print(f"📥 Chargement modèle embedding: {model_name}")
        
        self.embedding_model = SentenceTransformer(model_name)
        print("✅ Modèle embedding chargé")
    
    async def _init_lightrag(self):
        """Initialiser LightRAG"""
        if not LIGHTRAG_AVAILABLE:
            print("⚠️  LightRAG non disponible, utilisation de Qdrant seul")
            return
        
        storage_path = self.config["lightrag"]["storage_path"]
        os.makedirs(storage_path, exist_ok=True)
        
        # Configuration LightRAG
        self.lightrag = LightRAG(
            working_dir=storage_path,
            llm_model_func=gpt_4o_mini_complete,  # Vous pouvez changer ça
            embedding_func=EmbeddingFunc(
                embedding_dim=1024,
                max_token_size=8192,
                func=self._embed_text
            )
        )
        print("✅ LightRAG initialisé")
    
    async def _init_qdrant(self):
        """Initialiser Qdrant"""
        if not QDRANT_AVAILABLE:
            print("⚠️  Qdrant non disponible")
            return
        
        config = self.config["qdrant"]
        
        # Client Qdrant (local)
        self.qdrant_client = QdrantClient(path=config["storage_path"])
        
        # Créer la collection si elle n'existe pas
        collection_name = config["collection_name"]
        try:
            self.qdrant_client.get_collection(collection_name)
            print(f"✅ Collection Qdrant existante: {collection_name}")
        except:
            self.qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=config["vector_size"],
                    distance=Distance.COSINE
                )
            )
            print(f"✅ Collection Qdrant créée: {collection_name}")
    
    def _embed_text(self, text: str) -> List[float]:
        """Générer l'embedding d'un texte"""
        if self.embedding_model is None:
            raise RuntimeError("Modèle embedding non initialisé")
        
        embedding = self.embedding_model.encode(text)
        return embedding.tolist()
    
    async def add_document(self, text: str, metadata: Dict[str, Any]) -> str:
        """Ajouter un document au RAG"""
        if not self.initialized:
            await self.initialize()
        
        doc_id = metadata.get('document_id', f"doc_{len(text)}")
        
        # 1. Ajouter à LightRAG (si disponible)
        if self.lightrag:
            try:
                await self.lightrag.ainsert(text)
                print(f"✅ Document ajouté à LightRAG: {doc_id}")
            except Exception as e:
                print(f"⚠️  Erreur LightRAG: {e}")
        
        # 2. Ajouter à Qdrant (fallback)
        if self.qdrant_client:
            try:
                embedding = self._embed_text(text)
                point = PointStruct(
                    id=doc_id,
                    vector=embedding,
                    payload={
                        "text": text,
                        **metadata
                    }
                )
                
                self.qdrant_client.upsert(
                    collection_name=self.config["qdrant"]["collection_name"],
                    points=[point]
                )
                print(f"✅ Document ajouté à Qdrant: {doc_id}")
            except Exception as e:
                print(f"⚠️  Erreur Qdrant: {e}")
        
        return doc_id
    
    async def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Rechercher dans le RAG"""
        if not self.initialized:
            await self.initialize()
        
        results = []
        
        # 1. Essayer LightRAG d'abord
        if self.lightrag:
            try:
                lightrag_results = await self.lightrag.aquery(
                    query, 
                    param=QueryParam(mode="hybrid")
                )
                results.append({
                    "source": "lightrag",
                    "content": lightrag_results,
                    "score": 1.0
                })
            except Exception as e:
                print(f"⚠️  Erreur recherche LightRAG: {e}")
        
        # 2. Fallback Qdrant
        if self.qdrant_client and len(results) == 0:
            try:
                query_embedding = self._embed_text(query)
                qdrant_results = self.qdrant_client.search(
                    collection_name=self.config["qdrant"]["collection_name"],
                    query_vector=query_embedding,
                    limit=limit
                )
                
                for result in qdrant_results:
                    results.append({
                        "source": "qdrant",
                        "content": result.payload.get("text", ""),
                        "metadata": result.payload,
                        "score": result.score
                    })
            except Exception as e:
                print(f"⚠️  Erreur recherche Qdrant: {e}")
        
        return results

# Instance globale
premium_rag = None

async def get_premium_rag() -> PremiumRAGService:
    """Obtenir l'instance du RAG premium"""
    global premium_rag
    if premium_rag is None:
        premium_rag = PremiumRAGService()
        await premium_rag.initialize()
    return premium_rag
'''
    
    service_path = "./rag_premium_service.py"
    with open(service_path, 'w', encoding='utf-8') as f:
        f.write(service_code)
    
    print(f"✅ Service RAG premium créé: {service_path}")

def main():
    print("🚀 CONFIGURATION RAG PREMIUM")
    print("=" * 50)
    
    # 1. Créer la configuration
    config = create_rag_config()
    
    # 2. Vérifier les dépendances
    deps_ok = check_dependencies()
    
    # 3. Créer le service
    create_rag_service()
    
    print("\n" + "=" * 50)
    if deps_ok:
        print("🎉 RAG PREMIUM CONFIGURÉ AVEC SUCCÈS !")
        print("✅ Prêt pour l'intégration")
    else:
        print("⚠️  CONFIGURATION PARTIELLE")
        print("❌ Installez les dépendances manquantes")

if __name__ == "__main__":
    main()
