{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020", "DOM"], "outDir": "./dist/tsc", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationDir": "./dist", "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*", "../src/shared/**/*", "../src/types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}