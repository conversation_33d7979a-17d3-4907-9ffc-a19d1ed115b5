@echo off
echo.
echo ========================================
echo 🚀 DÉMARRAGE AUTOMATIQUE WEMA IA
echo ========================================
echo.

cd /d "%~dp0"

echo 📁 Répertoire de travail: %CD%
echo.

echo 🔧 Démarrage du backend (avec auto-start Docker + SearXNG)...
cd py_backend
start "WeMa IA Backend" cmd /k "python main.py"

echo ⏳ Attente du démarrage du backend...
timeout /t 10 /nobreak >nul

echo 🌐 Démarrage du frontend...
cd ..
start "WeMa IA Frontend" cmd /k "npm run dev"

echo.
echo ✅ WeMa IA en cours de démarrage !
echo.
echo 📖 Le backend démarre automatiquement :
echo    - Docker Desktop
echo    - SearXNG (recherche internet)
echo    - Tous les services
echo.
echo 🌐 Interfaces :
echo    - Frontend: http://localhost:5173 ou 5174
echo    - Backend: http://localhost:5001
echo    - SearXNG: http://localhost:8888
echo.
echo 📊 Statut démarrage: http://localhost:5001/startup/status
echo.
pause
