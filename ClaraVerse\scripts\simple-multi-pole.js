#!/usr/bin/env node
/**
 * 🏢 Système Multi-Pôles Simplifié
 * Identification par token/ID au lieu d'IP
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuration simplifiée
const POLES_CONFIG = {
  'gestion-patrimoine': {
    name: 'Gestion Patrimoine',
    port: 8000,
    updatePort: 8001,
    users: ['user-patrimoine-01', 'user-patrimoine-02'], // IDs utilisateurs
    features: ['rag', 'ocr', 'documents', 'compression']
  },
  'mandats-a': {
    name: 'Mandats A',
    port: 8010,
    updatePort: 8011,
    users: ['user-mandats-01', 'user-mandats-02'],
    features: ['rag', 'documents', 'basic-ocr']
  },
  'rh': {
    name: '<PERSON><PERSON><PERSON>ces Humaines',
    port: 8020,
    updatePort: 8021,
    users: ['user-rh-01'],
    features: ['rag', 'documents', 'confidential']
  }
};

const ADMIN_CONFIG = {
  port: 9000,
  adminToken: 'admin-master-token-2024'
};

class SimplePoleManager {
  constructor() {
    this.tokensFile = 'user-tokens.json';
    this.initializeTokens();
  }

  /**
   * Initialiser les tokens utilisateurs
   */
  initializeTokens() {
    if (!fs.existsSync(this.tokensFile)) {
      const tokens = {};
      
      // Générer tokens pour chaque utilisateur
      for (const [poleId, config] of Object.entries(POLES_CONFIG)) {
        tokens[poleId] = {};
        
        config.users.forEach(userId => {
          tokens[poleId][userId] = {
            token: this.generateToken(),
            name: userId.replace('user-', '').replace('-', ' '),
            poleId: poleId,
            createdAt: new Date().toISOString(),
            lastLogin: null
          };
        });
      }
      
      // Token admin
      tokens.admin = {
        'admin-user': {
          token: ADMIN_CONFIG.adminToken,
          name: 'Administrateur',
          poleId: 'admin',
          createdAt: new Date().toISOString(),
          lastLogin: null
        }
      };
      
      fs.writeFileSync(this.tokensFile, JSON.stringify(tokens, null, 2));
      console.log('✅ Tokens utilisateurs générés dans user-tokens.json');
    }
  }

  /**
   * Générer un token unique
   */
  generateToken() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Créer une instance pour un pôle (version simplifiée)
   */
  async createPoleInstance(poleId) {
    const config = POLES_CONFIG[poleId];
    if (!config) {
      throw new Error(`Pôle inconnu: ${poleId}`);
    }

    console.log(`🏢 Création instance ${config.name}...`);

    const instanceDir = `instances/${poleId}`;
    
    // Créer le dossier d'instance
    if (fs.existsSync(instanceDir)) {
      fs.rmSync(instanceDir, { recursive: true });
    }
    fs.mkdirSync(instanceDir, { recursive: true });

    // 1. Copier le code de base
    this.copyBaseCode(instanceDir);

    // 2. Configuration simple (sans personnalisation couleurs)
    this.configureInstance(instanceDir, poleId, config);

    // 3. Build
    await this.buildInstance(instanceDir, poleId, config);

    console.log(`✅ Instance ${config.name} créée sur port ${config.port}`);
  }

  /**
   * Copier le code de base
   */
  copyBaseCode(instanceDir) {
    const filesToCopy = [
      'py_backend',
      'src',
      'public',
      'package.json',
      'vite.config.ts',
      'tsconfig.json'
    ];

    filesToCopy.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(instanceDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  /**
   * Configuration simplifiée
   */
  configureInstance(instanceDir, poleId, config) {
    // 1. Configuration backend
    const backendConfig = {
      POLE_ID: poleId,
      POLE_NAME: config.name,
      CLARA_HOST: '0.0.0.0',
      CLARA_PORT: config.port,
      UPDATE_PORT: config.updatePort,
      AUTHORIZED_USERS: config.users.join(','),
      FEATURES: config.features.join(','),
      TOKEN_AUTH: 'true'
    };

    fs.writeFileSync(
      path.join(instanceDir, 'py_backend', '.env'),
      Object.entries(backendConfig).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 2. Configuration frontend (SANS personnalisation couleurs)
    const frontendConfig = {
      REACT_APP_MODE: 'user',
      REACT_APP_POLE_ID: poleId,
      REACT_APP_POLE_NAME: config.name,
      REACT_APP_BACKEND_URL: `http://*************:${config.port}`,
      REACT_APP_UPDATE_URL: `http://*************:${config.updatePort}`,
      REACT_APP_FEATURES: config.features.join(','),
      REACT_APP_TOKEN_AUTH: 'true'
    };

    fs.writeFileSync(
      path.join(instanceDir, '.env'),
      Object.entries(frontendConfig).map(([k, v]) => `${k}=${v}`).join('\n')
    );

    // 3. Créer composant de login par token
    this.createTokenLogin(instanceDir, poleId);

    // 4. Filtrer les fonctionnalités
    this.filterFeatures(instanceDir, config.features);
  }

  /**
   * Créer le composant de login par token
   */
  createTokenLogin(instanceDir, poleId) {
    const loginComponent = `
import React, { useState, useEffect } from 'react';
import { LogIn, Shield } from 'lucide-react';

interface TokenLoginProps {
  onLogin: (userInfo: any) => void;
  poleId: string;
  poleName: string;
}

const TokenLogin: React.FC<TokenLoginProps> = ({ onLogin, poleId, poleName }) => {
  const [token, setToken] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Vérifier si déjà connecté
  useEffect(() => {
    const savedToken = localStorage.getItem('wema_user_token');
    const savedUser = localStorage.getItem('wema_user_info');
    
    if (savedToken && savedUser) {
      try {
        const userInfo = JSON.parse(savedUser);
        if (userInfo.poleId === poleId) {
          onLogin(userInfo);
        }
      } catch (e) {
        // Token invalide, nettoyer
        localStorage.removeItem('wema_user_token');
        localStorage.removeItem('wema_user_info');
      }
    }
  }, [poleId, onLogin]);

  const handleLogin = async () => {
    if (!token.trim()) {
      setError('Veuillez saisir votre token');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Vérifier le token côté serveur
      const response = await fetch(\`http://*************:\${poleId === 'gestion-patrimoine' ? '8000' : poleId === 'mandats-a' ? '8010' : '8020'}/auth/verify-token\`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token: token.trim() })
      });

      if (response.ok) {
        const userInfo = await response.json();
        
        // Sauvegarder en local
        localStorage.setItem('wema_user_token', token.trim());
        localStorage.setItem('wema_user_info', JSON.stringify(userInfo));
        
        onLogin(userInfo);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Token invalide');
      }
    } catch (error) {
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLogin();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            WeMa IA
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {poleName}
          </p>
        </div>
        
        <div className="space-y-6">
          <div>
            <label htmlFor="token" className="block text-sm font-medium text-gray-700">
              Token d'accès
            </label>
            <input
              id="token"
              type="password"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              onKeyPress={handleKeyPress}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Saisissez votre token"
              disabled={loading}
            />
          </div>
          
          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}
          
          <button
            onClick={handleLogin}
            disabled={loading}
            className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <>
                <LogIn className="w-4 h-4 mr-2" />
                Se connecter
              </>
            )}
          </button>
        </div>
        
        <div className="text-center text-xs text-gray-500">
          <p>Contactez votre administrateur pour obtenir votre token d'accès</p>
        </div>
      </div>
    </div>
  );
};

export default TokenLogin;
`;

    fs.writeFileSync(
      path.join(instanceDir, 'src', 'components', 'TokenLogin.tsx'),
      loginComponent
    );
  }

  /**
   * Filtrer les fonctionnalités
   */
  filterFeatures(instanceDir, features) {
    const featureFlags = {
      rag: features.includes('rag'),
      ocr: features.includes('ocr') || features.includes('basic-ocr'),
      advancedOcr: features.includes('ocr'),
      documents: features.includes('documents'),
      compression: features.includes('compression'),
      confidential: features.includes('confidential')
    };

    fs.writeFileSync(
      path.join(instanceDir, 'src', 'config', 'features.json'),
      JSON.stringify(featureFlags, null, 2)
    );
  }

  /**
   * Build une instance
   */
  async buildInstance(instanceDir, poleId, config) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(instanceDir);
      
      // Installer les dépendances si nécessaire
      if (!fs.existsSync('node_modules')) {
        console.log('📦 Installation dépendances...');
        execSync('npm install', { stdio: 'inherit' });
      }

      // Build frontend
      console.log('⚛️ Build frontend...');
      execSync('npm run build', { stdio: 'inherit' });

      // Créer script de démarrage
      const startScript = \`@echo off
echo 🏢 Démarrage \${config.name}...

REM Backend avec authentification token
cd py_backend
call venv\\\\Scripts\\\\activate
start "Backend-\${poleId}" python start_server.py --production --host 0.0.0.0 --port \${config.port}

REM Serveur de mise à jour
cd ..
start "Updates-\${poleId}" node scripts/update-system.js server --port \${config.updatePort}

echo ✅ \${config.name} démarré sur port \${config.port}
echo 🔑 Tokens disponibles dans user-tokens.json
pause\`;

      fs.writeFileSync('start-pole.bat', startScript);

    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * Créer l'application admin simplifiée
   */
  async createAdminApp() {
    console.log('🎛️ Création application admin...');

    const adminDir = 'admin-app';
    
    if (fs.existsSync(adminDir)) {
      fs.rmSync(adminDir, { recursive: true });
    }
    fs.mkdirSync(adminDir, { recursive: true });

    // Copier le code de base
    this.copyBaseCode(adminDir);

    // Configuration admin
    const adminEnv = {
      REACT_APP_MODE: 'admin',
      REACT_APP_BACKEND_URL: \`http://*************:\${ADMIN_CONFIG.port}\`,
      REACT_APP_ADMIN_TOKEN: ADMIN_CONFIG.adminToken
    };

    fs.writeFileSync(
      path.join(adminDir, '.env'),
      Object.entries(adminEnv).map(([k, v]) => \`\${k}=\${v}\`).join('\\n')
    );

    // Créer le backend admin simple
    this.createSimpleAdminBackend(adminDir);

    console.log('✅ Application admin créée');
  }

  /**
   * Créer backend admin simple
   */
  createSimpleAdminBackend(adminDir) {
    const backendDir = path.join(adminDir, 'admin-backend');
    fs.mkdirSync(backendDir, { recursive: true });

    const serverCode = \`
const express = require('express');
const cors = require('cors');
const fs = require('fs');

const app = express();
app.use(cors());
app.use(express.json());

const POLES = \${JSON.stringify(POLES_CONFIG, null, 2)};
const ADMIN_TOKEN = '\${ADMIN_CONFIG.adminToken}';

// Middleware d'authentification admin
const requireAdminAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token !== ADMIN_TOKEN) {
    return res.status(401).json({ error: 'Token admin invalide' });
  }
  next();
};

// API pour le dashboard admin
app.get('/api/poles', requireAdminAuth, async (req, res) => {
  const polesStatus = {};
  
  for (const [poleId, config] of Object.entries(POLES)) {
    try {
      const response = await fetch(\\\`http://localhost:\\\${config.port}/health\\\`);
      polesStatus[poleId] = {
        ...config,
        status: response.ok ? 'online' : 'offline',
        lastCheck: new Date().toISOString()
      };
    } catch {
      polesStatus[poleId] = {
        ...config,
        status: 'offline',
        lastCheck: new Date().toISOString()
      };
    }
  }
  
  res.json(polesStatus);
});

// Obtenir les tokens utilisateurs
app.get('/api/tokens', requireAdminAuth, (req, res) => {
  try {
    const tokens = JSON.parse(fs.readFileSync('user-tokens.json', 'utf8'));
    res.json(tokens);
  } catch (error) {
    res.status(500).json({ error: 'Erreur lecture tokens' });
  }
});

// Redémarrer un pôle
app.post('/api/restart/:poleId', requireAdminAuth, (req, res) => {
  const { poleId } = req.params;
  console.log(\\\`Redémarrage demandé pour \\\${poleId}\\\`);
  res.json({ success: true, message: \\\`Redémarrage \\\${poleId} en cours\\\` });
});

// Mettre à jour un pôle
app.post('/api/update/:poleId', requireAdminAuth, (req, res) => {
  const { poleId } = req.params;
  console.log(\\\`Mise à jour demandée pour \\\${poleId}\\\`);
  res.json({ success: true, message: \\\`Mise à jour \\\${poleId} en cours\\\` });
});

const PORT = \${ADMIN_CONFIG.port};
app.listen(PORT, () => {
  console.log(\\\`🎛️ Serveur Admin démarré sur port \\\${PORT}\\\`);
  console.log(\\\`🔑 Token admin: \\\${ADMIN_TOKEN}\\\`);
});
\`;

    fs.writeFileSync(path.join(backendDir, 'server.js'), serverCode);
  }

  /**
   * Afficher les tokens générés
   */
  showTokens() {
    if (fs.existsSync(this.tokensFile)) {
      const tokens = JSON.parse(fs.readFileSync(this.tokensFile, 'utf8'));
      
      console.log('\\n🔑 TOKENS D\\'ACCÈS GÉNÉRÉS:\\n');
      
      // Tokens admin
      console.log('👑 ADMINISTRATEUR:');
      console.log(\`   Token: \${tokens.admin['admin-user'].token}\`);
      console.log('');
      
      // Tokens par pôle
      for (const [poleId, poleTokens] of Object.entries(tokens)) {
        if (poleId === 'admin') continue;
        
        const config = POLES_CONFIG[poleId];
        console.log(\`🏢 \${config.name.toUpperCase()}:\`);
        
        for (const [userId, userInfo] of Object.entries(poleTokens)) {
          console.log(\`   \${userInfo.name}: \${userInfo.token}\`);
        }
        console.log('');
      }
      
      console.log('💡 Distribuez ces tokens aux utilisateurs concernés');
      console.log('📄 Tokens sauvegardés dans: user-tokens.json');
    }
  }
}

// CLI
const manager = new SimplePoleManager();
const command = process.argv[2];
const poleId = process.argv[3];

switch (command) {
  case 'create':
    if (!poleId) {
      console.log('Usage: node simple-multi-pole.js create <pole-id>');
      process.exit(1);
    }
    manager.createPoleInstance(poleId);
    break;
    
  case 'create-all':
    Promise.all(
      Object.keys(POLES_CONFIG).map(id => manager.createPoleInstance(id))
    ).then(() => {
      console.log('✅ Toutes les instances créées');
    });
    break;
    
  case 'admin':
    manager.createAdminApp();
    break;
    
  case 'tokens':
    manager.showTokens();
    break;
    
  default:
    console.log(\`
🏢 Gestionnaire Multi-Pôles Simplifié

Commandes:
  create <pole-id>     Créer une instance pour un pôle
  create-all           Créer toutes les instances
  admin                Créer l'application admin
  tokens               Afficher tous les tokens

Pôles disponibles: \${Object.keys(POLES_CONFIG).join(', ')}

Exemples:
  node simple-multi-pole.js create gestion-patrimoine
  node simple-multi-pole.js create-all
  node simple-multi-pole.js admin
  node simple-multi-pole.js tokens
\`);
}
\`;
