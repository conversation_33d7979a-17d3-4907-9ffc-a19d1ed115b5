/**
 * 🚀 Session RAG Mode Service - Gestion professionnelle du mode RAG par session
 * 
 * Service centralisé pour gérer l'isolation du mode RAG par session de manière
 * professionnelle et maintenable.
 * 
 * Fonctionnalités :
 * - Mode RAG isolé par session (fast/performant)
 * - Migration automatique depuis le mode global
 * - Fallback intelligent vers les préférences globales
 * - API simple et cohérente
 * - Logging complet pour debug
 */

import { logger, LogCategory } from '../../utils/logger';
import type { ClaraSessionConfig } from '../../types/clara_assistant_types';

// Types pour le service
export type RagMode = 'fast' | 'performant';

export interface SessionRagModeConfig {
  sessionId: string;
  ragMode: RagMode;
  timestamp: number;
  source: 'session' | 'global' | 'default';
}

export interface RagModeStats {
  mode: RagMode;
  modeName: string;
  expectedPerformance: string;
  ragProcessing: string;
  source: string;
}

/**
 * Service professionnel pour la gestion du mode RAG par session
 */
export class SessionRagModeService {
  private static readonly GLOBAL_RAG_MODE_KEY = 'clara-rag-fast-mode';
  private static readonly SESSION_RAG_MODE_PREFIX = 'clara-session-rag-mode-';
  
  /**
   * 🎯 OBTENIR LE MODE RAG POUR UNE SESSION SPÉCIFIQUE
   * 
   * Ordre de priorité :
   * 1. Configuration de session (si définie)
   * 2. Stockage local par session (si existe)
   * 3. Mode global (migration automatique)
   * 4. Mode par défaut (performant)
   */
  static getRagModeForSession(sessionId: string, sessionConfig?: ClaraSessionConfig): SessionRagModeConfig {
    try {
      // 1. Priorité : Configuration de session
      if (sessionConfig?.ragMode) {
        logger.debug(LogCategory.SESSIONS, `🎯 RAG Mode from session config: ${sessionConfig.ragMode} for session ${sessionId}`);
        return {
          sessionId,
          ragMode: sessionConfig.ragMode,
          timestamp: Date.now(),
          source: 'session'
        };
      }

      // 2. Stockage local par session
      const sessionKey = this.SESSION_RAG_MODE_PREFIX + sessionId;
      const sessionRagMode = localStorage.getItem(sessionKey);
      if (sessionRagMode) {
        const mode = JSON.parse(sessionRagMode) ? 'fast' : 'performant';
        logger.debug(LogCategory.SESSIONS, `🎯 RAG Mode from session storage: ${mode} for session ${sessionId}`);
        return {
          sessionId,
          ragMode: mode,
          timestamp: Date.now(),
          source: 'session'
        };
      }

      // 3. Migration depuis mode global (compatibilité)
      const globalRagMode = localStorage.getItem(this.GLOBAL_RAG_MODE_KEY);
      if (globalRagMode) {
        const mode = JSON.parse(globalRagMode) ? 'fast' : 'performant';
        logger.info(LogCategory.SESSIONS, `🔄 Migrating global RAG mode (${mode}) to session ${sessionId}`);
        
        // Sauvegarder pour cette session
        this.setRagModeForSession(sessionId, mode);
        
        return {
          sessionId,
          ragMode: mode,
          timestamp: Date.now(),
          source: 'global'
        };
      }

      // 4. Mode par défaut
      logger.debug(LogCategory.SESSIONS, `🎯 Using default RAG mode (performant) for session ${sessionId}`);
      return {
        sessionId,
        ragMode: 'performant',
        timestamp: Date.now(),
        source: 'default'
      };

    } catch (error) {
      logger.error(LogCategory.SESSIONS, `❌ Error getting RAG mode for session ${sessionId}:`, error);
      return {
        sessionId,
        ragMode: 'performant',
        timestamp: Date.now(),
        source: 'default'
      };
    }
  }

  /**
   * 🚀 DÉFINIR LE MODE RAG POUR UNE SESSION SPÉCIFIQUE
   */
  static setRagModeForSession(sessionId: string, ragMode: RagMode): void {
    try {
      const sessionKey = this.SESSION_RAG_MODE_PREFIX + sessionId;
      const ragModeBoolean = ragMode === 'fast';
      
      localStorage.setItem(sessionKey, JSON.stringify(ragModeBoolean));
      
      logger.info(LogCategory.SESSIONS, `🚀 RAG Mode set for session ${sessionId}: ${ragMode.toUpperCase()} ${ragMode === 'fast' ? '⚡' : '🧠'}`);
      
    } catch (error) {
      logger.error(LogCategory.SESSIONS, `❌ Error setting RAG mode for session ${sessionId}:`, error);
    }
  }

  /**
   * 🔄 BASCULER LE MODE RAG POUR UNE SESSION
   */
  static toggleRagModeForSession(sessionId: string): RagMode {
    try {
      const currentConfig = this.getRagModeForSession(sessionId);
      const newMode: RagMode = currentConfig.ragMode === 'fast' ? 'performant' : 'fast';
      
      this.setRagModeForSession(sessionId, newMode);
      
      logger.info(LogCategory.SESSIONS, `🔄 RAG Mode toggled for session ${sessionId}: ${currentConfig.ragMode} → ${newMode}`);
      
      return newMode;
    } catch (error) {
      logger.error(LogCategory.SESSIONS, `❌ Error toggling RAG mode for session ${sessionId}:`, error);
      return 'performant';
    }
  }

  /**
   * 📊 OBTENIR LES STATISTIQUES DU MODE RAG POUR UNE SESSION
   */
  static getRagModeStatsForSession(sessionId: string, sessionConfig?: ClaraSessionConfig): RagModeStats {
    const config = this.getRagModeForSession(sessionId, sessionConfig);
    
    return {
      mode: config.ragMode,
      modeName: config.ragMode === 'fast' ? 'RAPIDE ⚡' : 'PERFORMANT 🧠',
      expectedPerformance: config.ragMode === 'fast' ? '2-3 secondes' : '10-30 secondes',
      ragProcessing: config.ragMode === 'fast' ? 'Désactivé (ajout direct)' : 'Activé (analyse intelligente)',
      source: config.source
    };
  }

  /**
   * 🗑️ NETTOYER LE STOCKAGE D'UNE SESSION SUPPRIMÉE
   */
  static cleanupSessionRagMode(sessionId: string): void {
    try {
      const sessionKey = this.SESSION_RAG_MODE_PREFIX + sessionId;
      localStorage.removeItem(sessionKey);
      
      logger.debug(LogCategory.SESSIONS, `🗑️ Cleaned up RAG mode storage for deleted session ${sessionId}`);
    } catch (error) {
      logger.warn(LogCategory.SESSIONS, `⚠️ Error cleaning up RAG mode for session ${sessionId}:`, error);
    }
  }

  /**
   * 🔧 MIGRATION : Nettoyer l'ancien système global (optionnel)
   */
  static migrateFromGlobalMode(): void {
    try {
      const globalMode = localStorage.getItem(this.GLOBAL_RAG_MODE_KEY);
      if (globalMode) {
        logger.info(LogCategory.SESSIONS, `🔄 Global RAG mode detected: ${JSON.parse(globalMode) ? 'fast' : 'performant'}`);
        logger.info(LogCategory.SESSIONS, `ℹ️ Global mode will be migrated per session as needed`);
        // Note: On ne supprime pas le mode global pour maintenir la compatibilité
      }
    } catch (error) {
      logger.warn(LogCategory.SESSIONS, `⚠️ Error during global mode migration:`, error);
    }
  }

  /**
   * 🧪 UTILITAIRES DE DEBUG
   */
  static debugSessionRagModes(): void {
    try {
      const allKeys = Object.keys(localStorage);
      const sessionRagKeys = allKeys.filter(key => key.startsWith(this.SESSION_RAG_MODE_PREFIX));
      
      logger.info(LogCategory.SESSIONS, `🧪 Debug: Found ${sessionRagKeys.length} session RAG mode configurations`);
      
      sessionRagKeys.forEach(key => {
        const sessionId = key.replace(this.SESSION_RAG_MODE_PREFIX, '');
        const mode = JSON.parse(localStorage.getItem(key) || 'false') ? 'fast' : 'performant';
        logger.info(LogCategory.SESSIONS, `  - Session ${sessionId}: ${mode}`);
      });
      
    } catch (error) {
      logger.error(LogCategory.SESSIONS, `❌ Error during debug:`, error);
    }
  }
}

/**
 * 🚀 FONCTIONS UTILITAIRES POUR COMPATIBILITÉ
 */

/**
 * Obtenir le mode RAG pour une session (fonction simple)
 */
export function getRagModeForSession(sessionId: string, sessionConfig?: ClaraSessionConfig): RagMode {
  return SessionRagModeService.getRagModeForSession(sessionId, sessionConfig).ragMode;
}

/**
 * Définir le mode RAG pour une session (fonction simple)
 */
export function setRagModeForSession(sessionId: string, ragMode: RagMode): void {
  SessionRagModeService.setRagModeForSession(sessionId, ragMode);
}

/**
 * Basculer le mode RAG pour une session (fonction simple)
 */
export function toggleRagModeForSession(sessionId: string): RagMode {
  return SessionRagModeService.toggleRagModeForSession(sessionId);
}

/**
 * Vérifier si le mode rapide est activé pour une session
 */
export function isRagFastModeEnabledForSession(sessionId: string, sessionConfig?: ClaraSessionConfig): boolean {
  return getRagModeForSession(sessionId, sessionConfig) === 'fast';
}
