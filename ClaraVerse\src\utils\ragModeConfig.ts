/**
 * 🎯 Configuration centralisée du mode RAG
 * 
 * Évite la duplication de code et garantit la cohérence
 */

export interface RagModeConfig {
  isRagFastMode: boolean;
  timestamp: number;
}

/**
 * Clé localStorage pour le mode RAG
 */
const RAG_MODE_STORAGE_KEY = 'clara-rag-fast-mode';

/**
 * Obtenir la configuration du mode RAG
 */
export function getRagModeConfig(): RagModeConfig {
  try {
    if (typeof window === 'undefined' || !localStorage) {
      return { isRagFastMode: false, timestamp: Date.now() };
    }

    const ragFastModeStr = localStorage.getItem(RAG_MODE_STORAGE_KEY);
    const isRagFastMode = ragFastModeStr ? JSON.parse(ragFastModeStr) : false;
    
    return {
      isRagFastMode,
      timestamp: Date.now()
    };
  } catch (error) {
    console.warn('⚠️ Erreur lecture mode RAG:', error);
    return { isRagFastMode: false, timestamp: Date.now() };
  }
}

/**
 * Définir le mode RAG
 */
export function setRagModeConfig(isRagFastMode: boolean): void {
  try {
    if (typeof window === 'undefined' || !localStorage) {
      console.warn('⚠️ localStorage non disponible');
      return;
    }

    localStorage.setItem(RAG_MODE_STORAGE_KEY, JSON.stringify(isRagFastMode));
    console.log(`🎯 Mode RAG défini: ${isRagFastMode ? 'RAPIDE ⚡' : 'PERFORMANT 🧠'}`);
  } catch (error) {
    console.error('❌ Erreur sauvegarde mode RAG:', error);
  }
}

/**
 * Vérifier si le mode rapide est activé
 */
export function isRagFastModeEnabled(): boolean {
  return getRagModeConfig().isRagFastMode;
}

/**
 * Obtenir le nom du mode actuel
 */
export function getCurrentRagModeName(): string {
  return isRagFastModeEnabled() ? 'RAPIDE ⚡' : 'PERFORMANT 🧠';
}

/**
 * Basculer le mode RAG
 */
export function toggleRagMode(): boolean {
  const currentMode = isRagFastModeEnabled();
  const newMode = !currentMode;
  setRagModeConfig(newMode);
  return newMode;
}

/**
 * Obtenir les statistiques du mode
 */
export function getRagModeStats(): {
  mode: string;
  isRagFastMode: boolean;
  expectedPerformance: string;
  ragProcessing: string;
} {
  const isRagFastMode = isRagFastModeEnabled();
  
  return {
    mode: getCurrentRagModeName(),
    isRagFastMode,
    expectedPerformance: isRagFastMode ? '2-3 secondes' : '10-30 secondes',
    ragProcessing: isRagFastMode ? 'Désactivé (ajout direct)' : 'Activé (analyse intelligente)'
  };
}
