/**
 * Tests pour le système RAG modulaire
 */

import { RagManager } from '../RagManager';
import { FastMode } from '../modes/FastMode';
import { PerformantMode } from '../modes/PerformantMode';
import { RagDocument } from '../types/RagTypes';

// Mock localStorage pour les tests
const mockLocalStorage = {
  getItem: (key: string) => {
    if (key === 'clara-rag-fast-mode') {
      return 'true'; // Mode rapide par défaut pour les tests
    }
    return null;
  },
  setItem: (key: string, value: string) => {
    console.log(`Mock localStorage.setItem: ${key} = ${value}`);
  }
};

// Remplacer localStorage dans l'environnement de test
(global as any).localStorage = mockLocalStorage;
(global as any).window = { localStorage: mockLocalStorage };

/**
 * Documents de test
 */
const testDocuments: RagDocument[] = [
  {
    id: 'doc1',
    filename: 'test-small.txt',
    content: 'Ceci est un petit document de test avec peu de contenu.',
    fileType: 'text'
  },
  {
    id: 'doc2',
    filename: 'test-large.txt',
    content: 'A'.repeat(25000), // Document volumineux > 20k caractères
    fileType: 'text'
  },
  {
    id: 'doc3',
    filename: 'cv-example.pdf',
    content: `
PROFIL PROFESSIONNEL
Développeur Full-Stack avec 5 ans d'expérience en JavaScript, TypeScript, React et Node.js.
Spécialisé dans le développement d'applications web modernes et d'APIs REST.

EXPÉRIENCE
2020-2025 : Développeur Senior chez TechCorp
- Développement d'applications React/TypeScript
- Architecture de microservices Node.js
- Optimisation des performances frontend

COMPÉTENCES
- Frontend: React, TypeScript, HTML5, CSS3
- Backend: Node.js, Express, PostgreSQL
- DevOps: Docker, AWS, CI/CD
    `.trim(),
    fileType: 'pdf'
  }
];

/**
 * Test du mode rapide
 */
async function testFastMode() {
  console.log('\n🚀 === TEST MODE RAPIDE ===');
  
  const fastMode = new FastMode();
  const startTime = Date.now();
  
  try {
    const result = await fastMode.processDocuments(
      testDocuments,
      'Quelles sont les compétences du candidat ?'
    );
    
    const processingTime = Date.now() - startTime;
    
    console.log('✅ Mode rapide terminé en', processingTime, 'ms');
    console.log('📊 Statistiques:', result.processingStats);
    console.log('📄 Contexte généré:', result.ragContext.substring(0, 200) + '...');
    
    // Vérifications
    if (processingTime > 100) {
      console.warn('⚠️ Mode rapide trop lent:', processingTime, 'ms');
    }
    
    if (result.processingStats.ragProcessedDocuments > 0) {
      console.warn('⚠️ Mode rapide ne devrait pas faire de traitement RAG');
    }
    
  } catch (error) {
    console.error('❌ Erreur mode rapide:', error);
  }
}

/**
 * Test du mode performant
 */
async function testPerformantMode() {
  console.log('\n🧠 === TEST MODE PERFORMANT ===');
  
  const performantMode = new PerformantMode();
  const startTime = Date.now();
  
  try {
    const result = await performantMode.processDocuments(
      testDocuments,
      'Quelles sont les compétences du candidat ?'
    );
    
    const processingTime = Date.now() - startTime;
    
    console.log('✅ Mode performant terminé en', processingTime, 'ms');
    console.log('📊 Statistiques:', result.processingStats);
    console.log('📄 Contexte généré:', result.ragContext.substring(0, 200) + '...');
    
    // Vérifications
    if (result.processingStats.directDocuments === 0) {
      console.warn('⚠️ Mode performant devrait traiter certains documents en direct');
    }
    
  } catch (error) {
    console.error('❌ Erreur mode performant:', error);
  }
}

/**
 * Test du gestionnaire RAG
 */
async function testRagManager() {
  console.log('\n🔧 === TEST RAG MANAGER ===');
  
  const ragManager = new RagManager();
  const startTime = Date.now();
  
  try {
    const result = await ragManager.processDocuments(
      testDocuments,
      'Résume le profil du candidat'
    );
    
    const processingTime = Date.now() - startTime;
    
    console.log('✅ RAG Manager terminé en', processingTime, 'ms');
    console.log('📊 Statistiques:', result.processingStats);
    console.log('🔍 Mode détecté:', result.processingStats.mode);
    
    // Vérifications
    if (!result.ragContext) {
      console.warn('⚠️ Aucun contexte RAG généré');
    }
    
    if (result.ragResults.length !== testDocuments.length) {
      console.warn('⚠️ Nombre de résultats incorrect');
    }
    
  } catch (error) {
    console.error('❌ Erreur RAG Manager:', error);
  }
}

/**
 * Test de conversion des documents
 */
function testDocumentConversion() {
  console.log('\n🔄 === TEST CONVERSION DOCUMENTS ===');
  
  const claraDocuments = [
    {
      id: 'clara1',
      filename: 'document.txt',
      content: 'Contenu du document',
      fileType: 'text',
      collectionName: 'test-collection'
    }
  ];
  
  const ragDocuments = RagManager.convertDocuments(claraDocuments);
  
  console.log('✅ Documents convertis:', ragDocuments);
  
  // Vérifications
  if (ragDocuments.length !== claraDocuments.length) {
    console.warn('⚠️ Nombre de documents incorrect après conversion');
  }
  
  if (ragDocuments[0].id !== 'clara1') {
    console.warn('⚠️ ID de document incorrect après conversion');
  }
}

/**
 * Exécution de tous les tests
 */
export async function runAllTests() {
  console.log('🧪 === DÉBUT DES TESTS RAG MODULAIRE ===');
  
  testDocumentConversion();
  await testFastMode();
  await testPerformantMode();
  await testRagManager();
  
  console.log('\n✅ === TESTS TERMINÉS ===');
}

// Exécution automatique si ce fichier est lancé directement
if (require.main === module) {
  runAllTests().catch(console.error);
}
