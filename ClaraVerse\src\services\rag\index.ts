/**
 * Point d'entrée principal du système RAG modulaire
 * 
 * Exports :
 * - RagManager : Gestionnaire principal
 * - Types : Tous les types RAG
 * - Modes : Classes de modes individuelles (pour tests/debug)
 */

// Gestionnaire principal
export { RagManager } from './RagManager';

// Types
export * from './types/RagTypes';

// Modes (pour tests et utilisation avancée)
export { BaseMode } from './modes/BaseMode';
export { FastMode } from './modes/FastMode';
export { PerformantMode } from './modes/PerformantMode';

// Utilitaires
export const RAG_CONSTANTS = {
  FAST_MODE_THRESHOLD: 50000,
  PERFORMANT_MODE_THRESHOLD: 20000,
  MAX_CONTEXT_LENGTH: 200000,
  DEFAULT_SCORE_DECAY: 0.1
} as const;

/**
 * Factory function pour créer une instance RagManager
 */
export function createRagManager(): RagManager {
  return new RagManager();
}

/**
 * Fonction utilitaire pour vérifier si le mode rapide est activé (avec support session)
 */
export function isRagFastModeEnabled(sessionId?: string, sessionConfig?: any): boolean {
  try {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return false;
    }

    // 🚀 NOUVEAU : Utiliser le mode par session si disponible
    if (sessionId) {
      // Import dynamique pour éviter les dépendances circulaires
      const { getRagModeForSession } = require('../session/SessionRagModeService');
      const sessionRagMode = getRagModeForSession(sessionId, sessionConfig);
      return sessionRagMode === 'fast';
    }

    // Fallback vers localStorage pour compatibilité
    const ragFastModeStr = localStorage.getItem('clara-rag-fast-mode');
    return ragFastModeStr ? JSON.parse(ragFastModeStr) : false;
  } catch {
    return false;
  }
}

/**
 * Fonction utilitaire pour basculer le mode RAG
 */
export function toggleRagMode(): boolean {
  try {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      console.warn('⚠️ RAG: localStorage non disponible');
      return false;
    }
    
    const currentMode = isRagFastModeEnabled();
    const newMode = !currentMode;
    
    localStorage.setItem('clara-rag-fast-mode', JSON.stringify(newMode));
    
    console.log(`🔄 RAG Mode basculé: ${currentMode ? 'PERFORMANT' : 'RAPIDE'} → ${newMode ? 'RAPIDE' : 'PERFORMANT'}`);
    
    return newMode;
  } catch (error) {
    console.error('❌ Erreur lors du basculement du mode RAG:', error);
    return false;
  }
}
