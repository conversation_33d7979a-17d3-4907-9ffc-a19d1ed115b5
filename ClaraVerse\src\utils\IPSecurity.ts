/**
 * 🔐 Sécurité par IP
 * Contrôle d'accès basé sur l'adresse IP
 */

export interface IPSecurityConfig {
  allowedIPs: string[];
  mode: 'admin' | 'user';
  poleId?: string;
}

class IPSecurityService {
  private config: IPSecurityConfig | null = null;

  /**
   * Initialiser la sécurité IP
   */
  public initialize(config: IPSecurityConfig): void {
    this.config = config;
    console.log(`🔐 Sécurité IP initialisée - Mode: ${config.mode}`);
  }

  /**
   * Obtenir l'IP du client (approximatif côté frontend)
   */
  public async getClientIP(): Promise<string> {
    try {
      // Méthode 1: API externe (pour test)
      const response = await fetch('https://api.ipify.org?format=json', {
        timeout: 3000
      });
      
      if (response.ok) {
        const data = await response.json();
        return data.ip;
      }
    } catch (error) {
      console.warn('Impossible d\'obtenir l\'IP externe:', error);
    }

    try {
      // Méthode 2: WebRTC (IP locale)
      const ip = await this.getLocalIP();
      if (ip) return ip;
    } catch (error) {
      console.warn('Impossible d\'obtenir l\'IP locale:', error);
    }

    // Fallback: IP par défaut (développement)
    return '*************';
  }

  /**
   * Obtenir l'IP locale via WebRTC
   */
  private getLocalIP(): Promise<string> {
    return new Promise((resolve, reject) => {
      const rtc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      rtc.createDataChannel('');
      rtc.createOffer().then(offer => rtc.setLocalDescription(offer));

      rtc.onicecandidate = (event) => {
        if (event.candidate) {
          const candidate = event.candidate.candidate;
          const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
          
          if (ipMatch && ipMatch[1]) {
            const ip = ipMatch[1];
            // Filtrer les IP locales
            if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
              rtc.close();
              resolve(ip);
            }
          }
        }
      };

      // Timeout après 5 secondes
      setTimeout(() => {
        rtc.close();
        reject(new Error('Timeout WebRTC'));
      }, 5000);
    });
  }

  /**
   * Vérifier si l'IP est autorisée
   */
  public async checkIPAuthorization(): Promise<{
    authorized: boolean;
    clientIP: string;
    reason?: string;
  }> {
    if (!this.config) {
      return {
        authorized: false,
        clientIP: 'unknown',
        reason: 'Configuration sécurité non initialisée'
      };
    }

    const clientIP = await this.getClientIP();
    
    // Vérifier si l'IP est dans la liste autorisée
    const authorized = this.config.allowedIPs.includes(clientIP);

    return {
      authorized,
      clientIP,
      reason: authorized ? undefined : `IP ${clientIP} non autorisée pour le mode ${this.config.mode}`
    };
  }

  /**
   * Vérifier l'autorisation avec gestion d'erreur
   */
  public async requireAuthorization(): Promise<void> {
    const result = await this.checkIPAuthorization();
    
    if (!result.authorized) {
      throw new Error(result.reason || 'Accès refusé');
    }

    console.log(`✅ Accès autorisé pour IP: ${result.clientIP}`);
  }

  /**
   * Obtenir les informations de sécurité
   */
  public getSecurityInfo(): {
    mode: string;
    allowedIPs: string[];
    poleId?: string;
  } | null {
    if (!this.config) return null;

    return {
      mode: this.config.mode,
      allowedIPs: this.config.allowedIPs,
      poleId: this.config.poleId
    };
  }

  /**
   * Middleware de vérification pour composants React
   */
  public createAuthGuard() {
    return async (WrappedComponent: React.ComponentType<any>) => {
      return (props: any) => {
        const [authorized, setAuthorized] = React.useState<boolean | null>(null);
        const [error, setError] = React.useState<string>('');

        React.useEffect(() => {
          this.checkIPAuthorization().then(result => {
            setAuthorized(result.authorized);
            if (!result.authorized) {
              setError(result.reason || 'Accès refusé');
            }
          }).catch(err => {
            setAuthorized(false);
            setError(err.message);
          });
        }, []);

        if (authorized === null) {
          return (
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Vérification des autorisations...</p>
              </div>
            </div>
          );
        }

        if (!authorized) {
          return (
            <div className="min-h-screen flex items-center justify-center bg-red-50">
              <div className="text-center max-w-md mx-auto p-6">
                <div className="text-6xl mb-4">🚫</div>
                <h1 className="text-2xl font-bold text-red-600 mb-4">Accès Refusé</h1>
                <p className="text-red-500 mb-4">{error}</p>
                <div className="text-sm text-gray-500">
                  <p>Mode: {this.config?.mode}</p>
                  {this.config?.poleId && <p>Pôle: {this.config.poleId}</p>}
                </div>
              </div>
            </div>
          );
        }

        return <WrappedComponent {...props} />;
      };
    };
  }
}

// Export singleton
export const ipSecurity = new IPSecurityService();

// Hook React pour vérification IP
export const useIPSecurity = () => {
  const [status, setStatus] = React.useState<{
    loading: boolean;
    authorized: boolean;
    error?: string;
    clientIP?: string;
  }>({
    loading: true,
    authorized: false
  });

  React.useEffect(() => {
    ipSecurity.checkIPAuthorization().then(result => {
      setStatus({
        loading: false,
        authorized: result.authorized,
        error: result.reason,
        clientIP: result.clientIP
      });
    }).catch(error => {
      setStatus({
        loading: false,
        authorized: false,
        error: error.message
      });
    });
  }, []);

  return status;
};

// Initialisation automatique basée sur les variables d'environnement
export const initializeIPSecurity = () => {
  const mode = process.env.REACT_APP_MODE as 'admin' | 'user';
  const allowedIPs = process.env.REACT_APP_ALLOWED_IPS?.split(',') || [];
  const poleId = process.env.REACT_APP_POLE_ID;

  if (mode && allowedIPs.length > 0) {
    ipSecurity.initialize({
      mode,
      allowedIPs,
      poleId
    });
  }
};
