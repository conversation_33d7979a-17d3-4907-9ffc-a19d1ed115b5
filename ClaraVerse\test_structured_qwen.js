/**
 * 🧪 Test Qwen 8B avec Structured Output LM Studio
 */

import fetch from 'node-fetch';

const LM_STUDIO_URL = 'http://localhost:1234';

// Schema JSON pour la compression
const COMPRESSION_SCHEMA = {
  "type": "object",
  "properties": {
    "conversationSummary": {
      "type": "string",
      "description": "Résumé narratif complet de la conversation"
    },
    "chronologicalFlow": {
      "type": "string", 
      "description": "Chronologie des événements et échanges importants"
    },
    "keyDecisions": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "Décisions et conclusions importantes"
    },
    "importantFacts": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "Faits importants à retenir"
    },
    "documentsContext": {
      "type": "object",
      "properties": {
        "documentsUsed": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "description": "Liste des documents utilisés"
        },
        "documentsSummary": {
          "type": "string",
          "description": "Résumé du contenu des documents"
        },
        "keyExtracts": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "description": "Extraits clés des documents"
        }
      },
      "required": ["documentsUsed", "documentsSummary", "keyExtracts"]
    }
  },
  "required": ["conversationSummary", "chronologicalFlow", "keyDecisions", "importantFacts", "documentsContext"]
};

async function testStructuredQwen() {
  console.log('🧪 Test Qwen 8B avec Structured Output...');
  
  const testConversation = `CONVERSATION À COMPRESSER:

[USER] Salut ! J'ai besoin d'aide pour analyser mon CV et préparer un entretien d'embauche.

[ASSISTANT] Bonjour ! Je serais ravi de vous aider avec votre CV et la préparation d'entretien. Pouvez-vous partager votre CV pour que je puisse l'analyser ?

[USER] Voici mon CV : Jean Dupont, développeur Full-Stack senior avec 8 ans d'expérience. J'ai travaillé chez TechCorp sur des applications React/Node.js pour 500K+ utilisateurs. J'ai dirigé une équipe de 5 développeurs et amélioré les performances de 40%. Mes compétences incluent React, TypeScript, Node.js, PostgreSQL, AWS, Docker, Kubernetes. Formation Master Informatique Paris-Saclay.

[ASSISTANT] Excellent profil ! Votre expérience de leadership et vos réalisations quantifiées sont très impressionnantes. Points forts identifiés : progression claire dans votre carrière, stack technologique moderne et recherchée, impact mesurable sur les performances. Suggestions d'amélioration : ajouter des certifications AWS pour renforcer votre profil cloud, détailler davantage vos soft skills de leadership, inclure des métriques business en plus des métriques techniques.

[USER] J'ai un entretien chez une fintech demain pour un poste de Lead Developer. Quelles questions techniques puis-je attendre ?

[ASSISTANT] Pour une fintech, préparez-vous à des questions spécifiques : sécurité des données financières et conformité réglementaire, optimisation de performance pour haute charge (millions de transactions), architecture distribuée et microservices, gestion d'erreurs critiques et rollback, intégration d'APIs bancaires et systèmes de paiement.

[USER] Peux-tu simuler un entretien technique avec moi ?

[ASSISTANT] Bien sûr ! Voici une question typique pour un Lead Developer fintech : "Comment concevriez-vous l'architecture d'un système de paiement capable de traiter 5 millions de transactions par jour avec une latence maximale de 100ms, tout en garantissant la sécurité et la conformité réglementaire ?"

[USER] Pour cette architecture, je proposerais une approche microservices avec event sourcing. Utilisation de message queues pour découpler les services, cache Redis pour les données fréquentes, base de données PostgreSQL avec réplication pour la persistance, et monitoring en temps réel avec alertes automatiques.

[ASSISTANT] Excellente réponse ! Votre approche microservices avec event sourcing est parfaitement adaptée. J'apprécie particulièrement la mention du monitoring et des alertes. Pour approfondir : comment géreriez-vous les transactions distribuées et la cohérence des données entre microservices ?`;

  const systemPrompt = `Tu es un expert en compression intelligente de conversations pour IA. Ta mission est de compresser une conversation en préservant ABSOLUMENT TOUT ce qui est important pour qu'un LLM puisse continuer la conversation naturellement.

ANALYSE REQUISE:
1. Identifier le FIL CHRONOLOGIQUE de la conversation
2. Extraire les DÉCISIONS et CONCLUSIONS importantes  
3. Préserver les RÉFÉRENCES aux documents/fichiers
4. Maintenir le CONTEXTE RELATIONNEL (qui a dit quoi)
5. Garder les INFORMATIONS FACTUELLES critiques

RÈGLES ABSOLUES:
- JAMAIS perdre d'information critique
- TOUJOURS maintenir la chronologie
- PRÉSERVER les nuances et le ton
- GARDER les références exactes
- MAINTENIR la cohérence narrative

La qualité de ta compression détermine si la conversation peut continuer naturellement. Sois PARFAIT.`;

  try {
    console.log('📤 Envoi à Qwen 8B avec schema JSON...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-8b', // Qwen 8B
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Analyse et compresse cette conversation selon le schema JSON fourni :\n\n${testConversation}`
          }
        ],
        temperature: 0.1,
        max_tokens: 1500,
        stream: false,
        // STRUCTURED OUTPUT - Format officiel LM Studio
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "conversation_compression",
            strict: "true",
            schema: COMPRESSION_SCHEMA
          }
        }
      })
    });
    
    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Erreur Qwen structured:', response.status, errorText);
      return false;
    }
    
    const result = await response.json();
    const aiResponse = result.choices?.[0]?.message?.content || '';
    
    console.log(`⏱️ Temps de réponse Qwen 8B: ${duration}ms`);
    console.log('📝 Réponse structured (premiers 500 chars):', aiResponse.substring(0, 500) + '...');
    
    try {
      const parsed = JSON.parse(aiResponse);
      console.log('✅ JSON PARFAITEMENT VALIDE avec Qwen 8B !');
      
      // Vérifier la structure complète
      const hasAllFields = 
        parsed.conversationSummary &&
        parsed.chronologicalFlow &&
        parsed.keyDecisions &&
        parsed.importantFacts &&
        parsed.documentsContext &&
        parsed.documentsContext.documentsUsed &&
        parsed.documentsContext.documentsSummary &&
        parsed.documentsContext.keyExtracts;
      
      console.log(`Structure complète: ${hasAllFields ? '✅' : '❌'}`);
      
      // Analyser la qualité du contenu
      const summaryLength = parsed.conversationSummary?.length || 0;
      const decisionsCount = parsed.keyDecisions?.length || 0;
      const factsCount = parsed.importantFacts?.length || 0;
      const documentsCount = parsed.documentsContext?.documentsUsed?.length || 0;
      
      console.log('\n📊 QUALITÉ DU CONTENU:');
      console.log(`Résumé: ${summaryLength} caractères ${summaryLength > 100 ? '✅' : '❌'}`);
      console.log(`Décisions: ${decisionsCount} items ${decisionsCount > 0 ? '✅' : '❌'}`);
      console.log(`Faits importants: ${factsCount} items ${factsCount > 0 ? '✅' : '❌'}`);
      console.log(`Documents: ${documentsCount} items ${documentsCount > 0 ? '✅' : '❌'}`);
      
      // Calculer les métriques
      const originalChars = testConversation.length;
      const compressedChars = aiResponse.length;
      const compressionRatio = originalChars / compressedChars;
      
      console.log('\n📈 MÉTRIQUES DE COMPRESSION:');
      console.log(`Original: ${originalChars} caractères`);
      console.log(`Compressé: ${compressedChars} caractères`);
      console.log(`Ratio: ${compressionRatio.toFixed(2)}x`);
      console.log(`Efficacité: ${((1 - compressedChars/originalChars) * 100).toFixed(1)}%`);
      console.log(`Performance: ${duration}ms`);
      
      // Score qualité global
      const qualityChecks = [
        summaryLength > 100,
        decisionsCount > 0,
        factsCount > 0,
        documentsCount > 0,
        hasAllFields,
        duration < 20000 // < 20 secondes
      ];
      
      const qualityScore = qualityChecks.filter(Boolean).length;
      console.log(`\n🎯 Score qualité global: ${qualityScore}/6`);
      
      // Afficher un extrait du résumé
      console.log('\n📋 EXTRAIT DU RÉSUMÉ:');
      console.log('=' .repeat(50));
      console.log(parsed.conversationSummary?.substring(0, 200) + '...');
      
      console.log('\n🎯 DÉCISIONS EXTRAITES:');
      parsed.keyDecisions?.slice(0, 3).forEach((decision, i) => {
        console.log(`${i + 1}. ${decision}`);
      });
      
      if (qualityScore >= 5) {
        console.log('\n🎉 QWEN 8B + STRUCTURED OUTPUT = PARFAIT !');
        return {
          success: true,
          duration,
          compressionRatio,
          qualityScore,
          model: 'qwen3-8b',
          structured: true,
          parsed
        };
      } else {
        console.log('\n⚠️ Qualité à améliorer');
        return { success: false, reason: 'Qualité insuffisante' };
      }
      
    } catch (parseError) {
      console.log('❌ Erreur parsing JSON (ne devrait pas arriver avec structured output):', parseError.message);
      console.log('📋 Réponse brute:', aiResponse);
      return { success: false, reason: 'JSON invalide' };
    }
    
  } catch (error) {
    console.error('❌ Erreur test structured Qwen:', error.message);
    return { success: false, reason: error.message };
  }
}

async function testPerformanceComparison() {
  console.log('\n🏁 Test de performance comparative...');
  
  const simpleConversation = `[USER] Bonjour
[ASSISTANT] Bonjour ! Comment puis-je vous aider ?
[USER] J'ai un CV à analyser : Jean Dupont, développeur React
[ASSISTANT] Parfait ! Votre profil React est intéressant.`;

  try {
    console.log('📤 Test conversation courte...');
    const startTime = Date.now();
    
    const response = await fetch(`${LM_STUDIO_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'qwen3-8b',
        messages: [
          {
            role: 'system',
            content: 'Tu es un expert en compression de conversations. Réponds selon le schema JSON fourni.'
          },
          {
            role: 'user',
            content: `Compresse cette conversation :\n\n${simpleConversation}`
          }
        ],
        temperature: 0.1,
        max_tokens: 500,
        stream: false,
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "conversation_compression",
            strict: "true",
            schema: COMPRESSION_SCHEMA
          }
        }
      })
    });
    
    const duration = Date.now() - startTime;
    
    if (response.ok) {
      const result = await response.json();
      const aiResponse = result.choices?.[0]?.message?.content || '';
      
      console.log(`⏱️ Conversation courte: ${duration}ms`);
      console.log('✅ Structured output fonctionne aussi sur conversations courtes');
      
      return { success: true, shortDuration: duration };
    }
    
  } catch (error) {
    console.log('❌ Erreur test performance:', error.message);
  }
  
  return { success: false };
}

// Test principal
async function runStructuredTests() {
  console.log('🧪 TESTS QWEN 8B + STRUCTURED OUTPUT\n');
  
  // Test principal
  const mainResult = await testStructuredQwen();
  
  if (mainResult.success) {
    console.log('\n✅ Test principal réussi ! Test de performance...');
    
    // Test performance
    const perfResult = await testPerformanceComparison();
    
    console.log('\n🎯 RÉSULTATS FINAUX:');
    console.log('==================');
    console.log('🎉 QWEN 8B + STRUCTURED OUTPUT VALIDÉ !');
    console.log(`🤖 Modèle: ${mainResult.model} (avec structured output)`);
    console.log(`⏱️ Performance: ${mainResult.duration}ms`);
    console.log(`📊 Compression: ${mainResult.compressionRatio.toFixed(2)}x`);
    console.log(`🎯 Qualité: ${mainResult.qualityScore}/6`);
    
    if (perfResult.success) {
      console.log(`⚡ Conversation courte: ${perfResult.shortDuration}ms`);
    }
    
    console.log('\n💡 CONFIGURATION OPTIMALE:');
    console.log('- Modèle: qwen3-8b');
    console.log('- Structured Output: ACTIVÉ');
    console.log('- Schema JSON: Validé');
    console.log('- Mode: Arrière-plan (< 20s)');
    console.log('- Seuil: 20K caractères');
    
    console.log('\n🚀 PRÊT POUR INTÉGRATION PRODUCTION !');
    
  } else {
    console.log('\n❌ Test principal échoué');
    console.log(`Raison: ${mainResult.reason}`);
    console.log('🔧 Vérifiez la configuration Structured Output dans LM Studio');
  }
}

runStructuredTests().catch(console.error);
