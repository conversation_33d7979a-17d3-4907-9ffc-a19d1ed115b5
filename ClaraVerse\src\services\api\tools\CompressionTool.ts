/**
 * 🗜️ Compression Tool - Tool Use Integration
 *
 * Responsabilités :
 * - Définition du tool compress_context pour les LLMs
 * - Exécution de la compression via PerfectCompressor
 * - Formatage des résultats pour les LLMs
 * - Support Qwen3 + LM Studio via format OpenAI
 */

import type { Tool } from '../../../db';
import { logger, LogCategory } from '../../../utils/logger';

/**
 * Configuration du tool compress_context
 */
export const COMPRESSION_TOOL: Tool = {
  id: 'compress_context',
  name: 'compress_context',
  description: 'Compress conversation context intelligently when it becomes too long. Use this when the conversation history exceeds reasonable limits to maintain performance while preserving important information.',
  parameters: [
    {
      name: 'context_to_compress',
      type: 'string',
      description: 'The conversation context that needs to be compressed. Usually the full conversation history.',
      required: true
    },
    {
      name: 'compression_ratio',
      type: 'number',
      description: 'Target compression ratio between 0.3 and 0.7 (0.5 = reduce to 50% of original size)',
      required: false
    },
    {
      name: 'preserve_recent_messages',
      type: 'number',
      description: 'Number of recent messages to preserve without compression (default: 3)',
      required: false
    }
  ],
  implementation: 'compress_context',
  isEnabled: true
};

/**
 * R<PERSON>ultat d'une compression pour tool use
 */
export interface CompressionResult {
  success: boolean;
  compressed_context?: string;
  original_length?: number;
  compressed_length?: number;
  compression_ratio?: number;
  compression_time?: number;
  error?: string;
}

/**
 * Gestionnaire du tool compress_context
 */
export class CompressionToolManager {
  
  /**
   * Exécuter une compression via tool call
   */
  public async executeCompression(args: {
    context_to_compress: string;
    compression_ratio?: number;
    preserve_recent_messages?: number;
  }): Promise<CompressionResult> {
    const startTime = Date.now();

    try {
      logger.info(LogCategory.TOOLS, `🗜️ Début compression context: ${args.context_to_compress.length} caractères`);

      // Validation des paramètres
      const compressionRatio = Math.max(0.3, Math.min(0.7, args.compression_ratio || 0.5));
      const preserveMessages = args.preserve_recent_messages || 3;

      // Appel au backend Python pour la compression
      const response = await fetch('/api/compress-context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context: args.context_to_compress,
          compression_ratio: compressionRatio,
          preserve_recent_messages: preserveMessages
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur API compression: ${response.status}`);
      }

      const result = await response.json();
      const compressionTime = Date.now() - startTime;

      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Compression failed',
          compression_time: compressionTime
        };
      }

      const originalLength = args.context_to_compress.length;
      const compressedLength = result.compressed_context?.length || 0;
      const actualRatio = originalLength > 0 ? compressedLength / originalLength : 1;

      logger.info(LogCategory.TOOLS, `✅ Compression réussie: ${originalLength} → ${compressedLength} chars (${(actualRatio * 100).toFixed(1)}%)`);

      return {
        success: true,
        compressed_context: result.compressed_context,
        original_length: originalLength,
        compressed_length: compressedLength,
        compression_ratio: actualRatio,
        compression_time: compressionTime
      };

    } catch (error) {
      const compressionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown compression error';
      
      logger.error(LogCategory.TOOLS, `❌ Erreur compression: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage,
        compression_time: compressionTime
      };
    }
  }

  /**
   * Obtenir le tool au format OpenAI pour les providers
   */
  public getOpenAIToolFormat() {
    return {
      type: 'function',
      function: {
        name: COMPRESSION_TOOL.name,
        description: COMPRESSION_TOOL.description,
        parameters: {
          type: 'object',
          properties: {
            context_to_compress: {
              type: 'string',
              description: 'The conversation context that needs to be compressed. Usually the full conversation history.'
            },
            compression_ratio: {
              type: 'number',
              minimum: 0.3,
              maximum: 0.7,
              default: 0.5,
              description: 'Target compression ratio between 0.3 and 0.7 (0.5 = reduce to 50% of original size)'
            },
            preserve_recent_messages: {
              type: 'integer',
              minimum: 1,
              maximum: 10,
              default: 3,
              description: 'Number of recent messages to preserve without compression'
            }
          },
          required: ['context_to_compress']
        }
      }
    };
  }

  /**
   * Formater le résultat pour le LLM
   */
  private formatCompressionResult(result: CompressionResult): string {
    if (!result.success) {
      return `❌ Compression failed: ${result.error}`;
    }

    const ratio = result.compression_ratio ? (result.compression_ratio * 100).toFixed(1) : 'unknown';
    const time = result.compression_time ? `${result.compression_time}ms` : 'unknown';
    
    return `✅ Context compressed successfully:
- Original: ${result.original_length} characters
- Compressed: ${result.compressed_length} characters  
- Ratio: ${ratio}%
- Time: ${time}

Compressed context:
${result.compressed_context}`;
  }
}

// Instance globale du gestionnaire
export const compressionToolManager = new CompressionToolManager();
