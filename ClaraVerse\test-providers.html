<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Providers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .provider { border: 1px solid #ccc; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: blue; }
    </style>
</head>
<body>
    <h1>🧪 Test des Providers</h1>
    <div id="status" class="loading">Chargement des providers...</div>
    <div id="providers"></div>

    <script>
        async function testProviders() {
            const statusDiv = document.getElementById('status');
            const providersDiv = document.getElementById('providers');

            try {
                // Test 1: Backend proxy
                statusDiv.innerHTML = '🔄 Test du backend proxy...';
                const response = await fetch('http://localhost:8000/proxy/providers');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const providers = data.providers || [];

                statusDiv.innerHTML = `<span class="success">✅ ${providers.length} providers trouvés via backend proxy</span>`;

                // Afficher les providers
                providersDiv.innerHTML = providers.map(provider => `
                    <div class="provider">
                        <h3>${provider.name}</h3>
                        <p><strong>Type:</strong> ${provider.type}</p>
                        <p><strong>URL:</strong> ${provider.baseUrl}</p>
                        <p><strong>Activé:</strong> ${provider.isEnabled ? 'Oui' : 'Non'}</p>
                        <p><strong>Primaire:</strong> ${provider.isPrimary ? 'Oui' : 'Non'}</p>
                        <p><strong>Modèles:</strong> ${provider.models ? provider.models.length : 0}</p>
                    </div>
                `).join('');

                // Test 2: Modèles LM Studio
                if (providers.find(p => p.type === 'lmstudio')) {
                    statusDiv.innerHTML += '<br>🔄 Test des modèles LM Studio...';
                    const modelsResponse = await fetch('http://localhost:8000/proxy/lmstudio/models');
                    
                    if (modelsResponse.ok) {
                        const modelsData = await modelsResponse.json();
                        statusDiv.innerHTML += `<br><span class="success">✅ ${modelsData.length} modèles LM Studio trouvés</span>`;
                    } else {
                        statusDiv.innerHTML += '<br><span class="error">❌ Erreur chargement modèles LM Studio</span>';
                    }
                }

            } catch (error) {
                statusDiv.innerHTML = `<span class="error">❌ Erreur: ${error.message}</span>`;
                console.error('Erreur test providers:', error);
            }
        }

        // Lancer le test au chargement
        testProviders();
    </script>
</body>
</html>
