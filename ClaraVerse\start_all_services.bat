@echo off
echo 🚀 Démarrage de tous les services Clara...

REM Démarrer LM Studio Server (si installé)
echo 📡 Vérification de LM Studio...
where lms >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ LM Studio trouvé, démarrage du serveur...
    start "LM Studio Server" cmd /c "lms server start --port 1234"
    timeout /t 3 >nul
) else (
    echo ⚠️ LM Studio CLI non trouvé, démarrez manuellement le serveur
)

REM Démarrer le backend Python
echo 🐍 Démarrage du backend Python...
cd py_backend
start "Clara Backend" cmd /c "python main.py"
cd ..

REM Attendre que les services démarrent
echo ⏳ Attente du démarrage des services...
timeout /t 5 >nul

REM Démarrer le frontend
echo 🌐 Démarrage du frontend...
start "Clara Frontend" cmd /c "npm run dev"

echo ✅ Tous les services sont en cours de démarrage !
echo 📝 Vérifiez les fenêtres de terminal pour les logs
pause
