#!/usr/bin/env python3
"""
Script d'installation du support des tableaux pour l'OCR
Installation des dépendances essentielles pour améliorer la détection de tableaux
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Execute une commande et gère les erreurs"""
    try:
        logger.info(f"🔧 {description}")
        logger.info(f"   Commande: {command}")
        
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"   ✅ Succès")
            if result.stdout.strip():
                logger.info(f"   📄 Output: {result.stdout.strip()}")
        else:
            logger.warning(f"   ⚠️ Échec (code {result.returncode})")
            if result.stderr.strip():
                logger.warning(f"   ❌ Erreur: {result.stderr.strip()}")
        
        return result.returncode == 0
    except Exception as e:
        logger.error(f"   ❌ Exception: {e}")
        return False

def install_table_dependencies():
    """Installe les dépendances pour le support des tableaux"""
    logger.info("📦 Installation des Dépendances pour Tableaux...")
    
    # Packages essentiels pour les tableaux
    packages = [
        "opencv-python>=4.8.0",      # Préprocessing d'images et détection de lignes
        "pymupdf>=1.23.0",           # Extraction PDF avancée
        "pdfplumber>=0.10.0",        # Extraction de tableaux PDF
    ]
    
    success_count = 0
    for package in packages:
        if run_command(f"pip install {package}", f"Installation de {package}"):
            success_count += 1
    
    logger.info(f"📊 Packages installés: {success_count}/{len(packages)}")
    return success_count == len(packages)

def test_dependencies():
    """Teste les dépendances installées"""
    logger.info("🧪 Test des Dépendances...")
    
    test_script = '''
import sys
try:
    import cv2
    print("✅ OpenCV:", cv2.__version__)
except ImportError:
    print("❌ OpenCV non disponible")

try:
    import fitz
    print("✅ PyMuPDF disponible")
except ImportError:
    print("❌ PyMuPDF non disponible")

try:
    import pdfplumber
    print("✅ pdfplumber disponible")
except ImportError:
    print("❌ pdfplumber non disponible")
'''
    
    with open("test_table_deps.py", "w") as f:
        f.write(test_script)
    
    run_command("python test_table_deps.py", "Test des dépendances")
    
    # Nettoyer
    try:
        import os
        os.remove("test_table_deps.py")
    except:
        pass

def main():
    """Installation principale"""
    logger.info("🚀 INSTALLATION DU SUPPORT DES TABLEAUX")
    logger.info("=" * 60)
    
    # Installation des dépendances
    deps_success = install_table_dependencies()
    
    # Test des dépendances
    test_dependencies()
    
    # Résumé
    logger.info("\n" + "=" * 60)
    logger.info("📊 RÉSUMÉ DE L'INSTALLATION")
    logger.info(f"   📦 Dépendances tableaux: {'✅' if deps_success else '❌'}")
    
    if deps_success:
        logger.info("\n🎉 SUPPORT DES TABLEAUX INSTALLÉ !")
        logger.info("   Fonctionnalités disponibles:")
        logger.info("   - Préprocessing d'images amélioré")
        logger.info("   - Extraction PDF intelligente")
        logger.info("   - Détection de tableaux simple")
        logger.info("   - Extraction de tableaux PDF")
    else:
        logger.info("\n⚠️ INSTALLATION PARTIELLE")
        logger.info("   Certaines fonctionnalités peuvent être limitées")
    
    logger.info("\n📋 UTILISATION:")
    logger.info("   1. Redémarrer le backend Python")
    logger.info("   2. Utiliser 'table_mode: true' pour les documents avec tableaux")
    logger.info("   3. Parfait pour les comptes sociaux Pappers !")
    
    logger.info("\n🔧 PARAMÈTRES RECOMMANDÉS POUR PAPPERS:")
    logger.info("   {")
    logger.info('     "detect_tables": true,')
    logger.info('     "table_mode": true,')
    logger.info('     "preserve_layout": true,')
    logger.info('     "language": "eng+fra"')
    logger.info("   }")

if __name__ == "__main__":
    main()
