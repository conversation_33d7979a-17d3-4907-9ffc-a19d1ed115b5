#!/usr/bin/env python3
"""
Test de la recherche internet via le backend WeMa IA
"""

import requests
import json
import time

def test_internet_search():
    """Tester la recherche internet"""
    
    print("🔍 Test de la recherche internet...")
    
    # Données de test
    search_data = {
        "query": "actualités intelligence artificielle 2024",
        "search_type": "general",
        "max_results": 3,
        "time_range": "month"
    }
    
    try:
        print(f"📡 Envoi de la requête: {search_data['query']}")
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:5001/search/internet",
            json=search_data,
            timeout=30
        )
        search_time = time.time() - start_time
        
        print(f"📊 Statut: {response.status_code}")
        print(f"⏱️ Temps de recherche: {search_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Recherche réussie!")
            
            # Afficher les métadonnées
            if 'metadata' in data:
                metadata = data['metadata']
                print(f"📈 Métadonnées:")
                print(f"  - Sources trouvées: {len(metadata.get('sources', []))}")
                print(f"  - Contenu scrapé: {len(metadata.get('scraped_content', []))}")
                print(f"  - Nombre de résultats: {metadata.get('results_count', 0)}")
            
            # Afficher le résultat formaté
            if 'result' in data:
                result = data['result']
                print(f"📝 Résultat formaté:")
                print(f"  Longueur: {len(result)} caractères")
                print(f"  Aperçu: {result[:200]}...")
            
            # Afficher les sources
            if 'metadata' in data and 'sources' in data['metadata']:
                sources = data['metadata']['sources']
                print(f"🔗 Sources ({len(sources)}):")
                for i, source in enumerate(sources[:3], 1):
                    print(f"  {i}. {source}")
                    
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

def test_news_search():
    """Tester la recherche d'actualités"""
    
    print("\n📰 Test de la recherche d'actualités...")
    
    search_data = {
        "query": "OpenAI GPT-4",
        "time_range": "month",
        "max_results": 3
    }
    
    try:
        print(f"📡 Envoi de la requête actualités: {search_data['query']}")
        
        response = requests.post(
            "http://localhost:5001/search/news",
            json=search_data,
            timeout=20
        )
        
        print(f"📊 Statut: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Recherche actualités réussie!")
            print(f"📝 Résultat: {len(data.get('result', ''))} caractères")
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur lors du test actualités: {e}")

if __name__ == "__main__":
    test_internet_search()
    test_news_search()
