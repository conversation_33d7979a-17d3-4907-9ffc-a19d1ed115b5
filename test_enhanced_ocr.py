#!/usr/bin/env python3
"""
Test du système OCR amélioré
"""

import requests
import base64
import json
import time
from PIL import Image, ImageDraw, ImageFont
import io

BASE_URL = "http://127.0.0.1:5000"

def create_test_image():
    """Crée une image de test avec du texte"""
    # Créer une image blanche
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Essayer d'utiliser une police par défaut
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Ajouter du texte de test
    test_text = [
        "DOCUMENT DE TEST OCR",
        "",
        "Nom: <PERSON>",
        "Email: <EMAIL>", 
        "Téléphone: +33 6 12 34 56 78",
        "Adresse: 123 rue de la Paix, 75001 Paris",
        "",
        "Ce document contient des informations",
        "personnelles pour tester l'OCR et",
        "l'anonymisation GDPR.",
        "",
        "Date: 11 juin 2025",
        "Numéro de carte: 4532-1234-5678-9012"
    ]
    
    y_position = 50
    for line in test_text:
        if line:  # Ne pas dessiner les lignes vides
            draw.text((50, y_position), line, fill='black', font=font)
        y_position += 35
    
    return img

def image_to_base64(image):
    """Convertit une image PIL en base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def test_ocr_status():
    """Test du statut OCR"""
    print("1. 🔍 Test du Statut OCR...")
    try:
        response = requests.get(f"{BASE_URL}/ocr/status")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ OCR disponible: {status.get('ocr_available', False)}")
            print(f"   📋 Formats supportés: {len(status.get('supported_formats', []))}")

            # Afficher les capacités
            if 'features' in status:
                features = status['features']
                print(f"   🚀 Fonctionnalités:")
                for feature, available in features.items():
                    print(f"      - {feature}: {'✅' if available else '❌'}")

            # Afficher les outils disponibles
            print(f"   🔧 Outils:")
            print(f"      - OpenCV: {'✅' if status.get('opencv_available', False) else '❌'}")
            print(f"      - PyMuPDF: {'✅' if status.get('pymupdf_available', False) else '❌'}")
            print(f"      - Preprocessing: {'✅' if status.get('preprocessing_enabled', False) else '❌'}")

            return True
        else:
            print(f"   ❌ Erreur: {response.text}")
            return False

    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def test_enhanced_ocr():
    """Test de l'OCR amélioré"""
    print("\n2. 🖼️ Test OCR avec Image de Test...")
    
    try:
        # Créer une image de test
        test_image = create_test_image()
        image_base64 = image_to_base64(test_image)
        
        print(f"   📄 Image créée: {test_image.size}")
        print(f"   🔢 Base64 généré: {len(image_base64)} caractères")
        
        # Préparer la requête OCR
        ocr_request = {
            "file_base64": image_base64,
            "file_type": "png",
            "preserve_layout": True,
            "language": "eng+fra",
            "dpi": 300
        }
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/ocr/process", json=ocr_request)
        processing_time = time.time() - start_time
        
        print(f"   ⏱️ Temps de traitement: {processing_time:.2f}s")
        print(f"   📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ OCR réussi: {result.get('success', False)}")
            
            # Afficher les métriques de performance
            if 'processing_time' in result:
                print(f"   ⚡ Temps backend: {result['processing_time']:.2f}s")
            if 'confidence_score' in result:
                print(f"   🎯 Score de confiance: {result['confidence_score']:.2f}")
            if 'ocr_enhanced' in result:
                print(f"   🚀 OCR amélioré utilisé: {result['ocr_enhanced']}")
            
            # Afficher les moteurs disponibles
            if 'available_engines' in result:
                engines = result['available_engines']
                active_engines = [k for k, v in engines.items() if v]
                print(f"   🔧 Moteurs actifs: {', '.join(active_engines)}")
            
            # Afficher le texte extrait
            text = result.get('text', '')
            formatted_text = result.get('formatted_text', '')
            
            print(f"   📝 Texte extrait ({len(text)} chars):")
            print(f"      {text[:200]}{'...' if len(text) > 200 else ''}")
            
            # Afficher les blocs de texte
            text_blocks = result.get('text_blocks', [])
            print(f"   📦 Blocs de texte: {len(text_blocks)}")
            
            for i, block in enumerate(text_blocks[:5]):  # Afficher les 5 premiers
                engine = block.get('ocr_engine', 'unknown')
                confidence = block.get('confidence', 0)
                text_content = block.get('text', '')
                print(f"      [{i}] {engine} ({confidence:.2f}): '{text_content[:50]}{'...' if len(text_content) > 50 else ''}'")
            
            # Métadonnées
            metadata = result.get('metadata', {})
            print(f"   📊 Métadonnées:")
            for key, value in metadata.items():
                print(f"      - {key}: {value}")
            
            return True
        else:
            print(f"   ❌ OCR échoué: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def test_pdf_processing():
    """Test du traitement PDF (si disponible)"""
    print("\n3. 📄 Test Traitement PDF...")
    
    # Créer un PDF simple à partir de l'image de test
    try:
        test_image = create_test_image()
        
        # Convertir en PDF
        pdf_buffer = io.BytesIO()
        test_image.save(pdf_buffer, format='PDF')
        pdf_data = pdf_buffer.getvalue()
        pdf_base64 = base64.b64encode(pdf_data).decode()
        
        print(f"   📄 PDF créé: {len(pdf_data)} bytes")
        
        # Tester l'OCR PDF
        pdf_request = {
            "file_base64": pdf_base64,
            "file_type": "pdf",
            "preserve_layout": True,
            "language": "eng+fra",
            "dpi": 300
        }
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/ocr/process", json=pdf_request)
        processing_time = time.time() - start_time
        
        print(f"   ⏱️ Temps de traitement PDF: {processing_time:.2f}s")
        print(f"   📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ PDF OCR réussi: {result.get('success', False)}")
            
            metadata = result.get('metadata', {})
            extraction_method = metadata.get('extraction_method', 'unknown')
            print(f"   🔧 Méthode d'extraction: {extraction_method}")
            
            if 'total_pages' in metadata:
                print(f"   📄 Pages traitées: {metadata['total_pages']}")
            
            text = result.get('text', '')
            print(f"   📝 Texte PDF extrait ({len(text)} chars)")
            
            return True
        else:
            print(f"   ❌ PDF OCR échoué: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception PDF: {e}")
        return False

def main():
    """Test principal du système OCR amélioré"""
    print("🚀 TEST DU SYSTÈME OCR AMÉLIORÉ")
    print("=" * 50)
    
    # Test du statut
    enhanced_available = test_ocr_status()
    
    # Test de l'OCR sur image
    ocr_success = test_enhanced_ocr()
    
    # Test du traitement PDF
    pdf_success = test_pdf_processing()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print(f"   🚀 OCR amélioré: {'✅' if enhanced_available else '❌'}")
    print(f"   🖼️ OCR Image: {'✅' if ocr_success else '❌'}")
    print(f"   📄 OCR PDF: {'✅' if pdf_success else '❌'}")
    
    if enhanced_available and ocr_success:
        print("\n🎉 SYSTÈME OCR AMÉLIORÉ OPÉRATIONNEL !")
        print("   Toutes les fonctionnalités avancées sont disponibles")
        print("   Vous pouvez maintenant traiter tous types de documents")
    elif ocr_success:
        print("\n✅ SYSTÈME OCR BASIQUE OPÉRATIONNEL")
        print("   L'OCR fonctionne mais sans les améliorations avancées")
        print("   Installez les dépendances pour activer les fonctionnalités avancées")
    else:
        print("\n❌ PROBLÈME AVEC LE SYSTÈME OCR")
        print("   Vérifiez l'installation et les dépendances")
    
    print("\n📋 PROCHAINES ÉTAPES:")
    print("   1. Tester avec de vrais documents dans l'interface")
    print("   2. Vérifier les performances sur différents types de fichiers")
    print("   3. Ajuster les paramètres selon vos besoins")

if __name__ == "__main__":
    main()
