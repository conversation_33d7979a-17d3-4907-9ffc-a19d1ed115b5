#!/usr/bin/env python3
"""
Résumé complet de l'état de la recherche internet WeMa IA
"""

import requests
import json

def test_all_search_endpoints():
    """Test de tous les endpoints de recherche"""
    
    print("🔍 RÉSUMÉ COMPLET - RECHERCHE INTERNET WEMA IA")
    print("=" * 60)
    
    endpoints = [
        {
            "name": "Recherche générale",
            "url": "http://localhost:5001/search/internet",
            "data": {"query": "intelligence artificielle 2024", "search_type": "general", "max_results": 2}
        },
        {
            "name": "Recherche actualités",
            "url": "http://localhost:5001/search/news", 
            "data": {"query": "ChatGPT nouvelles fonctionnalités", "time_range": "month", "max_results": 2}
        },
        {
            "name": "Recherche technique",
            "url": "http://localhost:5001/search/technical",
            "data": {"query": "Python FastAPI", "max_results": 2}
        }
    ]
    
    results = {}
    
    for endpoint in endpoints:
        print(f"\n🔍 Test: {endpoint['name']}")
        print("-" * 40)
        
        try:
            response = requests.post(
                endpoint["url"],
                json=endpoint["data"],
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Analyser la réponse
                success = data.get('success', False)
                result_length = len(data.get('result', ''))
                metadata = data.get('metadata', {})
                sources = metadata.get('sources', [])
                scraped = metadata.get('scraped_content', [])
                
                print(f"✅ Statut: {response.status_code}")
                print(f"✅ Succès: {success}")
                print(f"📝 Résultat: {result_length} caractères")
                print(f"🔗 Sources: {len(sources)}")
                print(f"📄 Contenu scrapé: {len(scraped)}")
                
                if sources:
                    print("🔗 Exemples de sources:")
                    for i, source in enumerate(sources[:2], 1):
                        print(f"  {i}. {source}")
                
                results[endpoint['name']] = {
                    "status": "✅ OK",
                    "sources": len(sources),
                    "content_length": result_length,
                    "scraped": len(scraped)
                }
                
            else:
                print(f"❌ Erreur: {response.status_code}")
                results[endpoint['name']] = {"status": f"❌ Erreur {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results[endpoint['name']] = {"status": f"❌ Exception: {e}"}
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 60)
    
    for name, result in results.items():
        print(f"{name}: {result['status']}")
        if 'sources' in result:
            print(f"  - Sources: {result['sources']}")
            print(f"  - Contenu: {result['content_length']} caractères")
            print(f"  - Scrapé: {result['scraped']} pages")
    
    # Test SearXNG direct
    print(f"\n🔍 Test SearXNG direct:")
    try:
        response = requests.get(
            "http://localhost:8888/search",
            params={'q': 'test', 'format': 'json'},
            timeout=5
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SearXNG: {len(data.get('results', []))} résultats")
        else:
            print(f"❌ SearXNG: Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ SearXNG: {e}")
    
    print("\n🎯 CONCLUSION:")
    all_ok = all("✅" in result['status'] for result in results.values())
    if all_ok:
        print("🎉 RECHERCHE INTERNET PARFAITEMENT FONCTIONNELLE!")
        print("✅ SearXNG opérationnel")
        print("✅ Tous les endpoints de recherche fonctionnent")
        print("✅ Sources réelles trouvées")
        print("✅ Contenu formaté correctement")
        print("\n💡 La recherche internet est prête pour l'utilisation!")
    else:
        print("⚠️ Certains problèmes détectés - voir détails ci-dessus")

if __name__ == "__main__":
    test_all_search_endpoints()
