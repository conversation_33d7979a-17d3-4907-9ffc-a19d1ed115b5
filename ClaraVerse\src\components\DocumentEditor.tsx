/**
 * Document Editor Component
 * Advanced document editing with GDPR awareness and chat integration
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  ArrowLeft,
  Save,
  RotateCcw,
  Eye,
  EyeOff,
  MessageSquare,
  Shield,
  FileText,
  Edit3,
  Copy,
  Download,
  Loader2,
  CheckCircle,
  AlertCircle,
  Maximize2,
  Minimize2
} from 'lucide-react';

import { DocumentInfo, DocumentEditRequest } from '../types/gdpr-types';
import { gdprApiService } from '../services/gdprApiService';

interface DocumentEditorProps {
  document: DocumentInfo;
  onBack: () => void;
  onSave: (updatedDocument: DocumentInfo) => void;
  onChatWithDocument: (document: DocumentInfo) => void;
}

const DocumentEditor: React.FC<DocumentEditorProps> = ({
  document,
  onBack,
  onSave,
  onChatWithDocument
}) => {
  // ============================================================================
  // State Management
  // ============================================================================

  const [content, setContent] = useState((document as any).content || '');
  const [originalContent, setOriginalContent] = useState((document as any).content || '');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // ============================================================================
  // Effects
  // ============================================================================

  useEffect(() => {
    setHasChanges(content !== originalContent);
  }, [content, originalContent]);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleSave = async () => {
    if (!hasChanges) return;

    setSaving(true);
    setSaveStatus('saving');

    try {
      const request: DocumentEditRequest = {
        documentId: document.id,
        newContent: content,
        preserveAnonymization: document.isAnonymized || false
      };

      const response = await gdprApiService.updateDocument(request);
      
      if (response.success && response.data) {
        setOriginalContent(content);
        setSaveStatus('saved');
        onSave(response.data);
        
        // Reset save status after 2 seconds
        setTimeout(() => setSaveStatus('idle'), 2000);
      } else {
        setSaveStatus('error');
        alert(`Error saving document: ${response.error}`);
      }
    } catch (error) {
      setSaveStatus('error');
      console.error('Error saving document:', error);
      alert('Error saving document');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (hasChanges && !confirm('Are you sure you want to discard your changes?')) {
      return;
    }
    setContent(originalContent);
    setIsEditing(false);
  };

  const handleCopyContent = () => {
    navigator.clipboard.writeText(content);
    alert('Content copied to clipboard!');
  };

  const handleDownload = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${document.filename}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 's') {
        e.preventDefault();
        handleSave();
      } else if (e.key === 'z') {
        e.preventDefault();
        handleReset();
      }
    }
  };

  // ============================================================================
  // Render Methods
  // ============================================================================

  const renderHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <button
          onClick={onBack}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        
        <div className="flex items-center gap-2">
          <FileText className="w-6 h-6 text-blue-500" />
          {document.isAnonymized && (
            <Shield className="w-5 h-5 text-green-500" title="GDPR Anonymized" />
          )}
        </div>
        
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            {document.filename}
          </h1>
          <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span>{document.fileType.toUpperCase()}</span>
            <span>{document.collectionName}</span>
            <span>{document.chunkCount} chunks</span>
            {hasChanges && (
              <span className="text-orange-500 dark:text-orange-400">• Unsaved changes</span>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {/* Save Status Indicator */}
        {saveStatus === 'saving' && (
          <div className="flex items-center gap-2 text-blue-500">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Saving...</span>
          </div>
        )}
        {saveStatus === 'saved' && (
          <div className="flex items-center gap-2 text-green-500">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">Saved</span>
          </div>
        )}
        {saveStatus === 'error' && (
          <div className="flex items-center gap-2 text-red-500">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">Error</span>
          </div>
        )}

        {/* Action Buttons */}
        <button
          onClick={() => onChatWithDocument(document)}
          className="flex items-center gap-2 px-3 py-2 bg-sakura-500 text-white rounded-lg hover:bg-sakura-600 transition-colors"
        >
          <MessageSquare className="w-4 h-4" />
          Chat
        </button>

        <button
          onClick={handleCopyContent}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          title="Copy content"
        >
          <Copy className="w-4 h-4" />
        </button>

        <button
          onClick={handleDownload}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          title="Download content"
        >
          <Download className="w-4 h-4" />
        </button>

        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
        </button>
      </div>
    </div>
  );

  const renderToolbar = () => (
    <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div className="flex items-center gap-2">
        <button
          onClick={() => setIsEditing(!isEditing)}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
            isEditing 
              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          <Edit3 className="w-4 h-4" />
          {isEditing ? 'Exit Edit' : 'Edit'}
        </button>

        <button
          onClick={() => setShowPreview(!showPreview)}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
            showPreview 
              ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          Preview
        </button>
      </div>

      {isEditing && (
        <div className="flex items-center gap-2">
          <button
            onClick={handleReset}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            disabled={!hasChanges}
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </button>

          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!hasChanges || isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            Save
          </button>
        </div>
      )}
    </div>
  );

  const renderContent = () => (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900 p-6' : ''}`}>
      {isFullscreen && renderHeader()}
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        {!isFullscreen && renderToolbar()}
        
        <div className="relative">
          {isEditing ? (
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full h-96 p-4 border-0 resize-none focus:ring-0 bg-transparent text-gray-900 dark:text-white font-mono text-sm leading-relaxed"
              placeholder="Start editing your document..."
            />
          ) : (
            <div className="p-4 h-96 overflow-y-auto">
              {showPreview ? (
                <div 
                  className="prose dark:prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ 
                    __html: content.replace(/\n/g, '<br>') 
                  }}
                />
              ) : (
                <pre className="whitespace-pre-wrap text-gray-900 dark:text-white font-mono text-sm leading-relaxed">
                  {content}
                </pre>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // ============================================================================
  // Main Render
  // ============================================================================

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {!isFullscreen && renderHeader()}
        {renderContent()}
        
        {/* Keyboard Shortcuts Help */}
        <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
          <span className="font-mono">Ctrl+S</span> to save • <span className="font-mono">Ctrl+Z</span> to reset
        </div>
      </div>
    </div>
  );
};

export default DocumentEditor;
