/**
 * Types spécifiques au système RAG
 */

export interface RagDocument {
  id: string;
  filename: string;
  content: string;
  fileType?: string;
  collectionName?: string;
}

export interface RagResult {
  content: string;
  score: number;
  metadata: {
    source_file: string;
    document_id: string;
    type: string;
  };
}

export interface RagContext {
  content: string;
  totalLength: number;
  documentsUsed: number;
  mode: 'fast' | 'performant';
  processingTime: number;
}

export interface RagConfig {
  mode: 'fast' | 'performant';
  threshold: number;
  maxContextLength: number;
  enableContextManagement: boolean;
}

export interface DocumentAnalysis {
  doc: RagDocument;
  contentLength: number;
  useRAG: boolean;
  processingMethod: 'direct' | 'rag' | 'hybrid';
}

export interface RagProcessingResult {
  ragContext: string;
  ragResults: RagResult[];
  processingStats: {
    mode: string;
    documentsProcessed: number;
    totalContextLength: number;
    processingTime: number;
    directDocuments: number;
    ragProcessedDocuments: number;
  };
}

export type OnContentChunk = (chunk: string) => void;

export interface RagModeInterface {
  processDocuments(
    documents: RagDocument[],
    userQuery: string,
    onContentChunk?: OnContentChunk
  ): Promise<RagProcessingResult>;
}
