/**
 * 🧪 Script de Test de Connectivité WeMa IA
 * 
 * Teste la connectivité vers tous les services :
 * - Backend local (localhost:5001)
 * - Serveur Ollama central (*********:11434)
 * - LM Studio local (localhost:1234)
 */

// Utiliser fetch natif de Node.js 18+ ou polyfill
const fetch = globalThis.fetch || require('node-fetch').default;

// Configuration des services
const SERVICES = {
  backend: {
    name: 'Backend Local',
    url: 'http://localhost:5001',
    healthEndpoint: '/health',
    timeout: 5000,
    required: true
  },
  ollama: {
    name: 'Ollama Central (Inférence IA)',
    url: 'http://***********:11434',
    healthEndpoint: '/api/tags',
    timeout: 8000,
    required: false // Fallback possible
  },
  lmstudio: {
    name: 'LM Studio Local (Fallback)',
    url: 'http://localhost:1234',
    healthEndpoint: '/v1/models',
    timeout: 3000,
    required: false // Fallback
  }
};

/**
 * Test de connectivité pour un service
 */
async function testService(serviceKey, config) {
  const startTime = Date.now();
  const testUrl = `${config.url}${config.healthEndpoint}`;
  
  console.log(`🔍 Testing ${config.name}...`);
  console.log(`   URL: ${testUrl}`);
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json().catch(() => ({}));
      
      console.log(`✅ ${config.name} - OK`);
      console.log(`   Response time: ${responseTime}ms`);
      console.log(`   Status: ${response.status}`);
      
      // Informations spécifiques par service
      if (serviceKey === 'ollama' && data.models) {
        console.log(`   Models available: ${data.models.length}`);
        if (data.models.length > 0) {
          console.log(`   Sample models: ${data.models.slice(0, 3).map(m => m.name).join(', ')}`);
        }
      } else if (serviceKey === 'lmstudio' && data.data) {
        console.log(`   Models available: ${data.data.length}`);
        if (data.data.length > 0) {
          console.log(`   Sample models: ${data.data.slice(0, 3).map(m => m.id).join(', ')}`);
        }
      } else if (serviceKey === 'backend') {
        console.log(`   Service: ${data.service || 'Unknown'}`);
        console.log(`   Version: ${data.version || 'Unknown'}`);
        console.log(`   RAG Available: ${data.rag_available || false}`);
      }
      
      return {
        success: true,
        responseTime,
        data
      };
    } else {
      console.log(`❌ ${config.name} - HTTP Error`);
      console.log(`   Status: ${response.status} ${response.statusText}`);
      console.log(`   Response time: ${responseTime}ms`);
      
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    if (error.name === 'AbortError') {
      console.log(`⏰ ${config.name} - Timeout`);
      console.log(`   Timeout after: ${config.timeout}ms`);
    } else {
      console.log(`❌ ${config.name} - Connection Error`);
      console.log(`   Error: ${error.message}`);
      console.log(`   Response time: ${responseTime}ms`);
    }
    
    return {
      success: false,
      error: error.message,
      responseTime
    };
  }
}

/**
 * Analyse du statut global
 */
function analyzeOverallStatus(results) {
  const backend = results.backend;
  const ollama = results.ollama;
  const lmstudio = results.lmstudio;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 ANALYSE DU STATUT GLOBAL');
  console.log('='.repeat(60));
  
  // Backend obligatoire
  if (!backend.success) {
    console.log('🚨 STATUT: CRITIQUE');
    console.log('❌ Backend local indisponible - Application non fonctionnelle');
    console.log('💡 Action requise: Démarrer le backend Python (py_backend/main.py)');
    return 'critical';
  }
  
  // Inférence IA
  if (ollama.success) {
    console.log('✅ STATUT: OPTIMAL');
    console.log('🌐 Serveur central Ollama accessible - Inférence IA optimale');
    console.log('🏗️ Architecture: Backend local + Inférence déléguée');
    return 'optimal';
  } else if (lmstudio.success) {
    console.log('⚠️ STATUT: DÉGRADÉ');
    console.log('🏠 Serveur central indisponible - Fallback LM Studio actif');
    console.log('💡 Recommandation: Vérifier la connectivité vers *********:11434');
    return 'degraded';
  } else {
    console.log('🚨 STATUT: CRITIQUE');
    console.log('❌ Aucun service d\'inférence IA disponible');
    console.log('💡 Actions requises:');
    console.log('   1. Vérifier la connectivité réseau vers *********:11434');
    console.log('   2. Ou démarrer LM Studio local sur localhost:1234');
    return 'critical';
  }
}

/**
 * Test principal
 */
async function main() {
  console.log('🚀 WeMa IA - Test de Connectivité');
  console.log('Architecture: Backend local + Inférence déléguée');
  console.log('='.repeat(60));
  
  const results = {};
  
  // Tester tous les services
  for (const [serviceKey, config] of Object.entries(SERVICES)) {
    results[serviceKey] = await testService(serviceKey, config);
    console.log(''); // Ligne vide pour la lisibilité
  }
  
  // Analyser le statut global
  const overallStatus = analyzeOverallStatus(results);
  
  // Résumé final
  console.log('\n' + '='.repeat(60));
  console.log('📋 RÉSUMÉ DES TESTS');
  console.log('='.repeat(60));
  
  for (const [serviceKey, config] of Object.entries(SERVICES)) {
    const result = results[serviceKey];
    const status = result.success ? '✅ OK' : '❌ FAIL';
    const time = result.responseTime ? `${result.responseTime}ms` : 'N/A';
    
    console.log(`${status} ${config.name.padEnd(30)} ${time.padStart(8)}`);
    if (!result.success && config.required) {
      console.log(`     ⚠️ Service critique indisponible`);
    }
  }
  
  console.log('='.repeat(60));
  
  // Code de sortie
  const exitCode = overallStatus === 'critical' ? 1 : 0;
  process.exit(exitCode);
}

// Gestion des erreurs non capturées
process.on('unhandledRejection', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

// Lancer le test
main().catch((error) => {
  console.error('❌ Erreur fatale:', error);
  process.exit(1);
});
