/**
 * 🔍 Test de vérification des modèles optimisés
 */

async function testModelExists() {
  console.log('🔍 === VÉRIFICATION DES MODÈLES OPTIMISÉS ===\n');

  try {
    // 1. Lister tous les modèles disponibles
    console.log('📋 1. Liste complète des modèles sur le serveur central...');
    const response = await fetch('http://localhost:5001/proxy/ollama/api/tags');
    
    if (!response.ok) {
      console.log(`❌ Erreur: ${response.status} ${response.statusText}`);
      return;
    }

    const data = await response.json();
    const models = data.models || [];
    
    console.log(`   📊 Total des modèles: ${models.length}\n`);
    
    // 2. Afficher tous les modèles avec détails
    console.log('📋 2. Modèles disponibles:');
    models.forEach((model, index) => {
      const size = model.size ? `(${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)` : '';
      console.log(`   ${index + 1}. ${model.name} ${size}`);
      if (model.modified) {
        console.log(`      📅 Modifié: ${new Date(model.modified).toLocaleString()}`);
      }
    });
    
    // 3. Rechercher spécifiquement nos modèles optimisés
    console.log('\n🎯 3. Recherche des modèles optimisés:');
    
    const qwen14bOptimized = models.find(m => m.name === 'qwen3-14b-optimized');
    const qwen32bOptimized = models.find(m => m.name === 'qwen3-32b-optimized');
    const qwen14bBase = models.find(m => m.name === 'qwen3:14b');
    const qwen32bBase = models.find(m => m.name.includes('qwen3:32b'));
    
    console.log(`   🎯 qwen3-14b-optimized: ${qwen14bOptimized ? '✅ TROUVÉ' : '❌ MANQUANT'}`);
    if (qwen14bOptimized) {
      console.log(`      📏 Taille: ${(qwen14bOptimized.size / 1024 / 1024 / 1024).toFixed(1)}GB`);
      console.log(`      📅 Modifié: ${new Date(qwen14bOptimized.modified).toLocaleString()}`);
    }
    
    console.log(`   🎯 qwen3-32b-optimized: ${qwen32bOptimized ? '✅ TROUVÉ' : '❌ MANQUANT'}`);
    if (qwen32bOptimized) {
      console.log(`      📏 Taille: ${(qwen32bOptimized.size / 1024 / 1024 / 1024).toFixed(1)}GB`);
      console.log(`      📅 Modifié: ${new Date(qwen32bOptimized.modified).toLocaleString()}`);
    }
    
    console.log(`   📋 qwen3:14b (base): ${qwen14bBase ? '✅ TROUVÉ' : '❌ MANQUANT'}`);
    console.log(`   📋 qwen3:32b (base): ${qwen32bBase ? '✅ TROUVÉ' : '❌ MANQUANT'}`);
    
    // 4. Rechercher tous les modèles Qwen
    console.log('\n🔍 4. Tous les modèles Qwen disponibles:');
    const qwenModels = models.filter(m => m.name.toLowerCase().includes('qwen'));
    if (qwenModels.length > 0) {
      qwenModels.forEach(model => {
        const size = model.size ? `(${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)` : '';
        console.log(`   📦 ${model.name} ${size}`);
      });
    } else {
      console.log('   ❌ Aucun modèle Qwen trouvé');
    }
    
    // 5. Test de fonctionnement avec un modèle existant
    console.log('\n🧪 5. Test avec un modèle existant...');
    
    // Prendre le premier modèle Qwen disponible
    const testModel = qwenModels[0] || models.find(m => m.name.includes('qwen'));
    
    if (testModel) {
      console.log(`   🤖 Test avec: ${testModel.name}`);
      
      try {
        const testResponse = await fetch('http://localhost:5001/proxy/ollama/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: testModel.name,
            messages: [
              {
                role: 'user',
                content: 'Réponds juste "Test réussi" en français.'
              }
            ],
            stream: false,
            options: {
              num_predict: 10
            }
          })
        });
        
        if (testResponse.ok) {
          const result = await testResponse.json();
          console.log(`   ✅ Modèle fonctionnel !`);
          console.log(`   📋 Réponse: "${result.message?.content || 'Pas de contenu'}"`);
        } else {
          console.log(`   ❌ Erreur test: ${testResponse.status}`);
        }
        
      } catch (error) {
        console.log(`   ❌ Erreur test: ${error.message}`);
      }
      
    } else {
      console.log('   ❌ Aucun modèle Qwen disponible pour test');
    }
    
    // 6. Recommandations
    console.log('\n💡 6. Recommandations:');
    
    if (!qwen14bOptimized && qwen14bBase) {
      console.log('   🔧 Créer qwen3-14b-optimized à partir de qwen3:14b');
      console.log('   📝 Commande: ollama create qwen3-14b-optimized -f Modelfile-qwen3-14b');
    }
    
    if (!qwen32bOptimized && qwen32bBase) {
      console.log('   🔧 Créer qwen3-32b-optimized à partir de qwen3:32b');
      console.log('   📝 Commande: ollama create qwen3-32b-optimized -f Modelfile-qwen3-32b');
    }
    
    if (!qwen14bOptimized && !qwen32bOptimized) {
      console.log('   ⚠️  Aucun modèle optimisé trouvé');
      console.log('   💡 Utiliser un modèle existant temporairement pour le Compressor');
      
      if (qwenModels.length > 0) {
        console.log(`   🎯 Modèle recommandé temporaire: ${qwenModels[0].name}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Erreur lors de la vérification: ${error.message}`);
  }
}

// Exécuter le test
testModelExists().catch(console.error);
