# GDPR Document Processor

## 🛡️ Overview

Le **GDPR Document Processor** est un système complet intégré à ClaraVerse qui permet de traiter des documents avec OCR et anonymisation conforme au RGPD, puis de les intégrer automatiquement dans le pipeline RAG pour l'assistant conversationnel.

## ✨ Fonctionnalités

### 📄 Traitement OCR Avancé
- **Support multi-formats** : PDF scannés, images (PNG, JPG, TIFF), DOCX, TXT
- **Préservation de la mise en page** : Maintient la structure originale du document
- **OCR multilingue** : Support français, anglais, allemand, espagnol, italien
- **Haute précision** : Utilise Tesseract avec optimisations DPI

### 🔒 Anonymisation RGPD Complète
- **Détection automatique** d'entités sensibles :
  - 👤 Noms de personnes
  - 📧 Adresses email
  - 📞 Numéros de téléphone
  - 🏢 Organisations
  - 📍 Adresses et lieux
  - 💳 Numéros de carte de crédit
  - 🆔 Numéros d'identification
  - 🌐 Adresses IP et URLs
  - 📅 Dates et heures

### 🎨 Interface Utilisateur Intuitive
- **Aperçu en temps réel** avec surlignage des anonymisations
- **Édition manuelle** pour corrections et ajustements
- **Contrôles granulaires** pour chaque type d'entité
- **Workflow étape par étape** avec indicateurs de progression

### 🔄 Intégration RAG Automatique
- **Ajout automatique** aux collections RAG
- **Métadonnées enrichies** pour un meilleur contexte
- **Support multi-collections** pour l'organisation

## 🏗️ Architecture Technique

### Backend (Python/FastAPI)
```
py_backend/
├── gdpr_anonymizer_premium.py     # Module d'anonymisation RGPD Premium (Assistant Impot)
├── ocr_processor_premium.py       # Traitement OCR Premium (Assistant Impot)
└── main.py                        # API endpoints intégrés
```

### Frontend (React/TypeScript)
```
src/
├── types/gdpr-types.ts                    # Types TypeScript
├── services/gdprApiService.ts             # Service API
├── components/GDPRDocumentProcessor.tsx   # Composant principal
└── components/GDPRDocumentProcessor/
    ├── DocumentUpload.tsx                 # Upload de fichiers
    ├── DocumentPreview.tsx                # Aperçu avec surlignage
    ├── AnonymizationControls.tsx          # Contrôles RGPD
    └── ProcessingWorkflow.tsx             # Workflow de traitement
```

## 🚀 Installation et Configuration

### Prérequis
- Python 3.8+
- Tesseract OCR installé
- Dépendances Python : `presidio-analyzer`, `presidio-anonymizer`, `pytesseract`, `Pillow`, `pdf2image`

### Installation Automatique
Les dépendances sont automatiquement installées via `requirements.txt` :

```bash
cd ClaraVerse/py_backend
pip install -r requirements.txt
```

### Configuration Tesseract
- **Windows** : Installer via [UB-Mannheim](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS** : `brew install tesseract`
- **Linux** : `sudo apt-get install tesseract-ocr`

## 📋 Guide d'Utilisation

### 1. Accès au Processeur GDPR
1. Ouvrir ClaraVerse
2. Cliquer sur **"GDPR Processor"** dans le menu latéral
3. L'interface de traitement s'ouvre

### 2. Upload de Document
1. **Glisser-déposer** un fichier ou cliquer pour parcourir
2. Formats supportés : PDF, PNG, JPG, DOCX, TXT
3. Taille maximum : 50MB
4. Le traitement OCR démarre automatiquement

### 3. Révision et Anonymisation
1. **Aperçu du texte** extrait avec l'OCR
2. Cliquer sur **"Anonymize Document"** pour détecter les entités sensibles
3. **Réviser les surlignages** - chaque couleur représente un type d'entité
4. **Éditer manuellement** si nécessaire en mode édition

### 4. Configuration Avancée
- **Langues OCR** : Choisir entre français, anglais, multilingue
- **Types d'entités** : Activer/désactiver des catégories spécifiques
- **Préservation de mise en page** : Maintenir la structure originale
- **Collection cible** : Sélectionner la collection RAG de destination

### 5. Finalisation
1. Cliquer sur **"Add to RAG Collection"**
2. Le document anonymisé est ajouté au système RAG
3. Disponible immédiatement pour l'assistant conversationnel

## 🎯 Cas d'Usage

### 📋 Documents Administratifs
- Contrats avec données personnelles
- Factures et devis
- Correspondances officielles

### 🏥 Documents Médicaux
- Rapports médicaux
- Prescriptions
- Dossiers patients (anonymisés)

### 💼 Documents d'Entreprise
- CV et candidatures
- Rapports internes
- Documents RH

### 📚 Documents de Recherche
- Études avec données personnelles
- Enquêtes et sondages
- Publications académiques

## 🔧 API Endpoints

### OCR Processing
```http
POST /ocr/process
Content-Type: application/json

{
  "file_base64": "base64_encoded_file",
  "file_type": "pdf",
  "preserve_layout": true,
  "language": "eng+fra",
  "dpi": 300
}
```

### GDPR Anonymization
```http
POST /gdpr/anonymize
Content-Type: application/json

{
  "text": "Document text to anonymize",
  "language": "en",
  "preserve_layout": true
}
```

### Combined Processing
```http
POST /documents/process-with-gdpr
Content-Type: application/json

{
  "file_base64": "base64_encoded_file",
  "file_type": "pdf",
  "filename": "document.pdf",
  "collection_name": "my_collection",
  "anonymize": true,
  "preserve_layout": true,
  "ocr_language": "eng+fra",
  "anonymization_language": "en"
}
```

## 🛡️ Sécurité et Confidentialité

### Traitement Local
- **100% local** : Aucune donnée n'est envoyée vers des services externes
- **Pas de cloud** : Tout le traitement se fait sur votre machine
- **Contrôle total** : Vous gardez la maîtrise de vos données

### Anonymisation Réversible
- **Mappings sécurisés** : Possibilité de restaurer les données originales
- **Anonymisation partielle** : Restaurer seulement certains types d'entités
- **Export des mappings** : Sauvegarde des correspondances pour audit

### Conformité RGPD
- **Pseudonymisation** conforme à l'article 4(5) du RGPD
- **Minimisation des données** : Seules les entités nécessaires sont traitées
- **Droit à l'effacement** : Possibilité de supprimer les mappings

## 🔍 Dépannage

### Problèmes OCR
- **Tesseract non trouvé** : Vérifier l'installation et le PATH
- **Qualité médiocre** : Augmenter le DPI ou améliorer la qualité du scan
- **Langue non reconnue** : Installer les packs de langue Tesseract

### Problèmes d'Anonymisation
- **Entités non détectées** : Ajuster la langue d'analyse
- **Faux positifs** : Désactiver certains types d'entités
- **Performance lente** : Réduire la taille du document

### Problèmes d'Interface
- **Composant non affiché** : Vérifier que les services backend sont démarrés
- **Erreurs de upload** : Vérifier la taille et le format du fichier
- **Surlignage incorrect** : Actualiser la page et réessayer

## 🚀 Développement et Contribution

### Structure du Code
- **Modulaire** : Chaque composant a une responsabilité claire
- **Typé** : TypeScript pour la sécurité de type
- **Testable** : Architecture permettant les tests unitaires

### Ajout de Nouvelles Fonctionnalités
1. **Backend** : Ajouter les endpoints dans `main.py`
2. **Types** : Définir les types dans `gdpr-types.ts`
3. **Service** : Étendre `gdprApiService.ts`
4. **UI** : Créer les composants React correspondants

### Tests
```bash
# Tests backend
cd py_backend
python -m pytest

# Tests frontend
npm run test
```

## 📊 Métriques et Monitoring

### Métriques Disponibles
- **Temps de traitement OCR** par type de document
- **Précision de l'anonymisation** par type d'entité
- **Taux de succès** du pipeline complet
- **Utilisation des collections** RAG

### Logs et Debugging
- **Logs détaillés** pour chaque étape du traitement
- **Codes d'erreur** spécifiques pour le diagnostic
- **Métriques de performance** pour l'optimisation

## 🔮 Roadmap

### Fonctionnalités Prévues
- [ ] **Support de nouveaux formats** : Excel, PowerPoint, RTF
- [ ] **OCR amélioré** : Support des tableaux complexes
- [ ] **Anonymisation avancée** : Détection contextuelle
- [ ] **Batch processing** : Traitement de lots de documents
- [ ] **API REST complète** : Intégration avec des systèmes externes
- [ ] **Audit trail** : Traçabilité complète des traitements

### Améliorations Techniques
- [ ] **Cache intelligent** : Mise en cache des résultats OCR
- [ ] **Parallélisation** : Traitement multi-thread
- [ ] **Optimisation mémoire** : Gestion des gros fichiers
- [ ] **Tests automatisés** : Suite de tests complète
