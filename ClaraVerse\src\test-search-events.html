<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Événements de Recherche</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .event-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .event-item {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: white;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.analyzing { background: #fff3cd; color: #856404; }
        .status.searching { background: #d4edda; color: #155724; }
        .status.extracting { background: #cce7ff; color: #004085; }
        .status.completed { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test des Événements de Recherche WeMa IA</h1>
        
        <div class="status" id="status">En attente...</div>
        
        <div>
            <button onclick="simulateSearch()">🚀 Simuler une Recherche</button>
            <button onclick="clearLog()">🗑️ Vider le Log</button>
        </div>
        
        <h3>📋 Log des Événements :</h3>
        <div class="event-log" id="eventLog"></div>
        
        <h3>📊 Sources Trouvées :</h3>
        <div id="sources"></div>
    </div>

    <script>
        let eventCount = 0;
        let currentSources = [];

        // Écouter les événements de recherche
        window.addEventListener('search-progress', (event) => {
            const { step, sources, completed } = event.detail;
            logEvent(`Événement reçu: ${step}`, { sources, completed });
            
            // Mettre à jour le statut
            updateStatus(step, completed);
            
            // Mettre à jour les sources
            if (sources && sources.length > 0) {
                currentSources = sources;
                updateSources(sources);
            }
        });

        function logEvent(message, data = {}) {
            eventCount++;
            const timestamp = new Date().toLocaleTimeString();
            const eventLog = document.getElementById('eventLog');
            
            const eventItem = document.createElement('div');
            eventItem.className = 'event-item';
            eventItem.innerHTML = `
                <strong>[${eventCount}] ${timestamp}</strong> - ${message}
                <br><small>${JSON.stringify(data, null, 2)}</small>
            `;
            
            eventLog.appendChild(eventItem);
            eventLog.scrollTop = eventLog.scrollHeight;
        }

        function updateStatus(step, completed) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${step}`;
            
            const statusTexts = {
                analyzing: '🧠 Décision IA - L\'IA détermine qu\'une recherche est nécessaire',
                searching: '🌐 Recherche Active - Collecte d\'informations en temps réel',
                extracting: '📄 Analyse du Contenu - Extraction et traitement des données',
                completed: '✨ Synthèse Finale - Formulation de la réponse complète'
            };
            
            statusEl.textContent = statusTexts[step] || step;
            
            if (completed) {
                statusEl.textContent += ' (TERMINÉ)';
            }
        }

        function updateSources(sources) {
            const sourcesEl = document.getElementById('sources');
            sourcesEl.innerHTML = sources.map((source, index) => 
                `<div style="margin: 5px 0; padding: 8px; background: #f8f9fa; border-radius: 5px;">
                    <strong>${index + 1}.</strong> ${source}
                </div>`
            ).join('');
        }

        function simulateSearch() {
            logEvent('🚀 Simulation de recherche démarrée');
            currentSources = [];
            
            // Simuler les étapes de recherche
            setTimeout(() => {
                const event1 = new CustomEvent('search-progress', {
                    detail: { step: 'analyzing', sources: [], completed: false }
                });
                window.dispatchEvent(event1);
            }, 500);

            setTimeout(() => {
                const event2 = new CustomEvent('search-progress', {
                    detail: { step: 'searching', sources: [], completed: false }
                });
                window.dispatchEvent(event2);
            }, 2000);

            setTimeout(() => {
                const mockSources = ['wikipedia.org', 'stackoverflow.com', 'github.com', 'mozilla.org'];
                const event3 = new CustomEvent('search-progress', {
                    detail: { step: 'extracting', sources: mockSources, completed: false }
                });
                window.dispatchEvent(event3);
            }, 5000);

            setTimeout(() => {
                const event4 = new CustomEvent('search-progress', {
                    detail: { step: 'completed', sources: currentSources, completed: true }
                });
                window.dispatchEvent(event4);
            }, 8000);
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
            document.getElementById('sources').innerHTML = '';
            document.getElementById('status').textContent = 'En attente...';
            document.getElementById('status').className = 'status';
            eventCount = 0;
            currentSources = [];
        }

        // Log initial
        logEvent('🎯 Système d\'événements de recherche initialisé');
    </script>
</body>
</html>
