/* 🎨 Animations de recherche internet - Style ChatGPT */

/* Animation de pulsation pour les éléments de recherche */
@keyframes search-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Animation de vague pour les barres de progression */
@keyframes progress-wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Animation de typing dots */
@keyframes typing-dots {
  0%, 20% {
    color: transparent;
    text-shadow: 0.25em 0 0 currentColor, 0.5em 0 0 transparent, 0.75em 0 0 transparent;
  }
  40% {
    color: transparent;
    text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 currentColor, 0.75em 0 0 transparent;
  }
  60% {
    color: transparent;
    text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent, 0.75em 0 0 currentColor;
  }
  80%, 100% {
    color: transparent;
    text-shadow: 0.25em 0 0 transparent, 0.5em 0 0 transparent, 0.75em 0 0 transparent;
  }
}

/* Animation de slide-in pour les résultats */
@keyframes slide-in-result {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Animation de glow pour les boutons actifs */
@keyframes button-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.3);
  }
}

/* Animation de rotation pour les icônes de chargement */
@keyframes icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation de bounce pour les notifications */
@keyframes notification-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Classes utilitaires pour les animations */
.search-pulse {
  animation: search-pulse 2s ease-in-out infinite;
}

.progress-wave {
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
  background-size: 200px 100%;
  animation: progress-wave 2s linear infinite;
}

.typing-dots::after {
  content: '...';
  animation: typing-dots 1.5s infinite;
}

.slide-in-result {
  animation: slide-in-result 0.4s ease-out;
}

.button-glow {
  animation: button-glow 2s ease-in-out infinite;
}

.icon-spin {
  animation: icon-spin 1s linear infinite;
}

.notification-bounce {
  animation: notification-bounce 1s ease-in-out;
}

/* Animations de transition fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-scale:hover {
  transform: scale(1.05);
}

/* Animation de gradient pour les backgrounds */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animate {
  background: linear-gradient(-45deg, #3b82f6, #6366f1, #8b5cf6, #3b82f6);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
}

/* Animation de fade-in pour les contenus */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

/* Animation de shimmer pour les placeholders */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 468px 100%;
  animation: shimmer 1.2s ease-in-out infinite;
}

.dark .shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 468px 100%;
}

/* Animation de typewriter pour les textes */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation: typewriter 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #3b82f6;
  }
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode adjustments */
.dark .search-pulse {
  filter: brightness(0.9);
}

.dark .button-glow {
  animation: button-glow 2s ease-in-out infinite;
  filter: brightness(0.8);
}
