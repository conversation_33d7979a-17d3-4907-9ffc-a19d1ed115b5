/**
 * 🚀 SYSTÈME DE LOGS OPTIMISÉ - WeMa IA
 * 
 * Système intelligent qui réduit la verbosité tout en gardant les informations importantes
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4
}

export enum LogCategory {
  SYSTEM = 'SYSTEM',
  RAG = 'RAG',
  PROVIDERS = 'PROVIDERS',
  SESSIONS = 'SESSIONS',
  DOCUMENTS = 'DOCUMENTS',
  PERFORMANCE = 'PERFORMANCE',
  UI = 'UI',
  STREAMING = 'STREAMING'
}

interface LogConfig {
  level: LogLevel;
  enabledCategories: Set<LogCategory>;
  enableTimestamps: boolean;
  enableGrouping: boolean;
  maxGroupSize: number;
}

class Logger {
  private config: LogConfig;
  private groupedLogs: Map<string, { count: number; lastSeen: number; data?: any }> = new Map();
  private readonly GROUP_TIMEOUT = 5000; // 5 seconds

  constructor() {
    // Configuration par défaut - plus silencieuse en production
    this.config = {
      level: import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.WARN,
      enabledCategories: new Set([
        LogCategory.SYSTEM,
        LogCategory.RAG,
        LogCategory.PROVIDERS
      ]),
      enableTimestamps: import.meta.env.DEV,
      enableGrouping: true,
      maxGroupSize: 5
    };

    // Nettoyer les logs groupés périodiquement
    setInterval(() => this.cleanupGroupedLogs(), this.GROUP_TIMEOUT);
  }

  /**
   * Configuration du logger
   */
  configure(config: Partial<LogConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * Activer/désactiver une catégorie
   */
  toggleCategory(category: LogCategory, enabled: boolean) {
    if (enabled) {
      this.config.enabledCategories.add(category);
    } else {
      this.config.enabledCategories.delete(category);
    }
  }

  /**
   * Changer le niveau de log
   */
  setLevel(level: LogLevel) {
    this.config.level = level;
  }

  /**
   * Log d'erreur (toujours affiché)
   */
  error(category: LogCategory, message: string, data?: any) {
    this.log(LogLevel.ERROR, category, message, data);
  }

  /**
   * Log d'avertissement
   */
  warn(category: LogCategory, message: string, data?: any) {
    this.log(LogLevel.WARN, category, message, data);
  }

  /**
   * Log d'information
   */
  info(category: LogCategory, message: string, data?: any) {
    this.log(LogLevel.INFO, category, message, data);
  }

  /**
   * Log de debug
   */
  debug(category: LogCategory, message: string, data?: any) {
    this.log(LogLevel.DEBUG, category, message, data);
  }

  /**
   * Log verbose (très détaillé)
   */
  verbose(category: LogCategory, message: string, data?: any) {
    this.log(LogLevel.VERBOSE, category, message, data);
  }

  /**
   * Log groupé pour éviter le spam
   */
  group(category: LogCategory, groupKey: string, message: string, data?: any) {
    if (!this.shouldLog(LogLevel.INFO, category)) return;

    const now = Date.now();
    const existing = this.groupedLogs.get(groupKey);

    if (existing && (now - existing.lastSeen) < this.GROUP_TIMEOUT) {
      existing.count++;
      existing.lastSeen = now;
      existing.data = data; // Garder les dernières données
      
      // Afficher seulement si on atteint le seuil
      if (existing.count === this.config.maxGroupSize) {
        this.log(LogLevel.INFO, category, `${message} (${existing.count} times)`, existing.data);
      }
    } else {
      // Premier log ou timeout dépassé
      this.groupedLogs.set(groupKey, { count: 1, lastSeen: now, data });
      this.log(LogLevel.INFO, category, message, data);
    }
  }

  /**
   * Log de performance avec timing
   */
  performance(category: LogCategory, operation: string, startTime: number, data?: any) {
    const duration = performance.now() - startTime;
    const message = `${operation} completed in ${duration.toFixed(2)}ms`;
    
    // Seulement si c'est lent ou en mode debug
    if (duration > 100 || this.config.level >= LogLevel.DEBUG) {
      this.log(LogLevel.INFO, category, message, data);
    }
  }

  /**
   * Méthode principale de log
   */
  private log(level: LogLevel, category: LogCategory, message: string, data?: any) {
    if (!this.shouldLog(level, category)) return;

    const prefix = this.buildPrefix(level, category);
    const consoleMethod = this.getConsoleMethod(level);

    if (data !== undefined) {
      consoleMethod(`${prefix} ${message}`, data);
    } else {
      consoleMethod(`${prefix} ${message}`);
    }
  }

  /**
   * Vérifier si on doit logger
   */
  private shouldLog(level: LogLevel, category: LogCategory): boolean {
    return level <= this.config.level && this.config.enabledCategories.has(category);
  }

  /**
   * Construire le préfixe du log
   */
  private buildPrefix(level: LogLevel, category: LogCategory): string {
    const levelIcon = this.getLevelIcon(level);
    const timestamp = this.config.enableTimestamps ? 
      `[${new Date().toLocaleTimeString()}] ` : '';
    
    return `${timestamp}${levelIcon} ${category}`;
  }

  /**
   * Icône pour le niveau de log
   */
  private getLevelIcon(level: LogLevel): string {
    switch (level) {
      case LogLevel.ERROR: return '❌';
      case LogLevel.WARN: return '⚠️';
      case LogLevel.INFO: return 'ℹ️';
      case LogLevel.DEBUG: return '🔧';
      case LogLevel.VERBOSE: return '📝';
      default: return '📋';
    }
  }

  /**
   * Méthode console appropriée
   */
  private getConsoleMethod(level: LogLevel) {
    switch (level) {
      case LogLevel.ERROR: return console.error;
      case LogLevel.WARN: return console.warn;
      default: return console.log;
    }
  }

  /**
   * Nettoyer les logs groupés expirés
   */
  private cleanupGroupedLogs() {
    const now = Date.now();
    for (const [key, log] of this.groupedLogs.entries()) {
      if (now - log.lastSeen > this.GROUP_TIMEOUT) {
        // Afficher le résumé final si nécessaire
        if (log.count > 1 && log.count < this.config.maxGroupSize) {
          console.log(`📊 ${key} occurred ${log.count} times in the last ${this.GROUP_TIMEOUT/1000}s`);
        }
        this.groupedLogs.delete(key);
      }
    }
  }

  /**
   * Obtenir les statistiques de logs
   */
  getStats() {
    return {
      level: LogLevel[this.config.level],
      enabledCategories: Array.from(this.config.enabledCategories),
      groupedLogsCount: this.groupedLogs.size,
      config: this.config
    };
  }

  /**
   * Mode silencieux (urgences seulement)
   */
  setSilentMode(enabled: boolean) {
    if (enabled) {
      this.config.level = LogLevel.ERROR;
      this.config.enabledCategories.clear();
      this.config.enabledCategories.add(LogCategory.SYSTEM);
    } else {
      this.config.level = import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO;
      this.config.enabledCategories = new Set([
        LogCategory.SYSTEM,
        LogCategory.RAG,
        LogCategory.PROVIDERS
      ]);
    }
  }
}

// Instance singleton
export const logger = new Logger();

// Fonctions de convenance
export const logError = (category: LogCategory, message: string, data?: any) => 
  logger.error(category, message, data);

export const logWarn = (category: LogCategory, message: string, data?: any) => 
  logger.warn(category, message, data);

export const logInfo = (category: LogCategory, message: string, data?: any) => 
  logger.info(category, message, data);

export const logDebug = (category: LogCategory, message: string, data?: any) => 
  logger.debug(category, message, data);

export const logGroup = (category: LogCategory, groupKey: string, message: string, data?: any) => 
  logger.group(category, groupKey, message, data);

export const logPerformance = (category: LogCategory, operation: string, startTime: number, data?: any) => 
  logger.performance(category, operation, startTime, data);

// Exposer le logger pour le debug en développement
if (import.meta.env.DEV) {
  (window as any).logger = logger;
  (window as any).LogLevel = LogLevel;
  (window as any).LogCategory = LogCategory;
}
