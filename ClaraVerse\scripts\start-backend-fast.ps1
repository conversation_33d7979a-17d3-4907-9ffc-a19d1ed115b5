# 🚀 Script de Démarrage Rapide Backend WeMa IA
# 
# D<PERSON><PERSON><PERSON> le backend en mode rapide sans RAG pour le développement

Write-Host "🚀 Démarrage Backend WeMa IA - Mode Rapide" -ForegroundColor Cyan
Write-Host "⚡ RAG désactivé pour un démarrage ultra-rapide" -ForegroundColor Yellow
Write-Host ""

# Définir les variables d'environnement pour le mode rapide
$env:FAST_MODE = "true"
$env:INFERENCE_SERVER_IP = "*********"
$env:INFERENCE_SERVER_PORT = "11434"

# Aller dans le répertoire backend
Set-Location "py_backend"

Write-Host "📂 Répertoire: $(Get-Location)" -ForegroundColor Gray
Write-Host "🌐 Serveur d'inférence: $env:INFERENCE_SERVER_IP`:$env:INFERENCE_SERVER_PORT" -ForegroundColor Gray
Write-Host ""

# Démarrer le backend
Write-Host "🚀 Lancement du backend..." -ForegroundColor Green
python -m uvicorn main:app --host 0.0.0.0 --port 5001 --reload
