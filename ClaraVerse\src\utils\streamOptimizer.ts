/**
 * 🚀 OPTIMISEUR DE STREAMING
 * Optimise les performances du streaming en réduisant les mises à jour UI
 */

export class StreamOptimizer {
  private contentBuffer: string = '';
  private totalContent: string = ''; // 🚀 AJOUTÉ: Accumulateur total
  private lastFlushTime: number = 0;
  private flushTimer: NodeJS.Timeout | null = null;
  private onFlush: (content: string) => void;
  
  // Configuration optimisée pour plus de fluidité
  private readonly FLUSH_INTERVAL = 50; // Flush max toutes les 50ms (plus fluide)
  private readonly MIN_CHUNK_SIZE = 10; // Taille minimum avant flush (plus réactif)
  private readonly MAX_BUFFER_SIZE = 200; // Taille max du buffer avant flush forcé (plus fluide)
  
  constructor(onFlush: (content: string) => void) {
    this.onFlush = onFlush;
  }
  
  /**
   * Ajoute du contenu au buffer avec flush intelligent
   * 🚀 CORRIGÉ: Accumule le contenu au lieu de le remplacer
   */
  addContent(chunk: string): void {
    if (!chunk) return;

    // 🚀 CORRECTION: Accumuler dans le buffer temporaire
    this.contentBuffer += chunk;

    const now = Date.now();
    const timeSinceLastFlush = now - this.lastFlushTime;
    const shouldFlushByTime = timeSinceLastFlush >= this.FLUSH_INTERVAL;
    const shouldFlushBySize = this.contentBuffer.length >= this.MAX_BUFFER_SIZE;
    const hasMinimumContent = this.contentBuffer.length >= this.MIN_CHUNK_SIZE;

    // Flush immédiat si buffer trop gros
    if (shouldFlushBySize) {
      this.flushNow();
      return;
    }

    // Flush si assez de temps s'est écoulé ET qu'on a du contenu minimum
    if (shouldFlushByTime && hasMinimumContent) {
      this.flushNow();
      return;
    }

    // Sinon, programmer un flush différé
    this.scheduleFlush();
  }
  
  /**
   * Programme un flush différé
   */
  private scheduleFlush(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
    }
    
    this.flushTimer = setTimeout(() => {
      if (this.contentBuffer) {
        this.flushNow();
      }
    }, this.FLUSH_INTERVAL);
  }
  
  /**
   * Flush immédiat du buffer
   * 🚀 CORRIGÉ: Envoie le contenu total accumulé
   */
  private flushNow(): void {
    if (this.contentBuffer) {
      // 🚀 CORRECTION: Accumuler dans le total
      this.totalContent += this.contentBuffer;

      // Envoyer le contenu TOTAL (pas juste le buffer)
      this.onFlush(this.totalContent);

      // Réinitialiser SEULEMENT le buffer temporaire
      this.contentBuffer = '';
      this.lastFlushTime = Date.now();
    }

    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }
  }
  
  /**
   * Flush final (pour la fin du stream)
   */
  flushFinal(): void {
    this.flushNow();
  }
  
  /**
   * Nettoie les timers
   * 🚀 CORRIGÉ: Nettoie aussi le contenu total
   */
  cleanup(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }
    this.contentBuffer = '';
    this.totalContent = ''; // 🚀 AJOUTÉ: Reset du contenu total
  }
}

/**
 * 🚀 HOOK REACT OPTIMISÉ POUR LE STREAMING
 */
export function useOptimizedStreaming(
  onContentUpdate: (content: string) => void
): (chunk: string) => void {
  const optimizerRef = useRef<StreamOptimizer | null>(null);
  
  // Créer l'optimiseur une seule fois
  if (!optimizerRef.current) {
    optimizerRef.current = new StreamOptimizer(onContentUpdate);
  }
  
  // Nettoyer à la destruction
  useEffect(() => {
    return () => {
      optimizerRef.current?.cleanup();
    };
  }, []);
  
  // Retourner la fonction optimisée
  return useCallback((chunk: string) => {
    optimizerRef.current?.addContent(chunk);
  }, []);
}

/**
 * 🚀 WRAPPER POUR onContentChunk OPTIMISÉ
 */
export function createOptimizedContentHandler(
  originalHandler: (content: string) => void
): (content: string) => void {
  const optimizer = new StreamOptimizer(originalHandler);
  let lastChunkTime = Date.now();
  let finalFlushTimer: NodeJS.Timeout | null = null;

  return (chunk: string) => {
    lastChunkTime = Date.now();
    optimizer.addContent(chunk);

    // 🚀 CORRECTION: Auto-flush final après 200ms sans nouveau chunk
    if (finalFlushTimer) {
      clearTimeout(finalFlushTimer);
    }

    finalFlushTimer = setTimeout(() => {
      // Si aucun nouveau chunk depuis 200ms, faire un flush final
      if (Date.now() - lastChunkTime >= 200) {
        optimizer.flushFinal();
      }
    }, 200);
  };
}

// Imports React nécessaires
import { useRef, useEffect, useCallback } from 'react';
