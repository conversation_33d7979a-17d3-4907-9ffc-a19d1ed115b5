# 🧪 Guide de Test WeMa IA

## 🎯 Configuration actuelle

### **IP Serveur configurée :**
- **Production** : `*************:1235` (ton serveur final)
- **Test local** : `localhost:1235` (pour tes tests)

### **Pôles disponibles :**
- ✅ **Gestion Patrimoine**
- ✅ **M&A** (corrigé)
- ✅ **Ressources Humaines**
- ✅ **Direction**
- ✅ **Comptabilité**
- ✅ **Autre**

## 🚀 Test en 3 étapes

### **Étape 1 : Configuration**
```bash
# Lancer la configuration automatique
test-setup.bat
```

### **Étape 2 : Démarrage des services**

#### **Terminal 1 - Serveur d'inférence :**
```bash
cd server-inference
python start_inference_server.py
```
**Résultat attendu :**
```
🧠 Démarrage serveur d'inférence WeMa IA
📡 Port: 1235
🔗 LM Studio: http://localhost:1234
✅ LM Studio détecté avec X modèles
🚀 Serveur démarré sur http://0.0.0.0:1235
```

#### **Terminal 2 - Backend :**
```bash
cd py_backend
python main.py
```
**Résultat attendu :**
```
🚀 Démarrage backend WeMa IA
📡 Port: 8000
🧠 Délégation d'inférence: localhost:1235
✅ Serveur démarré
```

#### **Terminal 3 - Frontend :**
```bash
npm run dev
```
**Résultat attendu :**
```
🚀 Démarrage frontend WeMa IA
📡 Port: 3000
✅ Application disponible sur http://localhost:3000
```

### **Étape 3 : Tests**

#### **Test 1 - Mode utilisateur :**
1. **Ouvrir** : `http://localhost:3000`
2. **Écran d'enregistrement** apparaît
3. **Saisir** :
   - Prénom : `Test`
   - Nom : `User`
   - Pôle : `M&A`
4. **Cliquer** "Commencer"
5. **Vérifier** : Accès à l'application WeMa IA

#### **Test 2 - Mode admin :**
1. **Ouvrir** : `http://localhost:3000?admin=true`
2. **Interface admin** apparaît directement
3. **Vérifier onglets** :
   - 👥 **Utilisateurs** : Liste des enregistrés
   - 🧠 **Inférence** : Statut du serveur
   - 📦 **Mises à jour** : Système de déploiement

#### **Test 3 - Délégation d'inférence :**
1. **Mode utilisateur** : Envoyer un message
2. **Vérifier logs backend** : `🌐 Utilisation serveur central`
3. **Si serveur indisponible** : `🏠 Fallback sur LM Studio local`

## 🔧 Dépannage

### **Serveur d'inférence ne démarre pas :**
```bash
# Vérifier LM Studio
curl http://localhost:1234/v1/models

# Si pas de LM Studio, fallback automatique
```

### **Backend ne se connecte pas au serveur :**
```bash
# Vérifier la configuration
cat .env | grep INFERENCE

# Tester la connexion
curl http://localhost:1235/health
```

### **Enregistrement utilisateur échoue :**
```bash
# Vérifier les logs backend
tail -f py_backend/logs/app.log

# Vérifier le dossier data
ls -la data/
```

## 📊 Flux de connexion

### **Mode utilisateur :**
```
1. App démarre
2. Vérifie localStorage pour wema_user_info
3. Si absent → Écran d'enregistrement
4. Utilisateur saisit infos
5. Sauvegarde local + envoi backend
6. Accès à l'application
```

### **Mode admin :**
```
1. App démarre avec ?admin=true
2. Détection mode admin
3. Interface admin directe
4. Monitoring en temps réel
```

### **Délégation d'inférence :**
```
1. Utilisateur envoie message
2. Backend traite RAG/OCR localement
3. Contexte + question → Serveur d'inférence
4. Serveur → LM Studio → Réponse
5. Réponse → Backend → Frontend
```

## 🎯 Résultats attendus

### **✅ Succès si :**
- Enregistrement utilisateur fonctionne
- Interface admin accessible
- Délégation d'inférence opérationnelle
- Fallback local en cas de problème
- Monitoring utilisateurs visible

### **❌ Problème si :**
- Écran d'enregistrement en boucle
- Interface admin inaccessible
- Erreurs de connexion serveur
- Pas de fallback local
- Monitoring vide

## 🔄 Passage en production

### **Quand tout fonctionne en test :**
1. **Copier** `.env.inference` vers `.env`
2. **Modifier** `INFERENCE_SERVER_IP=*************`
3. **Déployer** serveur d'inférence sur *************
4. **Distribuer** l'application aux utilisateurs

### **Configuration finale :**
```
Serveur (*************):
├── LM Studio (port 1234)
├── Serveur d'inférence (port 1235)
└── Interface admin (port 9000)

Clients (192.168.1.x):
├── Frontend + Backend local
├── RAG + OCR local
└── Inférence → Serveur central
```

## 📝 Notes importantes

- **Confidentialité** : Documents restent locaux
- **Performance** : Inférence centralisée
- **Fallback** : Local si serveur indisponible
- **Monitoring** : Admin voit tout en temps réel
- **Simplicité** : Pas de token, connexion auto
