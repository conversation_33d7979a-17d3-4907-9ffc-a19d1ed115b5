/**
 * Mode Performant RAG - Analyse complète et optimisée
 * 
 * Caractéristiques :
 * - Analyse hybride intelligente
 * - Gestion de contexte avancée
 * - Optimisations pour gros documents
 * - Recherche vectorielle si nécessaire
 */

import { BaseMode } from './BaseMode';
import {
  Rag<PERSON><PERSON>ument,
  RagR<PERSON>ult,
  RagProcessingR<PERSON>ult,
  OnContentChunk,
  DocumentAnalysis
} from '../types/RagTypes';
import { RAG_CONFIG } from '../../../config/constants';

export class PerformantMode extends BaseMode {
  constructor() {
    // Utiliser la configuration unifiée
    super(RAG_CONFIG.PERFORMANT_MODE.THRESHOLD, RAG_CONFIG.PERFORMANT_MODE.MAX_CONTEXT);
  }

  async processDocuments(
    documents: RagDocument[],
    userQuery: string,
    onContentChunk?: OnContentChunk
  ): Promise<RagProcessingResult> {
    const startTime = Date.now();
    
    console.log('🧠 MODE PERFORMANT: Analyse complète activée');
    
    if (onContentChunk) {
      onContentChunk(`📄 **Found ${documents.length} selected documents - analyzing...**\n\n`);
    }

    // 🔍 ANALYSE HYBRIDE INTELLIGENTE
    const documentAnalysis = this.analyzeDocuments(documents);
    
    this.logDocumentAnalysis(documentAnalysis);

    // 🚀 TRAITEMENT HYBRIDE
    const ragResults: RagResult[] = [];
    
    for (let index = 0; index < documentAnalysis.length; index++) {
      const { doc, contentLength, useRAG } = documentAnalysis[index];
      
      if (useRAG) {
        // 📊 Document volumineux → Traitement RAG (si implémenté)
        console.log(`🚀 RAG: Document ${doc.filename} (${contentLength} chars) → RAG processing`);
        // Pour l'instant, utilisation directe pour éviter les appels réseau
        ragResults.push(this.createRagResult(doc, 1.0 - (index * 0.1)));
      } else {
        // 📄 Document petit → Utilisation directe (optimal)
        console.log(`📄 Direct: Document ${doc.filename} (${contentLength} chars) → Direct content`);
        ragResults.push(this.createRagResult(doc, 1.0 - (index * 0.1)));
      }
    }

    // 🏗️ CONSTRUCTION DU CONTEXTE
    const { content: ragContext, totalLength } = this.buildRagContext(ragResults);

    const processingTime = Date.now() - startTime;

    // 📊 STATISTIQUES DÉTAILLÉES
    this.logProcessingStats('PERFORMANT 🧠', documents, ragResults, processingTime, documentAnalysis);

    if (onContentChunk) {
      onContentChunk(`✅ **Found ${ragResults.length} relevant documents**\n\n`);
    }

    return {
      ragContext,
      ragResults,
      processingStats: {
        mode: 'PERFORMANT 🧠',
        documentsProcessed: documents.length,
        totalContextLength: totalLength,
        processingTime,
        directDocuments: documentAnalysis.filter(a => !a.useRAG).length,
        ragProcessedDocuments: documentAnalysis.filter(a => a.useRAG).length
      }
    };
  }

  private logDocumentAnalysis(analysis: DocumentAnalysis[]): void {
    const totalDirectContent = analysis
      .filter(a => !a.useRAG)
      .reduce((sum, a) => sum + a.contentLength, 0);

    console.log(`🔍 RAG Hybride Analysis:`, {
      totalDocuments: analysis.length,
      directDocuments: analysis.filter(a => !a.useRAG).length,
      ragDocuments: analysis.filter(a => a.useRAG).length,
      totalDirectContent,
      threshold: this.threshold
    });

    // Log détaillé pour chaque document
    analysis.forEach(({ doc, contentLength, processingMethod }) => {
      console.log(`📋 ${doc.filename}: ${contentLength} chars → ${processingMethod}`);
    });
  }
}
