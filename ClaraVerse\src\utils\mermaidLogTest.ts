/**
 * 🧪 TEST DE SUPPRESSION DES LOGS MERMAID
 * 
 * Script pour vérifier que les logs Mermaid verbeux ont été supprimés
 */

export class MermaidLogTest {
  private originalConsole: any;
  private mermaidLogs: string[] = [];

  constructor() {
    this.originalConsole = { ...console };
  }

  /**
   * Démarrer l'interception des logs Mermaid
   */
  public startInterception(): void {
    this.mermaidLogs = [];

    // Intercepter console.log pour détecter les logs Mermaid
    console.log = (...args: any[]) => {
      const message = args.join(' ');
      
      // Détecter les logs Mermaid verbeux
      if (message.includes('🧹 Code Mermaid nettoyé') || 
          message.includes('Code Mermaid nettoyé') ||
          message.includes('graph TD') ||
          message.includes('classDef default fill:#DBEAFE')) {
        this.mermaidLogs.push(message);
      }
      
      // Appeler le console.log original
      this.originalConsole.log(...args);
    };

    console.log('🧪 Mermaid log interception started');
  }

  /**
   * Arrêter l'interception et obtenir les résultats
   */
  public stopInterception(): MermaidLogTestResults {
    // Restaurer console original
    console.log = this.originalConsole.log;

    const results: MermaidLogTestResults = {
      mermaidLogsDetected: this.mermaidLogs.length,
      mermaidLogMessages: this.mermaidLogs,
      isOptimized: this.mermaidLogs.length === 0,
      recommendations: []
    };

    // Générer des recommandations
    if (results.mermaidLogsDetected > 0) {
      results.recommendations.push('🚨 Logs Mermaid verbeux détectés - optimisation nécessaire');
      results.recommendations.push(`📊 ${results.mermaidLogsDetected} logs Mermaid trouvés`);
    } else {
      results.recommendations.push('✅ Aucun log Mermaid verbeux détecté - optimisation réussie');
    }

    console.log('🧪 Mermaid log interception stopped', results);
    return results;
  }

  /**
   * Test automatique des logs Mermaid
   */
  public async runMermaidLogTest(): Promise<MermaidLogTestResults> {
    console.log('🧪 Démarrage du test de logs Mermaid...');
    
    this.startInterception();

    // Simuler quelques activités qui pourraient générer des logs Mermaid
    await this.simulateMermaidActivity();

    const results = this.stopInterception();

    // Afficher le rapport
    this.displayReport(results);

    return results;
  }

  /**
   * Simuler des activités Mermaid
   */
  private async simulateMermaidActivity(): Promise<void> {
    // Simuler des logs qui ne devraient PAS apparaître
    console.log('Test normal log - should not be detected');
    console.log('Another normal log');
    
    // Ces logs ne devraient plus exister après optimisation
    // console.log('🧹 Code Mermaid nettoyé: graph TD'); // SUPPRIMÉ
    // console.log('Code Mermaid nettoyé: ...'); // SUPPRIMÉ
    
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Afficher le rapport de test
   */
  private displayReport(results: MermaidLogTestResults): void {
    console.log('\n🧪 RAPPORT DE TEST LOGS MERMAID');
    console.log('=====================================');
    console.log(`📊 Logs Mermaid détectés: ${results.mermaidLogsDetected}`);
    console.log(`✅ Optimisation réussie: ${results.isOptimized ? 'OUI' : 'NON'}`);
    
    if (results.mermaidLogMessages.length > 0) {
      console.log('\n📋 Messages détectés:');
      results.mermaidLogMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. ${msg.substring(0, 100)}...`);
      });
    }
    
    if (results.recommendations.length > 0) {
      console.log('\n💡 Recommandations:');
      results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
    console.log('=====================================\n');
  }
}

export interface MermaidLogTestResults {
  mermaidLogsDetected: number;
  mermaidLogMessages: string[];
  isOptimized: boolean;
  recommendations: string[];
}

// Instance singleton
export const mermaidLogTest = new MermaidLogTest();

// Exposer en développement
if (import.meta.env.DEV) {
  (window as any).mermaidLogTest = mermaidLogTest;
  (window as any).testMermaidLogs = () => mermaidLogTest.runMermaidLogTest();
}
