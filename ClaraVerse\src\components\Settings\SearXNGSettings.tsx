/**
 * 🔍 SearXNG Settings Component
 * Configuration de la recherche internet pour WeMa IA
 */

import React, { useState, useEffect } from 'react';
import { Search, Server, Check, X, AlertCircle, ExternalLink, Settings } from 'lucide-react';
import internetSearchService from '../../services/internetSearchService';

interface SearXNGSettingsProps {
  onConfigChange?: (config: SearXNGConfig) => void;
}

interface SearXNGConfig {
  enabled: boolean;
  url: string;
  defaultCategories: string[];
  defaultLanguage: string;
  safeSearch: number;
  maxResults: number;
  timeout: number;
}

const SearXNGSettings: React.FC<SearXNGSettingsProps> = ({ onConfigChange }) => {
  const [config, setConfig] = useState<SearXNGConfig>({
    enabled: false,
    url: 'http://localhost:8888',
    defaultCategories: ['general'],
    defaultLanguage: 'fr',
    safeSearch: 1,
    maxResults: 10,
    timeout: 10000
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');
  const [testResults, setTestResults] = useState<any[]>([]);

  // Catégories disponibles
  const availableCategories = [
    { id: 'general', name: 'Général', description: 'Recherche web générale' },
    { id: 'news', name: 'Actualités', description: 'Articles de presse récents' },
    { id: 'science', name: 'Science', description: 'Articles scientifiques' },
    { id: 'it', name: 'Informatique', description: 'Ressources techniques' },
    { id: 'images', name: 'Images', description: 'Recherche d\'images' },
    { id: 'videos', name: 'Vidéos', description: 'Contenu vidéo' }
  ];

  // Langues disponibles
  const availableLanguages = [
    { code: 'fr', name: 'Français' },
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' }
  ];

  // Charger la configuration depuis localStorage
  useEffect(() => {
    const savedConfig = localStorage.getItem('wema-searxng-config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(parsed);
        if (parsed.enabled && parsed.url) {
          internetSearchService.configure(parsed.url);
          testConnection(parsed.url, false);
        }
      } catch (error) {
        console.error('Erreur chargement config SearXNG:', error);
      }
    }
  }, []);

  // Sauvegarder la configuration
  const saveConfig = (newConfig: SearXNGConfig) => {
    setConfig(newConfig);
    localStorage.setItem('wema-searxng-config', JSON.stringify(newConfig));
    onConfigChange?.(newConfig);
    
    if (newConfig.enabled && newConfig.url) {
      internetSearchService.configure(newConfig.url);
    }
  };

  // Tester la connexion SearXNG
  const testConnection = async (url?: string, showResults = true) => {
    const testUrl = url || config.url;
    setIsTestingConnection(true);
    setConnectionStatus('unknown');
    
    try {
      // Configurer temporairement l'URL pour le test
      internetSearchService.configure(testUrl);
      
      // Effectuer une recherche de test
      const response = await internetSearchService.search('test search', {
        maxResults: 3,
        timeout: 5000
      });

      if (response.error) {
        setConnectionStatus('error');
        setTestResults([]);
      } else {
        setConnectionStatus('connected');
        if (showResults) {
          setTestResults(response.results.slice(0, 3));
        }
      }
    } catch (error) {
      setConnectionStatus('error');
      setTestResults([]);
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Gérer le changement d'URL
  const handleUrlChange = (newUrl: string) => {
    saveConfig({ ...config, url: newUrl });
  };

  // Gérer le changement de catégories
  const handleCategoryToggle = (categoryId: string) => {
    const newCategories = config.defaultCategories.includes(categoryId)
      ? config.defaultCategories.filter(c => c !== categoryId)
      : [...config.defaultCategories, categoryId];
    
    saveConfig({ ...config, defaultCategories: newCategories });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
          <Search className="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recherche Internet - SearXNG
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Configuration du moteur de recherche métasearch privé
          </p>
        </div>
      </div>

      {/* Activation */}
      <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">
            Activer la recherche internet
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Permet à WeMa IA de rechercher des informations en ligne
          </p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={config.enabled}
            onChange={(e) => saveConfig({ ...config, enabled: e.target.checked })}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
        </label>
      </div>

      {config.enabled && (
        <>
          {/* Configuration URL */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              URL de l'instance SearXNG
            </label>
            <div className="flex gap-2">
              <input
                type="url"
                value={config.url}
                onChange={(e) => handleUrlChange(e.target.value)}
                placeholder="http://localhost:8888"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => testConnection()}
                disabled={isTestingConnection}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                {isTestingConnection ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Server className="w-4 h-4" />
                )}
                Tester
              </button>
            </div>

            {/* Statut de connexion */}
            {connectionStatus !== 'unknown' && (
              <div className={`flex items-center gap-2 text-sm ${
                connectionStatus === 'connected' 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {connectionStatus === 'connected' ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <X className="w-4 h-4" />
                )}
                {connectionStatus === 'connected' 
                  ? 'Connexion réussie' 
                  : 'Impossible de se connecter à SearXNG'
                }
              </div>
            )}

            {/* Résultats de test */}
            {testResults.length > 0 && (
              <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <h5 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                  Résultats de test:
                </h5>
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-xs text-green-700 dark:text-green-300">
                      • {result.title}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Catégories par défaut */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Catégories de recherche par défaut
            </label>
            <div className="grid grid-cols-2 gap-2">
              {availableCategories.map((category) => (
                <label
                  key={category.id}
                  className="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50"
                >
                  <input
                    type="checkbox"
                    checked={config.defaultCategories.includes(category.id)}
                    onChange={() => handleCategoryToggle(category.id)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {category.name}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {category.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Autres paramètres */}
          <div className="grid grid-cols-2 gap-4">
            {/* Langue */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Langue par défaut
              </label>
              <select
                value={config.defaultLanguage}
                onChange={(e) => saveConfig({ ...config, defaultLanguage: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                {availableLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Safe Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Filtrage de contenu
              </label>
              <select
                value={config.safeSearch}
                onChange={(e) => saveConfig({ ...config, safeSearch: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value={0}>Désactivé</option>
                <option value={1}>Modéré</option>
                <option value={2}>Strict</option>
              </select>
            </div>
          </div>

          {/* Info SearXNG */}
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-1">Installation SearXNG requise</p>
                <p>
                  Pour utiliser la recherche internet, vous devez installer SearXNG localement.
                  Consultez la documentation officielle pour l'installation via Docker.
                </p>
                <a
                  href="https://docs.searxng.org/admin/installation.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 mt-2 text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Guide d'installation
                  <ExternalLink className="w-3 h-3" />
                </a>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SearXNGSettings;
