# 🚀 Système RAG Modulaire - WeMa IA

## 📁 Architecture

```
src/services/rag/
├── index.ts                    # Point d'entrée principal
├── RagManager.ts              # Gestionnaire principal
├── modes/
│   ├── BaseMode.ts            # Classe de base commune
│   ├── FastMode.ts            # Mode rapide (2-3 secondes)
│   └── PerformantMode.ts      # Mode performant (analyse complète)
├── types/
│   └── RagTypes.ts            # Types TypeScript
├── test/
│   └── RagTest.ts             # Tests unitaires
└── README.md                  # Cette documentation
```

## 🎯 Modes de Fonctionnement

### ⚡ Mode Rapide (FastMode)
- **Objectif** : Réponse en 2-3 secondes maximum
- **Méthode** : Ajout direct des documents au contexte
- **Utilisation** : Conversations interactives, prototypage
- **Seuil** : Documents jusqu'à 50K caractères en mode direct

### 🧠 Mode Performant (PerformantMode)
- **Objectif** : Analyse complète et optimisée
- **Méthode** : Analyse hybride intelligente
- **Utilisation** : Analyse approfondie, documents complexes
- **Seuil** : Documents > 20K caractères analysés via RAG

## 🔧 Utilisation

### Import et Initialisation
```typescript
import { RagManager, createRagManager } from './services/rag';

// Méthode 1 : Gestionnaire automatique
const ragManager = createRagManager();

// Méthode 2 : Instance manuelle
const ragManager = new RagManager();
```

### Traitement de Documents
```typescript
import { RagDocument } from './services/rag';

const documents: RagDocument[] = [
  {
    id: 'doc1',
    filename: 'document.pdf',
    content: 'Contenu du document...',
    fileType: 'pdf'
  }
];

const result = await ragManager.processDocuments(
  documents,
  'Quelle est la question de l\'utilisateur ?',
  (chunk) => console.log('Chunk reçu:', chunk) // Optionnel
);

console.log('Contexte RAG:', result.ragContext);
console.log('Statistiques:', result.processingStats);
```

### Conversion depuis Clara Documents
```typescript
const claraDocuments = [...]; // Documents du format Clara existant
const ragDocuments = RagManager.convertDocuments(claraDocuments);
```

## 🎛️ Configuration

### Basculement de Mode
Le mode est détecté automatiquement depuis `localStorage` :

```typescript
// Mode rapide
localStorage.setItem('clara-rag-fast-mode', 'true');

// Mode performant
localStorage.setItem('clara-rag-fast-mode', 'false');

// Utilitaires
import { isRagFastModeEnabled, toggleRagMode } from './services/rag';

const isRapid = isRagFastModeEnabled();
const newMode = toggleRagMode(); // Bascule entre les modes
```

### Paramètres Avancés
```typescript
// Mode rapide
const fastMode = new FastMode();
// Seuils : 50K chars, 200K contexte max

// Mode performant
const performantMode = new PerformantMode();
// Seuils : 20K chars, 100K contexte max
```

## 📊 Types de Retour

### RagProcessingResult
```typescript
interface RagProcessingResult {
  ragContext: string;           // Contexte formaté pour le LLM
  ragResults: RagResult[];      // Résultats détaillés
  processingStats: {
    mode: string;               // 'FAST ⚡' ou 'PERFORMANT 🧠'
    documentsProcessed: number;
    totalContextLength: number;
    processingTime: number;     // En millisecondes
    directDocuments: number;    // Documents traités directement
    ragProcessedDocuments: number; // Documents via RAG
  };
}
```

### RagResult
```typescript
interface RagResult {
  content: string;              // Contenu du document
  score: number;                // Score de pertinence (0-1)
  metadata: {
    source_file: string;        // Nom du fichier
    document_id: string;        // ID unique
    type: string;               // Type de fichier
  };
}
```

## 🧪 Tests

### Exécution des Tests
```bash
# Depuis le dossier ClaraVerse
npm run test:rag

# Ou directement
npx ts-node src/services/rag/test/RagTest.ts
```

### Tests Inclus
- ✅ Mode rapide (performance < 100ms)
- ✅ Mode performant (analyse hybride)
- ✅ Gestionnaire RAG (détection automatique)
- ✅ Conversion de documents
- ✅ Gestion d'erreurs

## 🔍 Debugging

### Logs de Debug
Le système génère des logs détaillés :

```
🔍 RAG Manager: Mode détecté → RAPIDE ⚡
⚡ MODE RAPIDE: Traitement ultra-simplifié activé
⚡ MODE RAPIDE: Traitement terminé en 15 ms
✅ RAG Manager: Traitement terminé en 15ms
```

### Vérification du Mode
```typescript
// Dans la console du navigateur
console.log('Mode RAG:', localStorage.getItem('clara-rag-fast-mode'));

// Dans le code
import { isRagFastModeEnabled } from './services/rag';
console.log('Mode rapide activé:', isRagFastModeEnabled());
```

## 🚀 Performance

### Benchmarks Attendus
- **Mode Rapide** : 2-50ms (selon taille des documents)
- **Mode Performant** : 50-500ms (selon complexité)
- **Ancien Système** : 2000-20000ms (remplacé)

### Optimisations
- ✅ Pas de recherche vectorielle en mode rapide
- ✅ Gestion de contexte simplifiée
- ✅ Traitement parallèle des documents
- ✅ Cache automatique des résultats

## 🔄 Migration

### Depuis l'Ancien Système
L'ancien code RAG dans `claraApiService.ts` a été remplacé par :

```typescript
// Ancien (200+ lignes complexes)
// ... logique RAG complexe ...

// Nouveau (5 lignes simples)
const ragDocuments = RagManager.convertDocuments(selectedDocuments);
const ragResult = await this.ragManager.processDocuments(ragDocuments, userQuery, onContentChunk);
ragContext = ragResult.ragContext;
```

### Compatibilité
- ✅ Interface identique pour `claraApiService.ts`
- ✅ Même format de sortie
- ✅ Même gestion d'erreurs
- ✅ Logs améliorés

## 🛠️ Développement

### Ajouter un Nouveau Mode
1. Créer une classe héritant de `BaseMode`
2. Implémenter `processDocuments()`
3. Ajouter au `RagManager`
4. Créer des tests

### Exemple de Nouveau Mode
```typescript
export class CustomMode extends BaseMode {
  async processDocuments(documents, userQuery, onContentChunk) {
    // Logique personnalisée
    return {
      ragContext: '...',
      ragResults: [...],
      processingStats: { ... }
    };
  }
}
```

## 📝 Notes Importantes

- 🔒 **Thread-Safe** : Chaque instance est isolée
- 🌐 **Browser/Node** : Compatible navigateur et serveur
- 📱 **Responsive** : Adaptatif selon les ressources
- 🔄 **Fallback** : Mode rapide en cas d'erreur du mode performant
- 🎯 **Français** : Optimisé pour les réponses en français
