@echo off
echo 🔍 Démarrage de SearXNG pour WeMa IA...

cd searxng

echo Vérification de Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker n'est pas installé ou démarré
    echo Veuillez installer Docker Desktop et le démarrer
    pause
    exit /b 1
)

echo Démarrage de SearXNG...
docker-compose up -d

echo Attente du démarrage de SearXNG...
timeout /t 10 /nobreak >nul

echo Test de l'API SearXNG...
curl -s "http://localhost:8888/search?q=test&format=json" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SearXNG est démarré et fonctionnel sur http://localhost:8888
) else (
    echo ⚠️ SearXNG démarre encore, patientez quelques secondes...
)

echo.
echo 🚀 SearXNG est prêt pour WeMa IA !
echo 📖 Interface web: http://localhost:8888
echo 🔧 API JSON: http://localhost:8888/search?q=VOTRE_RECHERCHE&format=json
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
