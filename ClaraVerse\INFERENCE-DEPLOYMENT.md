# 🧠 Guide de Déploiement - Délégation d'Inférence WeMa IA

## 🎯 Architecture finale

```
PC Utilisateur 1 (Patrimoine)
├── Frontend React ✅ LOCAL
├── Backend Python ✅ LOCAL
│   ├── RAG (LightRAG + Qdrant) ✅ LOCAL
│   ├── OCR (Tesseract) ✅ LOCAL  
│   ├── Documents SQLite ✅ LOCAL
│   └── Inférence → 🌐 SERVEUR

PC Utilisateur 2 (Mandats)
├── Frontend React ✅ LOCAL
├── Backend Python ✅ LOCAL
│   ├── RAG (LightRAG + Qdrant) ✅ LOCAL
│   ├── OCR (Tesseract) ✅ LOCAL
│   ├── Documents SQLite ✅ LOCAL
│   └── Inférence → 🌐 SERVEUR

Serveur Central (*************)
├── LM Studio (port 1234) 🧠 INFÉRENCE
├── Serveur d'inférence (port 1235) 🔄 PROXY
└── Dashboard Admin (port 9000) 🎛️ MONITORING
```

## 🚀 Installation Serveur (*************)

### 1. Préparer LM Studio
```bash
# Télécharger et installer LM Studio
# Télécharger un modèle (ex: <PERSON>wen 1.7B ou 8B)
# Démarrer le serveur local sur port 1234
```

### 2. Installer le serveur d'inférence
```bash
# Copier le dossier server-inference/ sur le serveur
cd server-inference
pip install fastapi uvicorn requests pydantic

# Démarrer le serveur
python start_inference_server.py
# OU
start_server.bat
```

### 3. Vérifier l'installation
```bash
# Tester LM Studio
curl http://localhost:1234/v1/models

# Tester le serveur d'inférence
curl http://localhost:1235/health
```

## 👥 Configuration Clients

### 1. Copier la configuration
```bash
# Copier .env.inference vers .env
cp .env.inference .env

# Modifier l'IP du serveur si nécessaire
INFERENCE_SERVER_IP=*************
INFERENCE_SERVER_PORT=1235
```

### 2. Démarrer l'application
```bash
# Backend
cd py_backend
python main.py

# Frontend
npm run dev
```

### 3. Vérifier la délégation
- Ouvrir l'application WeMa IA
- Envoyer un message de test
- Vérifier dans les logs : "🌐 Utilisation serveur central"

## 🎛️ Monitoring Admin

### Dashboard d'inférence
- Ajouter le composant `InferenceDashboard` à l'interface admin
- Surveiller les requêtes en temps réel
- Vérifier le statut de LM Studio

### URLs de monitoring
- Serveur d'inférence: http://*************:1235/health
- Statistiques: http://*************:1235/stats
- LM Studio: http://*************:1234/v1/models

## 🔧 Configuration Avancée

### Variables d'environnement serveur
```bash
# Port du serveur d'inférence
INFERENCE_PORT=1235

# URL de LM Studio
LM_STUDIO_URL=http://localhost:1234

# Nombre max de requêtes simultanées
MAX_CONCURRENT_REQUESTS=5
```

### Variables d'environnement client
```bash
# Serveur d'inférence
INFERENCE_SERVER_IP=*************
INFERENCE_SERVER_PORT=1235

# Fallback local si serveur indisponible
INFERENCE_FALLBACK_LOCAL=true

# Debug
DEBUG_INFERENCE=false
```

## 📊 Avantages de cette Architecture

### ✅ Performance utilisateur
- **8-25GB RAM libérée** par PC utilisateur
- **CPU/GPU disponible** pour autres tâches
- **Système réactif** et fluide
- **Démarrage rapide** (pas de chargement de modèles)

### ✅ Performance d'inférence
- **4-10x plus rapide** avec GPU dédié serveur
- **Modèles plus puissants** possibles sur serveur
- **Partage efficace** des ressources

### ✅ Confidentialité préservée
- **Documents restent locaux** (jamais envoyés au serveur)
- **RAG local** (traitement des documents sur le PC)
- **OCR local** (reconnaissance de texte sur le PC)
- **Seul le texte final** est envoyé pour inférence

### ✅ Maintenance simplifiée
- **Mise à jour centralisée** des modèles IA
- **Monitoring centralisé** des performances
- **Fallback automatique** sur local si serveur indisponible

## 🔍 Dépannage

### Serveur d'inférence ne démarre pas
```bash
# Vérifier Python
python --version  # Doit être 3.8+

# Installer les dépendances
pip install fastapi uvicorn requests pydantic

# Vérifier LM Studio
curl http://localhost:1234/v1/models
```

### Client ne se connecte pas au serveur
```bash
# Vérifier la configuration
cat .env | grep INFERENCE

# Tester la connexion
curl http://*************:1235/health

# Vérifier les logs backend
tail -f py_backend/logs/app.log
```

### Performance dégradée
- Vérifier la charge du serveur: `/stats`
- Augmenter `MAX_CONCURRENT_REQUESTS` si nécessaire
- Vérifier la latence réseau
- Considérer un modèle plus léger (Qwen 1.7B vs 8B)

## 📈 Optimisations

### Serveur puissant recommandé
- **CPU**: 8+ cores
- **RAM**: 32GB+ (pour modèles 8B+)
- **GPU**: RTX 4070+ (optionnel mais recommandé)
- **Réseau**: Gigabit Ethernet

### Modèles recommandés
- **Léger**: Qwen 1.7B (3GB RAM, rapide)
- **Équilibré**: Qwen 8B (12GB RAM, qualité/vitesse)
- **Puissant**: Gemma 27B (30GB RAM, haute qualité)

### Monitoring continu
- Surveiller `/stats` pour la charge
- Logs du serveur d'inférence
- Performance réseau entre clients et serveur

## 🎉 Résultat Final

**Architecture optimale** qui combine :
- **Confidentialité** : Données sensibles restent locales
- **Performance** : Inférence rapide sur serveur dédié
- **Simplicité** : Déploiement et maintenance faciles
- **Économie** : 1 serveur puissant vs N PC puissants
- **Flexibilité** : Fallback local si serveur indisponible
