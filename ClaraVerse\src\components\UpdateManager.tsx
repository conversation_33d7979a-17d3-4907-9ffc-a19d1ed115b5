/**
 * 🚀 Gestionnaire de mises à jour WeMa IA
 * Interface admin pour créer et déployer des mises à jour sélectives
 */

import React, { useState, useEffect } from 'react';
import { Package, Send, Settings, Users, Download, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface Feature {
  name: string;
  enabled: boolean;
  description: string;
  files: string[];
}

interface PoleConfig {
  pole: string;
  features: Record<string, boolean>;
  ips: string[];
  lastUpdate?: string;
  status: 'online' | 'offline' | 'updating';
}

interface UpdatePackage {
  version: string;
  target_poles: string[];
  target_pcs?: string[];
  features: Record<string, boolean>;
  description: string;
  created_at: string;
  status: 'created' | 'deploying' | 'deployed' | 'failed';
}

const UpdateManager: React.FC = () => {
  const [availableFeatures, setAvailableFeatures] = useState<Record<string, Feature>>({});
  const [poleConfigs, setPoleConfigs] = useState<Record<string, PoleConfig>>({});
  const [packages, setPackages] = useState<UpdatePackage[]>([]);
  const [selectedPoles, setSelectedPoles] = useState<string[]>([]);
  const [customFeatures, setCustomFeatures] = useState<Record<string, boolean>>({});
  const [updateDescription, setUpdateDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // Charger les données initiales
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Charger les fonctionnalités disponibles
      const featuresResponse = await fetch('/api/admin/features');
      if (featuresResponse.ok) {
        const features = await featuresResponse.json();
        setAvailableFeatures(features);
      }

      // Charger les configurations des pôles
      const polesResponse = await fetch('/api/admin/poles');
      if (polesResponse.ok) {
        const poles = await polesResponse.json();
        setPoleConfigs(poles);
      }

      // Charger les packages existants
      const packagesResponse = await fetch('/api/admin/packages');
      if (packagesResponse.ok) {
        const packages = await packagesResponse.json();
        setPackages(packages);
      }
    } catch (error) {
      console.error('Erreur chargement données:', error);
    }
  };

  const handleFeatureToggle = (featureName: string, enabled: boolean) => {
    setCustomFeatures(prev => ({
      ...prev,
      [featureName]: enabled
    }));
  };

  const createCustomBuild = async (pole: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/build', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pole,
          features: customFeatures,
          description: `Build personnalisé pour ${pole}`
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Build créé avec succès: ${result.build_path}`);
        loadInitialData(); // Recharger les données
      } else {
        throw new Error('Erreur création build');
      }
    } catch (error) {
      alert(`Erreur: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const createUpdatePackage = async () => {
    if (selectedPoles.length === 0) {
      alert('Sélectionnez au moins un pôle');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/package', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          poles: selectedPoles,
          features: customFeatures,
          description: updateDescription
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Package créé: ${result.package_path}`);
        setUpdateDescription('');
        loadInitialData();
      } else {
        throw new Error('Erreur création package');
      }
    } catch (error) {
      alert(`Erreur: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const deployPackage = async (packageVersion: string) => {
    if (!confirm(`Déployer la mise à jour ${packageVersion} ?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/deploy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          package_version: packageVersion
        })
      });

      if (response.ok) {
        alert('Déploiement lancé avec succès');
        loadInitialData();
      } else {
        throw new Error('Erreur déploiement');
      }
    } catch (error) {
      alert(`Erreur: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'offline':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'updating':
        return <Clock className="w-5 h-5 text-yellow-600" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      case 'updating':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Package className="w-7 h-7 mr-3 text-blue-600" />
            Gestionnaire de Mises à Jour
          </h2>
          <p className="text-gray-600 mt-1">Créer et déployer des mises à jour sélectives</p>
        </div>
      </div>

      {/* Statut des pôles */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Users className="w-5 h-5 mr-2" />
          Statut des Pôles
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(poleConfigs).map(([pole, config]) => (
            <div key={pole} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium capitalize">{pole}</h4>
                {getStatusIcon(config.status)}
              </div>
              
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(config.status)}`}>
                {config.status}
              </div>
              
              <div className="mt-2 text-sm text-gray-600">
                <div>IPs: {config.ips.join(', ')}</div>
                {config.lastUpdate && (
                  <div>Dernière MAJ: {new Date(config.lastUpdate).toLocaleDateString()}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Configuration des fonctionnalités */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Configuration des Fonctionnalités
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(availableFeatures).map(([featureName, feature]) => (
            <div key={featureName} className="flex items-center justify-between p-3 border rounded">
              <div>
                <h4 className="font-medium">{feature.name}</h4>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={customFeatures[featureName] ?? feature.enabled}
                  onChange={(e) => handleFeatureToggle(featureName, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Sélection des pôles cibles */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Pôles Cibles</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.keys(poleConfigs).map(pole => (
            <label key={pole} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedPoles.includes(pole)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedPoles([...selectedPoles, pole]);
                  } else {
                    setSelectedPoles(selectedPoles.filter(p => p !== pole));
                  }
                }}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="capitalize">{pole}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Description de la mise à jour */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Description de la Mise à Jour</h3>
        
        <textarea
          value={updateDescription}
          onChange={(e) => setUpdateDescription(e.target.value)}
          placeholder="Décrivez les changements de cette mise à jour..."
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={3}
        />
      </div>

      {/* Actions */}
      <div className="flex space-x-4">
        <button
          onClick={createUpdatePackage}
          disabled={loading || selectedPoles.length === 0}
          className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Package className="w-5 h-5 mr-2" />
          {loading ? 'Création...' : 'Créer Package'}
        </button>
        
        <button
          onClick={() => loadInitialData()}
          className="flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          <Download className="w-5 h-5 mr-2" />
          Actualiser
        </button>
      </div>

      {/* Liste des packages */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Packages de Mise à Jour</h3>
        
        <div className="space-y-3">
          {packages.map(pkg => (
            <div key={pkg.version} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Version {pkg.version}</h4>
                <p className="text-sm text-gray-600">{pkg.description}</p>
                <p className="text-xs text-gray-500">
                  Cibles: {pkg.target_poles.join(', ')} • {new Date(pkg.created_at).toLocaleString()}
                </p>
              </div>
              
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(pkg.status)}`}>
                  {pkg.status}
                </span>
                
                {pkg.status === 'created' && (
                  <button
                    onClick={() => deployPackage(pkg.version)}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    <Send className="w-4 h-4 mr-1" />
                    Déployer
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UpdateManager;
