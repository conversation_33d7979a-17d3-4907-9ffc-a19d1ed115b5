// RAG/search logic utilities for Assistant

export interface SearchResult {
  score: number;
  content: string;
  metadata?: {
    source_file?: string;
    [key: string]: any;
  };
}

export interface SearchResponse {
  results: SearchResult[];
}

export interface DocumentInfo {
  id: number;
  filename: string;
  collectionName: string;
  fileType: string;
  chunkCount: number;
  isAnonymized: boolean;
}

export interface RagSearchOptions {
  ragEnabled: boolean;
  tempDocuments: { collection: string }[];
  selectedDocuments: DocumentInfo[];
}

export const searchDocuments = async (
  query: string,
  pythonPort: number | null,
  temporaryDocs: { collection: string }[],
  ragEnabled: boolean
): Promise<SearchResponse | null> => {
  if (!pythonPort) return null;

  try {
    // If there are temporary documents, only use temp collections
    if (temporaryDocs.length > 0) {
      const tempResults = await Promise.all(
        temporaryDocs.map(async (doc) => {
          try {
            const response = await fetch(`http://0.0.0.0:${pythonPort}/documents/search`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                query,
                collection_name: doc.collection,
                k: 8,
              }),
            });
            if (!response.ok) {
              console.warn(`Search failed for collection ${doc.collection}:`, response.status);
              return { results: [] };
            }
            return await response.json() as SearchResponse;
          } catch (error) {
            console.warn(`Search error for collection ${doc.collection}:`, error);
            return { results: [] };
          }
        })
      );
      // For temp docs, use all results regardless of score
      const allTempResults = tempResults.flatMap(r => r.results || []);
      // Sort by score and return
      return {
        results: allTempResults
          .sort((a, b) => (b.score || 0) - (a.score || 0))
          .slice(0, 8)
      };
    }
    // 🚀 UNIFIED RAG: Si RAG activé et pas de docs temporaires, rechercher dans TOUTES les collections
    if (ragEnabled) {
      try {
        // 🚀 OPTIMISATION: Cache côté client + timeout + mode rapide
        const cacheKey = `rag_${query.substring(0, 50)}`;
        const cachedResult = sessionStorage.getItem(cacheKey);
        if (cachedResult) {
          const parsed = JSON.parse(cachedResult);
          const cacheAge = Date.now() - parsed.timestamp;
          if (cacheAge < 30000) { // Cache 30 secondes
            console.log('🎯 RAG Cache HIT:', query.substring(0, 30));
            return { results: parsed.results };
          }
        }

        // 🚀 Recherche avec timeout court
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 2000); // 2s timeout

        const response = await fetch(`http://localhost:${pythonPort}/rag/search`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query,
            use_cache: true,        // 🎯 Cache serveur
            use_lightrag: true,
            use_vector_store: true,
            limit: 5,              // 🎯 Moins de résultats = plus rapide
            fast_mode: true        // 🎯 Mode rapide
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const ragResults = await response.json();
          console.log(`🚀 RAG Fast: ${ragResults.vector_results?.length || 0} results in ${ragResults.processing_time || 'N/A'}s`);

          // Convertir les résultats RAG Premium au format attendu
          const results = ragResults.vector_results || [];
          const filteredResults = results.length <= 2
            ? results
            : results.filter((result: any) => (result.score || 0) > 0);

          // 🎯 Cache le résultat
          sessionStorage.setItem(cacheKey, JSON.stringify({
            results: filteredResults.slice(0, 5),
            timestamp: Date.now()
          }));

          return {
            results: filteredResults.slice(0, 5)
          };
        } else {
          console.warn('RAG Premium search failed, falling back to legacy search:', response.status);

          // 🎯 FALLBACK OPTIMISÉ: Une seule collection prioritaire
          try {
            const fallbackResponse = await fetch(`http://localhost:${pythonPort}/documents/search`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                query,
                collection_name: 'chat_uploads', // 🎯 Collection la plus pertinente
                k: 5,
              }),
            });

            if (fallbackResponse.ok) {
              const data = await fallbackResponse.json();
              return { results: data.results || [] };
            }
          } catch (error) {
            console.warn('⚠️ Fallback search failed:', error);
          }
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.warn('⚠️ RAG search timeout, using empty results');
        } else {
          console.warn('⚠️ RAG search error:', error);
        }
        return { results: [] };
      }
    }
    // If neither temp docs nor RAG enabled, return empty results
    return { results: [] };
  } catch (error) {
    console.error('Error searching documents:', error);
    return { results: [] };
  }
};

/**
 * RAG Search function for selected documents
 * Searches through selected documents and returns relevant chunks
 */
export const ragSearch = async (
  query: string,
  options: RagSearchOptions
): Promise<SearchResponse> => {
  const { ragEnabled, tempDocuments, selectedDocuments } = options;

  // If no documents selected, return empty results
  if (!selectedDocuments || selectedDocuments.length === 0) {
    return { results: [] };
  }

  try {
    // Use the Python backend port (default 8000)
    const pythonPort = 8000;

    // Group selected documents by collection
    const documentsByCollection = selectedDocuments.reduce((acc, doc) => {
      // Handle both collectionName and collection_name formats
      const collectionName = doc.collectionName || (doc as any).collection_name || 'default_collection';
      console.log(`📁 RAG: Document "${doc.filename}" in collection "${collectionName}"`);

      if (!acc[collectionName]) {
        acc[collectionName] = [];
      }
      acc[collectionName].push(doc);
      return acc;
    }, {} as Record<string, DocumentInfo[]>);

    console.log('📊 RAG: Documents grouped by collection:', Object.keys(documentsByCollection));

    // Search in each collection
    const searchPromises = Object.entries(documentsByCollection).map(async ([collectionName, docs]) => {
      try {
        // Create filter to only search within selected documents
        const documentFilter = {
          source_file: {
            $in: docs.map(doc => doc.filename)
          }
        };

        const searchPayload = {
          query,
          collection_name: collectionName,
          k: 8,
          filter: documentFilter
        };

        console.log(`🔍 RAG: Searching collection "${collectionName}" with payload:`, searchPayload);

        const response = await fetch(`http://localhost:${pythonPort}/documents/search`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(searchPayload),
        });

        console.log(`📡 RAG: Search response for "${collectionName}":`, response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.warn(`RAG search failed for collection ${collectionName}:`, response.status, errorText);
          return { results: [] };
        }

        const data = await response.json();
        console.log(`📚 RAG: Found ${data.results?.length || 0} results in collection "${collectionName}"`);
        return data as SearchResponse;
      } catch (error) {
        console.warn(`RAG search error for collection ${collectionName}:`, error);
        return { results: [] };
      }
    });

    // Wait for all searches to complete
    const searchResults = await Promise.all(searchPromises);

    // Combine and sort results
    const allResults = searchResults.flatMap(r => r.results || []);
    const sortedResults = allResults
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .slice(0, 8); // Limit to top 8 results

    console.log(`🔍 RAG Search: Found ${sortedResults.length} relevant chunks from ${Object.keys(documentsByCollection).length} collections`);

    return {
      results: sortedResults
    };

  } catch (error) {
    console.error('❌ RAG search error:', error);
    return { results: [] };
  }
};