<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Limitation Contexte 10K - Recherche + Documents</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }
        .scenario {
            background: #f1f5f9;
            border-left: 4px solid #3b82f6;
            padding: 16px;
            margin: 16px 0;
            border-radius: 0 8px 8px 0;
        }
        .result {
            background: #ecfdf5;
            border: 1px solid #10b981;
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            padding: 12px;
            border-radius: 8px;
            margin: 8px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
        }
        .stat-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
        }
        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }
        .code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 12px 0;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px 8px 8px 0;
        }
        button:hover {
            background: #2563eb;
        }
        .success { color: #10b981; font-weight: bold; }
        .warning-text { color: #f59e0b; font-weight: bold; }
        .error-text { color: #ef4444; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 Test Limitation Contexte 10K - Recherche + Documents</h1>
        <p>Ce test valide que la nouvelle logique limite correctement le contexte des documents à 10K caractères quand la recherche internet est activée.</p>
        
        <button onclick="runAllTests()">🚀 Exécuter tous les tests</button>
        <button onclick="clearResults()">🧹 Effacer les résultats</button>
    </div>

    <div id="results"></div>

    <script>
        // Simulation des documents avec différentes tailles
        const mockDocuments = [
            {
                id: 'doc1',
                filename: 'petit_document.txt',
                content: 'A'.repeat(2000), // 2K caractères
                fileType: 'text'
            },
            {
                id: 'doc2', 
                filename: 'document_moyen.pdf',
                content: 'B'.repeat(5000), // 5K caractères
                fileType: 'pdf'
            },
            {
                id: 'doc3',
                filename: 'gros_document.docx', 
                content: 'C'.repeat(8000), // 8K caractères
                fileType: 'docx'
            },
            {
                id: 'doc4',
                filename: 'tres_gros_document.pdf',
                content: 'D'.repeat(12000), // 12K caractères
                fileType: 'pdf'
            }
        ];

        // Simulation de la logique de limitation (copie de la logique ClaraAssistant)
        function simulateContextLimitation(documents, hasSearchMode) {
            const maxDocumentContext = 10000; // 10K caractères maximum pour les documents
            let totalDocumentSize = 0;
            const limitedDocuments = [];
            const excludedDocuments = [];

            for (const doc of documents) {
                // Utiliser la taille réelle du contenu du document
                const actualSize = (doc.content?.length || 0) + (doc.filename?.length || 0) + 100; // +100 pour les métadonnées

                if (totalDocumentSize + actualSize <= maxDocumentContext) {
                    limitedDocuments.push(doc);
                    totalDocumentSize += actualSize;
                } else {
                    excludedDocuments.push(doc);
                }
            }

            return {
                limitedDocuments,
                excludedDocuments,
                totalDocumentSize,
                hasSearchMode
            };
        }

        function runTest(testName, documents, hasSearchMode, expectedBehavior) {
            const result = simulateContextLimitation(documents, hasSearchMode);
            
            const container = document.createElement('div');
            container.className = 'test-container';
            
            container.innerHTML = `
                <h2 class="test-title">📋 ${testName}</h2>
                
                <div class="scenario">
                    <strong>Scénario :</strong> ${documents.length} documents, Recherche: ${hasSearchMode ? '✅ Activée' : '❌ Désactivée'}
                    <br><strong>Comportement attendu :</strong> ${expectedBehavior}
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">${documents.length}</div>
                        <div class="stat-label">Documents initiaux</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${result.limitedDocuments.length}</div>
                        <div class="stat-label">Documents inclus</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${result.excludedDocuments.length}</div>
                        <div class="stat-label">Documents exclus</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${result.totalDocumentSize.toLocaleString()}</div>
                        <div class="stat-label">Taille totale (chars)</div>
                    </div>
                </div>

                <div class="result">
                    <strong>✅ Résultat :</strong>
                    <ul>
                        <li><strong>Documents inclus :</strong> ${result.limitedDocuments.map(d => `${d.filename} (${d.content.length} chars)`).join(', ')}</li>
                        ${result.excludedDocuments.length > 0 ? `<li><strong>Documents exclus :</strong> ${result.excludedDocuments.map(d => `${d.filename} (${d.content.length} chars)`).join(', ')}</li>` : ''}
                        <li><strong>Taille totale contexte :</strong> ${result.totalDocumentSize.toLocaleString()} caractères</li>
                        <li><strong>Limite respectée :</strong> ${result.totalDocumentSize <= 10000 ? '<span class="success">✅ OUI</span>' : '<span class="error-text">❌ NON</span>'}</li>
                    </ul>
                </div>

                ${result.excludedDocuments.length > 0 ? `
                <div class="warning">
                    <strong>⚠️ Message utilisateur simulé :</strong><br>
                    Optimisation contexte : Recherche internet + documents détectée. ${result.excludedDocuments.length} document(s) exclu(s) pour respecter la limite de 10K caractères et maintenir des performances optimales. Documents inclus : ${result.limitedDocuments.map(d => d.filename).join(', ')}.
                </div>
                ` : ''}
            `;

            return container;
        }

        function runAllTests() {
            clearResults();
            const resultsContainer = document.getElementById('results');

            // Test 1: Documents seuls (pas de limitation)
            const test1 = runTest(
                'Test 1: Documents seuls (sans recherche)',
                mockDocuments,
                false,
                'Tous les documents devraient être inclus (pas de limite)'
            );
            resultsContainer.appendChild(test1);

            // Test 2: Recherche + petits documents (dans la limite)
            const test2 = runTest(
                'Test 2: Recherche + petits documents',
                mockDocuments.slice(0, 2), // 2K + 5K = 7K + métadonnées
                true,
                'Tous les documents devraient être inclus (< 10K)'
            );
            resultsContainer.appendChild(test2);

            // Test 3: Recherche + documents moyens (limite atteinte)
            const test3 = runTest(
                'Test 3: Recherche + documents moyens',
                mockDocuments.slice(0, 3), // 2K + 5K + 8K = 15K + métadonnées
                true,
                'Certains documents devraient être exclus (> 10K)'
            );
            resultsContainer.appendChild(test3);

            // Test 4: Recherche + tous les documents (limitation forte)
            const test4 = runTest(
                'Test 4: Recherche + tous les documents',
                mockDocuments, // 2K + 5K + 8K + 12K = 27K + métadonnées
                true,
                'Plusieurs documents devraient être exclus (>> 10K)'
            );
            resultsContainer.appendChild(test4);

            // Test 5: Un seul gros document avec recherche
            const test5 = runTest(
                'Test 5: Recherche + un gros document',
                [mockDocuments[3]], // 12K caractères
                true,
                'Le document devrait être exclu (> 10K)'
            );
            resultsContainer.appendChild(test5);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Exécuter les tests au chargement
        window.onload = () => {
            console.log('🧪 Page de test chargée - Prêt pour les tests de limitation contexte');
        };
    </script>
</body>
</html>
